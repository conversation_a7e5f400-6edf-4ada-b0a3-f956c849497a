package com.imile.attendance.rule;

import com.imile.attendance.base.BaseTest;
import com.imile.attendance.infrastructure.repository.employee.modle.UserInfoDO;
import com.imile.attendance.infrastructure.repository.rule.dao.PunchConfigDao;
import com.imile.attendance.infrastructure.repository.rule.dao.PunchConfigRangeDao;
import com.imile.attendance.infrastructure.repository.rule.model.PunchConfigDO;
import com.imile.attendance.infrastructure.repository.rule.model.PunchConfigRangeDO;
import com.imile.attendance.util.DateHelper;
import com.imile.attendance.infrastructure.repository.rule.query.RuleRangeUserQuery;
import org.junit.Test;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR> chen
 * @Date 2025/4/11 
 * @Description
 */
public class PunchConfigManageTest extends BaseTest {

    @Resource
    private PunchConfigManage punchConfigManage;
    @Resource
    private PunchConfigDao punchConfigDao;
    @Resource
    private PunchConfigRangeDao punchConfigRangeDao;

    @Test
    public void testlistAllRangeByUserId() {
        List<PunchConfigRangeDO> punchConfigRangeDOS = punchConfigRangeDao.listAllRangeByUserIds(Collections.singletonList(1103011271906697217L));
        Optional.ofNullable(punchConfigRangeDOS).ifPresent(System.out::println);
    }

    @Test
    public void testListCountryLevelConfigsByCountries() {
        List<PunchConfigDO> punchConfigDOS = punchConfigDao.listCountryLevelConfigsByCountries(Arrays.asList("CHN", "ISL"));
        Optional.ofNullable(punchConfigDOS).ifPresent(System.out::println);
    }

    @Test
    public void testmapByUserIds() {
        Map<Long, PunchConfigDO> map = punchConfigManage.mapByUserIds(Arrays.asList(1103011271906697217L), new Date());
        Optional.ofNullable(map).ifPresent(System.out::println);

        Date date = DateHelper.parseYYYYMMDDHHMMSS("2025-05-21 22:00:00");
        System.out.println(date.getTime());
        Map<Long, PunchConfigDO> ma2 = punchConfigManage.mapByUserIds(Arrays.asList(1103011271906697217L), date);
        Optional.ofNullable(ma2).ifPresent(System.out::println);
    }

    @Test
    public void testListOnJobNoDriverUsersExcludeConfigured(){
        System.out.println("=============single country==================");

        RuleRangeUserQuery singleCountryQuery = RuleRangeUserQuery.builder()
                .country("ITA")
                .build();
        List<UserInfoDO> userInfoDOS = punchConfigRangeDao.listOnJobNoDriverUsersExcludeConfigured(singleCountryQuery);
        Optional.ofNullable(userInfoDOS)
                .ifPresent(i->System.out.println(i.size()));

//        System.out.println("============normal+special country===================");
//
//        RuleRangeUserQuery multiCountryQuery = RuleRangeUserQuery.builder()
//                .countries(Arrays.asList("CHN", "OMN"))
//                .build();
//        List<UserInfoDO> userInfoDOS1 = punchConfigRangeDao.listOnJobNoDriverUsersExcludeConfigured(multiCountryQuery);
//        Optional.ofNullable(userInfoDOS1)
//                .ifPresent(i->System.out.println(i.size()));
//
//        System.out.println("===========multi special country====================");
//
//        RuleRangeUserQuery multiSpecialCountryQuery = RuleRangeUserQuery.builder()
//                .countries(Arrays.asList("KWT", "BRA"))
//                .build();
//        List<UserInfoDO> userInfoDOS2 = punchConfigRangeDao.listOnJobNoDriverUsersExcludeConfigured(multiSpecialCountryQuery);
//        Optional.ofNullable(userInfoDOS2)
//                .ifPresent(i->System.out.println(i.size()));
//
//        System.out.println("============multi normal country====================");
//
//        RuleRangeUserQuery multiNormalCountryQuery = RuleRangeUserQuery.builder()
//                .countries(Arrays.asList("ITA", "AUS"))
//                .build();
//        List<UserInfoDO> userInfoDOS3 = punchConfigRangeDao.listOnJobNoDriverUsersExcludeConfigured(multiNormalCountryQuery);
//        Optional.ofNullable(userInfoDOS3)
//                .ifPresent(i->System.out.println(i.size()));

    }

}
