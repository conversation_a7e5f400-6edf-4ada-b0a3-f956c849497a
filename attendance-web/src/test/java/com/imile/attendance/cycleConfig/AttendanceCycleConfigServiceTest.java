package com.imile.attendance.cycleConfig;

import com.imile.attendance.cycleConfig.dto.AttendanceDayCycleDTO;
import com.imile.attendance.cycleConfig.enums.AttendanceCycleTypeEnum;
import com.imile.attendance.cycleConfig.enums.CycleTypeEnum;
import com.imile.attendance.cycleConfig.factory.AttendanceCycleConfigFactory;
import com.imile.attendance.infrastructure.repository.common.AttendanceUserService;
import com.imile.attendance.infrastructure.repository.common.UserResourceService;
import com.imile.attendance.infrastructure.repository.common.dto.AttendanceUser;
import com.imile.attendance.infrastructure.repository.cycleConfig.dao.AttendanceCycleConfigDao;
import com.imile.attendance.infrastructure.repository.cycleConfig.model.AttendanceCycleConfigDO;
import com.imile.attendance.infrastructure.repository.cycleConfig.query.AttendanceCycleConfigQuery;
import com.imile.common.enums.StatusEnum;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.MockitoAnnotations;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertSame;
import static org.mockito.Mockito.*;

/**
 * <AUTHOR> chen
 * @Date 2025/3/24 
 * @Description
 */
public class AttendanceCycleConfigServiceTest {

    @InjectMocks
    private AttendanceCycleConfigService attendanceCycleConfigService;

    @Mock
    private AttendanceCycleConfigFactory cycleConfigFactory;

    @Mock
    private AttendanceCycleConfigDao attendanceCycleConfigDao;

    @Mock
    private AttendanceUserService userService;

    @Mock
    private UserResourceService userResourceService;

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    // Tests for getByCountryAndCycleType method
    @Test
    public void testGetByCountryAndCycleType_WithExistingActiveConfig() {
        // Arrange
        String country = "USA";
        Integer cycleType = AttendanceCycleTypeEnum.MONTH.getType();

        AttendanceCycleConfigDO activeConfig = new AttendanceCycleConfigDO();
        activeConfig.setStatus(StatusEnum.ACTIVE.getCode());

        List<AttendanceCycleConfigDO> configList = new ArrayList<>();
        configList.add(activeConfig);

        when(attendanceCycleConfigDao.selectByCondition(any(AttendanceCycleConfigQuery.class)))
                .thenReturn(configList);

        // Act
        AttendanceCycleConfigDO result = attendanceCycleConfigService.getByCountryAndCycleType(country, cycleType);

        // Assert
        Assertions.assertNotNull(result);
        assertSame(activeConfig, result);
        verify(attendanceCycleConfigDao).selectByCondition(any(AttendanceCycleConfigQuery.class));
        verify(cycleConfigFactory, never()).buildDefaultAttendanceCycleConfig();
    }

    @Test
    public void testGetByCountryAndCycleType_WithInactiveConfig() {
        // Arrange
        String country = "USA";
        Integer cycleType = AttendanceCycleTypeEnum.MONTH.getType();

        AttendanceCycleConfigDO inactiveConfig = new AttendanceCycleConfigDO();
        inactiveConfig.setStatus(StatusEnum.DISABLED.getCode());

        List<AttendanceCycleConfigDO> configList = new ArrayList<>();
        configList.add(inactiveConfig);

        AttendanceCycleConfigDO defaultConfig = new AttendanceCycleConfigDO();

        when(attendanceCycleConfigDao.selectByCondition(any(AttendanceCycleConfigQuery.class)))
                .thenReturn(configList);
        when(cycleConfigFactory.buildDefaultAttendanceCycleConfig())
                .thenReturn(defaultConfig);

        // Act
        AttendanceCycleConfigDO result = attendanceCycleConfigService.getByCountryAndCycleType(country, cycleType);

        // Assert
        assertNotNull(result);
        assertSame(defaultConfig, result);
        verify(attendanceCycleConfigDao).selectByCondition(any(AttendanceCycleConfigQuery.class));
        verify(cycleConfigFactory).buildDefaultAttendanceCycleConfig();
    }

    @Test
    public void testGetByCountryAndCycleType_WithNoConfig() {
        // Arrange
        String country = "USA";
        Integer cycleType = AttendanceCycleTypeEnum.MONTH.getType();

        List<AttendanceCycleConfigDO> emptyList = Collections.emptyList();
        AttendanceCycleConfigDO defaultConfig = new AttendanceCycleConfigDO();

        when(attendanceCycleConfigDao.selectByCondition(any(AttendanceCycleConfigQuery.class)))
                .thenReturn(emptyList);
        when(cycleConfigFactory.buildDefaultAttendanceCycleConfig())
                .thenReturn(defaultConfig);

        // Act
        AttendanceCycleConfigDO result = attendanceCycleConfigService.getByCountryAndCycleType(country, cycleType);

        // Assert
        Assertions.assertNotNull(result);
        Assertions.assertSame(defaultConfig, result);
        verify(attendanceCycleConfigDao).selectByCondition(any(AttendanceCycleConfigQuery.class));
        verify(cycleConfigFactory).buildDefaultAttendanceCycleConfig();
    }

    // Tests for getUserAttendanceCycleConfig method

    @Test
    public void testGetUserAttendanceCycleConfig_WithNullUserId() {
        // Act
        AttendanceCycleConfigDO result = attendanceCycleConfigService.getUserAttendanceCycleConfig(null);

        // Assert
        Assertions.assertNull(result);
        verify(userService, never()).getByUserId(any());
    }

    @Test
    public void testGetUserAttendanceCycleConfig_WithNonExistentUser() {
        // Arrange
        Long userId = 123L;

        when(userService.getByUserId(userId)).thenReturn(null);

        // Act
        AttendanceCycleConfigDO result = attendanceCycleConfigService.getUserAttendanceCycleConfig(userId);

        // Assert
        Assertions.assertNull(result);
        verify(userService).getByUserId(userId);
    }

    // Tests for getUserAttendanceCycleConfigUserCard method
    @Test
    public void testGetUserAttendanceCycleConfigUserCard_WithValidUser() {
        // Arrange
        Long userId = 123L;
        AttendanceUser user = new AttendanceUser();
        user.setLocationCountry("USA");

        AttendanceCycleConfigDO config = new AttendanceCycleConfigDO();

        when(userService.getByUserId(userId)).thenReturn(user);
        when(attendanceCycleConfigService.getByCountryAndCycleType(
                eq("USA"), eq(AttendanceCycleTypeEnum.MONTH.getType())))
                .thenReturn(config);

        // Act
        AttendanceCycleConfigDO result = attendanceCycleConfigService.getUserAttendanceCycleConfigUserCard(userId);

        // Assert
        Assertions.assertNotNull(result);
        Assertions.assertSame(config, result);
        verify(userService).getByUserId(userId);
    }

    @Test
    public void testGetUserAttendanceCycleConfigUserCard_WithNullUserId() {
        // Act
        AttendanceCycleConfigDO result = attendanceCycleConfigService.getUserAttendanceCycleConfigUserCard(null);

        // Assert
        Assertions.assertNull(result);
        verify(userService, never()).getByUserId(any());
    }

    @Test
    public void testGetUserAttendanceCycleConfigUserCard_WithNonExistentUser() {
        // Arrange
        Long userId = 123L;

        when(userService.getByUserId(userId)).thenReturn(null);

        // Act
        AttendanceCycleConfigDO result = attendanceCycleConfigService.getUserAttendanceCycleConfigUserCard(userId);

        // Assert
        Assertions.assertNull(result);
        verify(userService).getByUserId(userId);
    }

    // Tests for getUserAttendanceCycleConfigDay method
    @Test
    public void testGetUserAttendanceCycleConfigDay_WithValidParams() {
        // Arrange
        Long nowDayId = 20230101L;
        AttendanceCycleConfigDO config = new AttendanceCycleConfigDO();
        config.setCycleType(AttendanceCycleTypeEnum.MONTH.getType());
        config.setCycleStart("1");
        config.setCycleEnd(CycleTypeEnum.END_OF_MONTH_CODE);
        config.setAbnormalExpired(2);

        AttendanceDayCycleDTO expectedCycle = new AttendanceDayCycleDTO();

        // Mock the static method
        try (MockedStatic<AttendanceDayCycleDTO> mockedDTO = mockStatic(AttendanceDayCycleDTO.class)) {
            mockedDTO.when(() -> AttendanceDayCycleDTO.buildWithDayId(
                    eq(nowDayId),
                    eq(AttendanceCycleTypeEnum.MONTH.getType()),
                    eq("1"),
                    eq(CycleTypeEnum.END_OF_MONTH_CODE)
            )).thenReturn(expectedCycle);

            // Act
            AttendanceDayCycleDTO result = attendanceCycleConfigService.getUserAttendanceCycleConfigDay(nowDayId, config);

            // Assert
            assertNotNull(result);
            assertSame(expectedCycle, result);
        }
    }

}
