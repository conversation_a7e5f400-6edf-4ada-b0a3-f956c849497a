package com.imile.attendance.controller.warehouse;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.imile.attendance.annon.NoAuthRequired;
import com.imile.attendance.annon.NoLoginAuthRequired;
import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.controller.BaseController;
import com.imile.attendance.infrastructure.convert.ConverterService;
import com.imile.attendance.infrastructure.repository.warehouse.param.AddUserParam;
import com.imile.attendance.infrastructure.repository.warehouse.param.CertificateUploadParam;
import com.imile.attendance.infrastructure.repository.warehouse.param.UpdateUserParam;
import com.imile.attendance.warehouse.FaceEngineService;
import com.imile.attendance.warehouse.WarehouseClassesService;
import com.imile.attendance.warehouse.WarehouseExportService;
import com.imile.attendance.warehouse.WarehouseOcService;
import com.imile.attendance.warehouse.WarehouseOperatorAuthService;
import com.imile.attendance.warehouse.WarehouseReadService;
import com.imile.attendance.warehouse.WarehouseService;
import com.imile.attendance.warehouse.WarehouseSupplierService;
import com.imile.attendance.warehouse.WarehouseUserService;
import com.imile.attendance.warehouse.param.CheckVendorConfirmStatusParam;
import com.imile.attendance.warehouse.param.FaceCheckParam;
import com.imile.attendance.warehouse.param.FaceSaveParam;
import com.imile.attendance.warehouse.param.FaceSearchParam;
import com.imile.attendance.warehouse.param.FaceSearchRepeatParam;
import com.imile.attendance.warehouse.param.GetClassesByConditionParam;
import com.imile.attendance.warehouse.param.GetVendorConfirmContentParam;
import com.imile.attendance.warehouse.param.GetVendorListByOcListParam;
import com.imile.attendance.warehouse.param.InOrOutParam;
import com.imile.attendance.warehouse.param.InV2Param;
import com.imile.attendance.warehouse.param.OutParam;
import com.imile.attendance.warehouse.param.StatisticVendorExportParam;
import com.imile.attendance.warehouse.param.StatisticVendorParam;
import com.imile.attendance.warehouse.param.VendorConfirmClassesParam;
import com.imile.attendance.warehouse.param.WarehouseOperatorListParam;
import com.imile.attendance.warehouse.param.WarehouseOperatorSaveParam;
import com.imile.attendance.warehouse.param.WpmDataStatisticsParam;
import com.imile.attendance.warehouse.vo.CertificatesVO;
import com.imile.attendance.warehouse.vo.CheckVendorConfirmStatusResultVO;
import com.imile.attendance.warehouse.vo.ClassesBaseVO;
import com.imile.attendance.warehouse.vo.ClassesVO;
import com.imile.attendance.warehouse.vo.DataStatisticsBlackListVO;
import com.imile.attendance.warehouse.vo.DataStatisticsDetailsVO;
import com.imile.attendance.warehouse.vo.DataStatisticsVO;
import com.imile.attendance.warehouse.vo.GetVendorConfirmContentResultVO;
import com.imile.attendance.warehouse.vo.OcVO;
import com.imile.attendance.warehouse.vo.OutVO;
import com.imile.attendance.warehouse.vo.QrCodeResultVO;
import com.imile.attendance.warehouse.vo.QrCodeVerifyResultVO;
import com.imile.attendance.warehouse.vo.RecordListVO;
import com.imile.attendance.warehouse.vo.StatisticsVendorResultVO;
import com.imile.attendance.warehouse.vo.StatisticsVendorUserResultVO;
import com.imile.attendance.warehouse.vo.SubmitCheckVO;
import com.imile.attendance.warehouse.vo.UserFaceSearchRepeatVO;
import com.imile.attendance.warehouse.vo.UserFaceSearchVO;
import com.imile.attendance.warehouse.vo.UserVO;
import com.imile.attendance.warehouse.vo.VendorClassesConfirmVO;
import com.imile.attendance.warehouse.vo.VendorVO;
import com.imile.attendance.warehouse.vo.WarehouseOperatorListVO;
import com.imile.common.page.PaginationResult;
import com.imile.common.result.Result;
import com.imile.ucenter.api.authenticate.NoLoginRequired;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * 仓内考勤H5提供外部服务
 *
 * <AUTHOR> chen
 * @Date 2025/07/14
 * @Time 09:49
 * @Description
 */
@Slf4j
@RestController
@RequestMapping("/warehouse/h5")
public class WarehouseH5Controller extends BaseController {

    @Resource
    private FaceEngineService faceEngineService;
    @Resource
    private ConverterService converterService;
    @Resource
    private WarehouseSupplierService warehouseSupplierService;
    @Resource
    private WarehouseClassesService warehouseClassesService;
    @Resource
    private WarehouseOcService warehouseOcService;
    @Resource
    private WarehouseUserService warehouseUserService;
    @Resource
    private WarehouseService warehouseService;
    @Resource
    private WarehouseReadService warehouseReadService;
    @Resource
    private WarehouseOperatorAuthService warehouseOperatorAuthService;
    @Resource
    private WarehouseExportService warehouseExportService;

    /**
     * 获取当前登录人管理的网点
     */
    @GetMapping("/getAuthOcList")
    @NoAuthRequired
    @NoLoginAuthRequired
    public Result<List<OcVO>> getAuthOcList(String employeeType) {
        return Result.ok(warehouseOcService.getAuthOcList(employeeType));
    }

    /**
     * 获取指定用户拥有的网点权限
     */
    @GetMapping("/getUserOcList")
    @NoAuthRequired
    @NoLoginAuthRequired
    public Result<List<OcVO>> getUserOcList(@RequestParam("userId") Long userId) {
        return Result.ok(warehouseOcService.getAuthOcListByUserId(userId));
    }

    /**
     * 通过网点获取关联的供应商
     */
    @PostMapping("/getVendorListByOcList")
    @NoAuthRequired
    @NoLoginAuthRequired
    public Result<List<VendorVO>> getVendorListByOcList(@Validated @RequestBody GetVendorListByOcListParam param) {
        return Result.ok(warehouseSupplierService.getVendorList(param.getOcIdList()));
    }

    /**
     * 获取人员信息
     */
    @GetMapping("/getUser")
    @NoAuthRequired
    @NoLoginAuthRequired
    public Result<RecordListVO> getUser(@RequestParam("userId") Long userId) {
        RecordListVO user = warehouseUserService.getUser(userId);
        converterService.withAnnotationForSingle(user);
        return Result.ok(user);
    }

    /**
     * 通过工号、证件号获取所有关联的员工
     */
    @GetMapping("/getUserListByCode")
    @NoAuthRequired
    @NoLoginAuthRequired
    public Result<List<UserVO>> getUserListByCode(@RequestParam(value = "code") String code) {
        List<UserVO> list = warehouseUserService.getUserListByCode(code);
        return Result.ok(list);
    }

    /**
     * 通过工号、用户名获取所有关联的员工
     */
    @GetMapping("/searchUserList")
    @NoAuthRequired
    @NoLoginAuthRequired
    public Result<List<UserVO>> searchUserList(@RequestParam(value = "searchKey") String searchKey) {
        List<UserVO> list = warehouseUserService.searchUserList(searchKey);
        return Result.ok(list);
    }

    /**
     * 查询班次列表(小程序)
     */
    @GetMapping("/classes/list")
    @NoAuthRequired
    @NoLoginAuthRequired
    public Result<List<ClassesVO>> getClassesList(@RequestParam("ocId") Long ocId,
                                                  @RequestParam("currentTime") String currentTime,
                                                  @RequestParam("warehouseDate") String warehouseDate) {
        List<ClassesVO> vos = warehouseClassesService.getClassesList(ocId, currentTime, warehouseDate);
        return Result.ok(vos);
    }

    /**
     * 查询指定网点所选作业日下有数据的班次列表
     */
    @PostMapping("/getClassesByCondition")
    @NoAuthRequired
    @NoLoginAuthRequired
    public Result<List<ClassesBaseVO>> getClassesByCondition(@Validated @RequestBody GetClassesByConditionParam param) {
        List<ClassesBaseVO> vos = warehouseClassesService.getClassesByCondition(param);
        return Result.ok(vos);
    }

    /**
     * wpm 数据统计
     */
    @PostMapping("/data/statistics")
    @NoAuthRequired
    @NoLoginAuthRequired
    public Result<DataStatisticsVO> dataStatistics(@Validated @RequestBody WpmDataStatisticsParam param) {
        return Result.ok(warehouseReadService.dataStatistics(param));
    }

    /**
     * wpm数据统计人员明细
     */
    @PostMapping("/data/statistics/detail")
    @NoAuthRequired
    @NoLoginAuthRequired
    public Result<PaginationResult<DataStatisticsDetailsVO>> dataStatisticsDetail(@Validated @RequestBody WpmDataStatisticsParam param) {
        PaginationResult<DataStatisticsDetailsVO> paginationResult = warehouseReadService.dataStatisticsDetails(param);
        if (CollectionUtils.isNotEmpty(paginationResult.getResults())) {
            converterService.withAnnotation(paginationResult.getResults());
            paginationResult.getResults().forEach(item -> {
                if (CollectionUtils.isNotEmpty(item.getAbnormalVOList())) {
                    converterService.withAnnotation(item.getAbnormalVOList());
                }
            });
        }
        return Result.ok(paginationResult);
    }

    /**
     * wpm 黑名单人员名单
     */
    @PostMapping("/data/statistics/blackList")
    @NoAuthRequired
    @NoLoginAuthRequired
    public Result<PaginationResult<DataStatisticsBlackListVO>> dataStatisticsBlackList(@Validated @RequestBody WpmDataStatisticsParam param) {
        PaginationResult<DataStatisticsBlackListVO> paginationResult = warehouseReadService.dataStatisticsBlackList(param);
        if (CollectionUtils.isNotEmpty(paginationResult.getResults())) {
            converterService.withAnnotation(paginationResult.getResults());
        }
        return Result.ok(paginationResult);
    }

    /**
     * 证件扫描上传
     */
    @PostMapping("/certificates/upload")
    @NoAuthRequired
    @NoLoginAuthRequired
    public Result<CertificatesVO> certificatesUpload(@Validated CertificateUploadParam param) {
        log.info("证件扫描入参：{}", JSON.toJSONString(param));
        CertificatesVO v = warehouseUserService.certificatesUpload(param);
        log.info("证件扫描处理结果：{}", JSON.toJSONString(v));
        return Result.ok(v);
    }

    /**
     * 增加/修改仓内外包人员
     * 前置校验并且获取采购的证件照
     */
    @PostMapping("/submit/check")
    @NoAuthRequired
    @NoLoginAuthRequired
    public Result<SubmitCheckVO> submitCheck(@Validated @RequestBody AddUserParam param) {
        log.info("submitCheck={}", param);
        SubmitCheckVO resultVO = warehouseUserService.submitCheck(param);
        converterService.withAnnotationForSingle(resultVO);
        return Result.ok(resultVO);
    }

    /**
     * 增加/修改仓内外包人员
     * 采购身份证证件照和本次劳工证证件照不一致通知采购
     */
    @GetMapping("/different/notice")
    @NoAuthRequired
    @NoLoginAuthRequired
    public Result<Boolean> differentNotice(@NotBlank @RequestParam(value = "userCode") String userCode) {
        warehouseUserService.differentNotice(userCode);
        return Result.ok(Boolean.TRUE);
    }

    /**
     * 增加/修改仓内外包人员
     * 如果是新增，则会加一次证件录入
     * 员工管理页面编辑信息保存也是调用这个接口
     */
    @PostMapping("/saveOrUpdateUser")
    @NoAuthRequired
    @NoLoginAuthRequired
    public Result<UserVO> saveOrUpdateUser(@Validated @RequestBody AddUserParam param) {
        log.info("saveOrUpdateUser={}", param);
        param.setSource(BusinessConstant.WPM);
        return Result.ok(warehouseUserService.saveOrUpdateUser(param));
    }

    /**
     * 修改仓内外包人员供应商和网点信息
     */
    @PostMapping("/updateUserOcAndVendor")
    @NoAuthRequired
    @NoLoginAuthRequired
    public Result<UserVO> updateUserOcAndVendor(@Validated @RequestBody UpdateUserParam param) {
        log.info("updateUserOcAndVendor={}", param);
        warehouseUserService.updateUserOcAndVendor(param);
        return Result.ok();
    }

    /**
     * 入仓接口
     * 常规入仓
     */
    @PostMapping("/in")
    @NoAuthRequired
    @NoLoginAuthRequired
    public Result<Void> in(@Validated @RequestBody InOrOutParam param) {
        log.info("in warehouse params: {}", JSON.toJSONString(param));
        warehouseService.in(param);
        return Result.ok();
    }

    /**
     * 入仓接口
     * 原账号入仓 需要删除新的用户账号且更新证件号信息
     */
    @PostMapping("/in/V2")
    @NoAuthRequired
    @NoLoginAuthRequired
    public Result<Void> inV2(@Validated @RequestBody InV2Param param) {
        log.info("in warehouse V2 params: {}", JSON.toJSONString(param));
        warehouseService.inV2(param);
        return Result.ok();
    }

    /**
     * 离仓接口
     */
    @PostMapping("/out")
    @NoAuthRequired
    @NoLoginAuthRequired
    public Result<OutVO> out(@Validated @RequestBody OutParam param) {
        log.info("out warehouse params: {}", JSON.toJSONString(param));
        return Result.ok(warehouseService.out(param));
    }

    /**
     * 快速离仓接口
     */
/*    @PostMapping("/quick/out")
    @NoAuthRequired
    @NoLoginAuthRequired
    public Result<Void> quickOut(@Validated QuickOutParam param) {
        log.info("quickOut warehouse params: {}", JSON.toJSONString(param));
        warehouseService.quickOut(param);
        return Result.ok();
    }*/


    /**
     * 人脸录入
     */
    @PostMapping("/face/upload/file")
    @NoAuthRequired
    @NoLoginAuthRequired
    public Result<Void> faceUpload(@Validated FaceSaveParam param) {
        log.info("face upload params: {}", JSON.toJSONString(param));
        faceEngineService.faceInput(param);
        return Result.ok();
    }

    /**
     * 指定人脸核对
     */
    @PostMapping("/face/check")
    @NoAuthRequired
    @NoLoginAuthRequired
    public Result<UserFaceSearchVO> faceCheck(@Validated FaceCheckParam param) {
        log.info("face check params: {}", JSON.toJSONString(param));
        UserFaceSearchVO result = faceEngineService.faceCheck(param);
        log.info("face check result: {}", JSON.toJSONString(result));
        return Result.ok(result);
    }

    /**
     * 人脸搜索
     */
    @PostMapping("/face/search")
    @NoAuthRequired
    @NoLoginAuthRequired
    public Result<UserFaceSearchVO> faceSearch(@Validated FaceSearchParam param) {
        log.info("face search params: {}", JSON.toJSONString(param));
        UserFaceSearchVO result = faceEngineService.faceRecognition(param);
        log.info("face search result: {}", JSON.toJSONString(result));
        return Result.ok(result);
    }

    /**
     * 人脸搜索
     */
    @PostMapping("/face/search/V2")
    @NoAuthRequired
    @NoLoginAuthRequired
    public Result<UserFaceSearchRepeatVO> faceSearchV2(@Validated FaceSearchRepeatParam param) {
        log.info("face search V2 params: {}", JSON.toJSONString(param));
        UserFaceSearchRepeatVO result = faceEngineService.faceRecognitionV2(param);
        converterService.withAnnotationForSingle(result);
        log.info("face search V2 result: {}", JSON.toJSONString(result));
        return Result.ok(result);
    }

    /**
     * 劳务账号停用
     */
    @GetMapping("/disable/labor/account")
    @NoAuthRequired
    @NoLoginAuthRequired
    public Result<Boolean> disabledLaborAccount(@RequestParam(value = "userCode") String userCode) {
        return Result.ok(warehouseUserService.disabledLaborAccount(userCode));
    }

    /**
     * 网点白名单
     */
    @PostMapping("/oc/whiteList")
    @NoAuthRequired
    @NoLoginAuthRequired
    public Result<List<WarehouseOperatorListVO>> getOcWhiteList(@Validated @RequestBody WarehouseOperatorListParam param) {
        List<WarehouseOperatorListVO> result = warehouseOperatorAuthService.warehouseOperatorList(param);
        converterService.withAnnotation(result);
        return Result.ok(result);
    }

    /**
     * 新增网点白名单
     */
    @PostMapping("/oc/whiteList/save")
    @NoAuthRequired
    @NoLoginAuthRequired
    public Result<Boolean> bindOperatorRole(@Validated @RequestBody WarehouseOperatorSaveParam param) {
        return Result.ok(warehouseOperatorAuthService.bindOperatorRole(param));
    }

    /**
     * 删除网点白名单
     */
    @GetMapping("/oc/whiteList/delete")
    @NoAuthRequired
    @NoLoginAuthRequired
    public Result<Void> removeOperatorRole(@RequestParam(value = "id") Long id) {
        warehouseOperatorAuthService.removeOperatorRole(id);
        return Result.ok();
    }


    //************************************************************************免登录接口************************************************************************

    /**
     * 生成供应商二维码，暂时生成链接
     */
    @GetMapping("/qrCode")
    @NoLoginRequired
    public Result<QrCodeResultVO> generateQrCode(@RequestParam(value = "ocId") Long ocId,
                                                 String vendorCode,
                                                 Integer permission) {
        return Result.ok(warehouseSupplierService.generateQrCode(ocId, vendorCode, permission));
    }

    /**
     * 验证二维码token
     */
    @GetMapping("/verify/qrCode/token")
    @NoLoginRequired
    public Result<QrCodeVerifyResultVO> verifyToken(@RequestParam(value = "token") String token) {
        return Result.ok(warehouseSupplierService.verifyToken(token));
    }

    /**
     * 通过网点获取关联的供应商，免登录
     */
    @PostMapping("/noLogin/getVendorListByOcList")
    @NoLoginRequired
    public Result<List<VendorVO>> getNoLoginVendorListByOcList(@Validated @RequestBody GetVendorListByOcListParam param) {
        return Result.ok(warehouseSupplierService.getNoLoginVendorListByOcList(param.getOcIdList()));
    }

    /**
     * 查询指定网点所选作业日下有数据的班次列表, 免登录
     */
    @PostMapping("/noLogin/getClassesByCondition")
    @NoLoginRequired
    public Result<List<ClassesBaseVO>> getNoLoginClassesByCondition(@Validated @RequestBody GetClassesByConditionParam param) {
        List<ClassesBaseVO> vos = warehouseClassesService.getClassesByCondition(param);
        return Result.ok(vos);
    }

    /**
     * 统计供应商数据
     */
    @PostMapping("/statistic/vendor")
    @NoLoginRequired
    public Result<StatisticsVendorResultVO> statisticVendor(@RequestBody StatisticVendorParam param) {
        StatisticsVendorResultVO result = warehouseReadService.statisticVendor(param);
        return Result.ok(result);
    }

    /**
     * 统计供应商人员数据
     */
    @PostMapping("/statistic/vendor/user")
    @NoLoginRequired
    public Result<PaginationResult<StatisticsVendorUserResultVO>> statisticVendorUser(@RequestBody StatisticVendorParam param) {
        PaginationResult<StatisticsVendorUserResultVO> paginationResult = warehouseReadService.statisticVendorUser(param);
        if (CollectionUtils.isNotEmpty(paginationResult.getResults())) {
            converterService.withAnnotation(paginationResult.getResults());
            paginationResult.getResults().forEach(item -> {
                if (CollectionUtils.isNotEmpty(item.getAbnormalVOList())) {
                    converterService.withAnnotation(item.getAbnormalVOList());
                }
            });
        }
        return Result.ok(paginationResult);
    }

    /**
     * 供应商班次人员考勤详情导出
     */
    @GetMapping("/vendor/attendanceDetail/export")
    @NoLoginRequired
    public void vendorAttendanceDetailExport(HttpServletResponse response,
                                             @RequestParam(value = "ocId") Long ocId,
                                             @RequestParam(value = "vendorCode") String vendorCode,
                                             @RequestParam(value = "warehouseDate") String warehouseDate,
                                             @RequestParam(value = "classesId") Long classesId,
                                             @RequestParam(value = "lang") String lang) {
        StatisticVendorExportParam param = new StatisticVendorExportParam();
        param.setOcId(ocId);
        param.setVendorCode(vendorCode);
        param.setWarehouseDate(DateUtil.parseDate(warehouseDate));
        param.setClassesId(classesId);
        param.setLang(lang);
        warehouseExportService.vendorAttendanceDetailExport(response, param);
    }

    /**
     * 检查供应商确认状态
     */
    @PostMapping("/check/vendor/confirm/status")
    @NoLoginRequired
    public Result<CheckVendorConfirmStatusResultVO> checkVendorConfirmStatus(@RequestBody @Validated CheckVendorConfirmStatusParam param) {
        return Result.ok(warehouseService.checkVendorConfirmStatus(param));
    }

    /**
     * 获取供应商待确认内容
     */
    @PostMapping("/vendor/confirm/content")
    @NoLoginRequired
    public Result<GetVendorConfirmContentResultVO> getVendorConfirmContent(@RequestBody @Validated GetVendorConfirmContentParam param) {
        return Result.ok(warehouseReadService.getVendorConfirmContent(param));
    }

    /**
     * 供应商班次确认, 免登录
     */
    @PostMapping("/noLogin/confirm/classes")
    @NoLoginRequired
    public Result<Long> vendorClassedConfirm(@Validated VendorConfirmClassesParam param) {
        log.info("供应商班次确认入参：{}", JSON.toJSONString(param));
        return Result.ok(warehouseService.vendorClassedConfirm(param));
    }

    /**
     * H5供应商班次确认详情, 免登录
     */
    @GetMapping("/noLogin/confirm/detail")
    @NoLoginRequired
    public Result<VendorClassesConfirmVO> vendorClassedConfirmDetailH5(@RequestParam("id") Long id) {
        VendorClassesConfirmVO result = warehouseReadService.vendorClassedConfirmDetailH5(id);
        converterService.withAnnotationForSingle(result);
        return Result.ok(result);
    }


    //************************************************************************免登录接口************************************************************************
}
