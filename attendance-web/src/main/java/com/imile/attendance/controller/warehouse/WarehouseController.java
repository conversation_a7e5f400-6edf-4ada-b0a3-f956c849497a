package com.imile.attendance.controller.warehouse;

import com.imile.attendance.annon.ExportParamFill;
import com.imile.attendance.annon.NoAuthRequired;
import com.imile.attendance.annon.NoLoginAuthRequired;
import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.controller.BaseController;
import com.imile.attendance.infrastructure.convert.ConverterService;
import com.imile.attendance.infrastructure.repository.warehouse.param.AddUserParam;
import com.imile.attendance.infrastructure.repository.warehouse.query.OcUserQuery;
import com.imile.attendance.infrastructure.repository.warehouse.query.WarehouseDetailQuery;
import com.imile.attendance.ipep.dto.OssApiVo;
import com.imile.attendance.warehouse.WarehouseAttendanceHandlerService;
import com.imile.attendance.warehouse.WarehouseClassesService;
import com.imile.attendance.warehouse.WarehouseOcService;
import com.imile.attendance.warehouse.WarehouseReadService;
import com.imile.attendance.warehouse.WarehouseService;
import com.imile.attendance.warehouse.WarehouseSupplierService;
import com.imile.attendance.warehouse.WarehouseUserService;
import com.imile.attendance.warehouse.param.BingShiftParam;
import com.imile.attendance.warehouse.param.GetClassListByConditionParam;
import com.imile.attendance.warehouse.param.GetOcListByVendorCodeParam;
import com.imile.attendance.warehouse.param.GetVendorListByOcListParam;
import com.imile.attendance.warehouse.param.NoBingShiftReportParam;
import com.imile.attendance.warehouse.param.ReportParam;
import com.imile.attendance.warehouse.param.SimpleReportParam;
import com.imile.attendance.warehouse.param.UpdateWarehouseWorkClassesParam;
import com.imile.attendance.warehouse.param.UpdateWarehouseWorkOcParam;
import com.imile.attendance.warehouse.param.UpdateWarehouseWorkVendorParam;
import com.imile.attendance.warehouse.param.WarehouseOcParam;
import com.imile.attendance.warehouse.vo.BusZoneListVO;
import com.imile.attendance.warehouse.vo.ClassesDetailVO;
import com.imile.attendance.warehouse.vo.ClassesWebVO;
import com.imile.attendance.warehouse.vo.DateDetailReportVO;
import com.imile.attendance.warehouse.vo.DateReportSimpleVO;
import com.imile.attendance.warehouse.vo.DateReportVO;
import com.imile.attendance.warehouse.vo.MonthReportVO;
import com.imile.attendance.warehouse.vo.OcVO;
import com.imile.attendance.warehouse.vo.PunchConfigAndClassesVO;
import com.imile.attendance.warehouse.vo.ReportVO;
import com.imile.attendance.warehouse.vo.UserVO;
import com.imile.attendance.warehouse.vo.VendorClassesConfirmVO;
import com.imile.attendance.warehouse.vo.VendorVO;
import com.imile.attendance.warehouse.vo.WarehouseOcCountryVO;
import com.imile.attendance.warehouse.vo.WarehouseOcUserVO;
import com.imile.common.page.PaginationResult;
import com.imile.common.result.Result;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;


/**
 * 仓内考勤Service
 *
 * <AUTHOR>
 * @menu 考勤日报
 * @date 2025/6/14
 */
@Slf4j
@RestController
@RequestMapping("/warehouse")
public class WarehouseController extends BaseController {


    @Resource
    private WarehouseClassesService warehouseClassesService;

    @Resource
    private WarehouseService warehouseService;

    @Resource
    private WarehouseReadService warehouseReadService;

    @Resource
    private WarehouseOcService warehouseOcService;

    @Resource
    private WarehouseSupplierService warehouseSupplierService;

    @Resource
    private WarehouseUserService warehouseUserService;

    @Resource
    private WarehouseAttendanceHandlerService warehouseAttendanceHandlerService;

    @Resource
    private ConverterService converterService;

    /**
     * 查询国家列表
     */
    @GetMapping("/getCountryList")
    public Result<List<BusZoneListVO>> getCountryList() {
        return Result.ok(warehouseReadService.getCountryList());
    }

    /**
     * 指定条件查询网点列表
     */
    @PostMapping("/getOcListByCountry")
    public Result<List<OcVO>> getOcListByCondition(@Validated @RequestBody GetVendorListByOcListParam param) {
        return Result.ok(warehouseOcService.getOcListByCondition(param));
    }

    /**
     * 指定条件查询供应商
     */
    @PostMapping("/getVendorListByCondition")
    public Result<List<VendorVO>> getVendorListByCondition(@Validated @RequestBody GetVendorListByOcListParam param) {
        return Result.ok(warehouseSupplierService.getVendorListByCondition(param));
    }

    /**
     * 根据供应商编码查询服务网点列表
     */
    @PostMapping("/getOcListByVendorCode")
    public Result<List<OcVO>> getOcListByVendorCode(@Validated @RequestBody GetOcListByVendorCodeParam param) {
        return Result.ok(warehouseOcService.getOcListByVendorCode(param));
    }

    /**
     * 网点级连筛选
     */
    @PostMapping("/oc/tree")
    public Result<List<WarehouseOcCountryVO>> getWarehouseOcTree(@RequestBody WarehouseOcParam param) {
        return Result.ok(warehouseOcService.getWarehouseOcTree(param));
    }

    /**
     * 查询班次列表
     */
    @PostMapping("/web/classes/list")
    public Result<List<ClassesWebVO>> getClassesList(@RequestBody GetClassListByConditionParam param) {
        return Result.ok(warehouseClassesService.getClassesList(param));
    }

    /**
     * 查询班次列表
     * 调用方：财务
     * 入参是hermes网点Id列表
     */
    @PostMapping("/outer/classes/list")
    @NoAuthRequired
    @NoLoginAuthRequired
    public Result<List<ClassesWebVO>> getOuterClassesList(@RequestBody GetClassListByConditionParam param) {
        List<ClassesWebVO> vos = warehouseClassesService.getOuterClassesList(param);
        return Result.ok(vos);
    }

    /**
     * 查询考勤组和班次列表
     */
    @GetMapping("/classes/map")
    public Result<PunchConfigAndClassesVO> getPunchConfigAndClasses(@RequestParam("ocId") Long ocId) {
        PunchConfigAndClassesVO vo = warehouseClassesService.getPunchConfigAndClasses(ocId);
        return Result.ok(vo);
    }

    /**
     * 班次详情
     */
    @GetMapping("/classes/detail")
    public Result<List<ClassesDetailVO>> getClassesDetail(@RequestParam("classesId") Long classesId) {
        List<ClassesDetailVO> vos = warehouseClassesService.getClassesDetail(classesId);
        return Result.ok(vos);
    }

    /**
     * 供应商班次确认详情
     */
    @GetMapping("/confirm/detail")
    public Result<VendorClassesConfirmVO> vendorClassedConfirmDetailWeb(@RequestParam("id") Long id) {
        VendorClassesConfirmVO result = warehouseReadService.vendorClassedConfirmDetailWeb(id);
        converterService.withAnnotationForSingle(result);
        if (CollectionUtils.isNotEmpty(result.getAbnormalVOList())) {
            converterService.withAnnotation(result.getAbnormalVOList());
        }
        return Result.ok(result);
    }

    /**
     * 增加/修改仓内外包人员
     * 如果是新增，则会加一次证件录入
     * 员工管理页面编辑信息保存也是调用这个接口
     */
    @PostMapping("/saveOrUpdateUser")
    public Result<UserVO> saveOrUpdateUser(@Validated @RequestBody AddUserParam param) {
        log.info("saveOrUpdateUser={}", param);
        param.setSource(BusinessConstant.HRMS_WEB);
        return Result.ok(warehouseUserService.saveOrUpdateUser(param));
    }


    /**
     * 仓内日报
     */
    @PostMapping("/report/date")
    public Result<PaginationResult<DateReportVO>> dateReport(@Validated @RequestBody ReportParam param) {
        PaginationResult<DateReportVO> v = warehouseReadService.dateReport(param);
        converterService.withAnnotation(v.getResults());
        return Result.ok(v);
    }

    /**
     * 仓内日报导出
     **/
    @ExportParamFill
    @PostMapping("/export/date")
    public Result<PaginationResult<DateReportVO>> dateExport(ReportParam param) {
        PaginationResult<DateReportVO> v = warehouseReadService.dateReport(param);
        converterService.withAnnotation(v.getResults());
        return Result.ok(v);
    }

    /**
     * 仓内日报详细
     */
    @GetMapping("/report/date/detail")
    public Result<DateDetailReportVO> dateReportDetail(@RequestParam(value = "id") Long id) {
        DateDetailReportVO v = warehouseReadService.dateReportDetail(id);
        converterService.withAnnotationForSingle(v.getAttendanceResult());
        if (CollectionUtils.isNotEmpty(v.getWarehouseRecordList())) {
            converterService.withAnnotation(v.getWarehouseRecordList());
            v.getWarehouseRecordList().forEach(record -> converterService.withAnnotationForSingle(record.getFaceRecognitionDetail()));
        }
        if (CollectionUtils.isNotEmpty(v.getAttendanceResult().getAttendanceAbnormalTypes())) {
            converterService.withAnnotation(v.getAttendanceResult().getAttendanceAbnormalTypes());
        }
        if (CollectionUtils.isNotEmpty(v.getAttendanceAbnormalList())) {
            converterService.withAnnotation(v.getAttendanceAbnormalList());
        }
        return Result.ok(v);
    }

    /**
     * 仓内月报
     */
    @PostMapping("/report/month")
    public Result<PaginationResult<MonthReportVO>> monthReport(@Validated @RequestBody ReportParam param) {
        PaginationResult<MonthReportVO> v = warehouseReadService.monthReport(param);
        converterService.withAnnotation(v.getResults());
        return Result.ok(v);
    }

    /**
     * 仓内月报导出
     **/
    @ExportParamFill
    @PostMapping("/export/month")
    public Result<PaginationResult<MonthReportVO>> monthExport(ReportParam param) {
        PaginationResult<MonthReportVO> v = warehouseReadService.monthReport(param);
        converterService.withAnnotation(v.getResults());
        return Result.ok(v);
    }

    /**
     * 查询账号网点权限下待配置班次考勤记录数量
     */
    @GetMapping("/count/notBindShift")
    public Result<Integer> notBindShiftCount() {
        return Result.ok(warehouseReadService.noBindShiftCount());
    }

    /**
     * 待配置班次网点下拉列表
     */
    @GetMapping("/getNoBingShiftOcList")
    public Result<List<OcVO>> getNoBingShiftOcList() {
        return Result.ok(warehouseOcService.getNoBingShiftOcList());
    }

    /**
     * 仓内无绑定班次日报
     */
    @PostMapping("/report/date/noBingShift")
    public Result<List<DateReportVO>> dateReportNoBingShift(@Validated @RequestBody NoBingShiftReportParam param) {
        List<DateReportVO> v = warehouseReadService.dateReportNoBingShift(param);
        return Result.ok(v);
    }

    /**
     * 绑定班次
     */
    @PostMapping("/bingShift")
    public Result<Boolean> bingShift(@Validated @RequestBody BingShiftParam param) {
        return Result.ok(warehouseService.bingShift(param));
    }


    /**
     * 简易日报列表
     * 批量修改网点&供应商&班次
     */
    @PostMapping("/simple/report/date")
    public Result<List<DateReportSimpleVO>> simpleDateReport(@Validated @RequestBody SimpleReportParam param) {
        List<DateReportSimpleVO> v = warehouseReadService.simpleDateReport(param);
        return Result.ok(v);
    }

    /**
     * 仓内考勤更新工作供应商
     */
    @PostMapping("/update/workVendor")
    public Result<Void> updateWorkVendor(@Validated @RequestBody UpdateWarehouseWorkVendorParam param) {
        warehouseService.updateWorkVendor(param);
        return Result.ok();
    }

    /**
     * 仓内考勤更新工作网点
     */
    @PostMapping("/update/workOc")
    public Result<Void> updateWorkOc(@Validated @RequestBody UpdateWarehouseWorkOcParam param) {
        warehouseService.updateWorkOc(param);
        return Result.ok();
    }

    /**
     * 仓内考勤更新工作班次
     */
    @PostMapping("/update/workClasses")
    public Result<Void> updateWorkClasses(@Validated @RequestBody UpdateWarehouseWorkClassesParam param) {
        warehouseService.updateWorkClasses(param);
        return Result.ok();
    }

    /**
     * 网点员工管理页面
     */
    @PostMapping("/ocUserList")
    public Result<PaginationResult<ReportVO>> ocUserList(@Validated @RequestBody OcUserQuery param) {
        PaginationResult<ReportVO> v = warehouseUserService.ocUserList(param);
        converterService.withAnnotation(v.getResults());
        return Result.ok(v);
    }

    /**
     * 网点员工详情
     */
    @GetMapping("/ocUserDetail")
    public Result<WarehouseOcUserVO> ocUserDetail(@RequestParam(value = "id") Long id) {
        WarehouseOcUserVO v = warehouseUserService.ocUserDetail(id);
        converterService.withAnnotationForSingle(v);
        if (CollectionUtils.isNotEmpty(v.getCertificateVOList())) {
            converterService.withAnnotation(v.getCertificateVOList());
        }
        return Result.ok(v);
    }

    /**
     * 重试刷新考勤异常计算结果
     */
    @PostMapping("/retry/calculate/abnormal")
    public Result<Void> retryCalculateAbnormal(@Validated @RequestBody WarehouseDetailQuery param) {
        warehouseAttendanceHandlerService.retryCalculateAbnormal(param);
        return Result.ok();
    }

    /**
     * 仓内考勤补推财务
     */
    @PostMapping("/retry/push/fin")
    public Result<Void> retryPushFin(@Validated @RequestBody WarehouseDetailQuery param) {
        warehouseService.retryPushFin(param);
        return Result.ok();
    }

    /**
     * 获取OSS附件完整url
     */
    @GetMapping("/oss/fileUrl")
    public Result<OssApiVo> getOSSFileUrl(@RequestParam(value = "fileKey") String fileKey) {
        return Result.ok(warehouseReadService.getOssFileUrl(fileKey));
    }

    /**
     * 仓内考勤异常计算结果
     */
    @GetMapping("/calculate/abnormal")
    public Result<Void> calculateAbnormal(@RequestParam(value = "userCode") String userCode,
                                          @RequestParam(value = "dayId") Long dayId) {
        warehouseAttendanceHandlerService.calculateAbnormal(userCode, dayId);
        return Result.ok();
    }

    /**
     * WPM未配置班次考勤日查询
     */
    @GetMapping("/report/getNoBindShiftDay")
    public Result<List<String>> getNoBindShiftDay(@RequestParam("ocId") Long ocId) {
        List<String> dayIdList = warehouseClassesService.getNoBindShiftDay(ocId);
        return Result.ok(dayIdList);
    }
}
