package com.imile.attendance.controller.warehouse;

import com.imile.attendance.annon.NoAuthRequired;
import com.imile.attendance.annon.NoLoginAuthRequired;
import com.imile.attendance.warehouse.WarehouseExternalService;
import com.imile.attendance.warehouse.param.GetOcListByConditionParam;
import com.imile.attendance.warehouse.vo.OcVO;
import com.imile.attendance.warehouse.vo.WarehousePcsMonthReportCountVO;
import com.imile.attendance.warehouse.vo.WarehousePcsMonthReportPassRateVO;
import com.imile.attendance.warehouse.vo.WarehousePcsReportVO;
import com.imile.common.page.PaginationResult;
import com.imile.common.result.Result;
import com.imile.hrms.api.warehouse.param.GetPcsReportByConditionParam;
import com.imile.hrms.api.warehouse.param.GetPcsReportCountByConditionParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 仓内考勤提供外部服务
 * <AUTHOR>
 * @since 2024/9/27
 */
@Slf4j
@RestController
@RequestMapping("/external/warehouse")
public class WarehouseExternalController {
    @Resource
    private WarehouseExternalService warehouseExternalService;

    /**
     * 获取指定日期供应商操作站点
     */
    @PostMapping("/getOcListByCondition")
    @NoLoginAuthRequired
    @NoAuthRequired
    public Result<List<OcVO>> getOcListByCondition(@RequestBody @Validated GetOcListByConditionParam param) {
        return Result.ok(warehouseExternalService.getOcListByCondition(param));
    }

    /**
     * 获取PCS每日状态概览
     */
    @PostMapping("/pcs/dateReport")
    @NoLoginAuthRequired
    @NoAuthRequired
    public Result<PaginationResult<WarehousePcsReportVO>> pcsDateReport(@RequestBody @Validated GetPcsReportByConditionParam param) {
        return Result.ok(warehouseExternalService.pcsDateReport(param));
    }

    /**
     * 获取PCS周度或月度状态概览
     */
    @PostMapping("/pcs/weekOrMonthReport")
    @NoLoginAuthRequired
    @NoAuthRequired
    public Result<PaginationResult<WarehousePcsReportVO>> pcsWeekOrMonthReport(@RequestBody @Validated GetPcsReportByConditionParam param) {
        return Result.ok(warehouseExternalService.pcsWeekOrMonthReport(param));
    }

    /**
     * 获取PCS每日仓内操作状态数量
     */
    @PostMapping("/pcs/dateReportCount")
    @NoLoginAuthRequired
    @NoAuthRequired
    public Result<WarehousePcsMonthReportCountVO> pcsDateReportCount(@RequestBody @Validated GetPcsReportCountByConditionParam param) {
        return Result.ok(warehouseExternalService.pcsDateReportCount(param));
    }

    /**
     * 获取仓内月度操作合格率
     */
    @PostMapping("/pcs/dateMonthReportPassRate")
    @NoLoginAuthRequired
    @NoAuthRequired
    public Result<WarehousePcsMonthReportPassRateVO> pcsDateMonthReportPassRate(@RequestBody @Validated GetPcsReportCountByConditionParam param) {
        return Result.ok(warehouseExternalService.pcsDateMonthReportPassRate(param));
    }
}
