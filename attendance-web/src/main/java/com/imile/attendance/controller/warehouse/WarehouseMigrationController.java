package com.imile.attendance.controller.warehouse;

import com.imile.attendance.annon.NoAuthRequired;
import com.imile.attendance.migration.dto.AbnormalSyncDTO;
import com.imile.attendance.warehouse.WarehouseMigrationService;
import com.imile.common.result.Result;
import com.imile.ucenter.api.authenticate.NoLoginRequired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 仓内相关迁移
 *
 * <AUTHOR>
 * @since 2025/7/24
 */
@RequestMapping("/warehouse/migration")
@RestController
public class WarehouseMigrationController {

    @Resource
    private WarehouseMigrationService warehouseMigrationService;


    /**
     * 同步老系统仓内出勤明细、仓内打卡记录、仓内关联异常、仓内快照表到新系统
     */
    @PostMapping("/syncNewSystemWarehouseRecord")
    @NoAuthRequired
    @NoLoginRequired
    public Result<Void> syncNewSystemAbnormalRecord(@RequestBody @Validated AbnormalSyncDTO abnormalSyncDTO) {
        warehouseMigrationService.syncNewSystemAbnormalRecord(abnormalSyncDTO);
        return Result.ok();
    }

    /**
     * 同步人脸库信息
     */
    @GetMapping("/syncNewSystemFaceInfo")
    @NoAuthRequired
    @NoLoginRequired
    public Result<Void> syncNewSystemFaceInfo() {
        warehouseMigrationService.syncNewSystemFaceInfo();
        return Result.ok();
    }
}
