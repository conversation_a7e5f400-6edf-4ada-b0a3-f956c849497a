package com.imile.attendance.controller.cycleConfig;

import com.imile.attendance.annon.NoLoginAuthRequired;
import com.imile.attendance.cycleConfig.service.AttendanceCycleCalendarManagementService;
import com.imile.attendance.infrastructure.repository.cycleConfig.model.AttendanceCycleCalendarDO;
import com.imile.common.result.Result;
import com.imile.ucenter.api.authenticate.NoLoginRequired;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * 考勤周期日历管理控制器
 * 
 * 提供考勤周期日历数据的管理、监控和维护接口
 *
 * <AUTHOR> chen
 * @since 2025-08-11
 */
@Slf4j
@RestController
@RequestMapping("/attendance/cycle/calendar")
@Api(tags = "考勤周期日历管理")
public class AttendanceCycleCalendarController {

    @Resource
    private AttendanceCycleCalendarManagementService managementService;

    /**
     * 获取日历数据统计信息
     */
    @GetMapping("/statistics")
    @ApiOperation("获取日历数据统计信息")
    @NoLoginRequired
    public Result<Map<String, Object>> getStatistics() {
        log.info("获取考勤周期日历数据统计信息");
        
        try {
            Map<String, Object> statistics = managementService.getCalendarDataStatistics();
            return Result.ok(statistics);
        } catch (Exception e) {
            log.error("获取统计信息失败", e);
            return Result.fail("获取统计信息失败：" + e.getMessage());
        }
    }

    /**
     * 手动重建所有国家的日历数据
     */
    @PostMapping("/rebuild/all")
    @ApiOperation("重建所有国家的日历数据")
    @NoLoginRequired
    public Result<Map<String, Object>> rebuildAllCalendarData() {
        log.info("手动触发重建所有国家的日历数据");
        
        try {
            Map<String, Object> result = managementService.rebuildAllCalendarData();
            return Result.ok(result);
        } catch (Exception e) {
            log.error("重建所有日历数据失败", e);
            return Result.fail("重建失败：" + e.getMessage());
        }
    }

    /**
     * 重建指定国家的日历数据
     */
    @PostMapping("/rebuild/{country}")
    @ApiOperation("重建指定国家的日历数据")
    @NoLoginRequired
    public Result<Map<String, Object>> rebuildCalendarDataByCountry(
            @ApiParam("国家代码") @PathVariable String country) {
        log.info("手动触发重建国家 {} 的日历数据", country);
        
        try {
            Map<String, Object> result = managementService.rebuildCalendarDataByCountry(country);
            return Result.ok(result);
        } catch (Exception e) {
            log.error("重建国家 {} 的日历数据失败", country, e);
            return Result.fail("重建失败：" + e.getMessage());
        }
    }

    /**
     * 清理指定国家的日历数据
     */
    @DeleteMapping("/cleanup/{country}")
    @ApiOperation("清理指定国家的日历数据")
    @NoLoginRequired
    public Result<Map<String, Object>> cleanupCalendarDataByCountry(
            @ApiParam("国家代码") @PathVariable String country) {
        log.info("手动触发清理国家 {} 的日历数据", country);
        
        try {
            Map<String, Object> result = managementService.cleanupCalendarDataByCountry(country);
            return Result.ok(result);
        } catch (Exception e) {
            log.error("清理国家 {} 的日历数据失败", country, e);
            return Result.fail("清理失败：" + e.getMessage());
        }
    }

    /**
     * 查询指定国家的日历数据
     */
    @GetMapping("/query/{country}")
    @ApiOperation("查询指定国家的日历数据")
    @NoLoginRequired
    public Result<List<AttendanceCycleCalendarDO>> queryCalendarData(
            @ApiParam("国家代码") @PathVariable String country,
            @ApiParam("年份（可选）") @RequestParam(required = false) Integer year) {
        log.info("查询国家 {} 年份 {} 的日历数据", country, year);
        
        try {
            List<AttendanceCycleCalendarDO> data = managementService.queryCalendarData(country, year);
            return Result.ok(data);
        } catch (Exception e) {
            log.error("查询日历数据失败，国家：{}，年份：{}", country, year, e);
            return Result.fail("查询失败：" + e.getMessage());
        }
    }

    /**
     * 检查数据一致性
     */
    @GetMapping("/consistency/check")
    @ApiOperation("检查日历数据一致性")
    @NoLoginRequired
    public Result<Map<String, Object>> checkDataConsistency() {
        log.info("检查考勤周期日历数据一致性");
        
        try {
            Map<String, Object> result = managementService.checkDataConsistency();
            return Result.ok(result);
        } catch (Exception e) {
            log.error("检查数据一致性失败", e);
            return Result.fail("检查失败：" + e.getMessage());
        }
    }
}
