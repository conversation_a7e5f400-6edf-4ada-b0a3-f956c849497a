package com.imile.attendance.controller.warehouse;

import com.imile.attendance.controller.BaseController;
import com.imile.attendance.warehouse.WarehouseAttendanceConfigService;
import com.imile.attendance.warehouse.param.WarehouseAttendanceConfigQueryParam;
import com.imile.attendance.warehouse.param.WarehouseAttendanceConfigSaveParam;
import com.imile.attendance.warehouse.param.WarehouseAttendanceConfigUpdateStatusParam;
import com.imile.attendance.warehouse.vo.WarehouseAttendanceConfigVO;
import com.imile.common.page.PaginationResult;
import com.imile.common.result.Result;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 仓内考勤配置服务控制器
 *
 * <AUTHOR>
 * @since 2024/11/30
 */
@RestController
@RequestMapping("/warehouse/config")
public class WarehouseAttendanceConfigController extends BaseController {

    @Resource
    private WarehouseAttendanceConfigService warehouseAttendanceConfigService;


    /**
     * 仓内考勤规则列表查询
     *
     * @param param 入参
     * @return 仓内考勤规则
     */
    @PostMapping("/page")
    public Result<PaginationResult<WarehouseAttendanceConfigVO>> page(@RequestBody @Validated WarehouseAttendanceConfigQueryParam param) {
        return Result.ok(warehouseAttendanceConfigService.page(param));
    }

    /**
     * 仓内考勤规则保存
     *
     * @param param 入参
     * @return 无
     */
    @PostMapping("/saveOrUpdate")
    public Result<Void> saveOrUpdate(@RequestBody @Validated WarehouseAttendanceConfigSaveParam param) {
        warehouseAttendanceConfigService.saveOrUpdate(param);
        return Result.ok();
    }

    /**
     * 仓内考勤规则启停用
     *
     * @param param 入参
     * @return 无
     */
    @PostMapping("/update/status")
    public Result<Boolean> updateStatus(@RequestBody @Validated WarehouseAttendanceConfigUpdateStatusParam param) {
        return Result.ok(warehouseAttendanceConfigService.updateStatus(param));
    }
}
