#WPM
face.query.oss.file=Falha ao consultar o link de download do arquivo
the.person.has.resigned=Essa pessoa já saiu da empresa. Por favor, entre em contato com o RH para realizar o reingresso
this.account.has.been.deactivated=Essa conta está desativada. Por favor, entre em contato com o RH
the.user.account.does.not.exist=O usuário ou número do documento não existe
not.recognition.or.upload.cannot.in={0} não realizou reconhecimento facial, não é possível acessar o armazém
not.in.cannot.out={0} não há registro de entrada no armazém, não é possível liberar a saída
not.recognition.cannot.out={0} não realizou reconhecimento facial, não é possível deixar o armazém
in.cannot.in={0} já entrou no armazém, não é possível entrar novamente
out.other.oc.warehouse.then.in=Essa pessoa já entrou no armazém do ponto [{0}]. Por favor, saia antes de selecionar um novo ponto para entrada
after.out.then.in=Existe registro de saída pendentes. Por favor, faça o registro de saída antes de realizar uma nova entrada.
abnormal.attendance.days.obtained=Erro ao calcular os dias de frequência
face.in.first=Por favor, faça o reconhecimento facial para entrar antes de sair do armazém
attendance.group.not.config=Entre em contato com o HRBP para configurar as informações do turno de frequência
out.warehouse.then.in=Essa pessoa já entrou no armazém em [{0}]. Por favor, saia antes de selecionar um novo turno para entrada
ocr.scanning.failed=Falha ao escanear. Por favor, tente novamente ou insira os dados manualmente
warehouse.entry.failure=Falha ao entrar no armazém. Funcionário não é mão de obra terceirizada
duplicate.ID.number=Número do documento repetido. Não é possível registrar
unsupported.countries=OCR não suporta o país informado
update.failed.non.labor.dispatch.employee=Falha ao atualizar. Funcionário não é mão de obra terceirizada
face.engine.active.failed=Falha na ativação do mecanismo de reconhecimento facial
face.engine.initialize.failed=Falha na inicialização do mecanismo de reconhecimento facial
face.engine.obtain.failed=Falha ao obter o mecanismo de reconhecimento facial
face.detect.failed=Falha na detecção de rosto
face.feature.extract.failed=Falha na extração de características faciais
face.feature.compare.failed=Falha na comparação de características faciais
face.feature.library.matching.failed=Erro ao combinar o banco de dados de características faciais. Por favor, inicialize os dados primeiro
please.initialize.face.feature.library.first=Por favor, inicialize os dados do banco de rostos
face.feature.data.save.failed=Falha ao salvar os dados das características faciais
face.liveness.detect.failed=Falha na verificação facial
the.result.of.face.liveness.detection.non.living=O resultado da verificação da facial indica que não há vitalidade
the.result.of.face.liveness.detection.unknown=O resultado da verificação da facial é: desconhecido
the.result.of.face.liveness.detection.beyond.the.face=O resultado da verificação da facial indica que excede a área do rosto
facial.data.has.been.deleted=Os dados faciais foram excluídos. Por favor, limpe o cache
version.outdated=A versão do programa está desatualizada. Atualize por gentileza
driver.cant.use.wpm=Motoristas não podem usar o WPM. Utilize a funcionalidade de ponto para motoristas
cant.change.vendor=Essa pessoa já trabalhou hoje sob o fornecedor [{0}]. Não é possível trocar
quick.out.must.in.24.hours=É permitido tirar fotos para saída do armazém até 24 horas antes do horário de entrada
before.class.earliestPunchInTime=Não é possível registrar o ponto antes do horário mais cedo de entrada do turno. Escolha outro turno ou entre em contato com o BP para configurá-lo
vendor.classes.confirm.repeat=O resultado do turno do fornecedor de hoje já foi confirmado. Não repita a operação
warehouse.no.attendance.record=Não há registros de ponto de pessoas no armazém
not.yet.the.end.of.shift=Não é possível confirmar antes do horário de saída do turno
warehouse.attendance.config.name.exist=O nome da regra de gestão de pontos no armazém já existe
dept.exist.in.other.warehouse.attendance.config=O departamento já está presente na regra de gestão de frequência no armazém
already.working.in.the.warehouse=Falha ao entrar no armazém. Essa pessoa já trabalhou hoje, não é possível realizar nova entrada
already.working.out.the.warehouse=Falha ao sair do armazém. Essa pessoa já trabalhou hoje, não é possível realizar nova entrada
user.vendor.not.same.as.warehouse.vendor=Essa pessoa é funcionária(o) de {0}, não pode entrar no armazém de {1}
sex.not.empty=O campo de gênero não pode estar vazio
certificate.expire.date.not.empty=A data de expiração do documento não pode estar vazia
not.vendor.user.not.register=Pessoas que não sejam {0} não podem realizar operações de entrada no armazém
warehouse.employee.not.allow.revoke=Não é possível cancelar ausência para pessoas próprias do armazém
warehouse.not.allow.out.of.work=Pessoas do armazém não podem realizar trabalho externo
warehouse.not.allow.card.revoke=Pessoas do armazém não podem cancelar o registro de ponto manual
certificateCode.must.be.consistent=Os números de identificação de RG e certificado de trabalho são tanto CPF e precisam ser consistentes
system.time.not.macth.current.time=O tempo do telefone não está dentro da zona horaria local, por favor verifique o tempo do telefone
blacklist.personnel.are.not.allowed.to.enter.or.out.the.warehouse=O pessoal na lista negra não pode entrar ou sair do armazém \n Razão da lista negra: {0}
non.warehouse.employees.cant.use.wpm=O pessoal não armazém não pode entrar no armazém para operações
certificate.format.mismatch=O número de identificação deve ser um número de 11 dígitos
face.recognition.duplicate.facial=Fotos faciais duplicados, contas pessoais duplicadas {0}
employee.face.recognition.entrance.select.incorrect=Esta pessoa é um pessoal próprio. Por favor, mude para a entrada de reconhecimento facial "próprio"
class.employee.not.edit=Grupo de atendimento de usuários aplicável âmbito não vinculado
