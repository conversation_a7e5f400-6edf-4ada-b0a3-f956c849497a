success=操作成功
request.handle.fail=请求处理失败
no.access=无权限
param.validate.fail=参数校验失败
system.processing.exception=服务处理异常
Network.exception=网络出现异常
param.not.exists=参数配置不存在
http.status.not.200=http响应状态不为200
access.token.not.found=access token未找到
limited.by.third=被第三方限流
system.error=系统异常,请联系管理员
data.duplicate=数据重复
exception.paramValidError=参数校验异常
data.not.exist=数据不存在
none.system.login.permission=没有系统登录权限，请联系管理员进行授权
no.access.auth=没有数据访问权限



repeat.submit = 处理中
account.not.exist=用户不存在
country.not.exists=业务国家 {0} 不存在
account.repeat.handler.error=正在处理中，请稍候

dept.not.exist=部门不存在
post.not.exist=岗位不存在
param.not.null=参数不能为空
station.not.exits=网点不存在
this.account.has.been.deactivated=该账号已停用，请先联系HR处理
hyperlink.expire = 超链接已经失效
token.exception = token异常

country.not.empty=国家不能为空

user.not.have.calendar=该用户没有日历方案，请先创建日历方案
date.format.error=日期格式错误

log.record.module.not.exist=日志记录模块不存在或不支持

zkteco.area.and.terminal.sn.not.exist=中控区域和考勤机不存在映射数据
punch.card.version.behind.please.refresh.in.time=考勤打卡版本过低，请刷新页面后重试
export.time.range.cannot.exceed.one.month=导出时间范围不可超过一个月


attendance.cycle.no.exists=考勤周期记录不存在
attendance.cycle.status.no.change=考勤周期状态未变化
attendance.cycle.country.not.support.week={0}国家不支持配置周考勤周期
attendance.cycle.config.have.exists={0}国家已存在{1}考勤周期，请检查后重新设置！
user.not.have.attendance.cycle.config=该员工所在国家没有配置考勤周期方案，考勤周期默认为当月,如非当月请联系HR处理

leave.start.date.not.empty=请假开始时间不能为空
leave.end.date.not.empty=请假结束时间不能为空
expected.leave.time.not.empty=预计请假时长不能为空
remark.not.empty=备注不能为空
exist.clash.time.period=存在冲突时间段
this.day.not.have.punch.config=该天没有设置打卡规则
extended.leave.period=考勤周期{0}-{1}已结束，不再支持该时间段内的申请操作，请调整时间后提交
user.not.have.attendance.reissue.card.config=该员工没有设置补卡规则
correct.punch.time.can.not.be.empty=补卡时间不能为空
application.form.does.not.have.a.corresponding.vacation=申请单据不存在对应假期
attendance.cycle.config.get.cycle.type.enum=考勤周期类型不能为null
application.form.is.empty=申请单据不存在
form.status.not.cancel=当前单据状态不允许取消
out.of.office.start.date.not.empty=外勤开始时间不能为空
abnormal.record.date.not.empty=异常考勤日期不能为空
reissue.card.not.handler.this.abnormal.type=补卡不能处理该种异常类型
attendance.approval.basic.info.empty=考勤审批基础信息不存在
revoke.form.relation.form.empty=撤销申请单关联的发起申请单不存在
out.of.office.end.date.not.empty=外勤结束时间不能为空
user.attendance.reissue.card.count.is.over=已超出当前考勤周期可补卡上限
only.stage.status.can.be.delete=只有暂存状态的单据可以被删除
not.revoke.repeat=该单据已经被撤销，不允许重复操作




dept.exist.in.other.attendance.config=部门已存在 {0} 日历规则
user.exist.in.other.attendance.config=用户已存在 {0} 日历规则
attendance.config.name.exist=日历规则名称已存在
the.year.of.the.holiday.cannot.be.empty=法定假期年份参数不能为空
the.fake.details.parameter.cannot.be.empty=日历详情数据年份不能为空
the.names.of.statutory.holidays.are.repeated=法定假期名称重复
the.start.time.of.a.statutory.holiday.cannot.be.greater.than.the.end.time=法定假期开始时间不能大于结束时间
the.calendar.details.data.year.cannot.be.empty=日历详情数据年份不能为空
calendar.details.data.cannot.be.empty=日历详情数据不能为空
default.calendar.not.exist=默认日历方案不存在，请先创建默认日历
country.not.have.default.attendance.config=该国家不存在默认日历
the.leave.end.time.must.be.after.the.start.time=请假结束时间必须在开始时间之后
the.leave.balance.is.insufficient=请假余额不足
the.balance.of.leave.cannot.be.less.than.the.length.of.leave=请假余额不能小于请假时长
there.is.no.leave.type.information.for.the.details.of.the.application.form=申请单详情不存在请假类型信息
there.is.no.leave.start.or.end.time.for.the.details.of.the.application.form=申请单详情不存在请假开始或结束时间
the.user.lacks.detailed.data.for.the.leave.type=用户该假期类型缺少详情数据
leave.time.cannot.be.zero=请假时长不能为0
overtime.requests.are.not.allowed.on.weekdays=这个时间段是正常工作时间！
please.do.not.repeat.the.overtime.process=您已提交过加班单，请勿重复提加班流程！
the.imported.data.is.empty=导入的加班数据为空
overtime.days.are.not.on.the.same.day=加班日期不在同一天

leave.day.cannot.be.zero=结束日期未完全覆盖您的排班(跨夜班次)，请往后调整结束日期，谢谢!
out.of.office.is.not.allowed.to.take.more.than.7.days=按照公司政策，单次外勤申请不能超过7个工作日
out.of.office.time.can.not.be.zero=外勤时长不能为0
the.leave.duration.cannot.be.less.than.the.minimum.leave.duration.of.the.leave.type=请假时长不能小于该假期类型最小请假时长
warehouse.not.allow.out.of.work=仓内人员暂不支持外勤
warehouse.not.allow.card.revoke=仓内人员暂不支持撤销补卡
custom.attendance.not.close=不可以关闭，此考勤日历现有员工在使用
attendance.calendar.not.exist=该员工所属国家的考勤日历没有配置[{0}]年日历，请前往【calendar】配置[{1}]年日历
there.is.a.temporal.overlap.in.statutory.holiday.data=法定假期数据存在时间重叠
the.name.of.the.statutory.holiday.already.exists=法定假期名称已经存在
if.you.select.Require.a.leave.certificate.the.leave.condition.is.required=选择需要请假证明时，请假条件必填
the.duration.of.statutory.holidays.overlaps.with.those.of.existing.statutory.holidays=法定假期时间与已有法定假期时间重叠
startTime.cannot.be.empty=开始时间不能为空
endTime.cannot.be.empty=结束时间不能为空
startTime.cannot.be.later.than.endTime=开始时间不能晚于结束时间
time.range.limited.days=时间筛选范围限制1000天

user.not.have.abnormal.attendance.record.on.the.current.day=异常考勤记录不存在
abnormal.id.not.empty=异常考勤ID不能为空
abnormal.has.been.handler=异常考勤已被处理
abnormal.only.one.day=异常考勤只能是同一天
abnormal.in.review=异常考勤已在审批中

the.holiday.adjustment.amount.needs.to.be.between.-999.and.999=假期调整额度需要在-999 到 999 之间

punch.config.name.exist=打卡规则名称已存在
dept.exist.in.other.punch.config=部门适用范围跟【{0}】重复，请勿重复创建
user.exist.in.other.punch.config=该人员已绑定【{0}】，如需调整，请先从原规则中移除
punch.config.country.exist=国家适用范围跟【{0}】重复，请勿重复创建
punch.config.country.not.exist=国家级别的打卡规则不存在，国家【{0}】
punch.config.country.can.not.change=打卡规则的国家不能修改
punch.config.disable.can.not.update=停用的打卡规则不能编辑
punch.config.country.level.can.not.disabled=国家级别的打卡规则不能停用
disabled.config.user.in.other.config=不可启用，适用范围中的人员已有新规则
country.level.rule.can.not.change.range=国家级别的规则不能修改适用范围
attendance.punch.is.empty=打卡规则不存在

reissue.card.config.name.exist = 补卡规则名称已存在
dept.exist.in.other.reissue.card.config=部门适用范围跟【{0}】重复，请勿重复创建
user.exist.in.other.reissue.card.config=该人员已绑定【{0}】，如需调整，请先从原规则中移除
reissue.card.config.country.exist=国家适用范围跟【{0}】重复，请勿重复创建
reissue.card.config.country.can.not.change=补卡规则的国家不能修改
reissue.card.config.disable.can.not.update=停用的补卡规则不能编辑
reissue.card.config.country.level.can.not.disabled=国家级别的补卡规则不能停用

overtime.config.name.exist=加班规则名称已存在
dept.exist.in.other.overtime.config=部门适用范围跟【{0}】重复，请勿重复创建
user.exist.in.other.overtime.config=该人员已绑定【{0}】，如需调整，请先从原规则中移除
overtime.config.country.can.not.change=加班规则的国家不能修改
overtime.config.disable.can.not.update=停用的加班规则不能编辑
overtime.country.level.can.not.disabled=国家级别的加班规则不能停用
overtime.config.country.exist=国家适用范围跟【{0}】重复，请勿重复创建

no.gray-scale.personnel.need.to.operate.in.the.old.system=未切换新考勤系统的人员需要在HRMS系统操作
the.non-gray-scale.country.need.to.operate.in.the.new.system= 未切换新考勤系统的国家需要在HRMS系统操作
system.switching.contact.admin = 新老系统切换期间，变更请联系管理员

punch.in.content.decrypt.error=打卡参数解密失败
you.cannot.check.in.again.within.15.seconds=15s内不能再次打卡哦
employee.rule.config.error=规则异常，请联系HR调整打卡规则和排班
employee.punch.config.error=规则有误，请联系HR调整打卡规则中的上下班打卡间隔时长


scheduling.limit = 墨西哥仓内劳务员工限制页面排班
punch.class.save.not.empty=排班有效数据不能为空
batch.shift.user.not.match=已选【{0}】人，其中【{1}】不满足条件
user.are.occupied.by.running.task=用户别其他正在执行的任务占据
cycle.shift.user.must.belong.to.multi.class=循环排班用户必须属于多班次
cycle.shift.user.class.not.match.selected.class=循环班次用户的班次与所选班次不匹配
cycle.shift.user.class.not.have.relate.punch.class.rule=循环班次用户没有关联任何打卡班次规则

# 调动管理
transform.object.data.not.exist={0}数据不存在

###班次相关
classType.cannot.be.empty=MEX,BRA等国家,班次类型必填
classNature.illegality=班次性质不合法
className.duplicate=班次名称重复
restHours.abnormal=请修改时间，以满足出勤时长等于工作时长加休息时长
legalWorkingHours.abnormal=请修改时间，以满足各时段内工作时长之和等于总工作时长
attendanceHours.abnormal=请修改时间，以满足各时段内出勤时长之和等于总出勤时长
account.mismatch=适用人员不匹配"
class.info.duplicate=班次时段信息跟[{0}]班次重复，请勿重复创建，请在原班次基础上调整适用范围
className.length.limit.of.100.characters=班次名称长度限制100字符
class.info.CLASS_NOT_EXISTS=班次不存在
class.status.already.disabled=班次状态已停用,请勿重复操作
class.status.already.enable=班次状态已启用,请勿重复操作
class.enable.className.duplicate=不可启用,同班次名称已有新规则
class.enable.range.duplicate=不可启用,适用范围中的人员已有新规则
prohibit.disabled.class=不可以停用，此班次现有人员在使用
user.shift.is.occupied=班次适用范围内的用户存在其他未执行完的排班任务,请稍后操作
disable.shift.prohibit.editing=班次停用状态不允许编辑,请先启用班次状态

# 考勤设备模块校验
mac.address.already.exists=mac地址已存在
gps.address.already.exists=gps地址已存在
gps.name.already.exists=当前国家下gps名称已存在
please.enter.the.correct.MAC.address=请输入正确MAC地址 格式如 02:10:18:02:40:7b

# 假期模块校验
company.leave.repeat.error=假期类型重复
no.company.leave.exists.error=假期不存在
country.of.dispatch.required=派遣国家必填
the.current.country.does.not.maintain.the.corresponding.nationality.please.maintain.the.nationality.first=当前国家下没有维护对应国籍，请先维护国籍
leave.name.is.not.empty=假期名称不能为空
user.not.have.this.leave.type=用户没有该种假期类型

#仓内考勤
user.classNature.config.error=该员工的班次类型配置有误,请联系HR进行配置
the.person.has.resigned=该人员已离职，请先联系HR入职
the.user.account.does.not.exist=该用户账号/证件号不存在
not.recognition.or.upload.cannot.in={0}未刷脸，无法入仓
not.in.cannot.out={0}未入仓，无法离仓
not.recognition.cannot.out={0}未刷脸，无法离仓
in.cannot.in={0}已入仓，无法重复入仓
out.other.oc.warehouse.then.in=该人员已在网点[{0}]下入仓，请先离仓，再选择新的网点入仓
after.out.then.in=存在未出仓记录，请先离仓后操作入仓
abnormal.attendance.days.obtained=计算考勤日异常
face.in.first=请先进行刷脸入仓,再出仓
attendance.group.not.config=请联系HRBP配置考勤组信息
out.warehouse.then.in=该人员已在[{0}]下入仓，请先离仓，再选择新的班次入仓
ocr.scanning.failed=扫描失败，请重新扫描或录入
warehouse.entry.failure=入仓失败,非劳务派遣员工
duplicate.ID.number=证件号重复，无法登记
unsupported.countries=OCR不支持的国家
update.failed.non.labor.dispatch.employee=更新失败，非劳务派遣员工
face.engine.active.failed=人脸引擎激活失败
face.engine.initialize.failed=人脸引擎初始化失败
face.engine.obtain.failed=人脸引擎获取失败
face.detect.failed=人脸检测失败
face.feature.extract.failed=人脸特征提取失败
face.feature.compare.failed=人脸特征比较失败
face.feature.library.matching.failed=人脸特征库匹配异常,请先初始化数据
please.initialize.face.feature.library.first=请先初始化人脸库数据
face.feature.data.save.failed=人脸特征数据保存失败
face.liveness.detect.failed=人脸活性检测失败
the.result.of.face.liveness.detection.non.living=人脸活性检测结果为非活体
the.result.of.face.liveness.detection.unknown=人脸活性检测结果未知
the.result.of.face.liveness.detection.beyond.the.face=人脸活性检测结果超出人脸
facial.data.has.been.deleted=人脸数据已删除,请同步清除缓存
version.outdated=WPM小程序版本落后,请及时刷新
driver.cant.use.wpm=司机无法使用WPM，请使用司机考勤功能
cant.change.vendor=该人员今日已在[{0}]供应商下作业，不可更换
quick.out.must.in.24.hours=距离入仓时间24小时内允许拍照出仓
before.class.earliestPunchInTime=未到班次最早上班打卡时间，请重新选择班次，若没有合适的班次请联系BP配置
vendor.classes.confirm.repeat=供应商今日班次结果已确认,请勿重复操作
warehouse.no.attendance.record=仓内无人员出勤记录
not.yet.the.end.of.shift=未到班次下班时间无法确认
warehouse.attendance.config.name.exist=仓内考勤管理规则名称已存在
dept.exist.in.other.warehouse.attendance.config=部门已存在仓内考勤管理规则中
already.working.in.the.warehouse=入仓失败，该人员今日已作业，不可再次入仓作业
already.working.out.the.warehouse=出仓失败，该人员今日已作业，不可再次入仓作业
user.vendor.not.same.as.warehouse.vendor=该人员是{0}的员工,不可在{1}入仓
sex.not.empty=性别不能为空
certificate.expire.date.not.empty=证件失效时间不能为空
not.vendor.user.not.register=非{0}人员，不可入仓操作
certificateCode.must.be.consistent=RG和劳工证的证件号均为CPF需保持一致
system.time.not.macth.current.time=手机时间不在当地时区范围内，请检查手机时间
blacklist.personnel.are.not.allowed.to.enter.or.out.the.warehouse=黑名单人员，不可入仓或出仓\n黑名单原因：{0}
non.warehouse.employees.cant.use.wpm=非仓内人员不可入仓作业
certificate.format.mismatch=证件号必须为11位数字
employee.face.recognition.entrance.select.incorrect=该人员为自有人员，请切换至“自有”刷脸入口
face.recognition.duplicate.facial=人脸照片重复，重复人员账号 {0}




