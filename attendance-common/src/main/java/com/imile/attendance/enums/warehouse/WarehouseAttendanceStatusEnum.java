package com.imile.attendance.enums.warehouse;

import lombok.Getter;

/**
 * 仓内考勤状态枚举值
 *
 * <AUTHOR>
 */
@Getter
public enum WarehouseAttendanceStatusEnum {

    /**
     * 初始化
     */
    INIT(0, "初始值", "initialize"),
    /**
     * 正常
     */
    NORMAL(1, "正常", "normal"),
    /**
     * 异常
     */
    ABNORMAL(2, "异常", "abnormal"),
    /**
     * 无班次待配置班次
     */
    PENDING_SHIFT_CONFIG(3, "待配置班次", "pending shift config"),
    ;

    private final Integer code;

    private final String desc;

    private final String descEn;

    WarehouseAttendanceStatusEnum(Integer code, String desc, String descEn) {
        this.code = code;
        this.desc = desc;
        this.descEn = descEn;
    }
}
