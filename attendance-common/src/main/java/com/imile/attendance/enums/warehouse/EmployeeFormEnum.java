
package com.imile.attendance.enums.warehouse;

import lombok.Getter;

/**
 * <AUTHOR>
 * @since 2025/2/11
 */
public enum EmployeeFormEnum {
    /**
     * 长期工
     */
    LONG_TERM_WORKER("longTermWorker", "长期工"),

    /**
     * 短期工
     */
    SHORT_TERM_WORKER("shortTermWorkers", "短期工"),

    ;

    @Getter
    private final String code;

    @Getter
    private final String desc;


    EmployeeFormEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }
}
