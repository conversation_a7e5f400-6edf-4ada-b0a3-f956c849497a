package com.imile.attendance.enums.warehouse;

import lombok.Getter;

/**
 * 仓内黑名单类型枚举值
 * <AUTHOR>
 * @since 2025/2/17
 */
@Getter
public enum WarehouseBlackTypeEnum {

    /**
     * 注册
     */
    REGISTER(1, "注册"),
    /**
     * 入仓
     */
    IN(2, "入仓"),
    /**
     * 出仓
     */
    OUT(3, "出仓"),
    ;

    private final Integer code;

    private final String desc;


    WarehouseBlackTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }
}
