package com.imile.attendance.enums.warehouse;

import lombok.Getter;

/**
 * <AUTHOR>
 * @since 2024/7/7
 */
@Getter
public enum OcrErrorCodeEnum {

    OCR_FILE_UPLOAD_FAIL_THROW("30001", "ocr.scanning.failed", "扫描失败，请重新扫描或录入"),

    OCR_FILE_UPLOAD_FAIL("30002", "ocr.scanning.failed", "扫描失败，请重新扫描或录入"),

    EMPLOYEE_TYPE_NOT_MATCH("30003", "warehouse.entry.failure", "入仓失败,非劳务派遣员工"),

    DUPLICATE_ID_CARD("30004", "duplicate.ID.number", "证件号重复，无法登记"),

    NON_LABOR_DISPATCH_EMPLOYEE_CANNOT_UPDATE("30005", "update.failed.non.labor.dispatch.employee", "更新失败，非劳务派遣员工"),

    UNSUPPORTED_COUNTRIES("30006", "unsupported.countries", "OCR不支持的国家"),
    ;


    /**
     * 业务错误码
     */
    private final String code;
    /**
     * 错误描述(国际化文件)
     */
    private final String desc;
    /**
     * 错误描述(方便调用方理解)
     */
    private final String message;


    /**
     * @param code    编码
     * @param desc    key
     * @param message 描述
     */
    private OcrErrorCodeEnum(String code, String desc, String message) {
        this.code = code;
        this.desc = desc;
        this.message = message;
    }
}
