package com.imile.attendance.enums.warehouse;

import lombok.Getter;

/**
 * 仓内打卡类型枚举值
 *
 * <AUTHOR>
 */
@Getter
public enum WarehouseTypeEnum {

    /**
     * 初始化
     */
    INIT(0, "初始值"),
    /**
     * 入仓
     */
    IN(1, "入仓"),
    /**
     * 出仓
     */
    OUT(2, "出仓"),
    ;

    private final Integer code;

    private final String desc;


    WarehouseTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }
}
