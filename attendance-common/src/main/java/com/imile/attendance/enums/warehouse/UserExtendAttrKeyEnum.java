package com.imile.attendance.enums.warehouse;

import lombok.Getter;

@Getter
public enum UserExtendAttrKeyEnum {

    /**
     * 保险等级类型
     */
    insuranceCategory("insuranceCategory", "insurancecategory"),
    /**
     * 保险到期日
     */
    insuranceExpiryDate("insuranceExpiryDate", "insuranceexpirydate"),
    /**
     * 保险附件
     */
    insuranceFileUrl("insuranceFileUrl", "insurancefileurl"),

    /**
     * 保险附件名称
     */
    insuranceFileName("insuranceFileName", "insurancefilename"),





    /**
     * 司机工种属性
     */
    driverWorkType("driverWorkType","driverworktype"),

    /**
     * 司机设备id
     */
    driverDeviceIds("driverDeviceIds","driverdeviceids"),

    /**
     *  备用电话
     */
    alternativePhone("alternativePhone","alternativePhone"),
    /**
     * 用工形式
     */
    EMPLOYEE_FORM("employeeForm","employeeform")

    ;
    private String code;

    private String lowerCode;


    UserExtendAttrKeyEnum(String code, String lowerCode) {
        this.code = code;
        this.lowerCode = lowerCode;
    }

    public static UserExtendAttrKeyEnum getInstance(String code) {
        for (UserExtendAttrKeyEnum value : UserExtendAttrKeyEnum.values()) {
            if (value.getCode().equalsIgnoreCase(code)) {
                return value;
            }
        }
        return null;
    }

    public static UserExtendAttrKeyEnum getInstanceByLowerCode(String lowerCode) {
        for (UserExtendAttrKeyEnum value : UserExtendAttrKeyEnum.values()) {
            if (value.getLowerCode().equalsIgnoreCase(lowerCode)) {
                return value;
            }
        }
        return null;
    }
}
