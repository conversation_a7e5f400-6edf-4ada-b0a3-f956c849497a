package com.imile.attendance.enums.warehouse;

import lombok.Getter;

/**
 * 仓内打卡状态枚举值
 *
 * <AUTHOR>
 */
@Getter
public enum WarehouseStatusEnum {


    /**
     * 初始化
     */
    INIT(0, "initialize"),

    /**
     * 待出仓
     */
    WAIT_OUT(1, "待出仓"),

    /**
     * 已出仓
     */
    OUT(2, "已出仓"),
    ;

    private final Integer code;

    private final String desc;


    WarehouseStatusEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }
}
