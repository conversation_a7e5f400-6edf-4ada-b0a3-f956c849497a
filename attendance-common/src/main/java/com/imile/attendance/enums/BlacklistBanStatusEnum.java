package com.imile.attendance.enums;

import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;

/**
 * 封禁状态枚举
 *
 * @ClassName BlacklistBanStatus
 * <AUTHOR>
 * @Date 2023/5/16 16:19
 */
@Getter
public enum BlacklistBanStatusEnum {

    /**
     * 封禁中
     */
    UNDER_LOCKDOWN(1, "封禁中", 1, "DISABLED"),

    /**
     * 封禁结束
     */
    END_OF_LOCKDOWN(2, "封禁结束", 0, "ACTIVE"),

    /**
     * 封禁处理中
     */
    UNDER_LOCKDOWN_PROCESSING(3, "封禁处理中", 1, "ACTIVE"),

    /**
     * 默认
     */
    DEFAULT(0, "", 0,"ACTIVE"),
    ;

    /**
     * value
     */
    private final Integer value;

    /**
     * 描述
     */
    private final String desc;

    /**
     * 实际上是否被封禁
     * 1封禁状态、0正常状态
     */
    private final Integer isBan;

    /**
     * 状态(ACTIVE 生效,DISABLED)
     */
    private final String status;


    BlacklistBanStatusEnum(Integer value, String desc, Integer isBan, String status) {
        this.value = value;
        this.desc = desc;
        this.isBan = isBan;
        this.status = status;
    }

    public static BlacklistBanStatusEnum getInstance(Integer value) {
        for (BlacklistBanStatusEnum statusEnum : BlacklistBanStatusEnum.values()) {
            if (Objects.equals(value, statusEnum.getValue())) {
                return statusEnum;
            }
        }
        return DEFAULT;
    }

    /**
     * 封禁中、封禁处理中
     *
     * @return List<java.lang.Integer>
     */
    public static List<Integer> getBanStatusList() {
        return Arrays.asList(BlacklistBanStatusEnum.UNDER_LOCKDOWN.getValue(), BlacklistBanStatusEnum.UNDER_LOCKDOWN_PROCESSING.getValue());
    }

    /**
     * 用户封禁状态（status）是否被变更
     *
     * @param before 变更前状态
     * @param after 变更后状态
     * @return java.lang.Boolean
     */
    public static Boolean isChangeStatus(Integer before, Integer after){
        return !Objects.equals(BlacklistBanStatusEnum.getInstance(before).getStatus(),BlacklistBanStatusEnum.getInstance(after).getStatus());
    }


}
