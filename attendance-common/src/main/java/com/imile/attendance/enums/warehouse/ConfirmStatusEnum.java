package com.imile.attendance.enums.warehouse;

import lombok.Getter;

/**
 * 供应商日报确认状态表
 * <AUTHOR>
 * @since 2024/12/6
 */
@Getter
public enum ConfirmStatusEnum {

    NOT_CONFIGURE(0, "未配置"),
    NOT_YET_TIME(10, "未到时间"),
    WAIT_CONFIRM(15, "未确认"),
    CONFIRM(20, "已确认"),
    ;

    private final int value;
    private final String description;

    ConfirmStatusEnum(int value, String description) {
        this.value = value;
        this.description = description;
    }

    public static ConfirmStatusEnum getInstance(Integer code) {
        for (ConfirmStatusEnum value : ConfirmStatusEnum.values()) {
            if (value.getValue() == code) {
                return value;
            }
        }
        throw new IllegalArgumentException(code + "");
    }

}
