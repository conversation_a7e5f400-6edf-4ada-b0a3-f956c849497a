package com.imile.attendance.enums.warehouse;

import lombok.Getter;

/**
 * 仓内打卡状态枚举值
 *
 * <AUTHOR>
 */
@Getter
public enum PunchStatusEnum {

    /**
     * 正常打卡
     */
    NORMAL_PUNCH(1, "正常打卡"),

    /**
     * 异常打卡
     */
    ABNORMAL_PUNCH(2, "异常打卡"),
    ;

    private final Integer code;

    private final String desc;


    PunchStatusEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }
}
