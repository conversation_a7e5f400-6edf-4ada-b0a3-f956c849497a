package com.imile.attendance.enums.warehouse;

import lombok.Getter;

/**
 * 仓内异常状态枚举值
 *
 * <AUTHOR>
 */
@Getter
public enum WarehouseAbnormalStatusEnum {

    /**
     * 未处理
     */
    PENDING_PROCESSING(0, "待处理"),
    /**
     * 已处理
     */
    PROCESSED(1, "已处理"),

    /**
     * 确认异常
     */
    CONFIRM_ABNORMAL(2, "确认异常"),

    /**
     * 已过期
     */
    EXPIRED(3, "已过期"),
    ;

    private final Integer code;

    private final String desc;


    WarehouseAbnormalStatusEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }
}
