package com.imile.attendance.enums.warehouse;

import lombok.Getter;

/**
 * 刷脸状态枚举值
 *
 * <AUTHOR>
 */
@Getter
public enum FaceRecordStatusEnum {
    /**
     * 有效(待使用)
     */
    EFFECTIVE(0, "有效"),

    /**
     * 无效(已使用)
     */
    INVALID(1, "无效"),
    ;

    private final Integer code;

    private final String desc;


    FaceRecordStatusEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }
}
