package com.imile.attendance.util;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.entity.DateDO;
import com.imile.attendance.enums.MonthEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.time.Duration;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.TimeZone;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;

/**
 * 日期计算工具类
 *
 * <AUTHOR> chen
 * @Date 2025/3/24
 * @Description
 */
@Slf4j
public class DateHelper {

    private static final String HOUR_MIN = "HH:mm";

    private static final double HOUR = 3600 * 1000d;

    private static final double MINUS = 60 * 1000d;


    /**
     * yyyy/MM/dd 格式的日期格式化器
     */
    private static final String PATTERN_DATE_SLASH = "yyyy/MM/dd";

    /**
     * yyyy-MM-dd HH:mm:ss 格式的日期格式化器
     */
    public static final DateTimeFormatter NORM_DATETIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");


    public static String getJVMDefaultZoneId() {
        TimeZone systemTimeZone = TimeZone.getDefault();
        return systemTimeZone.getID();
    }

    /**
     * 获得年的部分
     *
     * @param date 日期
     * @return 年的部分
     */
    public static int year(Date date) {
        return DateUtil.year(date);
    }

    /**
     * 获得月份，从1开始计数
     *
     * @param date 日期
     * @return 月份，从1开始计数
     */
    public static int month(Date date) {
        return DateUtil.month(date) + 1;
    }

    /**
     * 获得指定日期是这个日期所在月份的第几天
     *
     * @param date 日期
     * @return 天
     */
    public static int dayOfMonth(Date date) {
        return DateUtil.dayOfMonth(date);
    }

    /**
     * 获取格式化的月份字符串（01-12）
     *
     * @param date 日期
     * @return 两位数的月份字符串
     */
    public static String formatMonth(Date date) {
        int month = month(date);
        return month < 10 ? "0" + month : String.valueOf(month);
    }

    /**
     * 将数字格式化为两位数的月份字符串，并进行有效性验证
     *
     * <p>该方法用于将整数类型的月份值格式化为两位数字符串，不足两位时前面补零。</p>
     * <p>示例：</p>
     * <ul>
     *   <li>1 → "01"</li>
     *   <li>2 → "02"</li>
     *   <li>10 → "10"</li>
     *   <li>12 → "12"</li>
     * </ul>
     *
     * @param month 月份值，必须在1-12的范围内
     * @return 两位数的月份字符串，格式为"01"到"12"
     * @throws IllegalArgumentException 如果月份值不在1-12的有效范围内
     */
    public static String formatMonth(int month) {
        if (month < 1 || month > 12) {
            throw new IllegalArgumentException("月份值必须在1-12的范围内，当前值为：" + month);
        }
        return month < 10 ? "0" + month : String.valueOf(month);
    }

    /**
     * 将字符串类型的月份值格式化为两位数的月份字符串，并进行有效性验证
     *
     * <p>该方法用于将字符串类型的月份值转换为整数后格式化为两位数字符串，不足两位时前面补零。</p>
     * <p>支持以下格式的字符串输入：</p>
     * <ul>
     *   <li>"1" → "01"</li>
     *   <li>"01" → "01"</li>
     *   <li>"2" → "02"</li>
     *   <li>"12" → "12"</li>
     * </ul>
     *
     * @param monthStr 月份值的字符串表示，必须能解析为1-12的整数
     * @return 两位数的月份字符串，格式为"01"到"12"
     * @throws IllegalArgumentException 如果字符串不能解析为有效整数或不在1-12的范围内
     * @throws NumberFormatException 如果字符串不是有效的数字格式
     */
    public static String formatMonth(String monthStr) {
        if (StringUtils.isEmpty(monthStr)) {
            throw new IllegalArgumentException("月份字符串不能为空");
        }

        try {
            int month = Integer.parseInt(monthStr.trim());
            return formatMonth(month);
        } catch (NumberFormatException e) {
            throw new IllegalArgumentException("月份字符串必须是有效的数字，当前值为：" + monthStr);
        }
    }

    /**
     * 将日期转为dayId,格式为yyyyMMdd
     */
    public static Long getDayId(Date date) {
        if (Objects.isNull(date)) {
            return null;
        }
        // yyyyMMdd
        return Long.parseLong(DateUtil.format(date, DatePattern.PURE_DATE_PATTERN));
    }

    /**
     * 将dayId转换为date,格式为yyyyMMdd
     */
    public static Date transferDayIdToDate(Long dayId) {
        if (dayId == null) {
            return null;
        }
        // yyyyMMdd
        return DateUtil.parse(dayId.toString(), DatePattern.PURE_DATE_PATTERN);
    }

    /**
     * 将dayId转换为date,格式为yyyyMMdd
     */
    public static Date transferDayIdToDate(String dayId) {
        if (StringUtils.isEmpty(dayId)) {
            return null;
        }
        // yyyyMMdd
        return DateUtil.parse(dayId, DatePattern.PURE_DATE_PATTERN);
    }

    /**
     * 获取偏移指定天数后的dayId
     *
     * @param dayId  当前dayId (格式：yyyyMMdd)
     * @param offset 偏移天数，正数表示后移，负数表示前移
     * @return 偏移后的dayId
     */
    public static Long getOffsetDayId(Long dayId, int offset) {
        Date date = transferDayIdToDate(dayId);
        Date offsetDay = DateUtil.offsetDay(date, offset);
        return getDayId(offsetDay);
    }

    /**
     * 获取后一天的dayId
     *
     * @param dayId 当前dayId (格式：yyyyMMdd)
     * @return 后一天的dayId
     */
    public static Long getNextDayId(Long dayId) {
        if (Objects.isNull(dayId)) {
            return null;
        }
        return getOffsetDayId(dayId, 1);
    }

    /**
     * 获取前一天的dayId
     *
     * @param dayId 当前dayId (格式：yyyyMMdd)
     * @return 前一天的dayId
     */
    public static Long getPreviousDayId(Long dayId) {
        if (Objects.isNull(dayId)) {
            return null;
        }
        return getOffsetDayId(dayId, -1);
    }

    /**
     * dayId格式转换为 15-Nov-22格式 20220101
     */
    public static String dayIdFormat(Long dayId) {
        if (Objects.isNull(dayId)) {
            return null;
        }
        String day = dayId.toString().substring(6);
        String monthInt = dayId.toString().substring(4, 6);
        MonthEnum instance = MonthEnum.getInstance(monthInt);
        String month = instance.getDesc();
        String year = dayId.toString().substring(0, 4);
        return day + "-" + month + "-" + year;
    }

    /**
     * 获取从startDayId到endDayId之间的所有dayId列表（包含开始和结束日期）
     *
     * @param startDayId 开始日期的dayId，格式为yyyyMMdd
     * @param endDayId   结束日期的dayId，格式为yyyyMMdd
     * @return 包含所有日期dayId的列表
     */
    public static List<Long> getDayIdList(long startDayId, long endDayId) {
        if (startDayId > endDayId) {
            throw new IllegalArgumentException("开始日期不能大于结束日期");
        }

        List<Long> dayIdList = new ArrayList<>();

        // 当前日期
        Date currentDate = transferDayIdToDate(startDayId);

        // 循环直到超过endDayId
        while (true) {
            long currentDayId = getDayId(currentDate);
            dayIdList.add(currentDayId);

            // 如果已经到达结束日期，则退出循环
            if (currentDayId >= endDayId) {
                break;
            }

            // 日期加1天
            currentDate = DateUtil.offsetDay(currentDate, 1);
        }

        return dayIdList;
    }

    /**
     * 将打卡时间添加默认日期前缀
     *
     * @param timeStr 输入的时间（格式为 HH:mm:ss）
     * @return 格式化后的时间字符串（格式为 "1970-01-01 HH:mm:ss"），若输入为空则返回空字符串
     */
    public static String appendDefaultDateStrToTimeStr(String timeStr) {
        if (StringUtils.isBlank(timeStr)) {
            // 输入为空时返回空字符串
            return "";
        }
        // 添加默认日期前缀：1970-01-01
        return BusinessConstant.DEFAULT_TIME + timeStr;
    }

    /**
     * 将打卡时间添加默认日期前缀(DatePattern.NORM_DATETIME_PATTERN)
     *
     * @param timeStr 输入的时间（格式为 HH:mm:ss）
     * @return 格式化后的时间（格式为 "1970-01-01 HH:mm:ss"），若输入为空则返回null
     */
    public static Date appendDefaultDateToTime(String timeStr) {
        String defaultDateToTimeStr = appendDefaultDateStrToTimeStr(timeStr);
        if (StringUtils.isBlank(defaultDateToTimeStr)) {
            return null;
        }
        return DateUtil.parse(defaultDateToTimeStr);
    }

    /**
     * 判断两个时间是否跨天
     *
     * @param firstTime  进入时间
     * @param secondTime 退出时间
     * @return 如果时间跨天返回 BusinessConstant.Y（代表“是”），否则返回 BusinessConstant.N（代表“否”）
     */
    public static Integer judgeCrossDay(Date firstTime, Date secondTime) {
        if (firstTime.before(secondTime)) {
            return BusinessConstant.N;
        }
        return BusinessConstant.Y;
    }

    /**
     * 将日期格式化为yyyyMMdd
     *
     * @param date 日期
     * @return 格式化后的日期字符串
     */
    public static String formatPureDate(Date date) {
        return date != null ? DateUtil.format(date, DatePattern.PURE_DATE_PATTERN) : "";
    }

    /**
     * 将时间格式化为 HH:mm:ss
     *
     * @param date 日期
     * @return 格式化后的时间字符串
     */
    public static String formatHHMMSS(Date date) {
        return date != null ? DateUtil.format(date, DatePattern.NORM_TIME_PATTERN) : "";
    }

    /**
     * 将日期格式化为 yyyy-MM-dd
     *
     * @param date 日期
     * @return 格式化后的日期字符串
     */
    public static String formatYYYYMMDD(Date date) {
        return date != null ? DateUtil.format(date, DatePattern.NORM_DATE_PATTERN) : "";
    }

    /**
     * 将日期格式化为 yyyy-MM-dd HH:mm:ss
     *
     * @param date 日期
     * @return 格式化后的日期字符串
     */
    public static String formatYYYYMMDDHHMMSS(Date date) {
        return date != null ? DateUtil.format(date, DatePattern.NORM_DATETIME_PATTERN) : "";
    }

    /**
     * 连接日期和时间字符串 yyyy-MM-dd HH:mm:ss
     *
     * @param dateStr 日期字符串 yyyy-MM-dd
     * @param timeStr 时间字符串 HH:mm:ss
     * @return 连接后的日期时间字符串
     */
    public static String concatDateAndTimeStr(String dateStr, String timeStr) {
        return dateStr + " " + timeStr;
    }

    /**
     * 连接日期和时间字符串 yyyy-MM-dd HH:mm:ss
     *
     * @param dateStr 日期字符串 yyyy-MM-dd
     * @param timeStr 时间字符串 HH:mm:ss
     * @return 连接后的日期时间
     */
    public static Date concatDateAndTime(String dateStr, String timeStr) {
        return parseYYYYMMDDHHMMSS(dateStr + " " + timeStr);
    }

    /**
     * 将日期字符串转换为日期
     *
     * @param dateStr 日期字符串 yyyy-MM-dd HH:mm:ss
     * @return Date 对应的日期
     */
    public static Date parseYYYYMMDDHHMMSS(String dateStr) {
        return StringUtils.isEmpty(dateStr) ? null : DateUtil.parse(dateStr, DatePattern.NORM_DATETIME_PATTERN);
    }

    public static Date parseYYYYMMDD(String dateStr) {
        return StringUtils.isEmpty(dateStr) ? null : DateUtil.parse(dateStr, DatePattern.NORM_DATE_PATTERN);
    }

    /**
     * 将日期格式化为 yyyy/MM/dd
     *
     * @param date 日期
     * @return 格式化后的日期字符串
     */
    public static String formatDateWithSlash(Date date) {
        return date != null ? DateUtil.format(date, PATTERN_DATE_SLASH) : "";
    }

    /**
     * 将 yyyy/MM/dd 格式的字符串转换为日期
     *
     * @param dateStr yyyy/MM/dd 格式的日期字符串
     * @return Date 对象，如果转换失败返回 null
     */
    public static Date parseDateWithSlash(String dateStr) {
        return StringUtils.isEmpty(dateStr) ? null : DateUtil.parse(dateStr, PATTERN_DATE_SLASH);
    }

    /**
     * 忽略时间的秒
     *
     * @param date 日期
     * @return 忽略秒
     */
    public static Date parseIgnoreSeconds(Date date) {
        return DateUtil.parse(DateUtil.format(date, DatePattern.NORM_DATETIME_MINUTE_PATTERN) + ":00", DatePattern.NORM_DATETIME_PATTERN);
    }

    /**
     * 计算两个时间的差值
     * @param date1
     * @param date2
     * @return
     */
    public static BigDecimal diffHour(Date date1, Date date2) {
        if (date1 == null || date2 == null) {
            return BigDecimal.ZERO;
        }
        return BigDecimalUtil.setDoubleScale((date1.getTime() - date2.getTime()) / HOUR, 2);
    }

    public static DateDO getDateByDayId(Long dayId) {
        String date = getString(dayId);
        DateTime parse = DateUtil.parse(date, DatePattern.NORM_DATE_PATTERN);
        DateDO dateDO = new DateDO();
        dateDO.setDate(parse);
        dateDO.setDay((long) DateUtil.dayOfMonth(parse));
        dateDO.setMonth((long) (DateUtil.month(parse) + 1));
        dateDO.setYear((long) DateUtil.year(parse));
        return dateDO;
    }

    public static String getString(Long dayId) {
        String date = new StringBuilder().append(String.valueOf(dayId).substring(0, 4))
                .append("-")
                .append(String.valueOf(dayId).substring(4, 6))
                .append("-")
                .append(String.valueOf(dayId).substring(6, 8)).toString();
        return date;
    }


    /**
     * 检查字符串是否为有效的 yyyy/MM/dd 格式日期
     *
     * @param dateStr 待检查的日期字符串
     * @return 是否为有效日期
     */
    public static boolean isValidDateWithSlash(String dateStr) {
        if (StringUtils.isEmpty(dateStr)) {
            return false;
        }
        try {
            DateUtil.parse(dateStr, PATTERN_DATE_SLASH);
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    public static Date pushDate(Date date, int pushDay) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date == null ? new Date() : date);
        calendar.add(Calendar.DATE, pushDay);
        return calendar.getTime();
    }

    /**
     * 计算当前时间-对应分钟数
     * @param date
     * @param minutes
     * @return
     */
    public static Date calcMinutesFromDate(Date date,
                                           BigDecimal minutes,
                                           Integer operate) {
        // 转换为Java 8的时间对象
        Instant endInstant = date.toInstant();

        // 计算要减去的时长（分钟→纳秒）
        BigDecimal nanosToSubtract = minutes
                .multiply(BigDecimal.valueOf(60_000_000_000L));

        // 创建Duration对象
        Duration duration = Duration.ofNanos(nanosToSubtract.longValue());

        // 计算新时间(相加)
        if (Objects.nonNull(operate) && 1 == operate) {
            return Date.from(endInstant.plus(duration));
        }
        // 计算新时间(相减)
        return Date.from(endInstant.minus(duration));
    }

    /**
     * 判断两个日期相差几天
     *
     * @param startDate
     * @param endDate
     * @return
     */
    public static int getDateDiff(Date startDate, Date endDate) {
        return (int) ((endDate.getTime() - startDate.getTime()) / (24 * 60 * 60 * 1000));
    }

    /**
     * 判断两个日期是否跨年
     *
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @return 是否跨年
     */
    public static Boolean judgeCrossYear(Date startDate, Date endDate) {
        if (startDate == null || endDate == null) {
            return false;
        }
        int firstYear = year(startDate);
        int secondYear = year(endDate);
        return firstYear != secondYear;
    }

    /**
     * 判断两个dayId是否跨年（dayId格式：yyyyMMdd）
     *
     * @param startDayId 第一个dayId
     * @param endDayId   第二个dayId
     * @return 是否跨年
     */
    public static Boolean judgeCrossYearByDayId(Long startDayId, Long endDayId) {
        if (startDayId == null || endDayId == null) {
            return false;
        }
        // 提取年份部分（前4位）
        int firstYear = (int) (startDayId / 10000);
        int secondYear = (int) (endDayId / 10000);
        return firstYear != secondYear;
    }

    /**
     * 根据dayId集合获取查询年份列表
     * 将dayId集合排序后，取最小和最大值判断是否跨年，返回对应的年份列表
     *
     * @param dayIdSet dayId集合，格式为yyyyMMdd
     * @return 年份列表，如果跨年则返回开始年份和结束年份，否则返回单个年份
     */
    public static List<Integer> getQueryYearsFromDayIdSet(Set<Long> dayIdSet) {
        if (CollectionUtils.isEmpty(dayIdSet)) {
            return Collections.emptyList();
        }

        // 将dayId集合排序并转换为列表
        List<Long> dayIdList = dayIdSet.stream()
                .sorted()
                .collect(Collectors.toList());

        // 获取开始和结束日期
        Date startDate = transferDayIdToDate(dayIdList.get(0));
        Date endDate = transferDayIdToDate(dayIdList.get(dayIdList.size() - 1));

        // 判断是否跨年并返回对应的年份列表
        return judgeCrossYear(startDate, endDate) ?
                Arrays.asList(year(startDate), year(endDate)) :
                Collections.singletonList(year(startDate));
    }

    /**
     * 根据时区转换日期
     *
     * @param timeZone 8 北京时间 ，3 沙特时间 ，4 迪拜时间 他是整数
     * @param date     日期
     * @return Date
     */
    public static Date convertDateByTimeZonePlus(String timeZone, Date date) {
        if (StringUtils.isEmpty(timeZone)) {
            timeZone = "8";
        }
        Date convertDate = null;
        if (date != null) {
            long systemOffset = Calendar.getInstance().getTimeZone().getOffset(System.currentTimeMillis());
            long timeZoneOffset = Calendar.getInstance(TimeZone.getTimeZone("GMT+" + timeZone)).getTimeZone()
                    .getOffset(System.currentTimeMillis());
            if (Integer.parseInt(timeZone) < 0) {
                timeZoneOffset = Calendar.getInstance(TimeZone.getTimeZone("GMT" + timeZone)).getTimeZone()
                        .getOffset(System.currentTimeMillis());
            }
            convertDate = new Date(date.getTime() + (timeZoneOffset - systemOffset));
        }
        return convertDate;
    }

    /**
     * 将日期字符串转换为对应时区的日期
     *
     * @param timeZone 8 北京时间 ，3 沙特时间 ，4 迪拜时间 他是整数
     * @param dateStr  日期字符串 yyyy-MM-dd HH:mm:ss
     * @return Date 返回对应时区的日期
     */
    public static Date convertDateStrByTimeZonePlus(String timeZone, String dateStr) {
        return convertDateByTimeZonePlus(timeZone,
                DateUtil.parse(dateStr, DatePattern.NORM_DATETIME_PATTERN));
    }

    public static BigDecimal diffMins(Date var1, Date var2) {
        if (var1 == null || var2 == null) {
            return BigDecimal.ZERO;
        }
        return BigDecimalUtil.setDoubleScale((var1.getTime() - var2.getTime()) / MINUS, 2);
    }

    public static Date beginOfDay(Date date) {
        if (Objects.isNull(date)) {
            return new Date();
        }
        return DateUtil.beginOfDay(date);
    }

    public static Date endOfDay(Date date) {
        if (Objects.isNull(date)) {
            return new Date();
        }
        return DateUtil.endOfDay(date);
    }

    public static int getYearFormDayId(Long dayId) {
        if (null == dayId) {
            return 0;
        }
        return Integer.parseInt(Long.toString(dayId).substring(0, 4));
    }

    /**
     * 格式化时区字符串
     *
     * @param zoneIdStr 时区字符串
     * @return 格式化后的时区字符串
     */
    public static String getFormattedZoneId(String zoneIdStr) {
        if (zoneIdStr.startsWith("+") || zoneIdStr.startsWith("-")) {
            // 已经带有符号的情况，直接使用
            return "GMT" + zoneIdStr;
        } else {
            // 纯数字情况，视为正时区，添加 "+"
            return "GMT+" + zoneIdStr;
        }
    }

    /**
     * 获取时区对象
     *
     * @param zoneIdStr 时区字符串
     * @return 时区对象
     */
    public static TimeZone getTimeZone(String zoneIdStr) {
        return TimeZone.getTimeZone(getFormattedZoneId(zoneIdStr));
    }

    /**
     * 根据时区获取日期时间DateTime
     *
     * @param date      日期
     * @param zoneIdStr 时区字符串
     * @return 日期时间对象
     */
    public static DateTime getDateTimeByTimeZone(Date date, String zoneIdStr) {
        return new DateTime(date, getTimeZone(zoneIdStr));
    }

    /**
     * 将毫秒级时间戳转换为指定时区下 "yyyy-MM-dd HH:mm:ss" 格式的字符串。
     * 支持多种时区格式：
     * - 带加号前缀：如 "+8", "+10"
     * - 纯数字：如 "8", "9", "10"
     * - 带减号前缀：如 "-8", "-4"
     *
     * @param timestampMillis 毫秒级的时间戳
     * @param zoneIdStr       目标时区的ID
     * @return 格式化后的日期时间字符串
     */
    public static String convertTimestampToDateStr(long timestampMillis, String zoneIdStr) {
        if (timestampMillis <= 0) {
            return "Invalid Timestamp";
        }
        Instant instant = Instant.ofEpochMilli(timestampMillis);
        ZoneId zoneId;
        try {
            // 处理不同格式的时区字符串
            String formattedZoneId = getFormattedZoneId(zoneIdStr);
            zoneId = ZoneId.of(formattedZoneId);
        } catch (Exception e) {
            log.warn("无效的时区ID: {}, 将使用系统默认时区。", zoneIdStr);
            zoneId = ZoneId.systemDefault();
        }
        LocalDateTime localDateTime = LocalDateTime.ofInstant(instant, zoneId);
        return localDateTime.format(NORM_DATETIME_FORMATTER);
    }

    /**
     * 将特定时区的日期时间字符串转换为毫秒级 Unix 时间戳。
     * 支持多种时区格式：
     * - 带加号前缀：如 "+8", "+10"
     * - 纯数字：如 "8", "9", "10"
     * - 带减号前缀：如 "-8", "-4"
     *
     * @param dateStr    日期时间字符串，格式为 "yyyy-MM-dd HH:mm:ss"
     * @param timezoneId 时区ID
     * @return 毫秒级的 Unix 时间戳
     */
    public static long convertDateStrToTimestamp(String dateStr, String timezoneId) {
        if (StringUtils.isEmpty(dateStr)) {
            log.warn("日期时间字符串为空");
            return 0L;
        }

        try {
            // 解析日期时间字符串
            LocalDateTime localDateTime = LocalDateTime.parse(dateStr, NORM_DATETIME_FORMATTER);

            // 处理不同格式的时区字符串
            String formattedZoneId = getFormattedZoneId(timezoneId);

            ZoneId zoneId;
            try {
                zoneId = ZoneId.of(formattedZoneId);
            } catch (Exception e) {
                log.warn("无效的时区ID: {}, 将使用系统默认时区。", timezoneId);
                zoneId = ZoneId.systemDefault();
            }

            // 将本地日期时间与时区关联
            ZonedDateTime zonedDateTime = localDateTime.atZone(zoneId);

            // 转换为 Instant 并获取毫秒时间戳
            return zonedDateTime.toInstant().toEpochMilli();
        } catch (Exception e) {
            log.warn("日期时间字符串解析失败: {}", dateStr, e);
            return 0L;
        }
    }

    /**
     * 忽略时间中的秒和毫秒部分
     * 例如：将 "2023-10-05 14:30:45" 转换为 "2023-10-05 14:30:00"
     *
     * @param time 原始时间
     * @return 忽略秒和毫秒后的时间，如果输入为null则返回null
     */
    public static Date truncateToMinute(Date time) {
        if (time == null) {
            return null;
        }
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(time);
        calendar.set(Calendar.SECOND, 0);
        // 同时清零毫秒
        calendar.set(Calendar.MILLISECOND, 0);
        return calendar.getTime();
    }

    /**
     * 判断当前日期是否处于凌晨0点
     * 忽略分钟、秒
     *
     * @param time 原始时间
     * @return 忽略秒和毫秒后的时间，如果输入为null则返回null
     */
    public static Boolean isMidNight(Date time) {
        if (time == null) {
            return false;
        }
        Calendar cal = Calendar.getInstance();
        cal.setTime(time);  // 设置 Calendar 时间为传入的 Date
        int hour = cal.get(Calendar.HOUR_OF_DAY); // 获取小时（24小时制）
        return hour == 0; // 忽略分钟和秒，只判断小时是否为0
    }

    /**
     * 获取中间时间
     * @param startDate
     * @param endDate
     * @return
     */
    public static Date getMidDate(Date startDate, Date endDate) {
        // 获取开始时间和结束时间的毫秒数
        long startMillis = startDate.getTime();
        long endMillis = endDate.getTime();
        // 计算中间值的毫秒数
        long midpointMillis = (startMillis + endMillis) / 2;
        // 返回中间值对应的Date对象
        return new Date(midpointMillis);
    }

    /**
     * 获取较晚时间
     * @param d1
     * @param d2
     * @return
     */
    public static Date max(Date d1, Date d2) {
        if (d1 == null) return d2;
        if (d2 == null) return d1;
        return d1.after(d2) ? d1 : d2;
    }

    /**
     * 获取较早时间
     * @param d1
     * @param d2
     * @return
     */
    public static Date min(Date d1, Date d2) {
        if (d1 == null) return d2;
        if (d2 == null) return d1;
        return d1.before(d2) ? d1 : d2;
    }

    public static void main(String[] args) {
//        System.out.println(convertTimestampToString(1747883423082L, "8"));
//        System.out.println(convertTimestampToString(1747883423082L, "10"));
//        System.out.println(convertTimestampToString(1747883423082L, "+11"));
//        System.out.println(convertTimestampToString(1747883423082L, "-8"));
//
//        System.out.println(DateHelper.formatHHMMSS(new Date()));

//        // 测试 convertStringToTimestamp 方法
//        String testDateTime = "2025-05-22 14:00:00";
//        System.out.println("原始日期时间字符串: " + testDateTime);
//        long timestamp = convertDateStrToTimestamp(testDateTime, "8");
//        System.out.println("转换为时间戳: " + timestamp);
//        System.out.println("时间戳转回字符串: " + convertTimestampToDateStr(timestamp, "8"));
//
//        // 测试不同时区
//        System.out.println("GMT+10 时间戳: " + convertDateStrToTimestamp(testDateTime, "10") +
//                " GMT+10 日期时间字符串: " + convertTimestampToDateStr(timestamp, "10"));
//        System.out.println("GMT-8 时间戳: " + convertDateStrToTimestamp(testDateTime, "-8") +
//                " GMT-8 日期时间字符串: " + convertTimestampToDateStr(timestamp, "-8"));
//
//        TimeZone systemTimeZone = TimeZone.getDefault();
//        System.out.println(systemTimeZone.getID());

        TimeZone timeZone8 = getTimeZone("+8");
        System.out.println(timeZone8.getID());
        TimeZone timeZone10 = getTimeZone("10");
        System.out.println(timeZone10.getID());

        Date currentDate = DateHelper.parseYYYYMMDDHHMMSS("2025-05-27 10:00:00");
        System.out.println(formatYYYYMMDDHHMMSS(currentDate) + ": 时间戳：" + currentDate.getTime() + ": dayId:" + getDayId(currentDate));
        System.out.println(formatYYYYMMDDHHMMSS(getDateTimeByTimeZone(currentDate, "+8")) + ": 时间戳：" + getDateTimeByTimeZone(currentDate, "+8").getTime());
        System.out.println(formatYYYYMMDDHHMMSS(getDateTimeByTimeZone(currentDate, "10")) + ": 时间戳：" + getDateTimeByTimeZone(currentDate, "10").getTime() + ": dayId:" + getDayId(getDateTimeByTimeZone(currentDate, "10")));
        System.out.println(formatYYYYMMDDHHMMSS(getDateTimeByTimeZone(currentDate, "+5")) + ": 时间戳：" + getDateTimeByTimeZone(currentDate, "+5").getTime());
        System.out.println(formatYYYYMMDDHHMMSS(getDateTimeByTimeZone(currentDate, "-6")) + ": 时间戳：" + getDateTimeByTimeZone(currentDate, "-8").getTime() + ": dayId:" + getDayId(getDateTimeByTimeZone(currentDate, "-6")));
        System.out.println(formatYYYYMMDDHHMMSS(getDateTimeByTimeZone(currentDate, "0")) + ": 时间戳：" + getDateTimeByTimeZone(currentDate, "0").getTime());
        System.out.println(formatYYYYMMDDHHMMSS(getDateTimeByTimeZone(currentDate, "+0")) + ": 时间戳：" + getDateTimeByTimeZone(currentDate, "+0").getTime());

//        Long attendanceDayId = 20250526L;
//        Long previousDayId = DateHelper.getPreviousDayId(attendanceDayId);
//        Long nextDayId = DateHelper.getNextDayId(attendanceDayId);
//        System.out.println("attendanceDayId:" + attendanceDayId + ",previousDayId:" + previousDayId + ",nextDayId:" + nextDayId);

    }
}
