package com.imile.attendance.util;

import com.google.zxing.BarcodeFormat;
import com.google.zxing.EncodeHintType;
import com.google.zxing.MultiFormatWriter;
import com.google.zxing.common.BitMatrix;
import com.google.zxing.qrcode.decoder.ErrorCorrectionLevel;
import lombok.extern.slf4j.Slf4j;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.Base64;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2024/11/30
 */
@Slf4j
public class QRCodeGeneratorUtil {

    public static String generateQRCode(String url) {
        try {
            // 设置二维码参数
            int width = 300;
            int height = 300;
            String format = "PNG";  // 输出图片格式
            Map<EncodeHintType, Object> hints = new HashMap<>();
            hints.put(EncodeHintType.ERROR_CORRECTION, ErrorCorrectionLevel.L);  // 低纠错
            hints.put(EncodeHintType.MARGIN, 1);  // 设置边距

            // 生成二维码的矩阵
            BitMatrix bitMatrix = new MultiFormatWriter().encode(url, BarcodeFormat.QR_CODE, width, height, hints);

            // 将矩阵转化为 BufferedImage
            BufferedImage bufferedImage = toBufferedImage(bitMatrix);

            // 转换 BufferedImage 为 Base64 字符串
            return convertToBase64(bufferedImage, format);
        } catch (Exception e) {
            log.error("二维码生成失败", e);
        }
        return null;
    }

    // 将二维码矩阵转换为 BufferedImage
    private static BufferedImage toBufferedImage(BitMatrix matrix) {
        int width = matrix.getWidth();
        int height = matrix.getHeight();
        BufferedImage image = new BufferedImage(width, height, BufferedImage.TYPE_INT_RGB);
        image.createGraphics();
        for (int x = 0; x < width; x++) {
            for (int y = 0; y < height; y++) {
                image.setRGB(x, y, matrix.get(x, y) ? 0x000000 : 0xFFFFFF); // 黑白
            }
        }
        return image;
    }

    // 将 BufferedImage 转换为 Base64 字符串
    private static String convertToBase64(BufferedImage image, String format) throws IOException {
        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        ImageIO.write(image, format, byteArrayOutputStream);
        byte[] imageBytes = byteArrayOutputStream.toByteArray();
        return Base64.getEncoder().encodeToString(imageBytes);  // 转换为 Base64
    }

    public static void main(String[] args) {
        try {
            String url = "https://www.example.com";  // 要转化为二维码的 URL
            String base64QRCode = generateQRCode(url);
            System.out.println("Base64 QR Code: " + base64QRCode);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

}
