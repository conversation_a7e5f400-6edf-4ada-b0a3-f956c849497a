package com.imile.attendance.util;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import com.google.common.base.Throwables;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.BorderStyle;
import org.apache.poi.ss.usermodel.DataValidation;
import org.apache.poi.ss.usermodel.DataValidationConstraint;
import org.apache.poi.ss.usermodel.DataValidationHelper;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.Name;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.util.CellRangeAddressList;
import org.apache.poi.xssf.usermodel.XSSFCell;
import org.apache.poi.xssf.usermodel.XSSFCellStyle;
import org.apache.poi.xssf.usermodel.XSSFDataValidationHelper;
import org.apache.poi.xssf.usermodel.XSSFFont;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.OutputStream;
import java.lang.reflect.Field;
import java.net.URLEncoder;
import java.util.Collection;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

/**
 * Excel 工具类 支持普通下拉列表和级联下拉列表设置
 *
 * <AUTHOR>
 * @since 2024/7/24
 */
@Slf4j
public class ExcelUtil {

    /**
     * 工作簿
     */
    private final XSSFWorkbook workbook;

    /**
     * 主工作sheet
     */
    private XSSFSheet mainSheet;

    /**
     * 数据
     */
    private Map<String, List<String>> areaList = new LinkedHashMap<>();

    /**
     * 隐藏页名称
     */
    private String hiddenSheetName = "hidden";

    /**
     * 第一行
     */
    private int firstRow = 1;

    /**
     * 一级名称
     */
    private String topName;

    /**
     * 级联集合
     */
    private List<Integer> selectColList;


    public ExcelUtil(XSSFWorkbook book) {
        this.workbook = book;
    }

    public XSSFWorkbook getWorkbook() {
        return workbook;
    }

    /**
     * 单个sheet 初始化
     *
     * @param sheetName sheet名称
     * @return this
     */
    public ExcelUtil createSheet(String sheetName) {
        XSSFSheet sheet = workbook.getSheet(sheetName);
        if (Objects.nonNull(sheet)) {
            this.mainSheet = sheet;
        } else {
            this.mainSheet = workbook.createSheet(sheetName);
        }
        return this;
    }

    /**
     * 多个sheet页初始化
     *
     * @param sheetNames  sheet列表
     * @return this
     */
    public ExcelUtil createMultipleSheet(Collection<String> sheetNames) {
        for (String sheetName : sheetNames) {
            XSSFSheet sheet = workbook.getSheet(sheetName);
            if (Objects.isNull(sheet)) {
                workbook.createSheet(sheetName);
            }
        }
        return this;
    }


    public ExcelUtil createSelectDateList(Map<String, List<String>> areaList) {
        this.areaList = areaList;
        return this;
    }


    public ExcelUtil createTopName(String topName) {
        this.topName = topName;
        return this;
    }

    public ExcelUtil createSelectColList(List<Integer> selectColList) {
        this.selectColList = selectColList;
        return this;
    }


    public ExcelUtil createHiddenName(String hiddenSheetName) {
        this.hiddenSheetName = hiddenSheetName;
        return this;
    }

    public ExcelUtil createFirstRow(int firstRow) {
        this.firstRow = firstRow;
        return this;
    }

    /**
     * 初始化标题行
     *
     * @param heads 标题头列表
     * @return this
     */
    public ExcelUtil createHead(List<String> heads) {
        XSSFRow startRow = this.mainSheet.createRow(0);
        // 创建单元格风格样式
        XSSFCellStyle cellStyle = this.workbook.createCellStyle();
        // 设置样式-单元格边框
        cellStyle.setBorderBottom(BorderStyle.MEDIUM);
        cellStyle.setBorderLeft(BorderStyle.MEDIUM);
        cellStyle.setBorderRight(BorderStyle.MEDIUM);
        cellStyle.setBorderTop(BorderStyle.MEDIUM);
        cellStyle.setAlignment(HorizontalAlignment.CENTER);
        cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        for (int i = 0; i < heads.size(); i++) {
            XSSFCell startCell = startRow.createCell(i);
            startCell.setCellValue(heads.get(i));
            startCell.setCellStyle(cellStyle);
        }
        return this;
    }

    /**
     * 初始化多个sheet页标题行
     *
     * @param headMap sheet页标题行Map
     * @return this
     */
    public ExcelUtil createMultipleHeads(Map<String, List<String>> headMap) {
        for (String sheetName : headMap.keySet()) {
            XSSFSheet sheet = workbook.getSheet(sheetName);
            XSSFRow startRow = sheet.createRow(0);
            startRow.setHeightInPoints(20);

            List<String> heads = headMap.get(sheetName);
            for (int i = 0; i < heads.size(); i++) {
                XSSFCell startCell = startRow.createCell(i);
                startCell.setCellValue(heads.get(i));

                // 创建单元格风格样式
                XSSFCellStyle cellStyle = this.workbook.createCellStyle();
                // 设置样式-单元格边框
                cellStyle.setBorderBottom(BorderStyle.MEDIUM);
                cellStyle.setBorderLeft(BorderStyle.MEDIUM);
                cellStyle.setBorderRight(BorderStyle.MEDIUM);
                cellStyle.setBorderTop(BorderStyle.MEDIUM);
                cellStyle.setAlignment(HorizontalAlignment.CENTER);
                cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
                XSSFFont font = workbook.createFont();
                font.setBold(true);

                if (startCell.getStringCellValue().contains("*")) {
                    font.setColor(IndexedColors.RED.getIndex());
                }
                cellStyle.setFont(font);
                startCell.setCellStyle(cellStyle);
                sheet.setColumnWidth(i, 5000);

            }
        }
        return this;
    }


    /**
     * 设置二级/多级级联下拉框
     */
    public ExcelUtil setCascadeDropDownBox() {
        //获取所有sheet页个数
        int sheetTotal = workbook.getNumberOfSheets();
        //处理下拉数据
        if (areaList != null && areaList.size() != 0) {
            //新建一个sheet页
            XSSFSheet hiddenSheet = workbook.getSheet(hiddenSheetName);
            if (hiddenSheet == null) {
                hiddenSheet = workbook.createSheet(hiddenSheetName);
                sheetTotal++;
            }
            int mainStart = 2;
            int mainEnd = mainStart;
            // 获取数据起始行
            int startRowNum = hiddenSheet.getLastRowNum() + 1;
            Set<String> keySet = areaList.keySet();
            for (String key : keySet) {
                XSSFRow fRow = hiddenSheet.createRow(startRowNum++);
                fRow.createCell(0).setCellValue(key);
                List<String> sons = areaList.get(key);
                for (int i = 1; i <= sons.size(); i++) {
                    fRow.createCell(i).setCellValue(sons.get(i - 1));
                }
                if (Objects.equals(topName, key)) {
                    mainEnd = sons.size();
                }
                // 添加名称管理器
                String range = getRange(1, startRowNum, sons.size());
                Name name = workbook.getName(key);
                if (Objects.isNull(name)) {
                    name = workbook.createName();
                    //key不可重复
                    name.setNameName(key);
                    String formula = hiddenSheetName + "!" + range;
                    name.setRefersToFormula(formula);
                }
            }
            //将数据字典sheet页隐藏掉
            workbook.setSheetHidden(sheetTotal - 1, true);

            // 设置父级下拉
            //获取新sheet页内容
            String mainFormula = hiddenSheetName + "!$A$" + mainStart + ":$A$" + (mainEnd + 1);

            for (int i = 0; i < selectColList.size(); i++) {
                Integer col = selectColList.get(i);
                if (i == 0) {
                    // 设置下拉列表值绑定到主sheet页具体哪个单元格起作用
                    mainSheet.addValidationData(setDataValidation(mainFormula, firstRow, col, col));
                } else {
                    Integer fatherCol = selectColList.get(i - 1);
                    // 设置子级下拉
                    // 当前列为子级下拉框的内容受父级哪一列的影响
                    String indirectFormula = "INDIRECT($" + decimalToTwentyHex(fatherCol + 1) + "" + (firstRow + 1) + ")";
                    mainSheet.addValidationData(setDataValidation(indirectFormula, firstRow, col, col));
                }
            }
        }
        return this;
    }


    /**
     * 计算formula
     *
     * @param offset   偏移量，如果给0，表示从A列开始，1，就是从B列
     * @param rowId    第几行
     * @param colCount 一共多少列
     * @return 如果给入参 1,1,10. 表示从B1-K1。最终返回 $B$1:$K$1
     */
    private String getRange(int offset, int rowId, int colCount) {
        char start = (char) ('A' + offset);
        if (colCount <= 25) {
            char end = (char) (start + colCount - 1);
            return "$" + start + "$" + rowId + ":$" + end + "$" + rowId;
        } else {
            char endPrefix = 'A';
            char endSuffix = 'A';
            // 26-51之间，包括边界（仅两次字母表计算）
            if ((colCount - 25) / 26 == 0 || colCount == 51) {
                // 边界值
                if ((colCount - 25) % 26 == 0) {
                    endSuffix = (char) ('A' + 25);
                } else {
                    endSuffix = (char) ('A' + (colCount - 25) % 26 - 1);
                }
            } else {// 51以上
                if ((colCount - 25) % 26 == 0) {
                    endSuffix = (char) ('A' + 25);
                    endPrefix = (char) (endPrefix + (colCount - 25) / 26 - 1);
                } else {
                    endSuffix = (char) ('A' + (colCount - 25) % 26 - 1);
                    endPrefix = (char) (endPrefix + (colCount - 25) / 26);
                }
            }
            return "$" + start + "$" + rowId + ":$" + endPrefix + endSuffix + "$" + rowId;
        }
    }

    /**
     * 设置下拉数据验证
     *
     * @param strFormula 单元格引用路径
     * @param firstRow   起始行
     * @param firstCol   起始列
     * @param endCol     终止列
     * @return 返回类型 DataValidation
     */
    private DataValidation setDataValidation(String strFormula, int firstRow, int firstCol, int endCol) {
        CellRangeAddressList regions = new CellRangeAddressList(firstRow, 65535, firstCol, endCol);
        DataValidationHelper dvHelper = new XSSFDataValidationHelper(workbook.getSheet(hiddenSheetName));
        DataValidationConstraint formulaListConstraint = dvHelper.createFormulaListConstraint(strFormula);
        return dvHelper.createValidation(formulaListConstraint, regions);
    }

    /**
     * 设置下拉数据验证
     *
     * @param strFormula 单元格引用路径
     * @param firstRow   起始行
     * @param endRow     终止行
     * @param firstCol   起始列
     * @param endCol     终止列
     * @param sheetName  sheet页名称
     * @return DataValidation
     */
    public DataValidation setTypeListDataValidation(String strFormula, int firstRow, int endRow, int firstCol, int endCol, String sheetName) {
        CellRangeAddressList regions = new CellRangeAddressList(firstRow, endRow, firstCol, endCol);
        DataValidationHelper dvHelper = new XSSFDataValidationHelper(workbook.getSheet(StringUtils.isNotEmpty(sheetName) ? sheetName : this.hiddenSheetName));
        DataValidationConstraint formulaListConstraint = dvHelper.createFormulaListConstraint(strFormula);
        return dvHelper.createValidation(formulaListConstraint, regions);
    }


    /**
     * 设置下拉框数据
     *
     * @param typeName 要渲染的sheet名称
     * @param values   下拉框的值
     * @param col      下拉列的下标
     */
    public void setDropDownBox(String typeName, Collection<String> values, Integer col) {
        //获取所有sheet页个数
        int sheetTotal = workbook.getNumberOfSheets();
        //处理下拉数据
        if (!values.isEmpty()) {
            //新建一个隐藏sheet页
            XSSFSheet hiddenSheet = workbook.getSheet(hiddenSheetName);
            if (hiddenSheet == null) {
                hiddenSheet = workbook.createSheet(hiddenSheetName);
                sheetTotal++;
            }
            // 获取数据起始行
            int startRowNum = hiddenSheet.getLastRowNum() + 1;
            int endRowNum = startRowNum;
            //写入下拉数据到新的sheet页中
            for (String value : values) {
                hiddenSheet.createRow(endRowNum++).createCell(0).setCellValue(value);
            }
            //将新建的sheet页隐藏掉
            workbook.setSheetHidden(sheetTotal - 1, true);
            //获取新sheet页内容
            String strFormula = hiddenSheetName + "!$A$" + ++startRowNum + ":$A$" + endRowNum;
            // 设置下拉
            XSSFSheet mainSheet = workbook.getSheet(typeName);
            mainSheet.addValidationData(setTypeListDataValidation(strFormula, 1, 65535, col, col, typeName));
        }
    }

    /**
     * Excel数据写入单元格
     *
     * @param sheetDataMap sheet数据Map
     */
    public void writeData(Map<String, List<?>> sheetDataMap) {
        if (MapUtils.isEmpty(sheetDataMap)) {
            return;
        }
        for (String sheetName : sheetDataMap.keySet()) {
            XSSFSheet sheet = workbook.getSheet(sheetName);
            List<?> excelLines = sheetDataMap.get(sheetName);
            if (CollectionUtils.isEmpty(excelLines)) {
                continue;
            }
            for (int i = 0; i < excelLines.size(); i++) {
                XSSFRow startRow = sheet.createRow(i + 1);
                Object obj = excelLines.get(i);
                Class<?> clazz = obj.getClass();
                Field[] fields = clazz.getDeclaredFields();
                if (fields.length == 0) {
                    continue;
                }
                for (int j = 0; j < fields.length; j++) {
                    try {
                        Field field = fields[j];
                        // 设置私有属性也可以访问
                        field.setAccessible(true);
                        // 获取字段值
                        Object value = field.get(obj);
                        startRow.createCell(j).setCellValue((String) value);
                    } catch (IllegalAccessException e) {
                        log.error("字段属性反射获取值异常： {}", Throwables.getStackTraceAsString(e));
                    }
                }
            }
        }
    }


    /**
     * 十进制转二十六进制
     */
    private String decimalToTwentyHex(int decimalNum) {
        StringBuilder result = new StringBuilder();
        while (decimalNum > 0) {
            int remainder = decimalNum % 26;
            //大写A的ASCII码值为65
            result.append((char) (remainder + 64));
            decimalNum = decimalNum / 26;
        }
        return result.reverse().toString();
    }

    public void writeFile() {
        writeFile(workbook);
    }


    /**
     * Excel写到本地路径
     *
     * @param book 工作簿
     */
    public static void writeFile(Workbook book) {
        try {
            String storeName = System.currentTimeMillis() + ".xlsx";
            String folder = "template//" + DateUtil.format(DateUtil.date(), "yyMMdd") + "/";
            String attachmentFolder = "D://" + File.separator;
            String address = folder + storeName;
            FileUtil.mkdir(attachmentFolder + folder);
            FileOutputStream fileOut = new FileOutputStream(attachmentFolder + address);
            book.write(fileOut);
            fileOut.close();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    /**
     * Excel 写到输出流
     *
     * @param response HttpServletResponse
     * @param fileName 文件名称
     */
    public void writeOutputStream(HttpServletResponse response, String fileName) {
        try {
            // 将工作簿写入输出流
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            workbook.write(outputStream);

            // 设置响应头，告诉浏览器返回的是一个Excel文件
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            response.setHeader("Content-Disposition", "attachment; filename=" + URLEncoder.encode(fileName, "UTF-8"));

            // 将Excel文件写入响应的输出流
            OutputStream outStream = response.getOutputStream();
            outputStream.writeTo(outStream);
            outStream.flush();
            outStream.close();
        } catch (Exception e) {
            log.error("文件输出异常, {}", Throwables.getStackTraceAsString(e));
        }
    }
}