package com.imile.attendance.constants;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR> chen
 * @Date 2025/1/15
 * @Description
 */
public class Constants {

    public static final String EN_US = "en_US";
    public static final String ZH_CN = "zh_CN";

    public static final Long ORG_ID = 10L;

    public static final Integer Y = 1;
    public static final Integer N = 0;

    public static final Integer JANUARY = 1;

    public static final Integer DECEMBER = 12;

    public static class HermesDict {
        public static final String EMPLOYMENT_FORM = "EmploymentForm";
    }

    public static class CacheKey {
        public static final String DICT_CACHE_KEY = "hermes:dict:";
        public static final String OC_CACHE_KEY = "hermes:entOc:";
        public static final String COUNTRY_CACHE_KEY = "hermes:country:";
        public static final String POST_CACHE_KEY = "hrms:post:";
        public static final String USER_CACHE_KEY = "attendance:user:";
        public static final String HRMS_USER_CACHE_KEY = "hrms:user:";
        public static final String RPC_USER_ID_CACHE_KEY = "rpc:userId:";
        public static final String RPC_USER_CODE_CACHE_KEY = "rpc:userCode:";
    }

    public static class TableSchema {
        public static final String attendance = "attendance";
        public static final String hrms = "hrms";
    }
}
