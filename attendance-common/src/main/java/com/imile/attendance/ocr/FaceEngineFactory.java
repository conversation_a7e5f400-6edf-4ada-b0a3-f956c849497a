package com.imile.attendance.ocr;

import com.arcsoft.face.EngineConfiguration;
import com.arcsoft.face.FaceEngine;
import com.arcsoft.face.enums.ErrorInfo;
import com.imile.attendance.enums.FaceErrorCodeEnum;
import com.imile.common.exception.BusinessException;
import com.imile.util.lang.I18nUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.pool2.BasePooledObjectFactory;
import org.apache.commons.pool2.PooledObject;
import org.apache.commons.pool2.impl.DefaultPooledObject;

/**
 * <AUTHOR>
 * @since 2024/7/5
 */
@Slf4j
public class FaceEngineFactory extends BasePooledObjectFactory<FaceEngine> {

    /**
     * 应用的APP_ID
     */
    private String appId;
    /**
     * 应用的SDK_KEY
     */
    private String sdkKey;

    /**
     * 激活码 增值版有
     */
    private String activeKey;

    /**
     * 人脸算法引擎库地址
     */
    private String libPath;

    /**
     * 引擎配置类
     */
    private EngineConfiguration engineConfiguration;


    public FaceEngineFactory(String libPath, String appId, String sdkKey, String activeKey, EngineConfiguration engineConfiguration) {
        this.libPath = libPath;
        this.appId = appId;
        this.sdkKey = sdkKey;
        this.activeKey = activeKey;
        this.engineConfiguration = engineConfiguration;
    }


    @Override
    public FaceEngine create() {

        FaceEngine faceEngine = new FaceEngine(libPath);
        int activeCode = faceEngine.activeOnline(appId, sdkKey);
        if (activeCode != ErrorInfo.MOK.getValue() && activeCode != ErrorInfo.MERR_ASF_ALREADY_ACTIVATED.getValue()) {
            log.error("引擎激活失败: {}", activeCode);
            throw BusinessException.get(FaceErrorCodeEnum.FACE_ENGINE_ACTIVE_FAIL.getCode(),
                    I18nUtils.getMessage(FaceErrorCodeEnum.FACE_ENGINE_ACTIVE_FAIL.getDesc()));
        }

        int initCode = faceEngine.init(engineConfiguration);
        if (initCode != ErrorInfo.MOK.getValue()) {
            log.error("引擎初始化失败: {}", initCode);
            throw BusinessException.get(FaceErrorCodeEnum.FACE_ENGINE_INIT_FAIL.getCode(),
                    I18nUtils.getMessage(FaceErrorCodeEnum.FACE_ENGINE_INIT_FAIL.getDesc()));
        }

        faceEngine.setLivenessParam(0.5f, 0.7f);

        return faceEngine;
    }

    @Override
    public PooledObject<FaceEngine> wrap(FaceEngine faceEngine) {
        return new DefaultPooledObject<>(faceEngine);
    }


    @Override
    public void destroyObject(PooledObject<FaceEngine> p) throws Exception {
        FaceEngine faceEngine = p.getObject();
        //引擎卸载
        faceEngine.unInit();
        super.destroyObject(p);
    }
}
