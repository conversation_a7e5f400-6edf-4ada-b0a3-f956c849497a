<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.imile.attendance.infrastructure.repository.employee.mapper.UserInfoMapper">
    <select id="getUserByCode"
            resultType="com.imile.attendance.infrastructure.repository.employee.dto.UserDTO">
        SELECT ui.id,
               ui.user_code              as userCode,
               ui.user_name              as userName,
               ui.sex,
               ui.birthday,
               ui.country_code           as countryCode,
               ui.phone,
               ui.location_country       as locationCountry,
               ui.location_province      as locationProvince,
               ui.location_city          as locationCity,
               ui.is_global_relocation   as isGlobalRelocation,
               ui.employee_type          as employeeType,
               ui.is_driver              as isDriver,
               ui.is_dtl                 as isDtl,
               ui.is_warehouse_staff     as isWarehouseStaff,
               ui.post_id                as postId,
               ui.dept_id                as deptId,
               ui.oc_id                  as ocId,
               ui.oc_code                as ocCode,
               ui.vendor_Id              as vendorId,
               ui.vendor_code            as vendorCode,
               ui.settlement_center_code as settlementCenterCode,
               ui.email,
               ui.`status`,
               ui.work_status            as workStatus,
               ui.class_nature           as classNature,
               ui.disabled_date          as disabledDate,
               uer.entry_date            as entryDate,
               uer.confirm_date          as confirmDate,
               udr.actual_dimission_date as actualDimissionDate,
               ui.is_delete              as isDelete,
               ui.record_version         as recordVersion,
               ui.create_date            as createDate,
               ui.last_upd_date          as lastUpdDate,
               ui.create_user_code       as createUserCode,
               ui.create_user_name       as createUserName,
               ui.last_upd_user_code     as lastUpdUserCode,
               ui.last_upd_user_name     as lastUpdUserName
        from user_info ui
                 LEFT JOIN user_entry_record uer on uer.user_id = ui.id and uer.is_delete = 0 and uer.entry_status = 'ENTRY'
                 LEFT JOIN user_dimission_record udr on udr.user_id = ui.id  and udr.is_delete = 0 and udr.dimission_status = 'DIMISSION'
        WHERE ui.is_delete = 0
        <if test="userCodeList!=null and userCodeList.size()>0">
            <foreach collection="userCodeList" separator="," open="and ui.user_code in (" close=")" item="userCode">
                #{userCode}
            </foreach>
        </if>
    </select>

    <select id="getUserInfoInformation"
            resultType="com.imile.attendance.infrastructure.repository.employee.dto.UserInformationDTO">
        select ui.id                     as userId,
               ui.user_code              as userCode,
               ui.user_name              as userName,
               ui.user_name_en           as userNameEn,
               ui.work_status            as workStatus,
               ui.status                 as status,
               ui.origin_country         as originCountry,
               ui.location_country       as locationCountry,
               ui.location_province      as locationProvince,
               ui.location_city          as locationCity,
               ui.vendor_code            as vendorCode,
               ui.vendor_name            as vendorName,
               ui.employee_type          as employeeType,
               uer.entry_date            as entryDate,
               udr.actual_dimission_date as dimissionDate,
               ui.dept_id                as deptId,
               ui.post_id                as postId
        from user_info ui
                 left join user_entry_record uer
                           on ui.id = uer.user_id and uer.is_delete = 0 and uer.entry_status = 'ENTRY'
                 left join user_dimission_record udr
                           on ui.id = udr.user_id and udr.is_delete = 0 and udr.dimission_status = 'DIMISSION'
        where ui.id = #{userId}
    </select>


    <select id="selectByAssociateCondition"
            parameterType="com.imile.attendance.infrastructure.repository.employee.query.UserDaoQuery"
            resultType="com.imile.attendance.infrastructure.repository.employee.modle.UserInfoDO">
        SELECT
        ui.id,
        ui.user_code ,
        ui.user_name ,
        ui.user_name_en
        FROM user_info ui
        <include refid="userAssociateCommonConditions"/>

        <include refid="permissionAndNormalCountryConditions"/>

        <if test="isNeedQuerySpecialCountry != null and isNeedQuerySpecialCountry">
            UNION
            (SELECT
            ui.id,
            ui.user_code,
            ui.user_name,
            ui.user_name_en
            FROM user_info ui
            <include refid="userAssociateCommonConditions"/>
            <include refid="permissionAndSpecialCountryConditions"/>
            )
        </if>

        <!-- 全局排序 -->
        ORDER BY
        <choose>
            <when test="keyword != null and keyword != ''">
                user_code ASC, id DESC
            </when>
            <otherwise>
                id DESC
            </otherwise>
        </choose>
        LIMIT 50

    </select>


    <sql id="userAssociateCommonConditions">
        <where>
            ui.is_delete = 0 AND ui.user_code IS NOT NULL
            <if test="userIds != null and userIds.size() > 0">
                AND ui.id IN
                <foreach collection="userIds" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="userCodes != null and userCodes.size() > 0">
                AND ui.user_code IN
                <foreach collection="userCodes" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="isDriver != null">
                AND ui.is_driver = #{isDriver}
            </if>
            <if test="workStatus != null and workStatus != ''">
                AND ui.work_status = #{workStatus}
            </if>
            <if test="status != null and status != ''">
                AND ui.status = #{status}
            </if>
            <if test="classNature != null and classNature != ''">
                AND ui.class_nature = #{classNature}
            </if>
            <if test="keyword != null and keyword != ''">
                AND (
                ui.id = #{keyword}
                OR ui.user_code LIKE CONCAT('%', #{keyword}, '%')
                OR ui.user_name LIKE CONCAT('%', #{keyword}, '%')
                OR ui.user_name_en LIKE CONCAT('%', #{keyword}, '%')
                )
            </if>
            <if test="country != null and country != ''">
                AND ui.location_country = #{country}
            </if>
            <if test="deptIds != null and deptIds.size() > 0">
                and ui.dept_id in
                <foreach collection="deptIds" item="deptId" separator="," open="(" close=")">
                    #{deptId}
                </foreach>
            </if>
        </where>
    </sql>



    <select id="userAttendanceArchive"
            parameterType="com.imile.attendance.infrastructure.repository.employee.query.UserArchiveQuery"
            resultType="com.imile.attendance.infrastructure.repository.employee.dto.UserArchiveDTO">
        SELECT ui.id,
               ui.user_code    AS userCode,
               ui.user_name    AS userName,
               ui.user_name_en AS userNameEn,
               ui.location_country AS locationCountry,
               ui.location_province AS locationProvince,
               ui.location_city AS locationCity,
               ui.employee_type AS employeeType,
               ui.is_driver AS isDriver,
               ui.is_global_relocation AS isGlobalRelocation,
               ui.dept_id AS deptId,
               ui.post_id AS postId,
               ui.status AS status,
               ui.work_status AS workStatus,
               ui.class_nature AS classNature,
               ui.last_upd_date AS lastUpdDate,
               ui.last_upd_user_name AS lastUpdUserName
        FROM user_info ui
        <include refid="userAttendanceArchiveCommonConditions"/>

        <include refid="permissionAndNormalCountryConditions"/>

        <if test="isNeedQuerySpecialCountry!=null and isNeedQuerySpecialCountry == true">
            union

            SELECT ui.id,
            ui.user_code    AS userCode,
            ui.user_name    AS userName,
            ui.user_name_en AS userNameEn,
            ui.location_country AS locationCountry,
            ui.location_province AS locationProvince,
            ui.location_city AS locationCity,
            ui.employee_type AS employeeType,
            ui.is_driver AS isDriver,
            ui.is_global_relocation AS isGlobalRelocation,
            ui.dept_id AS deptId,
            ui.post_id AS postId,
            ui.status AS status,
            ui.work_status AS workStatus,
            ui.class_nature AS classNature,
            ui.last_upd_date AS lastUpdDate,
            ui.last_upd_user_name AS lastUpdUserName
            FROM user_info ui
            <include refid="userAttendanceArchiveCommonConditions"/>

            <include refid="permissionAndSpecialCountryConditions"/>

        </if>
    </select>

    <!-- 员工档案条件片段 -->
    <sql id="userAttendanceArchiveCommonConditions">
        <where>
            ui.is_delete = 0
            AND ui.user_code IS NOT NULL
            <if test="workStatus != null and workStatus != ''">
                AND ui.work_status = #{workStatus}
            </if>
            <if test="classNature != null and classNature != ''">
                AND ui.class_nature = #{classNature}
            </if>
            <if test="isDriver != null">
                AND ui.is_driver = #{isDriver}
            </if>
            <if test="isGlobalRelocation != null">
                AND ui.is_global_relocation = #{isGlobalRelocation}
            </if>
            <if test="locationCountry != null and locationCountry != ''">
                AND ui.location_country = #{locationCountry}
            </if>
            <if test="deptIdList != null and deptIdList.size() > 0">
                and ui.dept_id in
                <foreach collection="deptIdList" item="deptId" separator="," open="(" close=")">
                    #{deptId}
                </foreach>
            </if>
            <if test="postIdList!=null and postIdList.size()>0">
                <foreach collection="postIdList" separator="," open="and ui.post_id in (" close=")" item="item">
                    #{item}
                </foreach>
            </if>

            <if test="userIdList != null and userIdList.size() > 0">
                <choose>
                    <!-- 当ID数量较少时使用普通IN查询 -->
                    <when test="userIdList.size() &lt;= 1000">
                        <foreach collection="userIdList" separator="," open="and ui.id in (" close=")" item="userId">
                            #{userId}
                        </foreach>
                    </when>
                    <!-- 当ID数量较多时分批处理 -->
                    <otherwise>
                        and (
                        <foreach collection="batchUserIdList" separator=" or " item="batch">
                            ui.id in
                            <foreach collection="batch" open="(" separator="," close=")" item="userId">
                                #{userId}
                            </foreach>
                        </foreach>
                        )
                    </otherwise>
                </choose>
            </if>
        </where>
    </sql>

    <!-- 权限和用工类型条件片段 -->
    <sql id="permissionAndNormalCountryConditions">
        <if test="normalDeptList!=null and normalDeptList.size()>0">
            and (ui.dept_id in
            <foreach collection="normalDeptList" item="deptId" index="i" open="(" close=")" separator=",">
                #{deptId}
            </foreach>
            <if test="normalCountryList!=null and normalCountryList.size()>0">
                or ui.location_country in
                <foreach collection="normalCountryList" item="locationCountry" index="i" open="(" close=")"
                         separator=",">
                    #{locationCountry}
                </foreach>
            </if>
            )
        </if>

        <if test="normalDeptList==null or normalDeptList.size()==0">
            <if test="normalCountryList!=null and normalCountryList.size()>0">
                and ui.location_country in
                <foreach collection="normalCountryList" item="locationCountry" index="i" open="(" close=")"
                         separator=",">
                    #{locationCountry}
                </foreach>
            </if>
        </if>

        <if test="normalEmployeeTypeList != null and normalEmployeeTypeList.size() > 0">
            <foreach collection="normalEmployeeTypeList" separator="," open="and ui.employee_type in (" close=")"
                     item="normalEmployeeType">
                #{normalEmployeeType}
            </foreach>
        </if>
    </sql>

    <sql id="permissionAndSpecialCountryConditions">
        <if test="specialDeptList!=null and specialDeptList.size()>0">
            and (ui.dept_id in
            <foreach collection="specialDeptList" item="deptId" index="i" open="(" close=")" separator=",">
                #{deptId}
            </foreach>
            <if test="specialCountryList!=null and specialCountryList.size()>0">
                or ui.location_country in
                <foreach collection="specialCountryList" item="locationCountry" index="i" open="(" close=")"
                         separator=",">
                    #{locationCountry}
                </foreach>
            </if>
            )
        </if>

        <if test="specialDeptList==null or specialDeptList.size()==0">
            <if test="specialCountryList!=null and specialCountryList.size()>0">
                and ui.location_country in
                <foreach collection="specialCountryList" item="locationCountry" index="i" open="(" close=")"
                         separator=",">
                    #{locationCountry}
                </foreach>
            </if>
        </if>

        <if test="specialEmployeeTypeList != null and specialEmployeeTypeList.size() > 0">
            <foreach collection="specialEmployeeTypeList" separator="," open="and ui.employee_type in (" close=")"
                     item="specialEmployeeType">
                #{specialEmployeeType}
            </foreach>
        </if>
    </sql>

    <select id="selectUserLocationCountry" resultType="java.lang.String">
        select distinct location_country
        from user_info
        where is_delete = 0
        and location_country is not null
    </select>

</mapper>