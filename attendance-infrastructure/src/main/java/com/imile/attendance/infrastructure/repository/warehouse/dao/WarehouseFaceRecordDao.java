package com.imile.attendance.infrastructure.repository.warehouse.dao;

import com.baomidou.mybatisplus.extension.service.IService;
import com.imile.attendance.infrastructure.repository.warehouse.model.WarehouseFaceRecordDO;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR> 4.0 sonnet
 * @Date 2025-07-09
 * @Description 仓内人脸识别记录表DAO接口
 */
public interface WarehouseFaceRecordDao extends IService<WarehouseFaceRecordDO> {
    WarehouseFaceRecordDO selectLastOne(Long userId, Date attendanceTime, Integer faceRecordStatus);

    void updateStatusByUserId(Long userId, Date warehouseDate, Integer faceRecordStatus);

    List<WarehouseFaceRecordDO> getByIdList(List<Long> faceRecordIdList);
}
