package com.imile.attendance.infrastructure.repository.abnormal.adapter;

import com.google.common.collect.Lists;
import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.hrms.RpcHrWarehouseClient;
import com.imile.attendance.infrastructure.config.EnableNewAttendanceConfig;
import com.imile.attendance.infrastructure.repository.abnormal.adapter.mapstruct.WarehouseDetailSnapshotMapstruct;
import com.imile.attendance.infrastructure.repository.migration.dao.MappingPunchClassConfigDao;
import com.imile.attendance.infrastructure.repository.migration.model.MappingPunchClassConfigDO;
import com.imile.attendance.infrastructure.repository.warehouse.dao.WarehouseDetailSnapshotDao;
import com.imile.attendance.infrastructure.repository.warehouse.model.WarehouseDetailSnapshotDO;
import com.imile.hrms.api.warehouse.dto.HrmsWarehouseDetailSnapshotDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.Executor;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2025/7/25
 */
@Slf4j
@Component
public class WarehouseDetailSnapshotAdapter {

    @Resource
    private EnableNewAttendanceConfig enableNewAttendanceConfig;
    @Resource
    private WarehouseDetailSnapshotDao warehouseDetailSnapshotDao;
    @Resource
    private RpcHrWarehouseClient hrWarehouseClient;
    @Resource
    private MappingPunchClassConfigDao mappingPunchClassConfigDao;
    @Resource
    private Executor bizTaskThreadPool;

    public Boolean isDoubleWriteMode() {
        return enableNewAttendanceConfig.getWarehouseDoubleWriteEnabled();
    }

    //=====================dao层适配===============================


    public void saveOrUpdateBatch(List<WarehouseDetailSnapshotDO> newList) {
        warehouseDetailSnapshotDao.saveOrUpdateBatch(newList);

        if (isDoubleWriteMode()) {
            bizTaskThreadPool.execute(() -> {
                List<HrmsWarehouseDetailSnapshotDTO> hrmsWarehouseDetailSnapshotDTOList = convertHrmsWarehouseDetailDTOList(newList);
                List<List<HrmsWarehouseDetailSnapshotDTO>> partitionList = Lists.partition(hrmsWarehouseDetailSnapshotDTOList, 200);
                partitionList.forEach(partition -> hrWarehouseClient.warehouseSnapshotBatch(partition));
            });
        }
    }

    public List<HrmsWarehouseDetailSnapshotDTO> convertHrmsWarehouseDetailDTOList(List<WarehouseDetailSnapshotDO> warehouseDetailSnapshotDOList) {
        List<Long> punchClassIdList = warehouseDetailSnapshotDOList.stream().map(WarehouseDetailSnapshotDO::getClassesId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        Map<Long, Long> hrPunchClassIdMap = mappingPunchClassConfigDao.listByPunchClassConfigIds(punchClassIdList).stream()
                .collect(Collectors.toMap(MappingPunchClassConfigDO::getPunchClassConfigId, MappingPunchClassConfigDO::getHrPunchClassId, (v1, v2) -> v1));

        List<HrmsWarehouseDetailSnapshotDTO> oldList = WarehouseDetailSnapshotMapstruct.INSTANCE.mapToOldList(warehouseDetailSnapshotDOList);
        oldList.forEach(old -> {
            old.setFromNewSystem(BusinessConstant.Y);
            if (Objects.nonNull(old.getClassesId()) && old.getClassesId() > 0) {
                old.setClassesId(hrPunchClassIdMap.getOrDefault(old.getClassesId(), old.getClassesId()));
            }
        });
        return oldList;
    }

}