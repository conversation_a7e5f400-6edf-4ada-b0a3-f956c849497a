package com.imile.attendance.infrastructure.repository.warehouse.dao;


import com.baomidou.mybatisplus.extension.service.IService;
import com.imile.attendance.infrastructure.repository.warehouse.model.WarehouseDetailSnapshotDO;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * 仓内考勤初始表表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024/11/26
 */
public interface WarehouseDetailSnapshotDao extends IService<WarehouseDetailSnapshotDO> {

    List<WarehouseDetailSnapshotDO> selectByWarehouseDateAndUserIds(Date warehouseDate, List<Long> userIdList);

}
