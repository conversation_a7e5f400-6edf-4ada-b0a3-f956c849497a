package com.imile.attendance.infrastructure.repository.warehouse.dao.impl;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.imile.attendance.infrastructure.repository.warehouse.dao.WarehouseDetailSnapshotDao;
import com.imile.attendance.infrastructure.repository.warehouse.mapper.WarehouseDetailSnapshotMapper;
import com.imile.attendance.infrastructure.repository.warehouse.model.WarehouseDetailSnapshotDO;
import com.imile.common.enums.IsDeleteEnum;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Objects;


/**
 * <p>
 * 仓内考勤初始表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-04
 */
@Service
public class WarehouseDetailDaoSnapshotImpl extends ServiceImpl<WarehouseDetailSnapshotMapper, WarehouseDetailSnapshotDO> implements WarehouseDetailSnapshotDao {


    @Override
    public List<WarehouseDetailSnapshotDO> selectByWarehouseDateAndUserIds(Date warehouseDate, List<Long> userIdList) {
        if (Objects.isNull(warehouseDate) || CollectionUtils.isEmpty(userIdList)){
            return Collections.emptyList();
        }
        LambdaQueryWrapper<WarehouseDetailSnapshotDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(WarehouseDetailSnapshotDO::getWarehouseDate,warehouseDate);
        queryWrapper.in(WarehouseDetailSnapshotDO::getUserId, userIdList);
        queryWrapper.eq(WarehouseDetailSnapshotDO::getIsDelete, IsDeleteEnum.NO.getCode());
        return super.list(queryWrapper);
    }
}
