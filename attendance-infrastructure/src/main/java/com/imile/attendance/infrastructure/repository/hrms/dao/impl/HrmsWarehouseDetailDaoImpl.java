package com.imile.attendance.infrastructure.repository.hrms.dao.impl;


import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.constants.Constants;
import com.imile.attendance.enums.WhetherEnum;
import com.imile.attendance.infrastructure.repository.hrms.dao.HrmsWarehouseDetailDao;
import com.imile.attendance.infrastructure.repository.hrms.mapper.HrmsWarehouseDetailMapper;
import com.imile.attendance.infrastructure.repository.hrms.modle.HrmsWarehouseDetailDO;
import com.imile.attendance.infrastructure.repository.warehouse.query.WarehouseDetailQuery;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * <p>
 * 仓内统计表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-04
 */
@DS(Constants.TableSchema.hrms)
@Service
public class HrmsWarehouseDetailDaoImpl extends ServiceImpl<HrmsWarehouseDetailMapper, HrmsWarehouseDetailDO> implements HrmsWarehouseDetailDao {

    @Override
    public List<HrmsWarehouseDetailDO> selectPage(WarehouseDetailQuery param) {
        if (Objects.isNull(param)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<HrmsWarehouseDetailDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(CollectionUtils.isNotEmpty(param.getIds()), HrmsWarehouseDetailDO::getId, param.getIds());
        queryWrapper.ge(Objects.nonNull(param.getStartTime()), HrmsWarehouseDetailDO::getWarehouseDate, param.getStartTime());
        queryWrapper.le(Objects.nonNull(param.getEndTime()), HrmsWarehouseDetailDO::getWarehouseDate, param.getEndTime());
        queryWrapper.in(CollectionUtils.isNotEmpty(param.getUserCodeList()), HrmsWarehouseDetailDO::getUserCode, param.getUserCodeList());
        queryWrapper.in(CollectionUtils.isNotEmpty(param.getOcIdList()), HrmsWarehouseDetailDO::getOcId, param.getOcIdList());
        queryWrapper.in(CollectionUtils.isNotEmpty(param.getCountryList()), HrmsWarehouseDetailDO::getCountry, param.getCountryList());
        queryWrapper.eq(HrmsWarehouseDetailDO::getIsDelete, WhetherEnum.NO.getKey());
        queryWrapper.gt(Objects.nonNull(param.getLastId()),HrmsWarehouseDetailDO::getId, param.getLastId());
        queryWrapper.eq(HrmsWarehouseDetailDO::getFromNewSystem, BusinessConstant.N);
        queryWrapper.orderByAsc(HrmsWarehouseDetailDO::getId);
        queryWrapper.last("limit 1000");
        return super.list(queryWrapper);
    }

}
