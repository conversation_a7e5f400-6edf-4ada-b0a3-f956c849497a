package com.imile.attendance.infrastructure.repository.warehouse.dao.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.imile.attendance.infrastructure.repository.warehouse.dao.UserFaceMarkRecordDao;
import com.imile.attendance.infrastructure.repository.warehouse.mapper.UserFaceMarkRecordMapper;
import com.imile.attendance.infrastructure.repository.warehouse.model.UserFaceMarkRecordDO;
import com.imile.common.enums.IsDeleteEnum;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;


/**
 * <AUTHOR>
 * @since 2024/7/5
 */
@Service
public class UserFaceMarkRecordDaoImpl extends ServiceImpl<UserFaceMarkRecordMapper, UserFaceMarkRecordDO> implements UserFaceMarkRecordDao {



    @Override
    public List<UserFaceMarkRecordDO> getByUserCode(String userCode) {
        if (StringUtils.isBlank(userCode)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<UserFaceMarkRecordDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UserFaceMarkRecordDO::getIsDelete, IsDeleteEnum.NO.getCode())
                .and(wrap -> wrap.eq(UserFaceMarkRecordDO::getUserCode, userCode)
                        .or()
                        .eq(UserFaceMarkRecordDO::getMarkUserCode, userCode));
        return super.list(queryWrapper);
    }
}
