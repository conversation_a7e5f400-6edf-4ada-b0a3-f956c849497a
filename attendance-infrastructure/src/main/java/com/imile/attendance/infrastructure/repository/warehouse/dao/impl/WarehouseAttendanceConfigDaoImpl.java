package com.imile.attendance.infrastructure.repository.warehouse.dao.impl;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.infrastructure.repository.warehouse.dao.WarehouseAttendanceConfigDao;
import com.imile.attendance.infrastructure.repository.warehouse.mapper.WarehouseAttendanceConfigMapper;
import com.imile.attendance.infrastructure.repository.warehouse.model.WarehouseAttendanceConfigDO;
import com.imile.attendance.infrastructure.repository.warehouse.param.WarehouseAttendanceConfigParam;
import com.imile.common.enums.IsDeleteEnum;
import com.imile.common.enums.StatusEnum;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Objects;


/**
 * <p>
 * 仓内考勤规则表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-30
 */
@Service
public class WarehouseAttendanceConfigDaoImpl extends ServiceImpl<WarehouseAttendanceConfigMapper, WarehouseAttendanceConfigDO> implements WarehouseAttendanceConfigDao {


    @Override
    public List<WarehouseAttendanceConfigDO> list(WarehouseAttendanceConfigParam param) {
        LambdaQueryWrapper<WarehouseAttendanceConfigDO> queryWrapper = Wrappers.lambdaQuery();
        if (StringUtils.isNotEmpty(param.getCountry())) {
            queryWrapper.eq(WarehouseAttendanceConfigDO::getCountry, param.getCountry());
        }

        if (StringUtils.isNotEmpty(param.getAttendanceConfigName())) {
            queryWrapper.like(WarehouseAttendanceConfigDO::getAttendanceConfigName, param.getAttendanceConfigName());
        }

        if (CollectionUtils.isNotEmpty(param.getDeptIds())) {
            queryWrapper.and(i -> param.getDeptIds().forEach(deptId -> i.like(WarehouseAttendanceConfigDO::getDeptIds, deptId).or()));
        }

        queryWrapper.eq(WarehouseAttendanceConfigDO::getIsDelete, IsDeleteEnum.NO.getCode());
        queryWrapper.eq(WarehouseAttendanceConfigDO::getIsLatest, BusinessConstant.Y);
        queryWrapper.orderByDesc(WarehouseAttendanceConfigDO::getCreateDate);
        return list(queryWrapper);
    }

    @Override
    public WarehouseAttendanceConfigDO selectById(Long id) {
        if (Objects.isNull(id)) {
            return null;
        }
        LambdaQueryWrapper<WarehouseAttendanceConfigDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(WarehouseAttendanceConfigDO::getId, id);
        queryWrapper.eq(WarehouseAttendanceConfigDO::getIsDelete, IsDeleteEnum.NO.getCode());
        queryWrapper.eq(WarehouseAttendanceConfigDO::getIsLatest, BusinessConstant.Y);
        queryWrapper.eq(WarehouseAttendanceConfigDO::getStatus, StatusEnum.ACTIVE.getCode());
        queryWrapper.orderByAsc(WarehouseAttendanceConfigDO::getCreateDate);
        return getOne(queryWrapper);
    }


    @Override
    public WarehouseAttendanceConfigDO selectLatestById(Long id) {
        if (Objects.isNull(id)) {
            return null;
        }
        LambdaQueryWrapper<WarehouseAttendanceConfigDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(WarehouseAttendanceConfigDO::getId, id);
        queryWrapper.eq(WarehouseAttendanceConfigDO::getIsDelete, IsDeleteEnum.NO.getCode());
        queryWrapper.eq(WarehouseAttendanceConfigDO::getIsLatest, BusinessConstant.Y);
        return getOne(queryWrapper);
    }

    @Override
    public WarehouseAttendanceConfigDO selectActiveById(Long id) {
        if (Objects.isNull(id)) {
            return null;
        }
        LambdaQueryWrapper<WarehouseAttendanceConfigDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(WarehouseAttendanceConfigDO::getId, id);
        queryWrapper.eq(WarehouseAttendanceConfigDO::getIsDelete, IsDeleteEnum.NO.getCode());
        queryWrapper.eq(WarehouseAttendanceConfigDO::getStatus, StatusEnum.ACTIVE.getCode());
        return getOne(queryWrapper);
    }


    @Override
    public List<WarehouseAttendanceConfigDO> selectActiveByIds(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<WarehouseAttendanceConfigDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(WarehouseAttendanceConfigDO::getId, ids);
        queryWrapper.eq(WarehouseAttendanceConfigDO::getIsDelete, IsDeleteEnum.NO.getCode());
        queryWrapper.eq(WarehouseAttendanceConfigDO::getStatus, StatusEnum.ACTIVE.getCode());
        return list(queryWrapper);
    }

    @Override
    public List<WarehouseAttendanceConfigDO> selectByCountry(String country) {
        if (StringUtils.isEmpty(country)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<WarehouseAttendanceConfigDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(WarehouseAttendanceConfigDO::getCountry, country);
        queryWrapper.eq(WarehouseAttendanceConfigDO::getIsDelete, IsDeleteEnum.NO.getCode());
        queryWrapper.eq(WarehouseAttendanceConfigDO::getIsLatest, BusinessConstant.Y);
        return list(queryWrapper);
    }

    @Override
    public WarehouseAttendanceConfigDO selectByDeptId(Long deptId) {
        LambdaQueryWrapper<WarehouseAttendanceConfigDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.apply("FIND_IN_SET('" + deptId.toString() + "', dept_ids)");
        queryWrapper.eq(WarehouseAttendanceConfigDO::getIsDelete, IsDeleteEnum.NO.getCode());
        queryWrapper.eq(WarehouseAttendanceConfigDO::getIsLatest, BusinessConstant.Y);
        queryWrapper.eq(WarehouseAttendanceConfigDO::getStatus, StatusEnum.ACTIVE.getCode());
        queryWrapper.last("limit 1");
        return getOne(queryWrapper);
    }

    @Override
    public List<WarehouseAttendanceConfigDO> selectByDateRange(Date date, Long deptId) {
        if (Objects.isNull(date) || Objects.isNull(deptId)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<WarehouseAttendanceConfigDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.le(WarehouseAttendanceConfigDO::getEffectTime, date);
        queryWrapper.ge(WarehouseAttendanceConfigDO::getExpireTime, date);
        queryWrapper.apply("FIND_IN_SET('" + deptId.toString() + "', dept_ids)");
        queryWrapper.eq(WarehouseAttendanceConfigDO::getIsDelete, IsDeleteEnum.NO.getCode());
        queryWrapper.eq(WarehouseAttendanceConfigDO::getStatus, StatusEnum.ACTIVE.getCode());
        return list(queryWrapper);
    }
}
