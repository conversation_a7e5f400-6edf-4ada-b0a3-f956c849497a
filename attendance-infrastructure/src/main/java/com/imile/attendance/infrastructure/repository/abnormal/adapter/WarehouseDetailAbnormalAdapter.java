package com.imile.attendance.infrastructure.repository.abnormal.adapter;

import com.google.common.collect.Lists;
import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.hrms.RpcHrWarehouseClient;
import com.imile.attendance.infrastructure.config.EnableNewAttendanceConfig;
import com.imile.attendance.infrastructure.repository.warehouse.dao.WarehouseDetailAbnormalDao;
import com.imile.attendance.infrastructure.repository.warehouse.model.WarehouseDetailAbnormalDO;
import com.imile.common.enums.IsDeleteEnum;
import com.imile.hrms.api.warehouse.dto.HrmsWarehouseDetailAbnormalDTO;
import com.imile.util.BeanUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.Executor;

/**
 * <AUTHOR>
 * @since 2025/7/25
 */
@Slf4j
@Component
public class WarehouseDetailAbnormalAdapter {

    @Resource
    private EnableNewAttendanceConfig enableNewAttendanceConfig;
    @Resource
    private WarehouseDetailAbnormalDao warehouseDetailAbnormalDao;
    @Resource
    private RpcHrWarehouseClient hrWarehouseClient;
    @Resource
    private Executor bizTaskThreadPool;

    public Boolean isDoubleWriteMode() {
        return enableNewAttendanceConfig.getWarehouseDoubleWriteEnabled();
    }

    //=====================dao层适配===============================

    public void saveOrUpdate(WarehouseDetailAbnormalDO newRecord) {
        warehouseDetailAbnormalDao.saveOrUpdate(newRecord);

        if (isDoubleWriteMode()) {
            bizTaskThreadPool.execute(() -> {
                HrmsWarehouseDetailAbnormalDTO hrmsWarehouseDetailAbnormalDTO = BeanUtils.convert(newRecord, HrmsWarehouseDetailAbnormalDTO.class);
                hrmsWarehouseDetailAbnormalDTO.setFromNewSystem(BusinessConstant.Y);
                hrWarehouseClient.warehouseAbnormalBatch(Collections.singletonList(hrmsWarehouseDetailAbnormalDTO));
            });
        }

    }


    public void saveOrUpdateBatch(List<WarehouseDetailAbnormalDO> newList) {
        warehouseDetailAbnormalDao.saveOrUpdateBatch(newList);

        if (isDoubleWriteMode()) {
            bizTaskThreadPool.execute(() -> {
                List<HrmsWarehouseDetailAbnormalDTO> hrmsWarehouseDetailAbnormalDTOList = BeanUtils.convert(HrmsWarehouseDetailAbnormalDTO.class, newList);
                hrmsWarehouseDetailAbnormalDTOList.forEach(item -> item.setFromNewSystem(BusinessConstant.Y));
                List<List<HrmsWarehouseDetailAbnormalDTO>> partitionList = Lists.partition(hrmsWarehouseDetailAbnormalDTOList, 200);
                partitionList.forEach(partition -> hrWarehouseClient.warehouseAbnormalBatch(partition));
            });
        }
    }


    public void removeByWarehouseDetailId(Long warehouseDetailId) {
        List<WarehouseDetailAbnormalDO> warehouseDetailAbnormalDOList = warehouseDetailAbnormalDao.selectByWarehouseDetailIds(Collections.singletonList(warehouseDetailId));
        warehouseDetailAbnormalDao.removeByWarehouseDetailId(warehouseDetailId);

        if (isDoubleWriteMode()) {
            bizTaskThreadPool.execute(() -> {
                List<HrmsWarehouseDetailAbnormalDTO> hrmsWarehouseDetailAbnormalDTOList = BeanUtils.convert(HrmsWarehouseDetailAbnormalDTO.class, warehouseDetailAbnormalDOList);
                hrmsWarehouseDetailAbnormalDTOList.forEach(item -> {
                    item.setFromNewSystem(BusinessConstant.Y);
                    item.setIsDelete(IsDeleteEnum.YES.getCode());
                });
                hrWarehouseClient.warehouseAbnormalBatch(hrmsWarehouseDetailAbnormalDTOList);
            });
        }

    }

    public void deleteByWarehouseDetailId(Long warehouseDetailId) {
        List<WarehouseDetailAbnormalDO> warehouseDetailAbnormalDOList = warehouseDetailAbnormalDao.selectByWarehouseDetailIds(Collections.singletonList(warehouseDetailId));
        warehouseDetailAbnormalDao.deleteByWarehouseDetailId(warehouseDetailId);

        if (isDoubleWriteMode()) {
            bizTaskThreadPool.execute(() -> {
                List<HrmsWarehouseDetailAbnormalDTO> hrmsWarehouseDetailAbnormalDTOList = BeanUtils.convert(HrmsWarehouseDetailAbnormalDTO.class, warehouseDetailAbnormalDOList);
                hrmsWarehouseDetailAbnormalDTOList.forEach(item -> {
                    item.setFromNewSystem(BusinessConstant.Y);
                    item.setIsDelete(IsDeleteEnum.YES.getCode());
                });
                hrWarehouseClient.warehouseAbnormalBatch(hrmsWarehouseDetailAbnormalDTOList);
            });
        }

    }

}