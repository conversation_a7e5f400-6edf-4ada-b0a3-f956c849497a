package com.imile.attendance.infrastructure.repository.hrms.modle;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.imile.attendance.base.BaseDO;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 用户人脸打标记录表
 *
 * <AUTHOR>
 * @since 2025/7/16
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("user_face_mark_record")
public class HrmsUserFaceMarkRecordDO extends BaseDO {

    private static final long serialVersionUID = 7508162473413356080L;
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 用户编码
     */
    @TableField("user_code")
    private String userCode;

    /**
     * 打标用户编码
     */
    @TableField("mark_user_code")
    private String markUserCode;
}
