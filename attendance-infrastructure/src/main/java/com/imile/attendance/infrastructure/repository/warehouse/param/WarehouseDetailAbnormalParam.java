package com.imile.attendance.infrastructure.repository.warehouse.param;

import lombok.Data;

import java.util.Date;
import java.util.List;


/**
 * <AUTHOR>
 * @since 2024/9/18
 */
@Data
public class WarehouseDetailAbnormalParam {
    /**
     * id
     */
    private Long id;

    /**
     * ids
     */
    private List<Long> ids;

    /**
     * 异常ID
     */
    private Long abnormalId;

    /**
     * 异常ID集合
     */
    private List<Long> abnormalIdList;

    /**
     * 网点id
     */
    private Long ocId;

    /**
     * 工作网点, 多选
     */
    private List<Long> ocIdList;

    /**
     * 供应商id
     */
    private Long vendorId;

    /**
     * 工作供应商, 多选
     */
    private List<Long> vendorIdList;

    /**
     * 用工形式
     */
    private String employmentForm;

    /**
     * 国家
     */
    private String country;

    /**
     * 开始时间
     */
    private Date startDate;

    /**
     * 结束时间
     */
    private Date endDate;
}
