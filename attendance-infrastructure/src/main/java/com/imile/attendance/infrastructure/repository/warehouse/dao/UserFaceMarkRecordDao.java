package com.imile.attendance.infrastructure.repository.warehouse.dao;


import com.baomidou.mybatisplus.extension.service.IService;
import com.imile.attendance.infrastructure.repository.warehouse.model.UserFaceMarkRecordDO;

import java.util.List;


/**
 * <AUTHOR>
 * @since 2025/7/16
 */
public interface UserFaceMarkRecordDao extends IService<UserFaceMarkRecordDO> {

    List<UserFaceMarkRecordDO> getByUserCode(String userCode);

}
