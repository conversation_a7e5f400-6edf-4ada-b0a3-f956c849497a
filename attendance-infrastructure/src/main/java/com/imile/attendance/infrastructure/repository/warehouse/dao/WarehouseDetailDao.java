package com.imile.attendance.infrastructure.repository.warehouse.dao;


import com.baomidou.mybatisplus.extension.service.IService;
import com.imile.attendance.infrastructure.repository.warehouse.model.WarehouseDetailDO;
import com.imile.attendance.infrastructure.repository.warehouse.query.WarehouseDetailQuery;

import java.util.Collection;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 仓内统计表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-04
 */
public interface WarehouseDetailDao extends IService<WarehouseDetailDO> {

    WarehouseDetailDO selectById(Long id);

    List<WarehouseDetailDO> selectByIds(Collection<Long> idList);

    List<WarehouseDetailDO> selectPage(WarehouseDetailQuery warehouseDetailQuery);

    List<WarehouseDetailDO> selectPcsPage(WarehouseDetailQuery warehouseDetailQuery);

    List<WarehouseDetailDO> selectByWarehouseDateAndUserId(Date warehouseDate, Long userId);

    List<WarehouseDetailDO> selectByCondition(WarehouseDetailQuery warehouseDetailQuery);

    List<WarehouseDetailDO> selectClassesByCondition(WarehouseDetailQuery warehouseDetailQuery);

    List<WarehouseDetailDO> selectDataStatisticsPage(WarehouseDetailQuery warehouseDetailQuery);

    List<WarehouseDetailDO> selectJoinAbnormalList(WarehouseDetailQuery warehouseDetailQuery);

    List<WarehouseDetailDO> selectJoinRecordList(WarehouseDetailQuery warehouseDetailQuery);

    List<WarehouseDetailDO> selectNoBindShiftByCondition(WarehouseDetailQuery warehouseDetailQuery);

    Integer noBindShiftCount(List<Long> ocIds);

    List<WarehouseDetailDO> selectLatestByUserIds(List<Long> userIdList);

}
