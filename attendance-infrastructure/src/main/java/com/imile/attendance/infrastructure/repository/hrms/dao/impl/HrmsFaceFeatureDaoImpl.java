package com.imile.attendance.infrastructure.repository.hrms.dao.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.imile.attendance.constants.Constants;
import com.imile.attendance.infrastructure.repository.hrms.dao.HrmsFaceFeatureDao;
import com.imile.attendance.infrastructure.repository.hrms.mapper.HrmsFaceFeatureMapper;
import com.imile.attendance.infrastructure.repository.hrms.modle.HrmsFaceFeatureDO;
import com.imile.attendance.infrastructure.repository.warehouse.query.FaceFeatureQuery;
import com.imile.common.enums.IsDeleteEnum;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;


/**
 * <AUTHOR>
 * @since 2024/7/5
 */
@DS(Constants.TableSchema.hrms)
@Service
public class HrmsFaceFeatureDaoImpl extends ServiceImpl<HrmsFaceFeatureMapper, HrmsFaceFeatureDO> implements HrmsFaceFeatureDao {


    @Override
    public List<HrmsFaceFeatureDO> selectByCondition(FaceFeatureQuery query) {
        LambdaQueryWrapper<HrmsFaceFeatureDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(HrmsFaceFeatureDO::getIsDelete, IsDeleteEnum.NO.getCode());
        queryWrapper.gt(Objects.nonNull(query.getLastId()), HrmsFaceFeatureDO::getId, query.getLastId());
        queryWrapper.orderByAsc(HrmsFaceFeatureDO::getId);
        queryWrapper.last("limit 1000");
        return super.list(queryWrapper);
    }
}
