package com.imile.attendance.infrastructure.logRecord.enums;

import lombok.Getter;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR> chen
 * @Date 2025/3/31
 * @Description
 */
@Getter
public enum OperationTypeEnum {

    CALENDAR_ADD("<PERSON><PERSON><PERSON><PERSON>_ADD", "新增日历", OperationCodeEnum.CALENDAR_MANAGE),
    CALENDAR_UPDATE("CALENDAR_UPDATE", "修改日历", OperationCodeEnum.CALENDAR_MANAGE),
    CALENDAR_ENABLE("CALENDAR_ENABLE", "启用日历", OperationCodeEnum.CALENDAR_MANAGE),
    CALENDAR_DISABLE("<PERSON><PERSON><PERSON><PERSON>_DISABLE", "停用日历", OperationCodeEnum.CALENDAR_MANAGE),
    SHIFT_ADD("SHIFT_ADD", "新增班次", OperationCodeEnum.SHIFT_MANAGE),

    PUNCH_CONFIG_ADD("PUNCH_CONFIG_ADD", "新增打卡规则", OperationCodeEnum.PUNCH_CONFIG_MANAGE),
    PUNCH_CONFIG_UPDATE("PUNCH_CONFIG_UPDATE", "更新打卡规则", OperationCodeEnum.PUNCH_CONFIG_MANAGE),
    PUNCH_CONFIG_ACTIVE("PUNCH_CONFIG_ACTIVE", "启用打卡规则", OperationCodeEnum.PUNCH_CONFIG_MANAGE),
    PUNCH_CONFIG_DISABLED("PUNCH_CONFIG_DISABLED", "停用打卡规则", OperationCodeEnum.PUNCH_CONFIG_MANAGE),
    PUNCH_CONFIG_EXPORT("PUNCH_CONFIG_EXPORT","导出打卡规则",OperationCodeEnum.PUNCH_CONFIG_MANAGE),

    PUNCH_CLASS_CONFIG_ADD("PUNCH_CLASS_CONFIG_ADD", "新增班次", OperationCodeEnum.SHIFT_MANAGE),
    PUNCH_CLASS_CONFIG_UPDATE("PUNCH_CLASS_CONFIG_UPDATE","更新班次",OperationCodeEnum.SHIFT_MANAGE),
    PUNCH_CLASS_CONFIG_ACTIVE("PUNCH_CLASS_CONFIG_ACTIVE","启用班次",OperationCodeEnum.SHIFT_MANAGE),
    PUNCH_CLASS_CONFIG_DISABLED("PUNCH_CLASS_CONFIG_DISABLED","停用班次",OperationCodeEnum.SHIFT_MANAGE),
    PUNCH_CLASS_CONFIG_EXPORT("PUNCH_CLASS_CONFIG_EXPORT","导出班次规则",OperationCodeEnum.SHIFT_MANAGE),
    CLASS_NATURE_SWITCH("CLASS_NATURE_SWITCH","班次性质切换",OperationCodeEnum.SHIFT_MANAGE),

    REISSUE_CARD_CONFIG_ADD("REISSUE_CARD_CONFIG_ADD", "新增补卡规则", OperationCodeEnum.REISSUE_CARD_CONFIG_MANAGE),
    REISSUE_CARD_CONFIG_UPDATE("REISSUE_CARD_CONFIG_UPDATE", "更新补卡规则", OperationCodeEnum.REISSUE_CARD_CONFIG_MANAGE),
    REISSUE_CARD_CONFIG_ACTIVE("REISSUE_CARD_CONFIG_ACTIVE", "启用补卡规则", OperationCodeEnum.REISSUE_CARD_CONFIG_MANAGE),
    REISSUE_CARD_CONFIG_DISABLED("REISSUE_CARD_CONFIG_DISABLED", "停用补卡规则", OperationCodeEnum.REISSUE_CARD_CONFIG_MANAGE),

    OVERTIME_CONFIG_ADD("OVERTIME_CONFIG_ADD", "新增加班规则", OperationCodeEnum.OVER_TIME_CONFIG_MANAGE),
    OVERTIME_CONFIG_UPDATE("OVERTIME_CONFIG_UPDATE", "更新加班规则", OperationCodeEnum.OVER_TIME_CONFIG_MANAGE),
    OVERTIME_CONFIG_ACTIVE("OVERTIME_CONFIG_ACTIVE", "启用加班规则", OperationCodeEnum.OVER_TIME_CONFIG_MANAGE),
    OVERTIME_CONFIG_DISABLED("OVERTIME_CONFIG_DISABLED", "停用加班规则", OperationCodeEnum.OVER_TIME_CONFIG_MANAGE),

    GPS_CONFIG_ADD("GPS_CONFIG_ADD", "新增GPS配置", OperationCodeEnum.GPS_CONFIG_MANAGE),
    GPS_CONFIG_UPDATE("GPS_CONFIG_UPDATE", "更新GPS配置", OperationCodeEnum.GPS_CONFIG_MANAGE),
    GPS_CONFIG_DELETE("GPS_CONFIG_DELETE", "删除GPS配置", OperationCodeEnum.GPS_CONFIG_MANAGE),
    GPS_CONFIG_IMPORT("GPS_CONFIG_IMPORT", "导入GPS配置", OperationCodeEnum.GPS_CONFIG_MANAGE),
    GPS_CONFIG_EXPORT("GPS_CONFIG_EXPORT", "导出GPS配置", OperationCodeEnum.GPS_CONFIG_MANAGE),

    ADD_SHIFT("ADD_SHIFT", "人员排班", OperationCodeEnum.SHIFT_SCHEDULE_MANAGE),
    BATCH_SHIFT("BATCH_SHIFT", "批量排班", OperationCodeEnum.SHIFT_SCHEDULE_MANAGE),
    CYCLE_SHIFT("CYCLE_SHIFT", "循环排班", OperationCodeEnum.SHIFT_SCHEDULE_MANAGE),
    CANCEL_CYCLE_SHIFT("CANCEL_CYCLE_SHIFT", "终止循环排班", OperationCodeEnum.SHIFT_SCHEDULE_MANAGE),
    IMPORT_SHIFT("IMPORT_SHIFT", "导入排班", OperationCodeEnum.SHIFT_SCHEDULE_MANAGE),
    EXPORT_SHIFT("EXPORT_SHIFT", "导出排班", OperationCodeEnum.SHIFT_SCHEDULE_MANAGE),


    EXPORT_ARCHIVE("EXPORT_ARCHIVE", "导出考勤档案", OperationCodeEnum.ATTENDANCE_ARCHIVE_MANAGE),
    WIFI_CONFIG_ADD("WIFI_CONFIG_ADD", "新增WIFI配置", OperationCodeEnum.WIFI_CONFIG_MANAGE),
    WIFI_CONFIG_UPDATE("WIFI_CONFIG_UPDATE", "更新WIFI配置", OperationCodeEnum.WIFI_CONFIG_MANAGE),
    WIFI_CONFIG_DELETE("WIFI_CONFIG_DELETE", "删除WIFI配置", OperationCodeEnum.WIFI_CONFIG_MANAGE),
    WIFI_CONFIG_IMPORT("WIFI_CONFIG_IMPORT", "导入WIFI配置", OperationCodeEnum.WIFI_CONFIG_MANAGE),
    WIFI_CONFIG_EXPORT("WIFI_CONFIG_EXPORT", "导出WIFI配置", OperationCodeEnum.WIFI_CONFIG_MANAGE),



    LEAVE_CONFIG_ADD("LEAVE_CONFIG_ADD", "新增假期配置", OperationCodeEnum.LEAVE_CONFIG_MANAGE),
    LEAVE_CONFIG_UPDATE("LEAVE_CONFIG_UPDATE", "修改假期配置", OperationCodeEnum.LEAVE_CONFIG_MANAGE),
    ;


    private final String code;

    private final String desc;

    private final OperationCodeEnum operationCodeEnum;


    OperationTypeEnum(String code, String desc, OperationCodeEnum operationCodeEnum) {
        this.code = code;
        this.desc = desc;
        this.operationCodeEnum = operationCodeEnum;
    }

    private static final Map<String, OperationTypeEnum> cacheMap = new ConcurrentHashMap<>();

    public static OperationTypeEnum getOperationType(String code) {
        return code == null ? null : cacheMap.get(code);
    }

    static {
        OperationTypeEnum[] attributes = values();
        for (OperationTypeEnum codeEnum : attributes) {
            cacheMap.put(codeEnum.getCode(), codeEnum);
        }

    }
}
