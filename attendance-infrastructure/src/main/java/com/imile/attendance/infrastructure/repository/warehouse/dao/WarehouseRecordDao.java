package com.imile.attendance.infrastructure.repository.warehouse.dao;


import com.baomidou.mybatisplus.extension.service.IService;
import com.imile.attendance.infrastructure.repository.warehouse.model.WarehouseRecordDO;
import com.imile.attendance.infrastructure.repository.warehouse.param.WarehouseRecordParam;

import java.util.Collection;
import java.util.List;

/**
 * <p>
 * 仓内记录表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-02
 */
public interface WarehouseRecordDao extends IService<WarehouseRecordDO> {

    List<WarehouseRecordDO> selectByCondition(WarehouseRecordParam warehouseRecordParam);

    List<WarehouseRecordDO> selectByWarehouseDetailIds(Collection<Long> warehouseDetailIds);

}
