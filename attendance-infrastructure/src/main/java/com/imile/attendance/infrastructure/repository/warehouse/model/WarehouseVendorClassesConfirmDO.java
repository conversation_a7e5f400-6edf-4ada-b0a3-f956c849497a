package com.imile.attendance.infrastructure.repository.warehouse.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.imile.attendance.base.BaseDO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 仓内考勤供应商班次确认表
 *
 * <AUTHOR>
 * @since 2024/12/06
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("warehouse_vendor_classes_confirm")
public class WarehouseVendorClassesConfirmDO extends BaseDO {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 网点id
     */
    private Long ocId;

    /**
     * 供应商编码
     */
    private String vendorCode;

    /**
     * 仓内作业日期
     */
    private Date warehouseDate;

    /**
     * 供应商班次确认时间
     */
    private Date confirmDate;

    /**
     * 班次id
     */
    private Long classesId;

    /**
     * 实际出勤人数
     */
    private Integer actualAttendanceNum;

    /**
     * 下班缺卡人数
     */
    private Integer afterOfficeLackNum;

    /**
     * 上班缺卡人数
     */
    private Integer beforeOfficeLackNum;

    /**
     * 迟到人数
     */
    private Integer lateNum;

    /**
     * 早退人数
     */
    private Integer leaveEarlyNum;

    /**
     * 时长异常人数
     */
    private Integer abnormalDurationNum;

    /**
     * 签名照
     */
    private String signedPhoto;

    /**
     * 人脸照
     */
    private String facePhoto;

}
