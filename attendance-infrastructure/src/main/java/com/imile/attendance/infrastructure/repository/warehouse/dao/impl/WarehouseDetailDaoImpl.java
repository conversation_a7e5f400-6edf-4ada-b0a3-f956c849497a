package com.imile.attendance.infrastructure.repository.warehouse.dao.impl;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.enums.warehouse.WarehouseAttendanceStatusEnum;
import com.imile.attendance.infrastructure.repository.warehouse.dao.WarehouseDetailDao;
import com.imile.attendance.infrastructure.repository.warehouse.mapper.WarehouseDetailMapper;
import com.imile.attendance.infrastructure.repository.warehouse.model.WarehouseDetailDO;
import com.imile.attendance.infrastructure.repository.warehouse.query.WarehouseDetailQuery;
import com.imile.common.enums.IsDeleteEnum;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * <p>
 * 仓内统计表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-04
 */
@Service
public class WarehouseDetailDaoImpl extends ServiceImpl<WarehouseDetailMapper, WarehouseDetailDO> implements WarehouseDetailDao {

    @Override
    public WarehouseDetailDO selectById(Long id) {
        if (Objects.isNull(id)) {
            return null;
        }
        LambdaQueryWrapper<WarehouseDetailDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(WarehouseDetailDO::getId, id);
        queryWrapper.eq(WarehouseDetailDO::getIsDelete, IsDeleteEnum.NO.getCode());
        return super.getOne(queryWrapper);
    }

    @Override
    public List<WarehouseDetailDO> selectByIds(Collection<Long> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<WarehouseDetailDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(WarehouseDetailDO::getId, idList);
        queryWrapper.eq(WarehouseDetailDO::getIsDelete, IsDeleteEnum.NO.getCode());
        return super.list(queryWrapper);
    }

    @Override
    public List<WarehouseDetailDO> selectPage(WarehouseDetailQuery param) {
        if (Objects.isNull(param)) {
            return Collections.emptyList();
        }
        return this.baseMapper.selectPage(param);
    }

    @Override
    public List<WarehouseDetailDO> selectPcsPage(WarehouseDetailQuery param) {
        if (Objects.isNull(param)) {
            return Collections.emptyList();
        }
        return this.baseMapper.selectPcsPage(param);
    }

    @Override
    public List<WarehouseDetailDO> selectByWarehouseDateAndUserId(Date warehouseDate, Long userId) {
        if (Objects.isNull(warehouseDate) || Objects.isNull(userId)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<WarehouseDetailDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(WarehouseDetailDO::getWarehouseDate, warehouseDate);
        queryWrapper.eq(WarehouseDetailDO::getUserId, userId);
        queryWrapper.orderByDesc(WarehouseDetailDO::getCreateDate);
        return super.list(queryWrapper);
    }

    @Override
    public List<WarehouseDetailDO> selectByCondition(WarehouseDetailQuery param) {
        if (Objects.isNull(param)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<WarehouseDetailDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(CollectionUtils.isNotEmpty(param.getIds()), WarehouseDetailDO::getId, param.getIds());
        queryWrapper.ge(Objects.nonNull(param.getStartTime()), WarehouseDetailDO::getWarehouseDate, param.getStartTime());
        queryWrapper.le(Objects.nonNull(param.getEndTime()), WarehouseDetailDO::getWarehouseDate, param.getEndTime());
        queryWrapper.eq(Objects.nonNull(param.getWarehouseDate()), WarehouseDetailDO::getWarehouseDate, param.getWarehouseDate());
        queryWrapper.in(CollectionUtils.isNotEmpty(param.getUserIdList()), WarehouseDetailDO::getUserId, param.getUserIdList());
        queryWrapper.in(CollectionUtils.isNotEmpty(param.getUserCodeList()), WarehouseDetailDO::getUserCode, param.getUserCodeList());
        queryWrapper.eq(Objects.nonNull(param.getOcId()), WarehouseDetailDO::getOcId, param.getOcId());
        queryWrapper.in(CollectionUtils.isNotEmpty(param.getOcIdList()), WarehouseDetailDO::getOcId, param.getOcIdList());
        queryWrapper.eq(Objects.nonNull(param.getClassId()), WarehouseDetailDO::getClassesId, param.getClassId());
        queryWrapper.in(CollectionUtils.isNotEmpty(param.getVendorIdList()), WarehouseDetailDO::getVendorId, param.getVendorIdList());
        queryWrapper.eq(StringUtils.isNotEmpty(param.getVendorCode()), WarehouseDetailDO::getVendorCode, param.getVendorCode());
        queryWrapper.in(CollectionUtils.isNotEmpty(param.getVendorCodeList()), WarehouseDetailDO::getVendorCode, param.getVendorCodeList());
        queryWrapper.in(CollectionUtils.isNotEmpty(param.getAttendanceStatusList()), WarehouseDetailDO::getAttendanceStatus, param.getAttendanceStatusList());
        queryWrapper.eq(StringUtils.isNotBlank(param.getCountry()), WarehouseDetailDO::getCountry, param.getCountry());
        queryWrapper.in(CollectionUtils.isNotEmpty(param.getEmployeeTypeList()), WarehouseDetailDO::getEmployeeType, param.getEmployeeTypeList());
        queryWrapper.eq(WarehouseDetailDO::getIsDelete, IsDeleteEnum.NO.getCode());
        queryWrapper.orderByDesc(WarehouseDetailDO::getCreateDate);
        return super.list(queryWrapper);
    }

    @Override
    public List<WarehouseDetailDO> selectClassesByCondition(WarehouseDetailQuery warehouseDetailQuery) {
        return this.baseMapper.selectClassesByCondition(warehouseDetailQuery);
    }

    @Override
    public List<WarehouseDetailDO> selectDataStatisticsPage(WarehouseDetailQuery param) {
        if (Objects.isNull(param)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<WarehouseDetailDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(CollectionUtils.isNotEmpty(param.getIds()), WarehouseDetailDO::getId, param.getIds());
        queryWrapper.eq(Objects.nonNull(param.getWarehouseDate()), WarehouseDetailDO::getWarehouseDate, param.getWarehouseDate());
        queryWrapper.eq(Objects.nonNull(param.getOcId()), WarehouseDetailDO::getOcId, param.getOcId());
        queryWrapper.eq(StringUtils.isNotEmpty(param.getVendorCode()), WarehouseDetailDO::getVendorCode, param.getVendorCode());
        queryWrapper.eq(Objects.nonNull(param.getClassesId()), WarehouseDetailDO::getClassesId, param.getClassesId());
        queryWrapper.eq(Objects.nonNull(param.getWarehouseStatus()), WarehouseDetailDO::getWarehouseStatus, param.getWarehouseStatus());
        queryWrapper.in(CollectionUtils.isNotEmpty(param.getEmployeeTypeList()), WarehouseDetailDO::getEmployeeType, param.getEmployeeTypeList());
        queryWrapper.eq(WarehouseDetailDO::getIsDelete,IsDeleteEnum.NO.getCode());
        queryWrapper.orderByDesc(WarehouseDetailDO::getCreateDate);
        return super.list(queryWrapper);
    }

    @Override
    public List<WarehouseDetailDO> selectJoinAbnormalList(WarehouseDetailQuery warehouseDetailQuery) {
        return this.baseMapper.selectJoinAbnormalList(warehouseDetailQuery);
    }

    @Override
    public List<WarehouseDetailDO> selectJoinRecordList(WarehouseDetailQuery warehouseDetailQuery) {
        return this.baseMapper.selectJoinRecordList(warehouseDetailQuery);
    }

    @Override
    public List<WarehouseDetailDO> selectNoBindShiftByCondition(WarehouseDetailQuery warehouseDetailQuery) {
        if (Objects.isNull(warehouseDetailQuery)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<WarehouseDetailDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(CollectionUtils.isNotEmpty(warehouseDetailQuery.getIds()), WarehouseDetailDO::getId, warehouseDetailQuery.getIds());
        queryWrapper.in(CollectionUtils.isNotEmpty(warehouseDetailQuery.getOcIdList()), WarehouseDetailDO::getOcId, warehouseDetailQuery.getOcIdList());
        queryWrapper.eq(Objects.nonNull(warehouseDetailQuery.getOcId()), WarehouseDetailDO::getOcId, warehouseDetailQuery.getOcId());
        queryWrapper.eq(WarehouseDetailDO::getAttendanceStatus, WarehouseAttendanceStatusEnum.PENDING_SHIFT_CONFIG.getCode());
        queryWrapper.eq(WarehouseDetailDO::getIsDelete, IsDeleteEnum.NO.getCode());
        return super.list(queryWrapper);
    }

    @Override
    public Integer noBindShiftCount(List<Long> ocIds) {
        if (CollectionUtils.isEmpty(ocIds)) {
            return BusinessConstant.ZERO;
        }
        LambdaQueryWrapper<WarehouseDetailDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(WarehouseDetailDO::getOcId, ocIds);
        queryWrapper.eq(WarehouseDetailDO::getAttendanceStatus, WarehouseAttendanceStatusEnum.PENDING_SHIFT_CONFIG.getCode());
        queryWrapper.eq(WarehouseDetailDO::getIsDelete, IsDeleteEnum.NO.getCode());
        return super.count(queryWrapper);
    }

    @Override
    public List<WarehouseDetailDO> selectLatestByUserIds(List<Long> userIdList) {
        return baseMapper.selectLatestByUserIds(userIdList);
    }

}
