package com.imile.attendance.infrastructure.repository.hrms.dao.impl;


import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.constants.Constants;
import com.imile.attendance.enums.WhetherEnum;
import com.imile.attendance.infrastructure.repository.hrms.dao.HrmsWarehouseDetailOriginalDao;
import com.imile.attendance.infrastructure.repository.hrms.mapper.HrmsWarehouseDetailOriginalMapper;
import com.imile.attendance.infrastructure.repository.hrms.modle.HrmsWarehouseDetailOriginalDO;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;


/**
 * <p>
 * 仓内考勤初始表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-04
 */
@DS(Constants.TableSchema.hrms)
@Service
public class HrmsWarehouseDetailDaoOriginalImpl extends ServiceImpl<HrmsWarehouseDetailOriginalMapper, HrmsWarehouseDetailOriginalDO> implements HrmsWarehouseDetailOriginalDao {

    @Override
    public List<HrmsWarehouseDetailOriginalDO> selectByIds(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<HrmsWarehouseDetailOriginalDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(HrmsWarehouseDetailOriginalDO::getId, ids);
        queryWrapper.eq(HrmsWarehouseDetailOriginalDO::getFromNewSystem, BusinessConstant.N);
        queryWrapper.eq(HrmsWarehouseDetailOriginalDO::getIsDelete, WhetherEnum.NO.getKey());
        return super.list(queryWrapper);
    }
}
