package com.imile.attendance.infrastructure.repository.warehouse.dao.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.imile.attendance.infrastructure.repository.warehouse.dao.WarehouseVendorClassesConfirmDao;
import com.imile.attendance.infrastructure.repository.warehouse.mapper.WarehouseVendorClassesConfirmMapper;
import com.imile.attendance.infrastructure.repository.warehouse.model.WarehouseVendorClassesConfirmDO;
import com.imile.attendance.infrastructure.repository.warehouse.param.WarehouseVendorClassesConfirmParam;
import com.imile.common.enums.IsDeleteEnum;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2024/12/6
 */
@Service
public class WarehouseVendorClassesConfirmDaoImpl extends ServiceImpl<WarehouseVendorClassesConfirmMapper, WarehouseVendorClassesConfirmDO> implements WarehouseVendorClassesConfirmDao {
    @Override
    public List<WarehouseVendorClassesConfirmDO> selectByContidition(WarehouseVendorClassesConfirmParam param) {
        LambdaQueryWrapper<WarehouseVendorClassesConfirmDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Objects.nonNull(param.getWarehouseDate()), WarehouseVendorClassesConfirmDO::getWarehouseDate, param.getWarehouseDate());
        queryWrapper.eq(Objects.nonNull(param.getVendorCode()), WarehouseVendorClassesConfirmDO::getVendorCode, param.getVendorCode());
        queryWrapper.eq(Objects.nonNull(param.getClassesId()), WarehouseVendorClassesConfirmDO::getClassesId, param.getClassesId());
        queryWrapper.eq(Objects.nonNull(param.getOcId()), WarehouseVendorClassesConfirmDO::getOcId, param.getOcId());
        queryWrapper.eq(WarehouseVendorClassesConfirmDO::getIsDelete, IsDeleteEnum.NO.getCode());
        return super.list(queryWrapper);
    }

    @Override
    public WarehouseVendorClassesConfirmDO selectById(Long id) {
        if (Objects.isNull(id)) {
            return null;
        }
        LambdaQueryWrapper<WarehouseVendorClassesConfirmDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(WarehouseVendorClassesConfirmDO::getId, id);
        queryWrapper.eq(WarehouseVendorClassesConfirmDO::getIsDelete, IsDeleteEnum.NO.getCode());
        return super.getOne(queryWrapper);
    }

    @Override
    public WarehouseVendorClassesConfirmDO selectConfirmResultBy(Long ocId, String vendorCode, Long classId, Date warehouseDate) {
        if (Objects.isNull(ocId) || Objects.isNull(vendorCode) || Objects.isNull(classId) || Objects.isNull(warehouseDate)) {
            return null;
        }
        LambdaQueryWrapper<WarehouseVendorClassesConfirmDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(WarehouseVendorClassesConfirmDO::getIsDelete, IsDeleteEnum.NO.getCode());
        queryWrapper.eq(WarehouseVendorClassesConfirmDO::getOcId, ocId);
        queryWrapper.eq(WarehouseVendorClassesConfirmDO::getVendorCode, vendorCode);
        queryWrapper.eq(WarehouseVendorClassesConfirmDO::getClassesId, classId);
        queryWrapper.eq(WarehouseVendorClassesConfirmDO::getWarehouseDate, warehouseDate);
        return this.getOne(queryWrapper);
    }
}
