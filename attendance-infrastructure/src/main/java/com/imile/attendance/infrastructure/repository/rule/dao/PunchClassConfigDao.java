package com.imile.attendance.infrastructure.repository.rule.dao;

import com.baomidou.mybatisplus.extension.service.IService;
import com.imile.attendance.infrastructure.repository.rule.dto.PunchClassConfigExportDTO;
import com.imile.attendance.infrastructure.repository.rule.model.PunchClassConfigDO;
import com.imile.attendance.infrastructure.repository.rule.query.PunchClassConfigQuery;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/4/7
 */
public interface PunchClassConfigDao extends IService<PunchClassConfigDO> {

    /**
     * 班次列表查询
     */
    List<PunchClassConfigDO> pageQuery(PunchClassConfigQuery query);

    /**
     * 根据ID查询
     */
    PunchClassConfigDO selectById(Long id);

    /**
     * 根据ID查询最新启用记录
     */
    PunchClassConfigDO selectLatestAndActiveById(Long id);

    /**
     * 根据ID集合查询
     */
    List<PunchClassConfigDO> selectByIds(Collection<Long> idList);

    /**
     * 根据ID集合查询最新且启用的班次规则
     */
    List<PunchClassConfigDO> selectLatestAndActiveByIds(Collection<Long> idList);

    /**
     * 根据班次名称和班次性质查询最新版本记录
     */
    List<PunchClassConfigDO> selectLatestByClassName(String className, String classNature);

    /**
     * 根据国家列表和班次性质查询最新且启用版本记录
     */
    List<PunchClassConfigDO> selectLatestAndActiveByCountry(Collection<String> countryList, String classNature);

    /**
     * 查询最新且启用的国家级班次记录
     */
    List<PunchClassConfigDO> selectLatestCountryRange(Collection<String> countryList, String classNature);

    /**
     * 根据部门和班次性质查询最新版本记录
     */
    List<PunchClassConfigDO> selectLatestAndActiveByDeptId(Long deptId, String classNature);

    /**
     * 根据班次性质所有最新的班次
     *
     * @param classNature 班次性质
     * @return 班次列表
     */
    List<PunchClassConfigDO> selectLatestByClassNature(String classNature);

    /**
     * 班次导出查询
     */
    List<PunchClassConfigExportDTO> export(PunchClassConfigQuery query);

    /**
     * 更新班次名称
     */
    void updateClassName(Long id, String className);

    /**
     * 停用状态
     */
    void disabledStatus(Long id);

    /**
     * 启用状态
     */
    void enableStatus(Long id);


    /**
     * 根据国家列表查询班次配置（不区分最新和启用）
     */
    List<PunchClassConfigDO> selectByCountries(Collection<String> countryList);

    /**
     * 查询所有非删除的班次
     */
    List<PunchClassConfigDO> selectAll();

    /**
     * 根据国家+部门+班次性质查询最新得记录
     */
    List<PunchClassConfigDO> selectLatestAndActiveByCondition(String country,
                                                              Long deptId,
                                                              String classNature);
}
