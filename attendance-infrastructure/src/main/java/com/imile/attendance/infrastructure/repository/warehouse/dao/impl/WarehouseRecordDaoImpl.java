package com.imile.attendance.infrastructure.repository.warehouse.dao.impl;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.imile.attendance.infrastructure.repository.warehouse.dao.WarehouseRecordDao;
import com.imile.attendance.infrastructure.repository.warehouse.mapper.WarehouseRecordMapper;
import com.imile.attendance.infrastructure.repository.warehouse.model.WarehouseRecordDO;
import com.imile.attendance.infrastructure.repository.warehouse.param.WarehouseRecordParam;
import com.imile.common.enums.IsDeleteEnum;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * <p>
 * 仓内记录表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-02
 */
@Service
public class WarehouseRecordDaoImpl extends ServiceImpl<WarehouseRecordMapper, WarehouseRecordDO> implements WarehouseRecordDao {

    @Override
    public List<WarehouseRecordDO> selectByCondition(WarehouseRecordParam param) {
        if (Objects.isNull(param)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<WarehouseRecordDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Objects.nonNull(param.getWarehouseDetailId()), WarehouseRecordDO::getWarehouseDetailId, param.getWarehouseDetailId());
        queryWrapper.in(CollectionUtils.isNotEmpty(param.getWarehouseDetailIds()), WarehouseRecordDO::getWarehouseDetailId, param.getWarehouseDetailIds());
        queryWrapper.in(CollectionUtils.isNotEmpty(param.getUserCodeList()), WarehouseRecordDO::getUserCode, param.getUserCodeList());
        queryWrapper.ge(Objects.nonNull(param.getStartTime()), WarehouseRecordDO::getWarehouseTime, param.getStartTime());
        queryWrapper.le(Objects.nonNull(param.getEndTime()), WarehouseRecordDO::getWarehouseTime, param.getEndTime());
        queryWrapper.eq(Objects.nonNull(param.getOcId()), WarehouseRecordDO::getOcId, param.getOcId());
        queryWrapper.eq(Objects.nonNull(param.getUserId()), WarehouseRecordDO::getUserId, param.getUserId());
        queryWrapper.eq(Objects.nonNull(param.getRecordType()), WarehouseRecordDO::getRecordType, param.getRecordType());
        queryWrapper.eq(WarehouseRecordDO::getIsDelete, IsDeleteEnum.NO.getCode());
        queryWrapper.orderByAsc(WarehouseRecordDO::getWarehouseTime);
        return super.list(queryWrapper);
    }

    @Override
    public List<WarehouseRecordDO> selectByWarehouseDetailIds(Collection<Long> warehouseDetailIds) {
        if (Objects.isNull(warehouseDetailIds)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<WarehouseRecordDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(WarehouseRecordDO::getWarehouseDetailId, warehouseDetailIds);
        queryWrapper.eq(WarehouseRecordDO::getIsDelete, IsDeleteEnum.NO.getCode());
        queryWrapper.orderByAsc(WarehouseRecordDO::getWarehouseTime);
        return super.list(queryWrapper);
    }
}
