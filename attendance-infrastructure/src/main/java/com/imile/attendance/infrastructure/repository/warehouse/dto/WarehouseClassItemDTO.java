package com.imile.attendance.infrastructure.repository.warehouse.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * {@code @author:} allen
 * {@code @className:} WarehouseClassItemDTO
 * {@code @since:} 2024-09-20 15:07
 * {@code @description:}
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class WarehouseClassItemDTO {

    /**
     * classId
     */
    private Long classId;
    /**
     * 最早上班时间
     */
    private Date dayPunchStartTime;

    /**
     * 最晚下班时间
     */
    private Date dayPunchEndTime;

    /**
     * 获取当前时间与开始时间的差值(单位毫秒)
     */
    private long betweenMs;

}
