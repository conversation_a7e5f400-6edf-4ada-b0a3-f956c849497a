package com.imile.attendance.infrastructure.repository.warehouse.dao.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.imile.attendance.infrastructure.repository.warehouse.dao.FaceFeatureDao;
import com.imile.attendance.infrastructure.repository.warehouse.mapper.FaceFeatureMapper;
import com.imile.attendance.infrastructure.repository.warehouse.model.FaceFeatureDO;
import com.imile.attendance.infrastructure.repository.warehouse.query.FaceFeatureQuery;
import com.imile.common.enums.IsDeleteEnum;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;


/**
 * <AUTHOR>
 * @since 2024/7/5
 */
@Service
public class FaceFeatureDaoImpl extends ServiceImpl<FaceFeatureMapper, FaceFeatureDO> implements FaceFeatureDao {

    @Override
    public FaceFeatureDO getByUserCode(String userCode) {
        if (StringUtils.isBlank(userCode)) {
            return null;
        }
        LambdaQueryWrapper<FaceFeatureDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(FaceFeatureDO::getUserCode, userCode);
        queryWrapper.eq(FaceFeatureDO::getIsDelete, IsDeleteEnum.NO.getCode());
        queryWrapper.last("limit 1");
        return super.getOne(queryWrapper);
    }

    @Override
    public List<FaceFeatureDO> selectByCondition(FaceFeatureQuery query) {
        LambdaQueryWrapper<FaceFeatureDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(CollectionUtils.isNotEmpty(query.getUserCodeList()),FaceFeatureDO::getUserCode,query.getUserCodeList());
        queryWrapper.in(CollectionUtils.isNotEmpty(query.getCountryList()),FaceFeatureDO::getCountry,query.getCountryList());
        queryWrapper.in(CollectionUtils.isNotEmpty(query.getEmployeeTypeList()),FaceFeatureDO::getEmployeeType,query.getEmployeeTypeList());
        queryWrapper.eq(FaceFeatureDO::getIsDelete, IsDeleteEnum.NO.getCode());
        queryWrapper.orderByAsc(FaceFeatureDO::getCreateDate);
        return super.list(queryWrapper);
    }
}
