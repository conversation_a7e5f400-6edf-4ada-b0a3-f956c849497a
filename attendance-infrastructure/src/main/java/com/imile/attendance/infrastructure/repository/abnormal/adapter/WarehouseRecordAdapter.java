package com.imile.attendance.infrastructure.repository.abnormal.adapter;

import com.google.common.collect.Lists;
import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.hrms.RpcHrWarehouseClient;
import com.imile.attendance.infrastructure.config.EnableNewAttendanceConfig;
import com.imile.attendance.infrastructure.repository.warehouse.dao.WarehouseRecordDao;
import com.imile.attendance.infrastructure.repository.warehouse.model.WarehouseRecordDO;
import com.imile.hrms.api.warehouse.dto.HrmsWarehouseRecordDTO;
import com.imile.util.BeanUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.Executor;

/**
 * <AUTHOR>
 * @since 2025/7/25
 */
@Slf4j
@Component
public class WarehouseRecordAdapter {

    @Resource
    private EnableNewAttendanceConfig enableNewAttendanceConfig;
    @Resource
    private WarehouseRecordDao warehouseRecordDao;
    @Resource
    private RpcHrWarehouseClient hrWarehouseClient;
    @Resource
    private Executor bizTaskThreadPool;

    public Boolean isDoubleWriteMode() {
        return enableNewAttendanceConfig.getWarehouseDoubleWriteEnabled();
    }

    //=====================dao层适配===============================

    public void saveOrUpdate(WarehouseRecordDO newRecord) {
        warehouseRecordDao.saveOrUpdate(newRecord);

        if (isDoubleWriteMode()) {
            bizTaskThreadPool.execute(() -> {
                HrmsWarehouseRecordDTO hrmsWarehouseRecordDTO = BeanUtils.convert(newRecord, HrmsWarehouseRecordDTO.class);
                hrmsWarehouseRecordDTO.setFromNewSystem(BusinessConstant.Y);
                hrWarehouseClient.warehouseRecordBatch(Collections.singletonList(hrmsWarehouseRecordDTO));
            });
        }

    }


    public void saveOrUpdateBatch(List<WarehouseRecordDO> newList) {
        warehouseRecordDao.saveOrUpdateBatch(newList);

        if (isDoubleWriteMode()) {
            bizTaskThreadPool.execute(() -> {
                List<HrmsWarehouseRecordDTO> hrmsWarehouseRecordDTOList = BeanUtils.convert(HrmsWarehouseRecordDTO.class, newList);
                hrmsWarehouseRecordDTOList.forEach(item -> item.setFromNewSystem(BusinessConstant.Y));
                List<List<HrmsWarehouseRecordDTO>> partitionList = Lists.partition(hrmsWarehouseRecordDTOList, 200);
                partitionList.forEach(partition -> hrWarehouseClient.warehouseRecordBatch(partition));
            });
        }
    }

}