package com.imile.attendance.infrastructure.repository.warehouse.dao;


import com.baomidou.mybatisplus.extension.service.IService;
import com.imile.attendance.infrastructure.repository.warehouse.model.WarehousePunchPeriodDO;

import java.util.List;


/**
 * <p>
 * 仓内考勤打卡周期表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-31
 */
public interface WarehousePunchPeriodDao extends IService<WarehousePunchPeriodDO> {

    List<WarehousePunchPeriodDO> selectByWarehouseDetailIds(Long warehouseDetailId);
}
