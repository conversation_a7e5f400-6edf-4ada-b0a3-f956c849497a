package com.imile.attendance.infrastructure.repository.warehouse.param;

import com.imile.common.constant.ValidCodeConstant;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @project hrms
 * @description 增加仓内外包人员
 * @date 2024/6/29 19:44:31
 */
@Data
public class AddUserParam {

    /**
     * 员工id
     */
    private Long userId;

    /**
     * 员工编码
     */
    private Long userCode;

    /**
     * 员工编码
     */
    @NotBlank(message = "userName cannot be empty")
    private String userName;

    /**
     * 1男 2女
     * 巴西劳务派遣扫描没有性别
     */
    private Integer sex;

    /**
     * 用户证件信息
     */
    @Valid
    @NotEmpty(message = ValidCodeConstant.NOT_EMPTY)
    private List<WarehouseUserCertificateParam> userCertificateParamList;

    /**
     * 网点编码
     */
    @NotNull(message = "oc cannot be empty")
    private Long ocId;

    /**
     * 网点对应国家(地理国)
     */
    @NotBlank(message = "country cannot be empty")
    private String country;

    /**
     * 供应商id
     */
    @NotNull(message = "vendor cannot be empty")
    private Long vendorId;

    /**
     * 供应商编码
     */
    @NotBlank(message = "vendor cannot be empty")
    private String vendorCode;

    /**
     * 用工类型 劳务派遣 正式/挂靠
     */
    @NotNull(message = "employeeType cannot be empty")
    private String employeeType;

    /**
     * 用工形式
     */
    private String employmentForm;

    /**
     * 来源
     * WPM
     * hrms web
     */
    private String source;

    /**
     * 班次信息id
     */
    private Long classId;

    /**
     * 仓内日期
     */
    private Date warehouseDate;


}
