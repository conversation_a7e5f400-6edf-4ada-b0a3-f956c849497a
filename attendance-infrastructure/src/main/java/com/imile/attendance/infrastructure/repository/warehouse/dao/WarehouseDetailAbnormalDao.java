package com.imile.attendance.infrastructure.repository.warehouse.dao;


import com.baomidou.mybatisplus.extension.service.IService;
import com.imile.attendance.infrastructure.repository.warehouse.dto.WarehouseDetailAbnormalDTO;
import com.imile.attendance.infrastructure.repository.warehouse.model.WarehouseDetailAbnormalDO;
import com.imile.attendance.infrastructure.repository.warehouse.param.WarehouseDetailAbnormalParam;

import java.util.Collection;
import java.util.List;

/**
 * 仓内统计表关联异常 服务类
 *
 * <AUTHOR>
 * @since 2024/8/27
 */
public interface WarehouseDetailAbnormalDao extends IService<WarehouseDetailAbnormalDO> {

    List<WarehouseDetailAbnormalDO> selectByWarehouseDetailId(Long warehouseDetailId, Integer processed);

    List<WarehouseDetailAbnormalDO> selectByWarehouseDetailIds(List<Long> warehouseDetailIds);

    WarehouseDetailAbnormalDO selectByAbnormalId(Long abnormalId);

    List<WarehouseDetailAbnormalDO> selectByAbnormalIdList(List<Long> abnormalIdList);

    boolean deleteByWarehouseDetailId(Long warehouseDetailId);

    boolean removeByWarehouseDetailId(Long warehouseDetailId);

    boolean deleteByWarehouseDetailIds(Collection<Long> warehouseDetailIdList);

    boolean deleteByIds(Collection<Long> ids);

    List<WarehouseDetailAbnormalDO> selectByCondition(WarehouseDetailAbnormalParam param);

    List<WarehouseDetailAbnormalDTO> selectJoinWarehouseDetailList(WarehouseDetailAbnormalParam param);
}
