package com.imile.attendance.infrastructure.repository.hrms.dao;


import com.baomidou.mybatisplus.extension.service.IService;
import com.imile.attendance.infrastructure.repository.hrms.modle.HrmsWarehouseRecordDO;

import java.util.Collection;
import java.util.List;

/**
 * <p>
 * 仓内记录表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-02
 */
public interface HrmsWarehouseRecordDao extends IService<HrmsWarehouseRecordDO> {


    List<HrmsWarehouseRecordDO> selectByWarehouseDetailIds(Collection<Long> warehouseDetailIds);

}
