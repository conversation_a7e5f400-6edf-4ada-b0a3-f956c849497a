package com.imile.attendance.infrastructure.repository.warehouse.dto;

import lombok.Data;

import java.math.BigDecimal;


/**
 * <AUTHOR>
 * @since 2024/10/15
 */
@Data
public class WarehouseDetailAbnormalDTO {
    /**
     * 仓内异常关联表
     */
    private Long id;

    /**
     * 关联办公室异常ID
     */
    private Long abnormalId;

    /**
     * 异常类型
     */
    private String abnormalType;

    /**
     * 关联考勤出勤明细
     */
    private Long warehouseDetailId;

    /**
     * 工作网点
     */
    private Long ocId;

    /**
     * 工作供应商
     */
    private Long vendorId;

    /**
     * 用工形式
     */
    private String employmentForm;

    /**
     * 实际出勤时长 单位:小时 包含休息时间
     */
    private BigDecimal actualAttendanceTime;

    /**
     * 实际工作总时长 单位:小时 不包含休息时间
     */
    private BigDecimal actualWorkingHours;
}
