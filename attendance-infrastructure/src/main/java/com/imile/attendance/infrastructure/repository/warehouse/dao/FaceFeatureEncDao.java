package com.imile.attendance.infrastructure.repository.warehouse.dao;


import com.baomidou.mybatisplus.extension.service.IService;
import com.imile.attendance.infrastructure.repository.warehouse.model.FaceFeatureEncDO;
import com.imile.attendance.infrastructure.repository.warehouse.query.FaceFeatureQuery;

import java.util.List;


/**
 * <AUTHOR>
 * @since 2025/7/4
 */
public interface FaceFeatureEncDao extends IService<FaceFeatureEncDO> {

    List<FaceFeatureEncDO> selectByCondition(FaceFeatureQuery query);
}
