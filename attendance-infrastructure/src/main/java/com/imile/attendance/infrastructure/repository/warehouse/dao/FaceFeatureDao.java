package com.imile.attendance.infrastructure.repository.warehouse.dao;


import com.baomidou.mybatisplus.extension.service.IService;
import com.imile.attendance.infrastructure.repository.warehouse.model.FaceFeatureDO;
import com.imile.attendance.infrastructure.repository.warehouse.query.FaceFeatureQuery;

import java.util.List;


/**
 * <AUTHOR>
 * @since 2024/7/5
 */
public interface FaceFeatureDao extends IService<FaceFeatureDO> {

    FaceFeatureDO getByUserCode(String userCode);

    List<FaceFeatureDO> selectByCondition(FaceFeatureQuery query);
}
