package com.imile.attendance.infrastructure.repository.warehouse.query;

import com.imile.common.query.BaseQuery;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.Collection;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @project hrms
 * @description 仓内统计参数
 * @date 2024/7/4 15:52:49
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class WarehouseDetailQuery extends BaseQuery {
    /**
     * id
     */
    private Long id;

    /**
     * 考勤结果id List
     */
    private Collection<Long> ids;

    /**
     * userIdList
     */
    private List<Long> userIdList;

    /**
     * userCodeList
     */
    private List<String> userCodeList;

    /**
     * 国家List
     */
    private List<String> countryList;

    /**
     * 国家
     */
    private String country;

    /**
     * 城市List
     */
    private List<String> cityList;

    /**
     * 城市
     */
    private String city;

    /**
     * 网点ID
     */
    private Long ocId;

    /**
     * 网点idList
     */
    private List<Long> ocIdList;

    /**
     * 供应商idList
     */
    private List<Long> vendorIdList;

    /**
     * 供应商编码
     */
    private String vendorCode;

    /**
     * 供应商编码List
     */
    private List<String> vendorCodeList;

    /**
     * 开始时间
     */
    private Date startDate;

    /**
     * 结束时间
     */
    private Date endDate;

    /**
     * 开始时间
     */
    private Date startTime;

    /**
     * 结束时间
     */
    private Date endTime;

    /**
     * 报表类型；DAY:日报 MONTH:月报
     */
    private String reportType;

    /**
     * 1正常 2异常
     */
    private Integer result;

    /**
     * 仓内日期
     */
    private Date warehouseDate;


    /**
     * 异常ID
     */
    private Long abnormalId;

    /**
     * 用工类型列表
     */
    private List<String> employeeTypeList;

    /**
     * 出入仓类型
     */
    private Integer recordType;

    /**
     * 异常类型
     */
    private String abnormalType;

    /**
     * 班次ID
     */
    private Long classesId;

    /**
     * 班次类型列表
     */
    private List<Integer> classesTypeList;

    /**
     * 操作状态
     */
    private String pcsStatus;

    /**
     * 考勤状态
     */
    private Integer attendanceStatus;

    /**
     * 仓内状态
     */
    private Integer warehouseStatus;

    /**
     * 考勤流水号
     */
    private String warehouseAttendanceCode;

    /**
     * 班次确认状态
     */
    private List<Integer> confirmStatusList;

    /**
     * 打卡状态（1:正常 2:异常）
     */
    private Integer punchStatus;

    /**
     * 班次
     */
    private Long classId;

    /**
     * 考勤状态
     */
    private List<Integer> attendanceStatusList;

    /**
     * 用工形式
     */
    private String employmentForm;

    private Long lastId;

}
