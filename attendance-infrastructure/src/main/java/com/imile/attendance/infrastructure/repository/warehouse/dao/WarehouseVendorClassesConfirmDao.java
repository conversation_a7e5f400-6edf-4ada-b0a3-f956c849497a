package com.imile.attendance.infrastructure.repository.warehouse.dao;


import com.baomidou.mybatisplus.extension.service.IService;
import com.imile.attendance.infrastructure.repository.warehouse.model.WarehouseVendorClassesConfirmDO;
import com.imile.attendance.infrastructure.repository.warehouse.param.WarehouseVendorClassesConfirmParam;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * 仓内考勤供应商班次确认表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024/12/06
 */
public interface WarehouseVendorClassesConfirmDao extends IService<WarehouseVendorClassesConfirmDO> {


    List<WarehouseVendorClassesConfirmDO> selectByContidition(WarehouseVendorClassesConfirmParam param);

    WarehouseVendorClassesConfirmDO selectById(Long id);
    WarehouseVendorClassesConfirmDO selectConfirmResultBy(Long ocId, String vendorCode, Long classId, Date warehouseDate);
}
