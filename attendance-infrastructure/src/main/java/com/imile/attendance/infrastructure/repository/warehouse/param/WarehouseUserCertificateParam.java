package com.imile.attendance.infrastructure.repository.warehouse.param;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.imile.common.constant.ValidCodeConstant;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.Date;

/**
 * @description:
 * @author: taokang
 * @createDate: 2024/12/11 14:44
 * @version: 1.0
 */
@Data
public class WarehouseUserCertificateParam {

    private Long id;

    /**
     * 证件类型编码
     */
    @NotBlank(message = ValidCodeConstant.NOT_BLANK)
    private String certificateTypeCode;

    /**
     * 证件号码
     */
    @NotBlank(message = ValidCodeConstant.NOT_BLANK)
    private String certificateCode;

    /**
     * 证件正面照路径
     */
    @NotBlank(message = ValidCodeConstant.NOT_BLANK)
    private String certificateFrontPath;

    /**
     * 证件背面照路径
     */
    private String certificateBackPath;

    /**
     * 生效日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date certificateReceiptDate;

    /**
     * 失效日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date certificateExpireDate;
}
