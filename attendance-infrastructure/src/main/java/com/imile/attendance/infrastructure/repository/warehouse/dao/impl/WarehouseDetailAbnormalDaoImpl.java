package com.imile.attendance.infrastructure.repository.warehouse.dao.impl;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.imile.attendance.infrastructure.repository.warehouse.dao.WarehouseDetailAbnormalDao;
import com.imile.attendance.infrastructure.repository.warehouse.dto.WarehouseDetailAbnormalDTO;
import com.imile.attendance.infrastructure.repository.warehouse.mapper.WarehouseDetailAbnormalMapper;
import com.imile.attendance.infrastructure.repository.warehouse.model.WarehouseDetailAbnormalDO;
import com.imile.attendance.infrastructure.repository.warehouse.param.WarehouseDetailAbnormalParam;
import com.imile.common.enums.IsDeleteEnum;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Objects;


/**
 * <p>
 * 仓内统计表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-04
 */
@Service
public class WarehouseDetailAbnormalDaoImpl extends ServiceImpl<WarehouseDetailAbnormalMapper, WarehouseDetailAbnormalDO> implements WarehouseDetailAbnormalDao {

    @Override
    public List<WarehouseDetailAbnormalDO> selectByWarehouseDetailId(Long warehouseDetailId, Integer processed) {
        if (Objects.isNull(warehouseDetailId)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<WarehouseDetailAbnormalDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(WarehouseDetailAbnormalDO::getWarehouseDetailId, warehouseDetailId);
        queryWrapper.eq(Objects.nonNull(processed), WarehouseDetailAbnormalDO::getProcessed, processed);
        queryWrapper.eq(WarehouseDetailAbnormalDO::getIsDelete, IsDeleteEnum.NO.getCode());
        return super.list(queryWrapper);
    }

    @Override
    public List<WarehouseDetailAbnormalDO> selectByWarehouseDetailIds(List<Long> warehouseDetailIds) {
        if (CollectionUtils.isEmpty(warehouseDetailIds)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<WarehouseDetailAbnormalDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(WarehouseDetailAbnormalDO::getWarehouseDetailId, warehouseDetailIds);
        queryWrapper.eq(WarehouseDetailAbnormalDO::getIsDelete, IsDeleteEnum.NO.getCode());
        return super.list(queryWrapper);
    }


    @Override
    public WarehouseDetailAbnormalDO selectByAbnormalId(Long abnormalId) {
        if (Objects.isNull(abnormalId)) {
            return new WarehouseDetailAbnormalDO();
        }
        LambdaQueryWrapper<WarehouseDetailAbnormalDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(WarehouseDetailAbnormalDO::getAbnormalId, abnormalId);
        queryWrapper.eq(WarehouseDetailAbnormalDO::getIsDelete, IsDeleteEnum.NO.getCode());
        queryWrapper.last("limit 1");
        return super.getOne(queryWrapper);
    }

    @Override
    public List<WarehouseDetailAbnormalDO> selectByAbnormalIdList(List<Long> abnormalIdList) {
        if (CollectionUtils.isEmpty(abnormalIdList)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<WarehouseDetailAbnormalDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(WarehouseDetailAbnormalDO::getAbnormalId, abnormalIdList);
        queryWrapper.eq(WarehouseDetailAbnormalDO::getIsDelete, IsDeleteEnum.NO.getCode());
        return super.list(queryWrapper);
    }

    @Override
    public boolean deleteByWarehouseDetailId(Long warehouseDetailId) {
        UpdateWrapper<WarehouseDetailAbnormalDO> updateWrapper = new UpdateWrapper<>();
        updateWrapper.lambda().set(WarehouseDetailAbnormalDO::getIsDelete, IsDeleteEnum.YES.getCode());
        updateWrapper.lambda().eq(WarehouseDetailAbnormalDO::getWarehouseDetailId, warehouseDetailId);
        return super.update(updateWrapper);
    }

    @Override
    public boolean removeByWarehouseDetailId(Long warehouseDetailId) {
        if (Objects.isNull(warehouseDetailId)) {
            return false;
        }
        LambdaQueryWrapper<WarehouseDetailAbnormalDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(WarehouseDetailAbnormalDO::getWarehouseDetailId, warehouseDetailId);
        return super.remove(queryWrapper);
    }

    @Override
    public boolean deleteByWarehouseDetailIds(Collection<Long> warehouseDetailIdList) {
        UpdateWrapper<WarehouseDetailAbnormalDO> updateWrapper = new UpdateWrapper<>();
        updateWrapper.lambda().set(WarehouseDetailAbnormalDO::getIsDelete, IsDeleteEnum.YES.getCode());
        updateWrapper.lambda().in(WarehouseDetailAbnormalDO::getWarehouseDetailId, warehouseDetailIdList);
        return super.update(updateWrapper);
    }

    @Override
    public boolean deleteByIds(Collection<Long> ids) {
        UpdateWrapper<WarehouseDetailAbnormalDO> updateWrapper = new UpdateWrapper<>();
        updateWrapper.lambda().set(WarehouseDetailAbnormalDO::getIsDelete, IsDeleteEnum.YES.getCode());
        updateWrapper.lambda().in(WarehouseDetailAbnormalDO::getId, ids);
        return super.update(updateWrapper);
    }

    @Override
    public List<WarehouseDetailAbnormalDO> selectByCondition(WarehouseDetailAbnormalParam param) {
        if (Objects.isNull(param)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<WarehouseDetailAbnormalDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(CollectionUtils.isNotEmpty(param.getOcIdList()), WarehouseDetailAbnormalDO::getOcId, param.getOcIdList());
        queryWrapper.in(CollectionUtils.isNotEmpty(param.getVendorIdList()), WarehouseDetailAbnormalDO::getVendorId, param.getVendorIdList());
        queryWrapper.eq(Objects.nonNull(param.getOcId()), WarehouseDetailAbnormalDO::getOcId, param.getOcId());
        queryWrapper.eq(Objects.nonNull(param.getVendorId()), WarehouseDetailAbnormalDO::getVendorId, param.getVendorId());
        queryWrapper.eq(StringUtils.isNotEmpty(param.getEmploymentForm()), WarehouseDetailAbnormalDO::getVendorId, param.getEmploymentForm());
        queryWrapper.eq(WarehouseDetailAbnormalDO::getIsDelete, IsDeleteEnum.NO.getCode());
        return super.list(queryWrapper);
    }

    @Override
    public List<WarehouseDetailAbnormalDTO> selectJoinWarehouseDetailList(WarehouseDetailAbnormalParam param) {
        return this.baseMapper.selectJoinWarehouseDetailList(param);
    }
}
