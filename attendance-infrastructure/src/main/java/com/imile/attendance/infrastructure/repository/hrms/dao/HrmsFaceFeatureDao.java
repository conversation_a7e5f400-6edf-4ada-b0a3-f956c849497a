package com.imile.attendance.infrastructure.repository.hrms.dao;


import com.baomidou.mybatisplus.extension.service.IService;
import com.imile.attendance.infrastructure.repository.hrms.modle.HrmsFaceFeatureDO;
import com.imile.attendance.infrastructure.repository.warehouse.query.FaceFeatureQuery;

import java.util.List;


/**
 * <AUTHOR>
 * @since 2024/7/5
 */
public interface HrmsFaceFeatureDao extends IService<HrmsFaceFeatureDO> {

    List<HrmsFaceFeatureDO> selectByCondition(FaceFeatureQuery query);
}
