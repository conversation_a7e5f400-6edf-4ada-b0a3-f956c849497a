package com.imile.attendance.infrastructure.repository.hrms.modle;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.imile.attendance.base.BaseDO;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 人脸特征向量表
 * <AUTHOR>
 * @since 2024/7/5
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("face_feature")
public class HrmsFaceFeatureDO extends BaseDO {

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 网点编码
     */
    @TableField("oc_code")
    private String ocCode;

    /**
     * 用户ID
     */
    @TableField("user_id")
    private Long userId;

    /**
     * 用户编码
     */
    @TableField("user_code")
    private String userCode;

    /**
     * 图片url
     */
    @TableField("url")
    private String url;

    /**
     * 人脸特征向量
     */
    @TableField("feature_data")
    private byte[] featureData;

    /**
     * 国家
     */
    @TableField("country")
    private String country;

    /**
     * 用工类型
     */
    @TableField("employee_type")
    private String employeeType;

    /**
     * 来源
     */
    @TableField("source")
    private String source;
}
