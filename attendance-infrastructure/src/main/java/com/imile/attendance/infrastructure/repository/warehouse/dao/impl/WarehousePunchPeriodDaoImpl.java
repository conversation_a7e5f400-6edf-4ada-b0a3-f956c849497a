package com.imile.attendance.infrastructure.repository.warehouse.dao.impl;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.imile.attendance.infrastructure.repository.warehouse.dao.WarehousePunchPeriodDao;
import com.imile.attendance.infrastructure.repository.warehouse.mapper.WarehousePunchPeriodMapper;
import com.imile.attendance.infrastructure.repository.warehouse.model.WarehousePunchPeriodDO;
import com.imile.common.enums.IsDeleteEnum;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Objects;


/**
 * <p>
 * 仓内考勤打卡周期表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-31
 */
@Service
public class WarehousePunchPeriodDaoImpl extends ServiceImpl<WarehousePunchPeriodMapper, WarehousePunchPeriodDO> implements WarehousePunchPeriodDao {


    @Override
    public List<WarehousePunchPeriodDO> selectByWarehouseDetailIds(Long warehouseDetailId) {
        if (Objects.isNull(warehouseDetailId)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<WarehousePunchPeriodDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(WarehousePunchPeriodDO::getWarehouseDetailId, warehouseDetailId);
        queryWrapper.eq(WarehousePunchPeriodDO::getIsDelete, IsDeleteEnum.NO.getCode());
        return super.list(queryWrapper);
    }
}
