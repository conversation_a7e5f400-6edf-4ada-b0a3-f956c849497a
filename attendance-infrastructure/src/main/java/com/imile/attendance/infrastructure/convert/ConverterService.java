package com.imile.attendance.infrastructure.convert;

import cn.hutool.core.collection.CollUtil;
import com.google.common.base.Splitter;
import com.imile.attendance.annon.HyperLink;
import com.imile.attendance.annon.OutWithTimeZone;
import com.imile.attendance.annon.WithDict;
import com.imile.attendance.annon.WithDictSeparator;
import com.imile.attendance.hermes.dto.DictVO;
import com.imile.attendance.hermes.support.RpcHermesDictClientSupport;
import com.imile.attendance.ipep.dto.OssApiVo;
import com.imile.attendance.ipep.support.RpcIpepClientSupport;
import com.imile.attendance.util.DateUtils;
import com.imile.util.EscapeUtil;
import com.imile.util.user.UserEvnHolder;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.ReflectionUtils;

import java.lang.reflect.Field;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.TimeZone;
import java.util.function.Consumer;
import java.util.stream.Collectors;

/**
 * 国际化枚举转换服务实现
 * <AUTHOR> chen
 * @Date 2025/1/17 
 * @Description
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ConverterService {

    private final RpcHermesDictClientSupport dictClientSupport;
    private final RpcIpepClientSupport rpcIpepClientSupport;

    /**
     * 通过注解的方式进行数据转换，转换单条
     *
     * @param <T> 元素类型
     * @return 消费者
     */
    public <T> T withAnnotationForSingle(T t) {
        if (t != null) {
            ((Consumer<T>) withAnnotation(t.getClass())).accept(t);
        }
        return t;
    }

    /**
     * 通过注解的方式进行数据转换,只为单条转换服务，当为list时切记不要循环调用
     *
     * @param clazz
     * @param <T>
     * @return
     */
    public <T> Consumer<T> withAnnotation(Class<T> clazz) {
        //获取该类的所有字段及父类的所有字段
        List<Field> fields = getFields(clazz);
        List<String> dictList = new ArrayList<>();
        for (Field field : fields) {
            if (field.isAnnotationPresent(WithDict.class)) {
                WithDict annotation = field.getAnnotation(WithDict.class);
                String typeCode = annotation.typeCode();
                dictList.add(typeCode);
            }
            if (field.isAnnotationPresent(WithDictSeparator.class)) {
                WithDictSeparator annotation = field.getAnnotation(WithDictSeparator.class);
                String typeCode = annotation.typeCode();
                dictList.add(typeCode);
            }
        }
        Map<String, Map<String, DictVO>> dictMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(dictList)) {
            dictMap.putAll(dictClientSupport.getByTypeCodes(dictList));
        }
        return bean -> {
            List<String> urlPaths = new ArrayList<>();
            List<Field> urlFieldList = new ArrayList<>();
            for (Field field : fields) {
                field.setAccessible(true);
                handleWithDict(bean, field, field.isAnnotationPresent(WithDict.class), dictMap);
                handleWithDictSeparator(bean, field, field.isAnnotationPresent(WithDictSeparator.class), dictMap);
                handleOutWithTimeZone(bean, field, field.isAnnotationPresent(OutWithTimeZone.class));
                handlerHyperLink(bean, field, field.isAnnotationPresent(HyperLink.class), urlPaths, urlFieldList);
            }
            // bean fields
            this.handlerUrl(bean, urlPaths, urlFieldList);
        };
    }

    private <T> List<Field> getFields(Class<T> clazz) {
        Field[] declaredFields = clazz.getDeclaredFields();
        List<Field> fields = new ArrayList<>();
        for (Field field : declaredFields) {
            fields.add(field);
        }

        Set<String> collect = fields.stream().map(item -> item.getName()).collect(Collectors.toSet());
        Class clazzTemp = clazz.getSuperclass();
        while (clazzTemp != null) {
            Field[] superFields = clazzTemp.getDeclaredFields();
            for (Field field : superFields) {
                if (collect.contains(field.getName())) {
                    continue;
                }
                fields.add(field);

            }
            clazzTemp = clazzTemp.getSuperclass();
        }
        return fields;
    }

    /**
     * 通过注解的方式进行数据转换 转换整个list
     *
     * @param <T> 元素类型
     * @return 消费者
     */
    public <T> List<T> withAnnotation(List<T> list) {
        if (!CollectionUtils.isEmpty(list)) {
            T t = list.get(0);
            Consumer<T> consumer = (Consumer<T>) withAnnotation(t.getClass());
            for (T bean : list) {
                consumer.accept(bean);
            }
        }
        return list;
    }


    /**
     * 处理数据字典的值
     *
     * @param t
     * @param field
     * @param withDict
     * @param <T>
     * @throws IllegalAccessException
     * @throws NoSuchFieldException
     */
    private <T> void handleWithDict(T t, Field field, boolean withDict, Map<String, Map<String, DictVO>> dictMap) {
        if (withDict) {
            try {
                WithDict annotation = field.getAnnotation(WithDict.class);
                String ref = annotation.ref();
                String typeCode = annotation.typeCode();
                if (!StringUtils.isEmpty(ref) && !StringUtils.isEmpty(typeCode)) {
                    Object value = field.get(t);
                    Map<String, DictVO> source = ObjectUtils.defaultIfNull(dictMap.get(typeCode), new HashMap<>(1));
                    String codeStr = handleCode(value);
                    if (!StringUtils.isEmpty(codeStr)) {
                        DictVO dictVO = source.get(codeStr);
                        if (dictVO != null) {
                            Field refField = ReflectionUtils.findField(field.getDeclaringClass(), ref);
                            refField.setAccessible(true);
                            refField.set(t, dictVO.getDataValue());
                        }
                    }
                }
                /*WithDict annotation = field.getAnnotation(WithDict.class);
                String ref = annotation.ref();
                String typeCode = annotation.typeCode();
                if (StringUtils.isEmpty(ref) || StringUtils.isEmpty(typeCode)) {
                    return;
                }

                Field refField = t.getClass().getDeclaredField(ref);
                refField.setAccessible(true);

                Map<String, DictVO> source = ObjectUtils.defaultIfNull(dictMap.get(typeCode), new HashMap<>(1));
                Object value = field.get(t);
                // 处理List
                if (value instanceof List) {
                    List<String> dictDescList = ((List<Object>) value).stream()
                            .map(str -> {
                                String codeStr = handleCode(str);
                                if (StringUtils.isEmpty(codeStr)) {
                                    return null;
                                }
                                return source.get(codeStr);
                            })
                            .filter(Objects::nonNull)
                            .map(DictVO::getDataValue)
                            .collect(Collectors.toList());
                    if (field.getGenericType().getTypeName().startsWith(List.class.getName())) {
                        refField.set(t, dictDescList);
                    }
                    // 处理单个
                } else {
                    String codeStr = handleCode(value);
                    if (!StringUtils.isEmpty(codeStr)) {
                        DictVO dictVO = source.get(codeStr);
                        if (dictVO != null) {
                            refField.set(t, dictVO.getDataValue());
                        }
                    }
                }*/
            } catch (Exception e) {
                log.error("数据转换->处理数据字典的值 异常：", e);
            }
        }
    }


    /**
     * 通过数据字典的数据进行转换, 此类数据使用分隔符写在一个字段中
     * 在将数据字典dataCode转换为dataValue时,dataCode可能存储了多
     *
     * @param t
     * @param field
     * @param withDictSeparator
     * @param dictMap
     * @param <T>
     */
    private <T> void handleWithDictSeparator(T t, Field field, boolean withDictSeparator, Map<String, Map<String, DictVO>> dictMap) {
        if (!withDictSeparator) {
            return;
        }
        try {
            WithDictSeparator annotation = field.getAnnotation(WithDictSeparator.class);
            String ref = annotation.ref();
            String typeCode = annotation.typeCode();
            String sSeparator = annotation.sSeparator();
            String tSeparator = annotation.tSeparator();
            if (!StringUtils.isEmpty(ref) && !StringUtils.isEmpty(typeCode)) {
                Object value = field.get(t);
                Map<String, DictVO> source = ObjectUtils.defaultIfNull(dictMap.get(typeCode), new HashMap<>(1));
                String codeStr = handleCode(value);
                if (!StringUtils.isEmpty(codeStr)) {
                    String values = Splitter.on(sSeparator).splitToList(codeStr)
                            .stream()
                            .map(source::get)
                            .map(dict -> dict != null ? dict.getDataValue() : "")
                            .collect(Collectors.joining(tSeparator));
                    Field refField = field.getDeclaringClass().getDeclaredField(ref);
                    if (refField != null) {
                        refField.setAccessible(true);
                        refField.set(t, values);
                    }
                }
            }
        } catch (Exception e) {
            log.error("数据转换->处理数据字典的值 异常：", e);
        }
    }


    /**
     * mysql模糊查询时对特殊字符"%“和”_"的处理
     *
     * @param t
     * @param field
     * @param sqlLike
     * @param <T>
     * @throws IllegalAccessException
     */
    private <T> void handleSqlLike(T t, Field field, boolean sqlLike) {
        if (sqlLike) {
            try {
                Object value = field.get(t);
                if (value instanceof String) {
                    EscapeUtil.escape((String) value);
                    field.set(t, EscapeUtil.escape((String) value));
                }
            } catch (Exception e) {
                log.error("数据转换->mysql模糊查询时对特殊字符%和_的处理 异常：", e);
            }
        }
    }

    private String handleCode(Object obj) {
        return obj == null ? null : obj instanceof String ? (String) obj : obj.toString();
    }

    private <T> void handleOutWithTimeZone(T t, Field field, boolean outWithAnnotation) {
        if (outWithAnnotation) {
            try {
                Object value = field.get(t);
                if (null == value) {
                    return;
                }
                if (value instanceof Date) {
                    value = DateUtils.fromEight((Date) value, getTimezone());
                } else if (value instanceof LocalDateTime) {
                    value = DateUtils.fromEight((LocalDateTime) value, getTimezone());
                } else {
                    throw new RuntimeException("The Type:" + value.getClass().getName() + " is not support! Only support Date and LocalDateTime!");
                }
                field.set(t, value);
            } catch (Exception e) {
                log.error("数据转换-》出参时间转换异常,{}", e.getMessage());
            }

        }

    }


    private int getTimezone() {
        return Optional.ofNullable(UserEvnHolder.getTimeZone())
                .map(TimeZone::getRawOffset)
                .map(offset -> offset / 3600000)
                .orElse(8);
    }

    private <T> void handlerHyperLink(T bean, Field field, boolean annotationPresent,
                                      List<String> urlPaths, List<Field> urlFieldList) {
        if (!annotationPresent) {
            return;
        }
        try {
            Object value = field.get(bean);
            if (value != null) {
                urlPaths.add((String) value);
                urlFieldList.add(field);
            }
        } catch (Exception e) {
            log.error("对图片路径处理，数据转化异常", e);
        }
    }

    private <T> void handlerUrl(T bean, List<String> urlPaths, List<Field> urlFieldList) {
        if (CollUtil.isEmpty(urlPaths) || CollUtil.isEmpty(urlFieldList)) {
            return;
        }
        Map<String, OssApiVo> urlByFileKeys = rpcIpepClientSupport.getUrlByFileKeys(1, urlPaths);
        if (urlByFileKeys.isEmpty()) {
            return;
        }
        try {
            for (Field field : urlFieldList) {
                field.setAccessible(true);
                //获取注解中的ref属性，将转化后的值写入到该字段
                HyperLink annotation = field.getAnnotation(HyperLink.class);
                String ref = annotation.ref();
                String pathValue = (String) field.get(bean);
                if (urlByFileKeys.containsKey(pathValue)) {
                    String fileUrl = urlByFileKeys.get(pathValue).getFileUrl();
                    Class<?> clazz = bean.getClass();
                    Field declaredField = getFieldByName(ref, clazz);
                    declaredField.setAccessible(true);
                    declaredField.set(bean, fileUrl);
                }
            }
        } catch (Exception e) {
            log.error("对图片路径处理，数据转化异常", e);
        }
    }

    private Field getFieldByName(String fieldName, Class<?> clazz) {
        while (true) {
            try {
                return clazz.getDeclaredField(fieldName);

            } catch (Exception e) {
                clazz = clazz.getSuperclass();
            }
        }
    }
}
