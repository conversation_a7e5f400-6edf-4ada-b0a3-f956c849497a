package com.imile.attendance.infrastructure.repository.warehouse.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.imile.attendance.base.BaseDO;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 仓内结果表关联异常
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-27
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("warehouse_detail_abnormal")
public class WarehouseDetailAbnormalDO extends BaseDO {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 考勤统计表ID
     */
    private Long warehouseDetailId;

    /**
     * 工作网点ID
     */
    private Long ocId;

    /**
     * 工作供应商ID
     */
    private Long vendorId;

    /**
     * 考勤异常id
     */
    private Long abnormalId;

    /**
     * 考勤异常类型
     */
    private String abnormalType;

    /**
     * 是否已处理 0:未处理 1:已处理
     */
    private Integer processed;
}
