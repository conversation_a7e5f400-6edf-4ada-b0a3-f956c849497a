
package com.imile.attendance.infrastructure.repository.abnormal.adapter.mapstruct;

import com.imile.attendance.infrastructure.repository.warehouse.model.WarehouseDetailSnapshotDO;
import com.imile.hrms.api.warehouse.dto.HrmsWarehouseDetailSnapshotDTO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;


/**
 * 异常表映射
 *
 * <AUTHOR>
 * @since 2025/6/18
 */
@Mapper
public interface WarehouseDetailSnapshotMapstruct {

    WarehouseDetailSnapshotMapstruct INSTANCE = Mappers.getMapper(WarehouseDetailSnapshotMapstruct.class);

    HrmsWarehouseDetailSnapshotDTO mapToOld(WarehouseDetailSnapshotDO warehouseDetailSnapshotDO);

    List<HrmsWarehouseDetailSnapshotDTO> mapToOldList(List<WarehouseDetailSnapshotDO> warehouseDetailSnapshotDOList);
}
