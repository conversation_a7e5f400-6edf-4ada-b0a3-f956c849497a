
package com.imile.attendance.infrastructure.repository.abnormal.adapter.mapstruct;

import com.imile.attendance.infrastructure.repository.warehouse.model.WarehouseDetailDO;
import com.imile.hrms.api.warehouse.dto.HrmsWarehouseDetailDTO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;


/**
 * 异常表映射
 *
 * <AUTHOR>
 * @since 2025/6/18
 */
@Mapper
public interface WarehouseDetailMapstruct {

    WarehouseDetailMapstruct INSTANCE = Mappers.getMapper(WarehouseDetailMapstruct.class);

    HrmsWarehouseDetailDTO mapToOld(WarehouseDetailDO warehouseDetailDO);

    List<HrmsWarehouseDetailDTO> mapToOldList(List<WarehouseDetailDO> warehouseDetailDOList);
}
