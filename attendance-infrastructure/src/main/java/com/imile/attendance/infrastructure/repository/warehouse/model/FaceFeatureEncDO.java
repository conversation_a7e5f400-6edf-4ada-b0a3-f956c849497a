package com.imile.attendance.infrastructure.repository.warehouse.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.imile.attendance.base.BaseDO;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 人脸特征向量加密表
 *
 * <AUTHOR>
 * @since 2025/7/4
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("face_feature_enc")
public class FaceFeatureEncDO extends BaseDO {

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 用户ID
     */
    @TableField("user_id")
    private Long userId;

    /**
     * 用户编码
     */
    @TableField("user_code")
    private String userCode;

    /**
     * 国家
     */
    @TableField("country")
    private String country;

    /**
     * 用工类型
     */
    @TableField("employee_type")
    private String employeeType;

    /**
     * 加密的图片短链
     */
    @TableField("enc_url")
    private String encUrl;

    /**
     * 加密带图片水印的短链
     */
    @TableField("enc_watermark_url")
    private String encWatermarkUrl;

    /**
     * 人脸特征向量
     */
    @TableField("feature_data")
    private byte[] featureData;

    /**
     * 来源
     */
    @TableField("source")
    private String source;
}
