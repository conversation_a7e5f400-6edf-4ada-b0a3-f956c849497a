package com.imile.attendance.infrastructure.repository.hrms.dao.impl;


import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.constants.Constants;
import com.imile.attendance.enums.WhetherEnum;
import com.imile.attendance.infrastructure.repository.hrms.dao.HrmsWarehouseDetailAbnormalDao;
import com.imile.attendance.infrastructure.repository.hrms.mapper.HrmsWarehouseDetailAbnormalMapper;
import com.imile.attendance.infrastructure.repository.hrms.modle.HrmsWarehouseDetailAbnormalDO;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;


/**
 * <p>
 * 仓内统计表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-04
 */
@DS(Constants.TableSchema.hrms)
@Service
public class HrmsWarehouseDetailAbnormalDaoImpl extends ServiceImpl<HrmsWarehouseDetailAbnormalMapper, HrmsWarehouseDetailAbnormalDO> implements HrmsWarehouseDetailAbnormalDao {

    @Override
    public List<HrmsWarehouseDetailAbnormalDO> selectByWarehouseDetailIds(List<Long> warehouseDetailIds) {
        if (CollectionUtils.isEmpty(warehouseDetailIds)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<HrmsWarehouseDetailAbnormalDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(HrmsWarehouseDetailAbnormalDO::getWarehouseDetailId, warehouseDetailIds);
        queryWrapper.eq(HrmsWarehouseDetailAbnormalDO::getFromNewSystem, BusinessConstant.N);
        queryWrapper.eq(HrmsWarehouseDetailAbnormalDO::getIsDelete, WhetherEnum.NO.getKey());
        return super.list(queryWrapper);
    }
}
