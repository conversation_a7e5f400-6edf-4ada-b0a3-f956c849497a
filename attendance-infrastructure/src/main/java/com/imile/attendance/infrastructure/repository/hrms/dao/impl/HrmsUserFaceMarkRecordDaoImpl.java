package com.imile.attendance.infrastructure.repository.hrms.dao.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.imile.attendance.constants.Constants;
import com.imile.attendance.infrastructure.repository.hrms.dao.HrmsUserFaceMarkRecordDao;
import com.imile.attendance.infrastructure.repository.hrms.mapper.HrmsUserFaceMarkRecordMapper;
import com.imile.attendance.infrastructure.repository.hrms.modle.HrmsUserFaceMarkRecordDO;
import com.imile.common.enums.IsDeleteEnum;
import org.springframework.stereotype.Service;

import java.util.List;


/**
 * <AUTHOR>
 * @since 2024/7/5
 */
@DS(Constants.TableSchema.hrms)
@Service
public class HrmsUserFaceMarkRecordDaoImpl extends ServiceImpl<HrmsUserFaceMarkRecordMapper, HrmsUserFaceMarkRecordDO> implements HrmsUserFaceMarkRecordDao {


    @Override
    public List<HrmsUserFaceMarkRecordDO> selectAll() {
        LambdaQueryWrapper<HrmsUserFaceMarkRecordDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(HrmsUserFaceMarkRecordDO::getIsDelete, IsDeleteEnum.NO.getCode());
        return super.list(queryWrapper);
    }
}
