package com.imile.attendance.infrastructure.repository.hrms.dao.impl;


import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.constants.Constants;
import com.imile.attendance.enums.WhetherEnum;
import com.imile.attendance.infrastructure.repository.hrms.dao.HrmsWarehouseRecordDao;
import com.imile.attendance.infrastructure.repository.hrms.mapper.HrmsWarehouseRecordMapper;
import com.imile.attendance.infrastructure.repository.hrms.modle.HrmsWarehouseRecordDO;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * <p>
 * 仓内记录表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-02
 */
@DS(Constants.TableSchema.hrms)
@Service
public class HrmsWarehouseRecordDaoImpl extends ServiceImpl<HrmsWarehouseRecordMapper, HrmsWarehouseRecordDO> implements HrmsWarehouseRecordDao {

    @Override
    public List<HrmsWarehouseRecordDO> selectByWarehouseDetailIds(Collection<Long> warehouseDetailIds) {
        if (Objects.isNull(warehouseDetailIds)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<HrmsWarehouseRecordDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(HrmsWarehouseRecordDO::getWarehouseDetailId, warehouseDetailIds);
        queryWrapper.eq(HrmsWarehouseRecordDO::getFromNewSystem, BusinessConstant.N);
        queryWrapper.eq(HrmsWarehouseRecordDO::getIsDelete, WhetherEnum.NO.getKey());
        return super.list(queryWrapper);
    }
}
