package com.imile.attendance.infrastructure.repository.hrms.dao.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.imile.attendance.constants.Constants;
import com.imile.attendance.infrastructure.repository.hrms.dao.HrmsFaceFeatureEncDao;
import com.imile.attendance.infrastructure.repository.hrms.mapper.HrmsFaceFeatureEncMapper;
import com.imile.attendance.infrastructure.repository.hrms.modle.HrmsFaceFeatureEncDO;
import com.imile.common.enums.IsDeleteEnum;
import org.springframework.stereotype.Service;

import java.util.List;


/**
 * <AUTHOR>
 * @since 2025/7/4
 */
@DS(Constants.TableSchema.hrms)
@Service
public class HrmsFaceFeatureEncDaoImpl extends ServiceImpl<HrmsFaceFeatureEncMapper, HrmsFaceFeatureEncDO> implements HrmsFaceFeatureEncDao {

    @Override
    public List<HrmsFaceFeatureEncDO> selectAll() {
        LambdaQueryWrapper<HrmsFaceFeatureEncDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(HrmsFaceFeatureEncDO::getIsDelete, IsDeleteEnum.NO.getCode());
        return super.list(queryWrapper);
    }
}
