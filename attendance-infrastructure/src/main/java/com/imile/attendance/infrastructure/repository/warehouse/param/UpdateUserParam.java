package com.imile.attendance.infrastructure.repository.warehouse.param;

import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @since 2024/8/12
 */
@Data
public class UpdateUserParam {

    /**
     * 员工id
     */
    @NotNull(message = "userId cannot be empty")
    private Long userId;

    /**
     * 网点编码
     */
    private Long ocId;

    /**
     * 供应商id
     */
    private Long vendorId;

    /**
     * 供应商编码
     */
    @NotNull(message = "vendorCode cannot be empty")
    private String vendorCode;

    /**
     * 用工类型 劳务派遣 正式/挂靠
     */
    @NotNull(message = "employeeType cannot be empty")
    private String employeeType;
}
