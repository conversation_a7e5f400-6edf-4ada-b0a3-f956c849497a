package com.imile.attendance.infrastructure.repository.hrms.modle;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.imile.attendance.base.BaseDO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 仓内考勤初始表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-04
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("hrms_warehouse_detail_original")
public class HrmsWarehouseDetailOriginalDO extends BaseDO {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 考勤流水号
     */
    private String warehouseAttendanceCode;

    /**
     * 国家
     */
    private String country;

    /**
     * 城市
     */
    private String city;

    /**
     * 网点id
     */
    private Long ocId;

    /**
     * 供应商id
     */
    private Long vendorId;

    /**
     * 供应商编码
     */
    private String vendorCode;

    /**
     * 人员id
     */
    private Long userId;

    /**
     * 人员编码
     */
    private String userCode;

    /**
     * 用工类型
     */
    private String employeeType;

    /**
     * 用户所属网点
     * 劳务派遣员工的网点和供应商经常会变
     * 这里存一份快照
     */
    private Long userOcId;

    /**
     * 用户所属供应商ID
     */
    private Long userVendorId;

    /**
     * 用户所属供应商编码
     */
    private String userVendorCode;

    /**
     * 仓内状态（1:待出仓 2:已出仓仓）
     */
    private Integer warehouseStatus;

    /**
     * 仓内日期
     */
    private Date warehouseDate;

    /**
     * 算薪日期
     */
    private Date salaryDate;

    /**
     * 财务推送时间
     */
    private Date syncFinDate;

    /**
     * 驻仓间隔 单位：分钟
     */
    private Long stayDuration;

    /**
     * 班次id
     */
    private Long classesId;

    /**
     * 班次类型
     */
    private Integer classesType;

    /**
     * 班次名称
     */
    private String classesName;

    /**
     * 考勤状态（0:初始值 1:正常 2:异常 3:待配置班次）
     */
    private Integer attendanceStatus;

    /**
     * 出勤类型（P 上班，WEEKEND 周末 ，HOLIDAY 节假日）
     */
    private String attendanceType;

    /**
     * 请假类型
     */
    private String leaveType;

    /**
     * 法定出勤时长时长 单位:小时
     */
    private BigDecimal legalWorkingHours;

    /**
     * 应出勤时长 单位:小时
     */
    private BigDecimal requiredAttendanceTime;

    /**
     * 缺勤时长 单位:小时
     */
    private BigDecimal absenceTime;

    /**
     * 加班时长 单位:小时
     */
    private BigDecimal overtimeHours;

    /**
     * 实际出勤时长 单位:小时
     */
    private BigDecimal actualAttendanceTime;

    /**
     * 仓内实际出勤时长 单位:小时 根据入离仓打卡记录计算得出
     */
    private BigDecimal warehouseActualAttendanceTime;

    /**
     * 请假时长 单位:小时
     */
    private BigDecimal leaveHours;

    /**
     * pcs考勤状态（0:初始化 1:正常 2:异常）
     */
    private Integer pcsStatus;

    /**
     * 工作网点经度
     */
    private BigDecimal ocLongitude;

    /**
     * 工作网点纬度
     */
    private BigDecimal ocLatitude;

    /**
     * 来源新系统同步
     */
    private Integer fromNewSystem;

}
