package com.imile.attendance.infrastructure.repository.warehouse.dao;

import com.baomidou.mybatisplus.extension.service.IService;
import com.imile.attendance.infrastructure.repository.warehouse.model.WarehouseAttendanceConfigDO;
import com.imile.attendance.infrastructure.repository.warehouse.param.WarehouseAttendanceConfigParam;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * 仓内考勤规则表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-30
 */
public interface WarehouseAttendanceConfigDao extends IService<WarehouseAttendanceConfigDO> {


    List<WarehouseAttendanceConfigDO> list(WarehouseAttendanceConfigParam param);

    WarehouseAttendanceConfigDO selectById(Long id);

    WarehouseAttendanceConfigDO selectLatestById(Long id);

    WarehouseAttendanceConfigDO selectActiveById(Long id);

    List<WarehouseAttendanceConfigDO> selectActiveByIds(List<Long> ids);

    List<WarehouseAttendanceConfigDO> selectByCountry(String country);

    WarehouseAttendanceConfigDO selectByDeptId(Long deptId);

    List<WarehouseAttendanceConfigDO> selectByDateRange(Date date, Long deptId);
}
