package com.imile.attendance.infrastructure.repository.warehouse.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.imile.attendance.base.BaseDO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 仓内考勤打卡周期表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-29
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("warehouse_punch_period")
public class WarehousePunchPeriodDO extends BaseDO {


    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 考勤结果表ID
     */
    private Long warehouseDetailId;

    /**
     * 序号
     */
    private Integer sortNo;


    /**
     * 入仓时间
     */
    private Date inTime;

    /**
     * 出仓时间
     */
    private Date outTime;

    /**
     * 考勤时长 单位：分钟
     */
    private BigDecimal attendanceHours;

    /**
     * 打卡状态（1:正常 2:异常）
     */
    private Integer punchStatus;

}
