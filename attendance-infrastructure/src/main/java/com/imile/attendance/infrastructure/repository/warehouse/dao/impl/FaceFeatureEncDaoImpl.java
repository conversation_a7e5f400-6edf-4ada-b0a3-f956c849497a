package com.imile.attendance.infrastructure.repository.warehouse.dao.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.imile.attendance.infrastructure.repository.warehouse.dao.FaceFeatureEncDao;
import com.imile.attendance.infrastructure.repository.warehouse.mapper.FaceFeatureEncMapper;
import com.imile.attendance.infrastructure.repository.warehouse.model.FaceFeatureEncDO;
import com.imile.attendance.infrastructure.repository.warehouse.query.FaceFeatureQuery;
import com.imile.common.enums.IsDeleteEnum;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.List;


/**
 * <AUTHOR>
 * @since 2025/7/4
 */
@Service
public class FaceFeatureEncDaoImpl extends ServiceImpl<FaceFeatureEncMapper, FaceFeatureEncDO> implements FaceFeatureEncDao {

    @Override
    public List<FaceFeatureEncDO> selectByCondition(FaceFeatureQuery query) {
        LambdaQueryWrapper<FaceFeatureEncDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(CollectionUtils.isNotEmpty(query.getUserCodeList()),FaceFeatureEncDO::getUserCode,query.getUserCodeList());
        queryWrapper.in(CollectionUtils.isNotEmpty(query.getCountryList()),FaceFeatureEncDO::getCountry,query.getCountryList());
        queryWrapper.in(CollectionUtils.isNotEmpty(query.getEmployeeTypeList()),FaceFeatureEncDO::getEmployeeType,query.getEmployeeTypeList());
        queryWrapper.eq(FaceFeatureEncDO::getIsDelete, IsDeleteEnum.NO.getCode());
        queryWrapper.orderByAsc(FaceFeatureEncDO::getCreateDate);
        return super.list(queryWrapper);
    }
}
