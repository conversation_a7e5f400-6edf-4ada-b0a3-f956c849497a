package com.imile.attendance.infrastructure.repository.warehouse.dao.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.imile.attendance.infrastructure.repository.warehouse.dao.WarehouseFaceRecordDao;
import com.imile.attendance.infrastructure.repository.warehouse.mapper.WarehouseFaceRecordMapper;
import com.imile.attendance.infrastructure.repository.warehouse.model.WarehouseFaceRecordDO;
import com.imile.common.enums.IsDeleteEnum;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR> 4.0 sonnet
 * @Date 2025-07-09
 * @Description 仓内人脸识别记录表DAO实现类
 */
@Component
@RequiredArgsConstructor
public class WarehouseFaceRecordDaoImpl extends ServiceImpl<WarehouseFaceRecordMapper, WarehouseFaceRecordDO> implements WarehouseFaceRecordDao {

    @Override
    public WarehouseFaceRecordDO selectLastOne(Long userId, Date attendanceTime, Integer faceRecordStatus) {
        if (Objects.isNull(userId) || Objects.isNull(faceRecordStatus)) {
            return null;
        }
        LambdaQueryWrapper<WarehouseFaceRecordDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(WarehouseFaceRecordDO::getUserId, userId);
        queryWrapper.eq(WarehouseFaceRecordDO::getFaceRecordStatus, faceRecordStatus);
        queryWrapper.le(Objects.nonNull(attendanceTime), WarehouseFaceRecordDO::getFaceRecordTime, attendanceTime);
        queryWrapper.eq(WarehouseFaceRecordDO::getIsDelete, IsDeleteEnum.NO.getCode());
        queryWrapper.orderByDesc(WarehouseFaceRecordDO::getFaceRecordTime);
        queryWrapper.last("limit 1");
        return super.getOne(queryWrapper);
    }

    @Override
    public void updateStatusByUserId(Long userId, Date warehouseDate, Integer faceRecordStatus) {
        baseMapper.updateStatusByUserId(userId, warehouseDate, faceRecordStatus);
    }

    @Override
    public List<WarehouseFaceRecordDO> getByIdList(List<Long> faceRecordIdList) {
        if (CollectionUtils.isEmpty(faceRecordIdList)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<WarehouseFaceRecordDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(CollectionUtils.isNotEmpty(faceRecordIdList), WarehouseFaceRecordDO::getId, faceRecordIdList);
        queryWrapper.eq(WarehouseFaceRecordDO::getIsDelete, IsDeleteEnum.NO.getCode());
        return super.list(queryWrapper);
    }
}
