package com.imile.attendance.infrastructure.repository.hrms.dao;


import com.baomidou.mybatisplus.extension.service.IService;
import com.imile.attendance.infrastructure.repository.hrms.modle.HrmsWarehouseDetailDO;
import com.imile.attendance.infrastructure.repository.warehouse.query.WarehouseDetailQuery;

import java.util.List;

/**
 * <p>
 * 仓内统计表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-04
 */
public interface HrmsWarehouseDetailDao extends IService<HrmsWarehouseDetailDO> {


    List<HrmsWarehouseDetailDO> selectPage(WarehouseDetailQuery query);

}
