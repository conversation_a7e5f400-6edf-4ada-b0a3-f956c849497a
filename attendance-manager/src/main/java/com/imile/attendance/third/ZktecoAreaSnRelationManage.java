package com.imile.attendance.third;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.infrastructure.repository.third.dao.ZktecoAreaSnRelationDao;
import com.imile.attendance.infrastructure.repository.third.model.ZktecoAreaSnRelationDO;
import com.imile.attendance.infrastructure.repository.third.query.ZktecoAreaRelationQueryDTO;
import com.imile.common.enums.IsDeleteEnum;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR> chen
 * @Date 2025/3/25 
 * @Description
 */
@Component
public class ZktecoAreaSnRelationManage {

    @Resource
    private ZktecoAreaSnRelationDao zktecoAreaSnRelationDao;

    
    public List<ZktecoAreaSnRelationDO> zktecoAreaRelationList(ZktecoAreaRelationQueryDTO queryDTO) {
        LambdaQueryWrapper<ZktecoAreaSnRelationDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(ZktecoAreaSnRelationDO::getIsDelete, IsDeleteEnum.NO.getCode());
        queryWrapper.eq(ZktecoAreaSnRelationDO::getIsLatest, BusinessConstant.ONE);
        if (StringUtils.isNotBlank(queryDTO.getZktecoAreaName())) {
            queryWrapper.like(ZktecoAreaSnRelationDO::getZktecoAreaName, queryDTO.getZktecoAreaName());
        }
        if (StringUtils.isNotBlank(queryDTO.getTerminalSn())) {
            queryWrapper.like(ZktecoAreaSnRelationDO::getTerminalSn, queryDTO.getTerminalSn());
        }
        if (queryDTO.getDeptId() != null) {
            queryWrapper.like(ZktecoAreaSnRelationDO::getDeptIds, queryDTO.getDeptId().toString());
        }
        if (StringUtils.isNotBlank(queryDTO.getCountry())) {
            queryWrapper.eq(ZktecoAreaSnRelationDO::getCountry, queryDTO.getCountry());
        }
        if (CollectionUtils.isNotEmpty(queryDTO.getCountryList())) {
            queryWrapper.in(ZktecoAreaSnRelationDO::getCountry, queryDTO.getCountryList());
        }
        return zktecoAreaSnRelationDao.list(queryWrapper);
    }

    
    public List<ZktecoAreaSnRelationDO> selectByDeptId(Long deptId) {
        if (deptId == null) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<ZktecoAreaSnRelationDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(ZktecoAreaSnRelationDO::getIsDelete, IsDeleteEnum.NO.getCode());
        queryWrapper.eq(ZktecoAreaSnRelationDO::getIsLatest, BusinessConstant.ONE);
        queryWrapper.like(ZktecoAreaSnRelationDO::getDeptIds, deptId.toString());
        return zktecoAreaSnRelationDao.list(queryWrapper);
    }

    
    public List<ZktecoAreaSnRelationDO> selectByUserId(Long userId) {
        if (userId == null) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<ZktecoAreaSnRelationDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(ZktecoAreaSnRelationDO::getIsDelete, IsDeleteEnum.NO.getCode());
        queryWrapper.eq(ZktecoAreaSnRelationDO::getIsLatest, BusinessConstant.ONE);
        queryWrapper.like(ZktecoAreaSnRelationDO::getUserIds, userId.toString());
        return zktecoAreaSnRelationDao.list(queryWrapper);
    }

    
    public ZktecoAreaSnRelationDO getById(Long id) {
        return zktecoAreaSnRelationDao.getById(id);
    }

    
    public List<ZktecoAreaSnRelationDO> getByAreaIdList(List<Integer> areaIdList) {
        if (CollectionUtils.isEmpty(areaIdList)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<ZktecoAreaSnRelationDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(ZktecoAreaSnRelationDO::getIsDelete, IsDeleteEnum.NO.getCode());
        queryWrapper.eq(ZktecoAreaSnRelationDO::getIsLatest, BusinessConstant.ONE);
        queryWrapper.in(ZktecoAreaSnRelationDO::getZktecoAreaId, areaIdList);
        return zktecoAreaSnRelationDao.list(queryWrapper);
    }

    
    public List<ZktecoAreaSnRelationDO> getNotExistByAreaIdList(List<Integer> areaIdList) {
        if (CollectionUtils.isEmpty(areaIdList)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<ZktecoAreaSnRelationDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(ZktecoAreaSnRelationDO::getIsDelete, IsDeleteEnum.NO.getCode());
        queryWrapper.eq(ZktecoAreaSnRelationDO::getIsLatest, BusinessConstant.ONE);
        queryWrapper.notIn(ZktecoAreaSnRelationDO::getZktecoAreaId, areaIdList);
        return zktecoAreaSnRelationDao.list(queryWrapper);
    }

    
    public void updateById(ZktecoAreaSnRelationDO relationDO) {
        zktecoAreaSnRelationDao.updateById(relationDO);
    }

    
    public void batchInsert(List<ZktecoAreaSnRelationDO> insertList) {
        if (CollectionUtils.isEmpty(insertList)) {
            return;
        }
        zktecoAreaSnRelationDao.saveBatch(insertList);
    }

    
    public void batchUpdate(List<ZktecoAreaSnRelationDO> updateList) {
        if (CollectionUtils.isEmpty(updateList)) {
            return;
        }
        zktecoAreaSnRelationDao.updateBatchById(updateList);
    }

    
    @Transactional(rollbackFor = Exception.class)
    public void relationSaveOrUpdate(List<ZktecoAreaSnRelationDO> insertList, List<ZktecoAreaSnRelationDO> updateList) {
        zktecoAreaSnRelationDao.saveBatch(insertList);
        zktecoAreaSnRelationDao.updateBatchById(updateList);
    }
}
