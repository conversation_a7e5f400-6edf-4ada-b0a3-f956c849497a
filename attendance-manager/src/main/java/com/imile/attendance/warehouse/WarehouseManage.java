package com.imile.attendance.warehouse;




import com.imile.attendance.infrastructure.repository.abnormal.model.AttendanceEmployeeDetailDO;
import com.imile.attendance.infrastructure.repository.abnormal.model.EmployeeAbnormalAttendanceDO;
import com.imile.attendance.infrastructure.repository.abnormal.model.EmployeeAbnormalOperationRecordDO;
import com.imile.attendance.infrastructure.repository.punch.model.EmployeePunchRecordDO;
import com.imile.attendance.infrastructure.repository.warehouse.model.WarehouseDetailAbnormalDO;
import com.imile.attendance.infrastructure.repository.warehouse.model.WarehouseDetailDO;
import com.imile.attendance.infrastructure.repository.warehouse.model.WarehousePunchPeriodDO;
import com.imile.attendance.infrastructure.repository.warehouse.model.WarehouseRecordDO;
import com.imile.attendance.infrastructure.repository.warehouse.model.WarehouseVendorClassesConfirmDO;

import java.util.List;


/**
 * <AUTHOR>
 * @project hrms
 * @description 仓内管理
 * @date 2024/7/1 14:26:15
 */
public interface WarehouseManage {

    void warehouseAttendanceAdd(WarehouseDetailDO warehouseDetailDO,
                                WarehouseRecordDO warehouseRecordDO,
                                EmployeePunchRecordDO punchRecordDO);

    void warehouseAttendanceAbnormalAdd(WarehouseDetailDO warehouseDetailDO,
                                        List<WarehouseDetailAbnormalDO> warehouseDetailAbnormalList);

    void warehouseAttendanceUpdate(WarehouseDetailDO warehouseDetailDO,
                                   boolean updateFaceRecord);

    void warehouseAttendanceUpdate(List<WarehouseDetailDO> warehouseDetailDOList,
                                   List<WarehouseRecordDO> warehouseRecordDOList,
                                   List<WarehouseDetailAbnormalDO> warehouseDetailAbnormalDOList,
                                   List<AttendanceEmployeeDetailDO> attendanceEmployeeDetailDOList,
                                   List<EmployeeAbnormalAttendanceDO> updateAbnormalAttendanceList,
                                   List<EmployeeAbnormalOperationRecordDO> deleteAbnormalOperationRecordList,
                                   List<WarehouseVendorClassesConfirmDO> vendorClassesConfirmDOList,
                                   List<EmployeePunchRecordDO> employeePunchRecordDOList);

    void warehouseAttendanceAbnormalUpdate(WarehouseDetailDO warehouseDetailDO,
                                           List<WarehouseDetailAbnormalDO> warehouseDetailAbnormalList,
                                           List<WarehousePunchPeriodDO> warehousePunchPeriodDOList,
                                           boolean updateFaceRecord);

    void warehouseAttendanceConfirm(List<WarehouseDetailDO> warehouseDetailDOList,
                                    WarehouseVendorClassesConfirmDO warehouseVendorClassesConfirmDO);
}
