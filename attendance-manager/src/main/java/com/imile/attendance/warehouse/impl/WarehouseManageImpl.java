package com.imile.attendance.warehouse.impl;


import com.imile.attendance.abnormal.EmployeeAbnormalAttendanceManage;
import com.imile.attendance.enums.warehouse.FaceRecordStatusEnum;
import com.imile.attendance.infrastructure.repository.abnormal.adapter.AttendanceEmployeeDetailAdapter;
import com.imile.attendance.infrastructure.repository.abnormal.adapter.EmployeeAbnormalOperationRecordAdapter;
import com.imile.attendance.infrastructure.repository.abnormal.adapter.EmployeePunchRecordAdapter;
import com.imile.attendance.infrastructure.repository.abnormal.adapter.WarehouseDetailAbnormalAdapter;
import com.imile.attendance.infrastructure.repository.abnormal.adapter.WarehouseDetailAdapter;
import com.imile.attendance.infrastructure.repository.abnormal.adapter.WarehouseRecordAdapter;
import com.imile.attendance.infrastructure.repository.abnormal.model.AttendanceEmployeeDetailDO;
import com.imile.attendance.infrastructure.repository.abnormal.model.EmployeeAbnormalAttendanceDO;
import com.imile.attendance.infrastructure.repository.abnormal.model.EmployeeAbnormalOperationRecordDO;
import com.imile.attendance.infrastructure.repository.punch.model.EmployeePunchRecordDO;
import com.imile.attendance.infrastructure.repository.warehouse.dao.WarehouseFaceRecordDao;
import com.imile.attendance.infrastructure.repository.warehouse.dao.WarehousePunchPeriodDao;
import com.imile.attendance.infrastructure.repository.warehouse.dao.WarehouseVendorClassesConfirmDao;
import com.imile.attendance.infrastructure.repository.warehouse.model.WarehouseDetailAbnormalDO;
import com.imile.attendance.infrastructure.repository.warehouse.model.WarehouseDetailDO;
import com.imile.attendance.infrastructure.repository.warehouse.model.WarehousePunchPeriodDO;
import com.imile.attendance.infrastructure.repository.warehouse.model.WarehouseRecordDO;
import com.imile.attendance.infrastructure.repository.warehouse.model.WarehouseVendorClassesConfirmDO;
import com.imile.attendance.warehouse.WarehouseManage;
import groovy.util.logging.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @project hrms
 * @description 仓内管理实现类
 * @date 2024/7/1 14:26:35
 */
@Slf4j
@Service
public class WarehouseManageImpl implements WarehouseManage {

    @Resource
    private WarehouseFaceRecordDao faceRecordDao;

    @Resource
    private WarehouseVendorClassesConfirmDao warehouseVendorClassesConfirmDao;

    @Resource
    private WarehousePunchPeriodDao warehousePunchPeriodDao;

    @Resource
    private WarehouseDetailAdapter warehouseDetailAdapter;

    @Resource
    private WarehouseDetailAbnormalAdapter warehouseDetailAbnormalAdapter;

    @Resource
    private WarehouseRecordAdapter warehouseRecordAdapter;

    @Resource
    private EmployeeAbnormalAttendanceManage employeeAbnormalAttendanceManage;

    @Resource
    private AttendanceEmployeeDetailAdapter attendanceEmployeeDetailAdapter;

    @Resource
    private EmployeePunchRecordAdapter employeePunchRecordAdapter;

    @Resource
    private EmployeeAbnormalOperationRecordAdapter employeeAbnormalOperationRecordAdapter;


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void warehouseAttendanceAdd(WarehouseDetailDO warehouseDetailDO,
                                       WarehouseRecordDO warehouseRecordDO,
                                       EmployeePunchRecordDO punchRecordDO) {
        if (Objects.nonNull(warehouseDetailDO)) {
            warehouseDetailAdapter.saveOrUpdate(warehouseDetailDO);
        }
        if (Objects.nonNull(warehouseRecordDO)) {
            warehouseRecordAdapter.saveOrUpdate(warehouseRecordDO);
        }
        if (Objects.nonNull(punchRecordDO)) {
            employeePunchRecordAdapter.save(punchRecordDO);
        }
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void warehouseAttendanceAbnormalAdd(WarehouseDetailDO warehouseDetailDO,
                                               List<WarehouseDetailAbnormalDO> warehouseDetailAbnormalList) {
        if (Objects.nonNull(warehouseDetailDO)) {
            warehouseDetailAdapter.saveOrUpdate(warehouseDetailDO);
        }

        if (CollectionUtils.isNotEmpty(warehouseDetailAbnormalList)) {
            warehouseDetailAbnormalAdapter.saveOrUpdateBatch(warehouseDetailAbnormalList);
        }
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void warehouseAttendanceUpdate(WarehouseDetailDO warehouseDetailDO, boolean updateFaceRecord) {
        if (Objects.isNull(warehouseDetailDO)) {
            return;
        }
        warehouseDetailAdapter.saveOrUpdate(warehouseDetailDO);
        if (updateFaceRecord) {
            faceRecordDao.updateStatusByUserId(warehouseDetailDO.getUserId(), warehouseDetailDO.getWarehouseDate(), FaceRecordStatusEnum.INVALID.getCode());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void warehouseAttendanceUpdate(List<WarehouseDetailDO> warehouseDetailDOList,
                                          List<WarehouseRecordDO> warehouseRecordDOList,
                                          List<WarehouseDetailAbnormalDO> warehouseDetailAbnormalDOList,
                                          List<AttendanceEmployeeDetailDO> attendanceEmployeeDetailDOList,
                                          List<EmployeeAbnormalAttendanceDO> updateAbnormalAttendanceList,
                                          List<EmployeeAbnormalOperationRecordDO> deleteAbnormalOperationRecordList,
                                          List<WarehouseVendorClassesConfirmDO> vendorClassesConfirmDOList,
                                          List<EmployeePunchRecordDO> employeePunchRecordDOList) {
        if (CollectionUtils.isNotEmpty(warehouseDetailDOList)) {
            warehouseDetailAdapter.saveOrUpdateBatch(warehouseDetailDOList);
        }
        if (CollectionUtils.isNotEmpty(warehouseRecordDOList)) {
            warehouseRecordAdapter.saveOrUpdateBatch(warehouseRecordDOList);
        }
        if (CollectionUtils.isNotEmpty(warehouseDetailAbnormalDOList)) {
            warehouseDetailAbnormalAdapter.saveOrUpdateBatch(warehouseDetailAbnormalDOList);
        }
        if (CollectionUtils.isNotEmpty(attendanceEmployeeDetailDOList)) {
            attendanceEmployeeDetailAdapter.updateBatchById(attendanceEmployeeDetailDOList);
        }
        if (CollectionUtils.isNotEmpty(updateAbnormalAttendanceList)) {
            employeeAbnormalAttendanceManage.abnormalBatchUpdate(updateAbnormalAttendanceList);

        }
        if (CollectionUtils.isNotEmpty(deleteAbnormalOperationRecordList)) {
            employeeAbnormalOperationRecordAdapter.updateBatchById(deleteAbnormalOperationRecordList);
        }
        if (CollectionUtils.isNotEmpty(vendorClassesConfirmDOList)) {
            warehouseVendorClassesConfirmDao.updateBatchById(vendorClassesConfirmDOList);
        }
        if (CollectionUtils.isNotEmpty(employeePunchRecordDOList)) {
            employeePunchRecordAdapter.updateBatchById(employeePunchRecordDOList);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void warehouseAttendanceAbnormalUpdate(WarehouseDetailDO warehouseDetailDO,
                                                  List<WarehouseDetailAbnormalDO> warehouseDetailAbnormalList,
                                                  List<WarehousePunchPeriodDO> warehousePunchPeriodDOList,
                                                  boolean updateFaceRecord) {
        if (Objects.isNull(warehouseDetailDO)) {
            return;
        }
        warehouseDetailAdapter.saveOrUpdate(warehouseDetailDO);
        if (updateFaceRecord) {
            faceRecordDao.updateStatusByUserId(warehouseDetailDO.getUserId(), warehouseDetailDO.getWarehouseDate(), FaceRecordStatusEnum.INVALID.getCode());
        }
        if (CollectionUtils.isNotEmpty(warehousePunchPeriodDOList)) {
            //先删后插入
            List<WarehousePunchPeriodDO> warehousePunchPeriodList = warehousePunchPeriodDao.selectByWarehouseDetailIds(warehouseDetailDO.getId());
            if (CollectionUtils.isNotEmpty(warehousePunchPeriodList)) {
                warehousePunchPeriodDao.removeByIds(warehousePunchPeriodList.stream().map(WarehousePunchPeriodDO::getId).collect(Collectors.toList()));
            }
            warehousePunchPeriodDao.saveBatch(warehousePunchPeriodDOList);
        }
        if (CollectionUtils.isNotEmpty(warehouseDetailAbnormalList)) {
            warehouseDetailAbnormalAdapter.removeByWarehouseDetailId(warehouseDetailDO.getId());
            warehouseDetailAbnormalAdapter.saveOrUpdateBatch(warehouseDetailAbnormalList);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void warehouseAttendanceConfirm(List<WarehouseDetailDO> warehouseDetailDOList, WarehouseVendorClassesConfirmDO warehouseVendorClassesConfirmDO) {
        if (Objects.nonNull(warehouseVendorClassesConfirmDO)) {
            warehouseVendorClassesConfirmDao.save(warehouseVendorClassesConfirmDO);
        }
        if (CollectionUtils.isNotEmpty(warehouseDetailDOList)) {
            warehouseDetailAdapter.saveOrUpdateBatch(warehouseDetailDOList);
        }
    }
}
