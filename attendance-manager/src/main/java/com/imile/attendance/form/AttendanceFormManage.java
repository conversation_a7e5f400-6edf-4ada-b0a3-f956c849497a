package com.imile.attendance.form;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.imile.attendance.abnormal.EmployeeAbnormalAttendanceManage;
import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.enums.form.ApplicationFormAttrKeyEnum;
import com.imile.attendance.enums.form.FormStatusEnum;
import com.imile.attendance.enums.form.FormTypeEnum;
import com.imile.attendance.form.dto.AttendanceFormDTO;
import com.imile.attendance.form.bo.AttendanceFormDetailBO;
import com.imile.attendance.infrastructure.form.FormAttrUtils;
import com.imile.attendance.infrastructure.repository.abnormal.adapter.EmployeeAbnormalOperationRecordAdapter;
import com.imile.attendance.infrastructure.repository.abnormal.model.EmployeeAbnormalAttendanceDO;
import com.imile.attendance.infrastructure.repository.abnormal.model.EmployeeAbnormalOperationRecordDO;
import com.imile.attendance.infrastructure.repository.employee.dao.UserLeaveRecordDao;
import com.imile.attendance.infrastructure.repository.employee.dao.UserLeaveStageDetailDao;
import com.imile.attendance.infrastructure.repository.employee.modle.UserLeaveRecordDO;
import com.imile.attendance.infrastructure.repository.employee.modle.UserLeaveStageDetailDO;
import com.imile.attendance.infrastructure.repository.form.dao.AttendanceFormAttrDao;
import com.imile.attendance.infrastructure.repository.form.dao.AttendanceFormDao;
import com.imile.attendance.infrastructure.repository.form.dao.AttendanceFormRelationDao;
import com.imile.attendance.infrastructure.repository.form.dao.UserCycleReissueCardCountDao;
import com.imile.attendance.infrastructure.repository.form.model.AttendanceFormAttrDO;
import com.imile.attendance.infrastructure.repository.form.model.AttendanceFormDO;
import com.imile.attendance.infrastructure.repository.form.model.AttendanceFormRelationDO;
import com.imile.attendance.infrastructure.repository.form.model.UserCycleReissueCardCountDO;
import com.imile.attendance.infrastructure.repository.form.query.ApplicationFormQuery;
import com.imile.attendance.infrastructure.repository.form.query.AttendanceApprovalInfoQuery;
import com.imile.attendance.util.DateHelper;
import com.imile.util.BeanUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR> chen
 * @Date 2025/4/9
 * @Description 请假/外勤等单据manage
 */
@Component
public class AttendanceFormManage {

    @Resource
    private AttendanceFormDao attendanceFormDao;
    @Resource
    private AttendanceFormAttrDao attendanceFormAttrDao;
    @Resource
    private AttendanceFormRelationDao attendanceFormRelationDao;
    @Resource
    private UserCycleReissueCardCountDao userCardConfigDao;
    @Resource
    private UserLeaveStageDetailDao userLeaveStageDetailDao;
    @Resource
    private UserLeaveRecordDao userLeaveRecordDao;
    @Resource
    private EmployeeAbnormalOperationRecordAdapter employeeAbnormalOperationRecordAdapter;
    @Resource
    private EmployeeAbnormalAttendanceManage employeeAbnormalAttendanceManage;

    // --- 拷贝原有HRMS查询方法
    public List<AttendanceFormDO> selectForm(ApplicationFormQuery query) {
        return attendanceFormDao.selectForm(query);
    }


    public List<AttendanceFormDO> selectByIdList(List<Long> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return Collections.emptyList();
        }
        return attendanceFormDao.listByIds(idList);
    }

    public AttendanceFormDetailBO getFormDetailById(Long formId) {
        if (formId == null) {
            return null;
        }
        AttendanceFormDO formDO = attendanceFormDao.getById(formId);
        List<AttendanceFormAttrDO> attrDOList = attendanceFormAttrDao.selectFormAttrByFormIdList(Collections.singletonList(formId));
        List<AttendanceFormRelationDO> relationDOList = attendanceFormRelationDao.selectRelationByFormIdList(Collections.singletonList(formId));
        return AttendanceFormDetailBO.of(
                formDO,
                attrDOList,
                relationDOList
        );
    }

    public AttendanceFormDetailBO getFormDetailByCode(String applicationFormCode) {
        if (StringUtils.isBlank(applicationFormCode)) {
            return null;
        }
        ApplicationFormQuery applicationFormQuery = new ApplicationFormQuery();
        applicationFormQuery.setApplicationFormCode(applicationFormCode);
        List<AttendanceFormDO> applicationFormDOList = attendanceFormDao.selectForm(applicationFormQuery);
        if (CollectionUtils.isEmpty(applicationFormDOList)) {
            return null;
        }
        AttendanceFormDO attendanceFormDO = applicationFormDOList.get(0);
        List<AttendanceFormAttrDO> attrDOList =
                attendanceFormAttrDao.selectFormAttrByFormIdList(Collections.singletonList(attendanceFormDO.getId()));
        List<AttendanceFormRelationDO> relationDOList =
                attendanceFormRelationDao.selectRelationByFormIdList(Collections.singletonList(attendanceFormDO.getId()));
        return AttendanceFormDetailBO.of(
                attendanceFormDO,
                attrDOList,
                relationDOList
        );
    }

    public List<AttendanceFormDO> selectAttendanceApprovalInfo(AttendanceApprovalInfoQuery query) {
        return attendanceFormDao.selectAttendanceApprovalInfo(query);
    }

    public List<AttendanceFormAttrDO> selectFormAttrByFormIdList(List<Long> formIdList) {
        if (CollectionUtils.isEmpty(formIdList)) {
            return new ArrayList<>();
        }
        return attendanceFormAttrDao.selectFormAttrByFormIdList(formIdList);
    }

    public List<AttendanceFormAttrDO> selectFormAttrByFormId(Long formId) {
        return attendanceFormAttrDao.selectFormAttrByFormId(formId);
    }

    public List<AttendanceFormRelationDO> selectRelationByFormIdList(List<Long> formIdList) {
        return attendanceFormRelationDao.selectRelationByFormIdList(formIdList);
    }

    public List<AttendanceFormRelationDO> selectRelationByRelationIdList(List<Long> relationIdList) {
        return attendanceFormRelationDao.selectRelationByRelationIdList(relationIdList);
    }

    // --- manage操作
    @Transactional(rollbackFor = Exception.class)
    public void delete(AttendanceFormDO formDO,
                       List<AttendanceFormRelationDO> relationDOList,
                       List<AttendanceFormAttrDO> formAttrDOList) {
        if (formDO != null) {
            attendanceFormDao.updateById(formDO);
        }
        if (CollectionUtils.isNotEmpty(relationDOList)) {
            attendanceFormRelationDao.updateBatchById(relationDOList);
        }
        if (CollectionUtils.isNotEmpty(formAttrDOList)) {
            attendanceFormAttrDao.updateBatchById(formAttrDOList);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateMqForm(AttendanceFormDO formDO,
                             UserCycleReissueCardCountDO userCardConfigDO,
                             EmployeeAbnormalAttendanceDO abnormalAttendanceDO,
                             List<UserLeaveStageDetailDO> userLeaveStageDetailInfoList,
                             UserLeaveRecordDO userLeaveRecord) {
        if (formDO != null) {
            attendanceFormDao.updateById(formDO);
        }
        if (userCardConfigDO != null) {
            userCardConfigDao.updateById(userCardConfigDO);
        }
        if (abnormalAttendanceDO != null) {
            employeeAbnormalAttendanceManage.abnormalUpdate(abnormalAttendanceDO);
        }
        if (CollUtil.isNotEmpty(userLeaveStageDetailInfoList)) {
            userLeaveStageDetailDao.updateBatchById(userLeaveStageDetailInfoList);
        }
        if (ObjectUtil.isNotNull(userLeaveRecord)) {
            userLeaveRecordDao.save(userLeaveRecord);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateApprovalFormConfirmCycle(List<AttendanceFormDO> updateFormList,
                                               List<AttendanceFormAttrDO> addAttrList,
                                               List<EmployeeAbnormalAttendanceDO> updateAbnormalList,
                                               List<EmployeeAbnormalOperationRecordDO> addRecordList) {
        if (CollectionUtils.isNotEmpty(updateFormList)) {
            attendanceFormDao.updateBatchById(updateFormList);
        }
        if (CollectionUtils.isNotEmpty(addAttrList)) {
            attendanceFormAttrDao.saveBatch(addAttrList);
        }
        if (CollectionUtils.isNotEmpty(updateAbnormalList)) {
            employeeAbnormalAttendanceManage.abnormalBatchUpdate(updateAbnormalList);
        }
        if (CollectionUtils.isNotEmpty(addRecordList)) {
            employeeAbnormalOperationRecordAdapter.saveBatch(addRecordList);
        }
    }

    public List<AttendanceFormDetailBO> listByUserIds(List<Long> userIdList, List<String> formStatusList, List<String> formTypeList) {
        if (CollectionUtils.isEmpty(userIdList)) {
            return Collections.emptyList();
        }
        ApplicationFormQuery applicationFormQuery = new ApplicationFormQuery();
        applicationFormQuery.setUserIdList(userIdList);
        applicationFormQuery.setStatusList(formStatusList);
        applicationFormQuery.setFromTypeList(formTypeList);
        List<AttendanceFormDO> attendanceFormDOList = attendanceFormDao.selectForm(applicationFormQuery);
        if (CollectionUtils.isEmpty(attendanceFormDOList)) {
            return Collections.emptyList();
        }
        List<Long> formIdList = attendanceFormDOList.stream().map(AttendanceFormDO::getId).collect(Collectors.toList());
        Map<Long, List<AttendanceFormAttrDO>> formAttrMap = attendanceFormAttrDao.selectFormAttrByFormIdList(formIdList)
                .stream().collect(Collectors.groupingBy(AttendanceFormAttrDO::getFormId));
        List<AttendanceFormDetailBO> result = new ArrayList<>();
        for (AttendanceFormDO attendanceFormDO : attendanceFormDOList) {
            AttendanceFormDetailBO attendanceFormDetailBO = AttendanceFormDetailBO.of(attendanceFormDO, formAttrMap.get(attendanceFormDO.getId()), null);
            result.add(attendanceFormDetailBO);
        }
        return result;
    }

    /**
     * 过滤请假或外勤单据中有效的单据ID
     * 跟考勤的开始结束时间有部分交集就算
     *
     * @param startDayId
     * @param endDayId
     * @param attendanceFormDetailBOList
     * @return
     */
    public List<AttendanceFormDO> getEffectFormList(Long startDayId, Long endDayId,
                                                    List<AttendanceFormDetailBO> attendanceFormDetailBOList) {
        List<AttendanceFormDO> effectFormList = new ArrayList<>();
        //查询请假周期包含当天的审批通过单据，并且没有被销假(这里不能用当天，要用周期，因为如果班次跨天，如果请了第二天的假，也是算前一天的)
        for (AttendanceFormDetailBO formDO : attendanceFormDetailBOList) {
            List<AttendanceFormAttrDO> formAttrDOList = formDO.getAttrDOList();
            if (CollectionUtils.isEmpty(formAttrDOList)) {
                continue;
            }
            List<AttendanceFormAttrDO> isRevokeDO = formAttrDOList.stream()
                    .filter(item -> StringUtils.equalsIgnoreCase(item.getAttrKey(), ApplicationFormAttrKeyEnum.isRevoke.getLowerCode()))
                    .collect(Collectors.toList());

            if (CollectionUtils.isNotEmpty(isRevokeDO)
                    && StringUtils.isNotBlank(isRevokeDO.get(0).getAttrValue())
                    && isRevokeDO.get(0).getAttrValue().equals(BusinessConstant.Y.toString())) {
                continue;
            }
            List<AttendanceFormAttrDO> leaveStartDateDO = formAttrDOList.stream()
                    .filter(item -> StringUtils.equalsIgnoreCase(item.getAttrKey(), ApplicationFormAttrKeyEnum.leaveStartDate.getLowerCode()))
                    .collect(Collectors.toList());

            List<AttendanceFormAttrDO> leaveEndDateDO = formAttrDOList.stream()
                    .filter(item -> StringUtils.equalsIgnoreCase(item.getAttrKey(), ApplicationFormAttrKeyEnum.leaveEndDate.getLowerCode()))
                    .collect(Collectors.toList());

            List<AttendanceFormAttrDO> outOfOfficeStartDateDO = formAttrDOList.stream()
                    .filter(item -> StringUtils.equalsIgnoreCase(item.getAttrKey(), ApplicationFormAttrKeyEnum.outOfOfficeStartDate.getLowerCode()))
                    .collect(Collectors.toList());

            List<AttendanceFormAttrDO> outOfOfficeEndDateDO = formAttrDOList.stream()
                    .filter(item -> StringUtils.equalsIgnoreCase(item.getAttrKey(), ApplicationFormAttrKeyEnum.outOfOfficeEndDate.getLowerCode()))
                    .collect(Collectors.toList());

            AttendanceFormDO attendanceFormDO = formDO.getFormDO();
            //查询请假时间是不是包含本次周期
            //有交集就可以
            if (CollectionUtils.isNotEmpty(leaveStartDateDO) && CollectionUtils.isNotEmpty(leaveEndDateDO)) {
                Long leaveStartDayId = DateHelper.getDayId(DateHelper.parseYYYYMMDDHHMMSS(leaveStartDateDO.get(0).getAttrValue()));
                Long leaveEndDayId = DateHelper.getDayId(DateHelper.parseYYYYMMDDHHMMSS(leaveEndDateDO.get(0).getAttrValue()));
                if (leaveEndDayId.compareTo(startDayId) < 0 || leaveStartDayId.compareTo(endDayId) > 0) {
                    continue;
                }
                effectFormList.add(attendanceFormDO);
            }
            if (CollectionUtils.isNotEmpty(outOfOfficeStartDateDO) && CollectionUtils.isNotEmpty(outOfOfficeEndDateDO)) {
                Long outOfOfficeStartDate = DateHelper.getDayId(DateHelper.parseYYYYMMDDHHMMSS(outOfOfficeStartDateDO.get(0).getAttrValue()));
                Long outOfOfficeEndDate = DateHelper.getDayId(DateHelper.parseYYYYMMDDHHMMSS(outOfOfficeEndDateDO.get(0).getAttrValue()));
                if (outOfOfficeEndDate.compareTo(startDayId) < 0 || outOfOfficeStartDate.compareTo(endDayId) > 0) {
                    continue;
                }
                effectFormList.add(attendanceFormDO);
            }
        }
        return effectFormList;
    }

    public List<AttendanceFormDO> getEffectReissueCardFormList(Long attendanceDayId,
                                                               List<AttendanceFormDetailBO> attendanceFormDetailBOList) {
        List<AttendanceFormDO> effectFormList = new ArrayList<>();
        for (AttendanceFormDetailBO formDO : attendanceFormDetailBOList) {
            List<AttendanceFormAttrDO> formAttrDOList = formDO.getAttrDOList();
            if (CollectionUtils.isEmpty(formAttrDOList)) {
                continue;
            }
            List<AttendanceFormAttrDO> isRevokeDO = formAttrDOList.stream()
                    .filter(item -> StringUtils.equalsIgnoreCase(item.getAttrKey(), ApplicationFormAttrKeyEnum.isRevoke.getLowerCode()))
                    .collect(Collectors.toList());

            if (CollectionUtils.isNotEmpty(isRevokeDO)
                    && StringUtils.isNotBlank(isRevokeDO.get(0).getAttrValue())
                    && isRevokeDO.get(0).getAttrValue().equals(BusinessConstant.Y.toString())) {
                continue;
            }

            AttendanceFormDO attendanceFormDO = formDO.getFormDO();

            if (Objects.equals(FormTypeEnum.REISSUE_CARD.getCode(), attendanceFormDO.getFormType())) {
                List<AttendanceFormAttrDO> reissueCardDayIdAttr = formAttrDOList.stream()
                        .filter(item -> StringUtils.equalsIgnoreCase(item.getAttrKey(), ApplicationFormAttrKeyEnum.reissueCardDayId.getLowerCode()))
                        .collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(reissueCardDayIdAttr)) {
                    Long reissueCardDayId = Long.valueOf(reissueCardDayIdAttr.get(0).getAttrValue());
                    if (reissueCardDayId.compareTo(attendanceDayId) == 0) {
                        effectFormList.add(attendanceFormDO);
                    }
                }
            }
        }
        return effectFormList;
    }

    /**
     * 获取指定日期的审批中/审批通过单据信息
     * @param userId
     * @param dayId
     * @return
     */
    public List<AttendanceFormDTO> getEffectForm(Long userId, Long dayId) {
        ApplicationFormQuery formQuery = ApplicationFormQuery.builder()
                .userId(userId)
                .fromTypeList(FormTypeEnum.getAttendanceCodeList())
                .statusList(FormStatusEnum.getAttendanceCodeList())
                .build();
        List<AttendanceFormDO> formDOList = this.selectForm(formQuery);
        if (CollectionUtils.isEmpty(formDOList)) {
            return Collections.emptyList();
        }
        List<Long> formIdList = formDOList.stream()
                .map(AttendanceFormDO::getId)
                .collect(Collectors.toList());
        List<AttendanceFormAttrDO> formAttrDOListAll = this.selectFormAttrByFormIdList(formIdList);
        Map<Long, List<AttendanceFormAttrDO>> passFormAttrMap = formAttrDOListAll.stream()
                .collect(Collectors.groupingBy(AttendanceFormAttrDO::getFormId));
        List<AttendanceFormDTO> effectFormList = new ArrayList<>();
        for (AttendanceFormDO formDO : formDOList) {
            List<AttendanceFormAttrDO> formAttrDOList = passFormAttrMap.get(formDO.getId());
            if (CollectionUtils.isEmpty(formAttrDOList)) {
                continue;
            }
            AttendanceFormAttrDO isRevoke = FormAttrUtils.getFormAttr(formAttrDOList, ApplicationFormAttrKeyEnum.isRevoke);
            if (Objects.nonNull(isRevoke) && StringUtils.isNotBlank(isRevoke.getAttrValue()) &&
                    isRevoke.getAttrValue().equals(BusinessConstant.Y.toString())) {
                continue;
            }
            AttendanceFormAttrDO leaveStartDateAttr = FormAttrUtils.getFormAttr(formAttrDOList, ApplicationFormAttrKeyEnum.leaveStartDate);
            AttendanceFormAttrDO leaveEndDateAttr = FormAttrUtils.getFormAttr(formAttrDOList, ApplicationFormAttrKeyEnum.leaveEndDate);
            AttendanceFormAttrDO reissueCardDayIdAttr = FormAttrUtils.getFormAttr(formAttrDOList, ApplicationFormAttrKeyEnum.reissueCardDayId);
            AttendanceFormAttrDO outOfOfficeStartDateAttr = FormAttrUtils.getFormAttr(formAttrDOList, ApplicationFormAttrKeyEnum.outOfOfficeStartDate);
            AttendanceFormAttrDO outOfOfficeEndDateAttr = FormAttrUtils.getFormAttr(formAttrDOList, ApplicationFormAttrKeyEnum.outOfOfficeEndDate);
            //查询请假时间是不是包含本次周期
            if (Objects.nonNull(leaveStartDateAttr) && Objects.nonNull(leaveEndDateAttr)) {
                Long leaveStartDayId = DateHelper.getDayId(DateHelper.parseYYYYMMDDHHMMSS(leaveStartDateAttr.getAttrValue()));
                Long leaveEndDayId = DateHelper.getDayId(DateHelper.parseYYYYMMDDHHMMSS(leaveEndDateAttr.getAttrValue()));
                if (leaveEndDayId.compareTo(dayId) < 0 || leaveStartDayId.compareTo(dayId) > 0) {
                    continue;
                }
                //结束时间不能为当天的最早时间
                Date attendanceDate = DateHelper.transferDayIdToDate(dayId);
                DateTime beginOfDay = DateUtil.beginOfDay(attendanceDate);
                if (DateHelper.parseYYYYMMDDHHMMSS(leaveEndDateAttr.getAttrValue()).compareTo(beginOfDay) == 0) {
                    continue;
                }
                AttendanceFormDTO formDTO = BeanUtils.convert(formDO, AttendanceFormDTO.class);
                formDTO.setLeaveStartDate(leaveStartDateAttr.getAttrValue());
                formDTO.setLeaveEndDate(leaveEndDateAttr.getAttrValue());
                effectFormList.add(formDTO);
            }
            if (Objects.nonNull(outOfOfficeStartDateAttr) && Objects.nonNull(outOfOfficeEndDateAttr)) {
                Long outOfOfficeStartDayId = DateHelper.getDayId(DateHelper.parseYYYYMMDDHHMMSS(outOfOfficeStartDateAttr.getAttrValue()));
                Long outOfOfficeEndDayId = DateHelper.getDayId(DateHelper.parseYYYYMMDDHHMMSS(outOfOfficeEndDateAttr.getAttrValue()));
                if (outOfOfficeEndDayId.compareTo(dayId) < 0 || outOfOfficeStartDayId.compareTo(dayId) > 0) {
                    continue;
                }
                AttendanceFormDTO formDTO = BeanUtils.convert(formDO, AttendanceFormDTO.class);
                formDTO.setOutOfOfficeStartDate(outOfOfficeStartDateAttr.getAttrValue());
                formDTO.setOutOfOfficeEndDate(outOfOfficeEndDateAttr.getAttrValue());
                effectFormList.add(formDTO);
            }
            if (Objects.nonNull(reissueCardDayIdAttr)) {
                Long reissueCardDayId = Long.valueOf(reissueCardDayIdAttr.getAttrValue());
                if (dayId.equals(reissueCardDayId)) {
                    AttendanceFormDTO formDTO = BeanUtils.convert(formDO, AttendanceFormDTO.class);
                    formDTO.setReissueCardDayId(reissueCardDayId);
                    effectFormList.add(formDTO);
                }
            }
        }
        return effectFormList;
    }
}
