package com.imile.attendance.rule.impl;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.google.common.collect.Lists;
import com.imile.attendance.enums.rule.PunchConfigTypeEnum;
import com.imile.attendance.infrastructure.repository.rule.dao.PunchClassConfigDao;
import com.imile.attendance.infrastructure.repository.rule.dao.PunchClassConfigRangeDao;
import com.imile.attendance.infrastructure.repository.rule.dto.RuleConfigModifyDTO;
import com.imile.attendance.infrastructure.repository.rule.dto.UserDayConfigDTO;
import com.imile.attendance.infrastructure.repository.rule.query.PunchConfigRangeByDateQuery;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import com.imile.attendance.infrastructure.repository.common.AttendanceUserService;
import com.imile.attendance.infrastructure.repository.common.dto.AttendanceUser;
import com.imile.attendance.infrastructure.repository.rule.dao.PunchConfigDao;
import com.imile.attendance.infrastructure.repository.rule.dao.PunchConfigRangeDao;
import com.imile.attendance.infrastructure.repository.rule.model.PunchConfigDO;
import com.imile.attendance.infrastructure.repository.rule.model.PunchConfigRangeDO;
import com.imile.attendance.rule.PunchConfigManage;
import com.imile.attendance.rule.bo.CountryPunchConfig;
import com.imile.attendance.rule.bo.PunchConfigBO;

/**
 * <AUTHOR> chen
 * @Date 2025/4/8
 * @Description
 */
@Component
public class PunchConfigManageImpl implements PunchConfigManage {

    @Resource
    private PunchConfigDao punchConfigDao;
    @Resource
    private PunchConfigRangeDao punchConfigRangeDao;
    @Resource
    private AttendanceUserService userService;

    @Override
    @Transactional
    public void configRangeUpdateOrAdd(List<PunchConfigRangeDO> updateList, List<PunchConfigRangeDO> addList) {
        if (CollectionUtils.isNotEmpty(updateList)) {
            punchConfigRangeDao.updateBatchById(updateList);
        }
        if (CollectionUtils.isNotEmpty(addList)) {
            punchConfigRangeDao.saveBatch(addList);
        }
    }

    @Override
    @Transactional
    public void configUpdateAndAdd(PunchConfigDO updateConfig, PunchConfigDO addConfig) {
        if (null != updateConfig) {
            punchConfigDao.updateById(updateConfig);
        }
        if (null != addConfig) {
            punchConfigDao.save(addConfig);
        }
    }

    @Override
    @Transactional
    public void configUpdateAndAdd(PunchConfigDO updateConfig, PunchConfigDO addConfig,
                                   List<PunchConfigRangeDO> updatedConfigRanges, List<PunchConfigRangeDO> addConfigRanges) {
        if (null != updateConfig) {
            punchConfigDao.updateById(updateConfig);
        }
        if (null != addConfig) {
            punchConfigDao.save(addConfig);
        }
        if (CollectionUtils.isNotEmpty(updatedConfigRanges)) {
            punchConfigRangeDao.updateBatchById(updatedConfigRanges);
        }
        if (CollectionUtils.isNotEmpty(addConfigRanges)) {
            punchConfigRangeDao.saveBatch(addConfigRanges);
        }
    }

    @Override
    public PunchConfigDO getPunchConfigById(Long punchConfigId) {
        return punchConfigDao.getById(punchConfigId);
    }

    @Override
    public List<PunchConfigDO> getPunchConfigByIds(List<Long> punchConfigIdList) {
        if (CollectionUtils.isEmpty(punchConfigIdList)) {
            return new ArrayList<>();
        }

        return punchConfigDao.listByConfigIds(punchConfigIdList);
    }

    @Override
    public PunchConfigBO getPunchConfigBO(String configNo) {
        if (StringUtils.isEmpty(configNo)) {
            return null;
        }
        PunchConfigDO punchConfigDO = punchConfigDao.getLatestByConfigNo(configNo);
        if (null == punchConfigDO) {
            return null;
        }
        List<PunchConfigRangeDO> punchConfigRangeDOS = punchConfigRangeDao.listByConfigId(punchConfigDO.getId());
        return PunchConfigBO.of(punchConfigDO, punchConfigRangeDOS);
    }

    @Override
    public CountryPunchConfig getCountryConfig(String country) {
        if (StringUtils.isEmpty(country)) {
            return CountryPunchConfig.empty();
        }
        List<PunchConfigDO> countryPunchConfigs = punchConfigDao.getByCountry(country);
        return CountryPunchConfig.of(country, countryPunchConfigs);
    }

    @Override
    public List<CountryPunchConfig> getCountryConfigList(List<String> countries) {
        List<CountryPunchConfig> list = new ArrayList<>();
        if (CollectionUtils.isEmpty(countries)) {
            return list;
        }
        List<PunchConfigDO> countryPunchConfigs = punchConfigDao.getByCountries(countries);
        if (CollectionUtils.isEmpty(countryPunchConfigs)) {
            return list;
        }
        Map<String, List<PunchConfigDO>> countryPunchConfigMap = countryPunchConfigs.stream()
                .collect(Collectors.groupingBy(PunchConfigDO::getCountry));
        countryPunchConfigMap.forEach((country, configList) -> {
            list.add(CountryPunchConfig.of(country, configList));
        });
        return list;
    }


    @Override
    public Map<Long, PunchConfigDO> getConfigMapByUserIdList(List<Long> userIds) {
        if (CollectionUtils.isEmpty(userIds)) {
            return Collections.emptyMap();
        }
        // 查询用户
        List<AttendanceUser> users = userService.listUsersByIds(userIds);
        if (CollectionUtils.isEmpty(users)) {
            return Collections.emptyMap();
        }
        Map<Long, PunchConfigDO> userConfigMap = new HashMap<>();
        // 查询用户是否在范围表里
        List<PunchConfigRangeDO> punchConfigRangeDOS = punchConfigRangeDao.listConfigRanges(userIds);
        if (CollectionUtils.isEmpty(punchConfigRangeDOS)) {
            return userConfigMap;
        }

        // 查询<userId, PunchConfigRangeDO>
        Map<Long, PunchConfigRangeDO> rangeMap = punchConfigRangeDOS.stream()
                .collect(Collectors.toMap(PunchConfigRangeDO::getBizId, Function.identity(), (a, b) -> a));

        // 查询<ruleConfigId, PunchConfigDO>
        Map<Long, PunchConfigDO> configMap = punchConfigDao.listLatestByConfigIds(
                punchConfigRangeDOS.stream()
                        .map(PunchConfigRangeDO::getRuleConfigId)
                        .distinct()
                        .collect(Collectors.toList()))
                .stream()
                .collect(Collectors.toMap(PunchConfigDO::getId, Function.identity(), (a, b) -> a));

        // 查询在配置范围内的用户
        List<Long> inRangeUserIdList = new ArrayList<>(rangeMap.keySet());
        inRangeUserIdList.forEach(inRangeUserId -> {
            userConfigMap.put(inRangeUserId, configMap.get(rangeMap.get(inRangeUserId).getRuleConfigId()));
        });
        return userConfigMap;
    }

    @Override
    public Map<Long, PunchConfigDO> mapByUserIds(List<Long> userIds, Date endDate) {
        if (CollectionUtils.isEmpty(userIds)) {
            return Collections.emptyMap();
        }
        long endDateTimeStamp = endDate.getTime();
        List<PunchConfigRangeDO> punchConfigRangeDOList = punchConfigRangeDao.listAllRangeByUserIds(userIds)
                .stream()
                .filter(item -> item.getEffectTimestamp().compareTo(endDateTimeStamp) < 1 &&
                        item.getExpireTimestamp().compareTo(endDateTimeStamp) > -1)
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(punchConfigRangeDOList)) {
            return Collections.emptyMap();
        }

        Map<Long, PunchConfigRangeDO> rangeMap = punchConfigRangeDOList.stream()
                .collect(Collectors.toMap(PunchConfigRangeDO::getBizId, Function.identity(), (a, b) -> a));


        Map<Long, PunchConfigDO> punchConfigMap = punchConfigDao.listByConfigIds(punchConfigRangeDOList.stream()
                        .map(PunchConfigRangeDO::getRuleConfigId)
                        .distinct()
                        .collect(Collectors.toList()))
                .stream()
                .collect(Collectors.toMap(PunchConfigDO::getId, Function.identity(), (a, b) -> a));

        Map<Long, PunchConfigDO> userConfigMap = new HashMap<>();

        for (Map.Entry<Long, PunchConfigRangeDO> rangeEntry : rangeMap.entrySet()) {
            Long userId = rangeEntry.getKey();
            PunchConfigRangeDO punchConfigRangeDO = rangeEntry.getValue();
            if (Objects.isNull(punchConfigMap.get(punchConfigRangeDO.getRuleConfigId()))) {
                continue;
            }
            userConfigMap.put(userId, punchConfigMap.get(punchConfigRangeDO.getRuleConfigId()));
        }
        return userConfigMap;
    }


    @Override
    public List<RuleConfigModifyDTO> selectAllByBizId(Long bizId) {
        List<PunchConfigRangeDO> punchConfigRangeDOList = punchConfigRangeDao.listAllConfigRanges(bizId);
        if (CollectionUtils.isEmpty(punchConfigRangeDOList)) {
            return Collections.emptyList();
        }
        List<Long> ruleConfigIdList = punchConfigRangeDOList.stream().map(PunchConfigRangeDO::getRuleConfigId).distinct().collect(Collectors.toList());
        Map<Long, PunchConfigDO> punchConfigMap = punchConfigDao.listByConfigIds(ruleConfigIdList)
                .stream().collect(Collectors.toMap(PunchConfigDO::getId, Function.identity()));
        return punchConfigRangeDOList.stream().map(range->{
            RuleConfigModifyDTO modifyDTO = new RuleConfigModifyDTO();
            PunchConfigDO punchConfigDO = punchConfigMap.getOrDefault(range.getRuleConfigId(),new PunchConfigDO());
            modifyDTO.setCountry(punchConfigDO.getCountry());
            modifyDTO.setRuleName(punchConfigDO.getConfigName());
            modifyDTO.setStartDate(range.getEffectTime());
            modifyDTO.setEndDate(range.getExpireTime());
            modifyDTO.setCreateUserName(range.getCreateUserName());
            return modifyDTO;
        }).collect(Collectors.toList());
    }

    @Override
    public PunchConfigDO getByUserIdAndDate(Long userId, Date endDate) {
        Map<Long, PunchConfigDO> userConfigMap = this.mapByUserIds(Arrays.asList(userId), endDate);
        if (Objects.isNull(userConfigMap)) {
            return null;
        }
        PunchConfigDO punchConfig = userConfigMap.get(userId);
        return punchConfig;
    }

    @Override
    public Boolean selectIfNeedPunchByUserIds(Long userId, Date endDate) {
        PunchConfigDO punchConfig = this.getByUserIdAndDate(userId, endDate);
        if (Objects.nonNull(punchConfig)
                && PunchConfigTypeEnum.NO_NEED_PUNCH_WORK.getCode().equals(punchConfig.getConfigType())) {
            return true;
        }
        return false;
    }

    @Override
    public List<UserDayConfigDTO> selectDayConfigByUserIdList(List<Long> userIdList,
                                                              Date startDate, Date endDate) {
        if (CollectionUtils.isEmpty(userIdList)) {
            return Lists.newArrayList();
        }

        Long beforeDayId = Long.valueOf(DateUtil.format(startDate, "yyyyMMdd"));
        Long afterDayId = Long.valueOf(DateUtil.format(endDate, "yyyyMMdd"));

        List<UserDayConfigDTO> userDayConfigDTOList = Lists.newArrayList();
        PunchConfigRangeByDateQuery query = PunchConfigRangeByDateQuery
                .builder()
                .userIds(userIdList)
                .startDate(startDate.getTime())
                .endDate(endDate.getTime())
                .build();
        List<PunchConfigRangeDO> punchConfigRangeList = punchConfigRangeDao.selectConfigRangeByDate(query);

        if (CollectionUtils.isEmpty(punchConfigRangeList)) {
            return Lists.newArrayList();
        }
        // 打卡范围通过用户分组
        Map<Long, List<PunchConfigRangeDO>> rangeMap = punchConfigRangeList
                .stream()
                .collect(Collectors.groupingBy(PunchConfigRangeDO::getBizId));
        // 查询打卡规则
        Map<Long, PunchConfigDO> punchConfigMap = punchConfigDao.listByConfigIds(punchConfigRangeList.stream()
                        .map(PunchConfigRangeDO::getRuleConfigId)
                        .distinct()
                        .collect(Collectors.toList()))
                .stream()
                .collect(Collectors.toMap(PunchConfigDO::getId, Function.identity(), (a, b) -> a));
        // 遍历dayId 找出对应dayId得打卡规则
        Long tempDayId = beforeDayId;
        while (tempDayId <= afterDayId) {
            DateTime date = DateUtil.endOfDay(DateUtil.parse(tempDayId.toString(), DatePattern.PURE_DATE_PATTERN));
            for (Map.Entry<Long, List<PunchConfigRangeDO>> entry : rangeMap.entrySet()) {
                Long userId = entry.getKey();
                List<PunchConfigRangeDO> configRange = entry.getValue();
                if (CollectionUtils.isEmpty(configRange)) {
                    continue;
                }
                List<PunchConfigRangeDO> filterRange = configRange.stream()
                        .filter(item -> item.getEffectTime().compareTo(date) < 1
                                && item.getExpireTime().compareTo(date) > -1)
                        .collect(Collectors.toList());
                if (CollectionUtils.isEmpty(filterRange)) {
                    continue;
                }
                PunchConfigDO punchConfigDO = punchConfigMap.get(filterRange.get(0).getRuleConfigId());
                if (Objects.isNull(punchConfigDO)) {
                    continue;
                }
                // 构建实体
                userDayConfigDTOList.add(UserDayConfigDTO.builder()
                        .userId(userId)
                        .dayId(tempDayId)
                        .punchConfigDO(punchConfigDO)
                        .build());
            }
            tempDayId = Long.valueOf(DateUtil.format(DateUtil.offsetDay(DateUtil.parse(tempDayId.toString()), 1), "yyyyMMdd"));
        }

        return userDayConfigDTOList;
    }

}
