package com.imile.attendance.abnormal;

import com.imile.attendance.enums.abnormal.AbnormalAttendanceStatusEnum;
import com.imile.attendance.infrastructure.repository.abnormal.adapter.AttendanceEmployeeDetailAdapter;
import com.imile.attendance.infrastructure.repository.abnormal.adapter.EmployeeAbnormalAttendanceAdapter;
import com.imile.attendance.infrastructure.repository.abnormal.adapter.EmployeeAbnormalAttendanceSnapshotAdapter;
import com.imile.attendance.infrastructure.repository.abnormal.adapter.EmployeeAbnormalOperationRecordAdapter;
import com.imile.attendance.infrastructure.repository.abnormal.adapter.WarehouseDetailAbnormalAdapter;
import com.imile.attendance.infrastructure.repository.abnormal.adapter.WarehouseDetailAdapter;
import com.imile.attendance.infrastructure.repository.abnormal.adapter.mapstruct.EmployeeAbnormalAttendanceMapstruct;
import com.imile.attendance.infrastructure.repository.abnormal.adapter.mapstruct.EmployeeAbnormalAttendanceSnapshotMapstruct;
import com.imile.attendance.infrastructure.repository.abnormal.dao.EmployeeAbnormalAttendanceDao;
import com.imile.attendance.infrastructure.repository.abnormal.dao.EmployeeAbnormalAttendanceSnapshotDao;
import com.imile.attendance.infrastructure.repository.abnormal.dao.EmployeeAbnormalOperationRecordDao;
import com.imile.attendance.infrastructure.repository.abnormal.model.AttendanceEmployeeDetailDO;
import com.imile.attendance.infrastructure.repository.abnormal.model.EmployeeAbnormalAttendanceDO;
import com.imile.attendance.infrastructure.repository.abnormal.model.EmployeeAbnormalAttendanceSnapshotDO;
import com.imile.attendance.infrastructure.repository.abnormal.model.EmployeeAbnormalOperationRecordDO;
import com.imile.attendance.infrastructure.repository.form.dao.AttendanceFormAttrDao;
import com.imile.attendance.infrastructure.repository.form.dao.AttendanceFormDao;
import com.imile.attendance.infrastructure.repository.form.model.AttendanceFormAttrDO;
import com.imile.attendance.infrastructure.repository.form.model.AttendanceFormDO;
import com.imile.attendance.infrastructure.repository.hrms.modle.HrmsEmployeeAbnormalAttendanceDO;
import com.imile.attendance.infrastructure.repository.hrms.modle.HrmsEmployeeAbnormalAttendanceSnapshotDO;
import com.imile.attendance.infrastructure.repository.migration.dao.MappingPunchClassConfigDao;
import com.imile.attendance.infrastructure.repository.migration.dao.MappingPunchClassConfigItemDao;
import com.imile.attendance.infrastructure.repository.migration.dao.MappingRuleConfigDao;
import com.imile.attendance.infrastructure.repository.migration.model.MappingPunchClassConfigDO;
import com.imile.attendance.infrastructure.repository.migration.model.MappingPunchClassConfigItemDO;
import com.imile.attendance.infrastructure.repository.migration.model.MappingRuleConfigDO;
import com.imile.attendance.infrastructure.repository.warehouse.model.WarehouseDetailAbnormalDO;
import com.imile.attendance.infrastructure.repository.warehouse.model.WarehouseDetailDO;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2025/5/19
 */
@Component
public class EmployeeAbnormalAttendanceManage {

    @Resource
    private EmployeeAbnormalAttendanceDao employeeAbnormalAttendanceDao;
    @Resource
    private EmployeeAbnormalOperationRecordDao employeeAbnormalOperationRecordDao;
    @Resource
    private AttendanceFormDao attendanceFormDao;
    @Resource
    private AttendanceFormAttrDao attendanceFormAttrDao;
    @Resource
    private AttendanceEmployeeDetailAdapter attendanceEmployeeDetailAdapter;
    @Resource
    private EmployeeAbnormalAttendanceAdapter employeeAbnormalAttendanceAdapter;
    @Resource
    private EmployeeAbnormalAttendanceSnapshotAdapter employeeAbnormalAttendanceSnapshotAdapter;
    @Resource
    private EmployeeAbnormalOperationRecordAdapter employeeAbnormalOperationRecordAdapter;
    @Resource
    private EmployeeAbnormalAttendanceSnapshotDao employeeAbnormalAttendanceSnapshotDao;
    @Resource
    private MappingPunchClassConfigDao mappingPunchClassConfigDao;
    @Resource
    private MappingRuleConfigDao mappingRuleConfigDao;
    @Resource
    private MappingPunchClassConfigItemDao mappingPunchClassConfigItemDao;
    @Resource
    private WarehouseDetailAdapter warehouseDetailAdapter;
    @Resource
    private WarehouseDetailAbnormalAdapter warehouseDetailAbnormalAdapter;

    public Map<Long, List<EmployeeAbnormalAttendanceDO>> mapByUserIdsAndDayIds(List<Long> userIdList, List<Long> dayIdList) {
        List<EmployeeAbnormalAttendanceDO> abnormalAttendanceDOList = employeeAbnormalAttendanceDao.selectAbnormalAttendanceByDayIdList(userIdList, dayIdList)
                .stream()
                .filter(item -> !AbnormalAttendanceStatusEnum.TYPE_OF_PASS_OR_EXPIRED.contains(item.getStatus()))
                .collect(Collectors.toList());
        return abnormalAttendanceDOList.stream().collect(Collectors.groupingBy(EmployeeAbnormalAttendanceDO::getUserId));
    }

    public Map<Long, List<EmployeeAbnormalAttendanceDO>> mapAllByUserIdsAndDayIds(List<Long> userIdList, List<Long> dayIdList) {
        List<EmployeeAbnormalAttendanceDO> abnormalAttendanceDOList = employeeAbnormalAttendanceDao.selectAbnormalAttendanceByDayIdList(userIdList, dayIdList)
                .stream()
                .collect(Collectors.toList());
        return abnormalAttendanceDOList.stream().collect(Collectors.groupingBy(EmployeeAbnormalAttendanceDO::getUserId));
    }

    /**
     * 查询用户当前考勤日的所有异常考勤
     */
    public List<EmployeeAbnormalAttendanceDO> selectAbnormalByUserIdAndDayId(Long userId, Long dayId) {
        return employeeAbnormalAttendanceDao.selectAbnormalByUserIdAndDayId(userId, dayId);
    }


    /**
     * 查询用户在一段时间内的异常考勤
     */
    public List<EmployeeAbnormalAttendanceDO> selectAbnormalByUserId(Long userId, Long startDayId, Long endDayId) {
        return employeeAbnormalAttendanceDao.selectAbnormalByUserId(userId, startDayId, endDayId);
    }

    /**
     * 查询用户指定天的异常考勤
     */
    public List<EmployeeAbnormalAttendanceDO> selectAbnormalAttendanceByDayIdList(List<Long> userIdList, List<Long> dayIdList) {
        return employeeAbnormalAttendanceDao.selectAbnormalAttendanceByDayIdList(userIdList, dayIdList);
    }

    public List<EmployeeAbnormalAttendanceDO> selectByIdList(List<Long> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return Collections.emptyList();
        }
        return employeeAbnormalAttendanceDao.listByIds(idList);
    }

    /**
     * 查询用户的所有异常考勤
     */
    public List<EmployeeAbnormalAttendanceDO> selectAbnormalByUserIdList(List<Long> userIdList) {
        return employeeAbnormalAttendanceDao.selectAbnormalByUserIdList(userIdList);
    }

    /**
     * 查询异常考勤记录
     */
    public List<EmployeeAbnormalOperationRecordDO> selectRecordByAbnormalList(List<Long> abnormalList) {
        if (CollectionUtils.isEmpty(abnormalList)) {
            return new ArrayList<>();
        }
        return employeeAbnormalOperationRecordDao.selectByAbnormalList(abnormalList);
    }


    @Transactional(rollbackFor = Exception.class)
    public void updateApprovalFormConfirmCycle(List<AttendanceFormDO> updateFormList,
                                               List<AttendanceFormAttrDO> addAttrList,
                                               List<EmployeeAbnormalAttendanceDO> updateAbnormalList,
                                               List<EmployeeAbnormalOperationRecordDO> addRecordList) {
        if (CollectionUtils.isNotEmpty(updateFormList)) {
            attendanceFormDao.updateBatchById(updateFormList);
        }
        if (CollectionUtils.isNotEmpty(addAttrList)) {
            attendanceFormAttrDao.saveBatch(addAttrList);
        }
        if (CollectionUtils.isNotEmpty(updateAbnormalList)) {
            abnormalBatchUpdate(updateAbnormalList);
        }
        if (CollectionUtils.isNotEmpty(addRecordList)) {
            employeeAbnormalOperationRecordAdapter.saveBatch(addRecordList);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void abnormalConfirmSave(List<EmployeeAbnormalAttendanceDO> abnormalAttendanceDOList,
                                    List<EmployeeAbnormalOperationRecordDO> abnormalOperationRecordDOList) {
        if (CollectionUtils.isNotEmpty(abnormalAttendanceDOList)) {
            abnormalBatchUpdate(abnormalAttendanceDOList);

        }
        if (CollectionUtils.isNotEmpty(abnormalOperationRecordDOList)) {
            employeeAbnormalOperationRecordAdapter.saveBatch(abnormalOperationRecordDOList);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void abnormalOffSave(AttendanceEmployeeDetailDO employeeDetailDO,
                                EmployeeAbnormalAttendanceDO abnormalAttendanceDO,
                                EmployeeAbnormalOperationRecordDO abnormalOperationRecordDO,
                                List<AttendanceEmployeeDetailDO> existPresentEmployeeDetailDOList,
                                WarehouseDetailAbnormalDO warehouseDetailAbnormalDO,
                                WarehouseDetailDO warehouseDetailDO) {
        if (employeeDetailDO != null) {
            attendanceEmployeeDetailAdapter.save(employeeDetailDO);
        }
        if (abnormalAttendanceDO != null) {
            abnormalUpdate(abnormalAttendanceDO);
        }
        if (abnormalOperationRecordDO != null) {
            employeeAbnormalOperationRecordAdapter.save(abnormalOperationRecordDO);
        }
        if (CollectionUtils.isNotEmpty(existPresentEmployeeDetailDOList)) {
            attendanceEmployeeDetailAdapter.updateBatchById(existPresentEmployeeDetailDOList);
        }
        if (warehouseDetailAbnormalDO != null) {
            warehouseDetailAbnormalAdapter.saveOrUpdate(warehouseDetailAbnormalDO);
        }
        if (warehouseDetailDO != null) {
            warehouseDetailAdapter.saveOrUpdate(warehouseDetailDO);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void batchAbnormalPresentUpdate(List<EmployeeAbnormalAttendanceDO> updateAbnormalList,
                                           List<AttendanceFormDO> updateFormList,
                                           List<AttendanceEmployeeDetailDO> addEmployeeDetailList,
                                           List<EmployeeAbnormalOperationRecordDO> addOperationList,
                                           List<AttendanceEmployeeDetailDO> updateEmployeeDetailList) {
        if (CollectionUtils.isNotEmpty(updateAbnormalList)) {
            abnormalBatchUpdate(updateAbnormalList);
        }
        if (CollectionUtils.isNotEmpty(updateFormList)) {
            attendanceFormDao.updateBatchById(updateFormList);
        }
        if (CollectionUtils.isNotEmpty(addEmployeeDetailList)) {
            attendanceEmployeeDetailAdapter.saveBatch(addEmployeeDetailList);
        }
        if (CollectionUtils.isNotEmpty(addOperationList)) {
            employeeAbnormalOperationRecordAdapter.saveBatch(addOperationList);
        }
        if (CollectionUtils.isNotEmpty(updateEmployeeDetailList)) {
            attendanceEmployeeDetailAdapter.updateBatchById(updateEmployeeDetailList);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void batchAbnormalUpdate(List<EmployeeAbnormalAttendanceDO> updateAbnormalList,
                                    List<EmployeeAbnormalOperationRecordDO> updateOperationList,
                                    List<AttendanceEmployeeDetailDO> updateEmployeeDetailList) {
        if (CollectionUtils.isNotEmpty(updateAbnormalList)) {
            abnormalBatchUpdate(updateAbnormalList);
        }
        if (CollectionUtils.isNotEmpty(updateOperationList)) {
            employeeAbnormalOperationRecordAdapter.updateBatchById(updateOperationList);
        }
        if (CollectionUtils.isNotEmpty(updateEmployeeDetailList)) {
            attendanceEmployeeDetailAdapter.updateBatchById(updateEmployeeDetailList);
        }
    }

    // 异常快照
    public Map<Long, List<EmployeeAbnormalAttendanceSnapshotDO>> mapSnapShotByUserIdsAndDayIds(List<Long> userIdList, List<Long> dayIdList) {
        List<EmployeeAbnormalAttendanceSnapshotDO> abnormalSnapShotAttendanceDOList = employeeAbnormalAttendanceSnapshotDao.selectByUserIdListAndDayIdList(userIdList, dayIdList)
                .stream()
                .collect(Collectors.toList());
        return abnormalSnapShotAttendanceDOList.stream().collect(Collectors.groupingBy(EmployeeAbnormalAttendanceSnapshotDO::getUserId));
    }

    public void abnormalUpdate(EmployeeAbnormalAttendanceDO employeeAbnormalAttendanceDO){
        if (Objects.isNull(employeeAbnormalAttendanceDO)){
            return;
        }
        employeeAbnormalAttendanceAdapter.updateById(employeeAbnormalAttendanceDO,convertHrmsEmployeeAbnormalAttendance(employeeAbnormalAttendanceDO));
    }

    public void abnormalBatchUpdate(List<EmployeeAbnormalAttendanceDO> employeeAbnormalAttendanceDOList){
        if (CollectionUtils.isEmpty(employeeAbnormalAttendanceDOList)){
            return;
        }
        employeeAbnormalAttendanceAdapter.updateBatchById(employeeAbnormalAttendanceDOList,convertHrmsEmployeeAbnormalAttendanceList(employeeAbnormalAttendanceDOList));
    }

    public void abnormalBatchSave(List<EmployeeAbnormalAttendanceDO> employeeAbnormalAttendanceDOList){
        if (CollectionUtils.isEmpty(employeeAbnormalAttendanceDOList)){
            return;
        }
        employeeAbnormalAttendanceAdapter.saveBatch(employeeAbnormalAttendanceDOList,convertHrmsEmployeeAbnormalAttendanceList(employeeAbnormalAttendanceDOList));
    }

    /*public void abnormalSnapshotBatchUpdate(List<EmployeeAbnormalAttendanceSnapshotDO> employeeAbnormalAttendanceSnapshotDOList){
        if (CollectionUtils.isEmpty(employeeAbnormalAttendanceSnapshotDOList)){
            return;
        }
        employeeAbnormalAttendanceSnapshotAdapter.updateBatchById(employeeAbnormalAttendanceSnapshotDOList,convertHrmsEmployeeAbnormalAttendanceSnapshotList(employeeAbnormalAttendanceSnapshotDOList));
    }*/

    public void abnormalSnapshotBatchSave(List<EmployeeAbnormalAttendanceSnapshotDO> employeeAbnormalAttendanceSnapshotDOList){
        if (CollectionUtils.isEmpty(employeeAbnormalAttendanceSnapshotDOList)){
            return;
        }
        employeeAbnormalAttendanceSnapshotAdapter.saveBatch(employeeAbnormalAttendanceSnapshotDOList,convertHrmsEmployeeAbnormalAttendanceSnapshotList(employeeAbnormalAttendanceSnapshotDOList));
    }


    public HrmsEmployeeAbnormalAttendanceDO convertHrmsEmployeeAbnormalAttendance(EmployeeAbnormalAttendanceDO employeeAbnormalAttendanceDO) {
        MappingPunchClassConfigDO mappingPunchClassConfigDO = null;
        MappingPunchClassConfigItemDO mappingPunchClassConfigItemDO = null;
        MappingRuleConfigDO mappingRuleConfigDO = mappingRuleConfigDao.getByRuleId(employeeAbnormalAttendanceDO.getPunchConfigId());
        if (Objects.nonNull(employeeAbnormalAttendanceDO.getPunchClassConfigId()) && employeeAbnormalAttendanceDO.getPunchClassConfigId() > 0) {
            mappingPunchClassConfigDO = mappingPunchClassConfigDao.getByPunchClassConfigId(employeeAbnormalAttendanceDO.getPunchClassConfigId());
            mappingPunchClassConfigItemDO = mappingPunchClassConfigItemDao.getByPunchClassConfigItemId(employeeAbnormalAttendanceDO.getPunchClassItemConfigId());
        }
        HrmsEmployeeAbnormalAttendanceDO old = EmployeeAbnormalAttendanceMapstruct.INSTANCE.mapToOld(employeeAbnormalAttendanceDO);
        if (Objects.nonNull(mappingRuleConfigDO)) {
            old.setPunchConfigId(mappingRuleConfigDO.getHrPunchConfigId());
        }
        if (Objects.nonNull(mappingPunchClassConfigDO)) {
            old.setPunchClassConfigId(mappingPunchClassConfigDO.getHrPunchClassId());
        }
        if (Objects.nonNull(mappingPunchClassConfigItemDO)) {
            old.setPunchClassItemConfigId(mappingPunchClassConfigItemDO.getHrPunchClassItemId());
        }
        return old;
    }

    public List<HrmsEmployeeAbnormalAttendanceDO> convertHrmsEmployeeAbnormalAttendanceList(List<EmployeeAbnormalAttendanceDO> employeeAbnormalAttendanceDOList) {
        List<Long> punchConfigIdList = employeeAbnormalAttendanceDOList.stream().map(EmployeeAbnormalAttendanceDO::getPunchConfigId).distinct().collect(Collectors.toList());
        List<Long> punchClassIdList = employeeAbnormalAttendanceDOList.stream().map(EmployeeAbnormalAttendanceDO::getPunchClassConfigId).distinct().collect(Collectors.toList());

        Map<Long, Long> hrPunchConfigIdMap = mappingRuleConfigDao.listByRuleIds(punchConfigIdList).stream()
                .collect(Collectors.toMap(MappingRuleConfigDO::getRuleId, MappingRuleConfigDO::getHrPunchConfigId, (v1, v2) -> v1));

        Map<Long, Long> hrPunchClassIdMap = mappingPunchClassConfigDao.listByPunchClassConfigIds(punchClassIdList).stream()
                .collect(Collectors.toMap(MappingPunchClassConfigDO::getPunchClassConfigId, MappingPunchClassConfigDO::getHrPunchClassId, (v1, v2) -> v1));

        Map<Long, Long> hrPunchClassItemIdMap = mappingPunchClassConfigItemDao.listByPunchClassConfigIds(punchClassIdList).stream()
                .collect(Collectors.toMap(MappingPunchClassConfigItemDO::getPunchClassConfigItemId, MappingPunchClassConfigItemDO::getHrPunchClassItemId, (v1, v2) -> v1));

        List<HrmsEmployeeAbnormalAttendanceDO> oldList = EmployeeAbnormalAttendanceMapstruct.INSTANCE.mapToOldList(employeeAbnormalAttendanceDOList);
        oldList.forEach(old -> {
            if (Objects.nonNull(old.getPunchClassConfigId()) && old.getPunchClassConfigId() > 0) {
                old.setPunchClassConfigId(hrPunchClassIdMap.getOrDefault(old.getPunchClassConfigId(), old.getPunchClassConfigId()));
                old.setPunchClassItemConfigId(hrPunchClassItemIdMap.getOrDefault(old.getPunchClassItemConfigId(), old.getPunchClassItemConfigId()));
            }
            if (Objects.nonNull(old.getPunchConfigId())) {
                old.setPunchConfigId(hrPunchConfigIdMap.getOrDefault(old.getPunchConfigId(), old.getPunchConfigId()));
            }
        });
        return oldList;
    }

    public List<HrmsEmployeeAbnormalAttendanceSnapshotDO> convertHrmsEmployeeAbnormalAttendanceSnapshotList(List<EmployeeAbnormalAttendanceSnapshotDO> employeeAbnormalAttendanceSnapshotDOList) {
        List<Long> punchConfigIdList = employeeAbnormalAttendanceSnapshotDOList.stream().map(EmployeeAbnormalAttendanceSnapshotDO::getPunchConfigId).distinct().collect(Collectors.toList());
        List<Long> punchClassIdList = employeeAbnormalAttendanceSnapshotDOList.stream().map(EmployeeAbnormalAttendanceSnapshotDO::getPunchClassConfigId).distinct().collect(Collectors.toList());

        Map<Long, Long> hrPunchConfigIdMap = mappingRuleConfigDao.listByRuleIds(punchConfigIdList).stream()
                .collect(Collectors.toMap(MappingRuleConfigDO::getRuleId, MappingRuleConfigDO::getHrPunchConfigId, (v1, v2) -> v1));

        Map<Long, Long> hrPunchClassIdMap = mappingPunchClassConfigDao.listByPunchClassConfigIds(punchClassIdList).stream()
                .collect(Collectors.toMap(MappingPunchClassConfigDO::getPunchClassConfigId, MappingPunchClassConfigDO::getHrPunchClassId, (v1, v2) -> v1));

        Map<Long, Long> hrPunchClassItemIdMap = mappingPunchClassConfigItemDao.listByPunchClassConfigIds(punchClassIdList).stream()
                .collect(Collectors.toMap(MappingPunchClassConfigItemDO::getPunchClassConfigItemId, MappingPunchClassConfigItemDO::getHrPunchClassItemId, (v1, v2) -> v1));

        List<HrmsEmployeeAbnormalAttendanceSnapshotDO> oldList = EmployeeAbnormalAttendanceSnapshotMapstruct.INSTANCE.mapToOldList(employeeAbnormalAttendanceSnapshotDOList);
        oldList.forEach(old -> {
            if (Objects.nonNull(old.getPunchClassConfigId()) && old.getPunchClassConfigId() > 0) {
                old.setPunchClassConfigId(hrPunchClassIdMap.getOrDefault(old.getPunchClassConfigId(), old.getPunchClassConfigId()));
                old.setPunchClassItemConfigId(hrPunchClassItemIdMap.getOrDefault(old.getPunchClassItemConfigId(), old.getPunchClassItemConfigId()));
            }
            if (Objects.nonNull(old.getPunchConfigId())) {
                old.setPunchConfigId(hrPunchConfigIdMap.getOrDefault(old.getPunchConfigId(), old.getPunchConfigId()));
            }
        });
        return oldList;
    }
}
