package com.imile.attendance.migration.dto;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/6/16
 */
@Data
public class AbnormalSyncDTO implements Serializable {
    /**
     * 国家集合
     */
    @NotNull(message = "countryList cannot be empty")
    private List<String> countryList;

    /**
     * 部门集合
     */
    private List<Long> deptIdList;

    /**
     * 用户集合
     */
    private List<String> userCodeList;

    /**
     * 同步起始日期
     */
    private Long startDayId;

    /**
     * 同步结束日期
     */
    private Long endDayId;
}
