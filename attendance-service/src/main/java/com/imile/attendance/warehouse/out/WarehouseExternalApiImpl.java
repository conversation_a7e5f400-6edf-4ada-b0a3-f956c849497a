package com.imile.attendance.warehouse.out;

import cn.hutool.core.date.DateUtil;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.Lists;
import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.enums.ErrorCodeEnum;
import com.imile.attendance.enums.abnormal.AttendanceAbnormalTypeEnum;
import com.imile.attendance.enums.warehouse.WarehouseAttendanceStatusEnum;
import com.imile.attendance.enums.warehouse.WarehousePcsStatusEnum;
import com.imile.attendance.exception.BusinessLogicException;
import com.imile.attendance.gensis.support.RpcUserCertificateSupport;
import com.imile.attendance.infrastructure.repository.common.AttendanceDeptService;
import com.imile.attendance.infrastructure.repository.common.dto.AttendanceDept;
import com.imile.attendance.infrastructure.repository.employee.dao.UserInfoDao;
import com.imile.attendance.infrastructure.repository.employee.modle.UserInfoDO;
import com.imile.attendance.infrastructure.repository.warehouse.dao.WarehouseDetailAbnormalDao;
import com.imile.attendance.infrastructure.repository.warehouse.dao.WarehouseDetailDao;
import com.imile.attendance.infrastructure.repository.warehouse.model.WarehouseDetailAbnormalDO;
import com.imile.attendance.infrastructure.repository.warehouse.model.WarehouseDetailDO;
import com.imile.attendance.infrastructure.repository.warehouse.query.WarehouseDetailQuery;
import com.imile.attendance.util.PageUtil;
import com.imile.attendance.warehouse.api.WarehouseExternalApi;
import com.imile.attendance.warehouse.dto.OcDTO;
import com.imile.attendance.warehouse.dto.UserEmploymentFormDTO;
import com.imile.attendance.warehouse.dto.WarehousePcsMonthReportCountDTO;
import com.imile.attendance.warehouse.dto.WarehousePcsMonthReportPassRateDTO;
import com.imile.attendance.warehouse.dto.WarehousePcsReportDTO;
import com.imile.attendance.warehouse.param.GetPcsReportByConditionParam;
import com.imile.attendance.warehouse.param.GetPcsReportCountByConditionParam;
import com.imile.attendance.warehouse.param.UserEmploymentFormParam;
import com.imile.common.page.PaginationResult;
import com.imile.genesis.api.enums.CertificateTypeEnum;
import com.imile.genesis.api.model.result.user.UserCertificateDTO;
import com.imile.rpc.common.RpcResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Service;
import org.jetbrains.annotations.NotNull;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.BiConsumer;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024/9/30
 */
@Service(version = "1.0.0")
@Slf4j
public class WarehouseExternalApiImpl implements WarehouseExternalApi {

    @Resource
    private WarehouseDetailDao warehouseDetailDao;
    @Resource
    private WarehouseDetailAbnormalDao warehouseDetailAbnormalDao;
    @Resource
    private AttendanceDeptService deptService;
    @Resource
    private UserInfoDao userInfoDao;
    @Resource
    private RpcUserCertificateSupport userCertificateSupport;

    @Override
    public RpcResult<PaginationResult<WarehousePcsReportDTO>> pcsDateReport(GetPcsReportByConditionParam param) {
        param.check();
        List<Long> ocList = param.getOcIdList();
        Date startDate = DateUtil.parseDate(DateUtil.formatDate(param.getStartDate()));
        Date endDate = DateUtil.parseDate(DateUtil.formatDate(param.getEndDate()));
        if (CollectionUtils.isEmpty(ocList)) {
            List<OcDTO> ocDTOList = getOcListByCondition(startDate, endDate, param.getVendorCode());
            if (CollectionUtils.isEmpty(ocDTOList)) {
                return RpcResult.ok(PaginationResult.get(Collections.emptyList(), param));
            }
            ocList = ocDTOList.stream().map(OcDTO::getOcId).collect(Collectors.toList());
        }

        List<Long> searchKeyUserIdList = new ArrayList<>();
        boolean searchFlag = false;
        if (StringUtils.isNotBlank(param.getSearchUserKey())) {
            List<UserInfoDO> userInfoDOList = userInfoDao.selectBySearchCode(param.getSearchUserKey());
            searchKeyUserIdList = userInfoDOList.stream().map(UserInfoDO::getId).collect(Collectors.toList());
            searchFlag = true;
        }
        if (searchFlag && CollectionUtils.isEmpty(searchKeyUserIdList)) {
            return RpcResult.ok(PaginationResult.get(Collections.emptyList(), param));
        }

        WarehouseDetailQuery warehouseDetailParam = new WarehouseDetailQuery();
        warehouseDetailParam.setCountry(param.getCountry());
        warehouseDetailParam.setCity(param.getCity());
        warehouseDetailParam.setOcIdList(ocList);
        warehouseDetailParam.setVendorCodeList(Collections.singletonList(param.getVendorCode()));
        warehouseDetailParam.setUserIdList(searchKeyUserIdList);
        warehouseDetailParam.setStartDate(startDate);
        warehouseDetailParam.setEndDate(endDate);
        warehouseDetailParam.setPcsStatus(param.getPcsStatus());
        warehouseDetailParam.setAttendanceStatus(param.getAttendanceStatus());
        warehouseDetailParam.setClassesTypeList(param.getClassesTypeList());
        Page<WarehouseDetailDO> page = PageHelper.startPage(param.getCurrentPage(), param.getShowCount());
        List<WarehouseDetailDO> list = warehouseDetailDao.selectPcsPage(warehouseDetailParam);
        if (CollectionUtils.isEmpty(list)) {
            return RpcResult.ok(PaginationResult.get(Collections.emptyList(), param));
        }

        List<Long> userIdList = list.stream().map(WarehouseDetailDO::getUserId).distinct().collect(Collectors.toList());
        List<String> userCodeList = list.stream().map(WarehouseDetailDO::getUserCode).distinct().collect(Collectors.toList());
        Map<Long, UserInfoDO> userNameMap = userInfoDao.getByUserIds(userIdList)
                .stream()
                .collect(Collectors.toMap(UserInfoDO::getId, Function.identity(), (v1, v2) -> v1));

        List<Long> ocIdList = list.stream()
                .map(o -> Arrays.asList(o.getOcId(), o.getUserOcId()))
                .flatMap(Collection::stream)
                .distinct()
                .collect(Collectors.toList());
        Map<Long, String> ocMap = deptService.listByDeptIds(ocIdList).stream().
                collect(Collectors.toMap(AttendanceDept::getId, AttendanceDept::getDeptNameEn, (v1, v2) -> v1));

        List<Long> warehouseDetailIds = list
                .stream()
                .filter(warehouse -> Objects.equals(WarehouseAttendanceStatusEnum.ABNORMAL.getCode(), warehouse.getAttendanceStatus()))
                .map(WarehouseDetailDO::getId)
                .collect(Collectors.toList());


        List<Long> workMissCardWarehouseDetailList = warehouseDetailAbnormalDao.selectByWarehouseDetailIds(warehouseDetailIds)
                .stream()
                .filter(warehouseAbnormal -> Objects.equals(AttendanceAbnormalTypeEnum.BEFORE_OFFICE_LACK.getCode(), warehouseAbnormal.getAbnormalType()))
                .map(WarehouseDetailAbnormalDO::getWarehouseDetailId)
                .distinct().collect(Collectors.toList());

        Map<String, UserCertificateDTO> certificateCodeMap = userCertificateSupport.listUserCertificateList(userCodeList, CertificateTypeEnum.INE.getCode())
                .stream()
                .collect(Collectors.toMap(UserCertificateDTO::getUserCode, Function.identity(), (v1, v2) -> v1));


        List<WarehousePcsReportDTO> collect = convertWarehousePcsDateReportDTOS(list, userNameMap, ocMap, workMissCardWarehouseDetailList, certificateCodeMap);
        return  RpcResult.ok(PageUtil.getPageResult(collect, param, (int) page.getTotal(), page.getPages()));
    }

    @Override
    public RpcResult<PaginationResult<WarehousePcsReportDTO>> pcsWeekOrMonthReport(GetPcsReportByConditionParam param) {
        param.check();
        BusinessLogicException.checkTrue(StringUtils.isEmpty(param.getReportType()), ErrorCodeEnum.PARAM_VALID_ERROR.getCode(), ErrorCodeEnum.PARAM_VALID_ERROR.getDesc(), "reportType");
        BusinessLogicException.checkTrue(!Lists.newArrayList(BusinessConstant.WEEK, BusinessConstant.MONTH).contains(param.getReportType()), ErrorCodeEnum.PARAM_VALID_ERROR.getCode(), ErrorCodeEnum.PARAM_VALID_ERROR.getDesc(), "reportType");
        List<Long> ocList = param.getOcIdList();
        Date startDate = DateUtil.parseDate(DateUtil.formatDate(param.getStartDate()));
        Date endDate = DateUtil.parseDate(DateUtil.formatDate(param.getEndDate()));
        if (CollectionUtils.isEmpty(ocList)) {
            List<OcDTO> ocDTOList = getOcListByCondition(startDate, endDate, param.getVendorCode());
            if (CollectionUtils.isEmpty(ocDTOList)) {
                return RpcResult.ok(PaginationResult.get(Collections.emptyList(), param));
            }
            ocList = ocDTOList.stream().map(OcDTO::getOcId).collect(Collectors.toList());
        }

        List<Long> searchKeyUserIdList = new ArrayList<>();
        boolean searchFlag = false;
        if (StringUtils.isNotBlank(param.getSearchUserKey())) {
            List<UserInfoDO> userInfoDOList = userInfoDao.selectBySearchCode(param.getSearchUserKey());
            searchKeyUserIdList = userInfoDOList.stream().map(UserInfoDO::getId).collect(Collectors.toList());
            searchFlag = true;
        }
        if (searchFlag && CollectionUtils.isEmpty(searchKeyUserIdList)) {
            return RpcResult.ok(PaginationResult.get(Collections.emptyList(), param));
        }

        WarehouseDetailQuery warehouseDetailParam = new WarehouseDetailQuery();
        warehouseDetailParam.setCountry(param.getCountry());
        warehouseDetailParam.setCity(param.getCity());
        warehouseDetailParam.setOcIdList(ocList);
        warehouseDetailParam.setVendorCodeList(Collections.singletonList(param.getVendorCode()));
        warehouseDetailParam.setUserIdList(searchKeyUserIdList);
        warehouseDetailParam.setStartDate(startDate);
        warehouseDetailParam.setEndDate(endDate);
        warehouseDetailParam.setPcsStatus(param.getPcsStatus());
        warehouseDetailParam.setReportType(param.getReportType());
        Page<WarehouseDetailDO> page = PageHelper.startPage(param.getCurrentPage(), param.getShowCount());
        List<WarehouseDetailDO> warehouseDetailDOS = warehouseDetailDao.selectPcsPage(warehouseDetailParam);
        if (CollectionUtils.isEmpty(warehouseDetailDOS)) {
            return RpcResult.ok(PaginationResult.get(Collections.emptyList(), param));
        }

        List<Long> userIdList = warehouseDetailDOS.stream()
                .map(WarehouseDetailDO::getUserId)
                .collect(Collectors.toList());

        WarehouseDetailQuery detailParam = new WarehouseDetailQuery();
        detailParam.setStartDate(startDate);
        detailParam.setEndDate(endDate);
        detailParam.setOcIdList(ocList);
        detailParam.setVendorCodeList(Collections.singletonList(param.getVendorCode()));
        detailParam.setUserIdList(userIdList);
        List<WarehouseDetailDO> warehouseDetailDOList = warehouseDetailDao.selectPcsPage(detailParam);

        Map<String, List<WarehouseDetailDO>> groupMap = warehouseDetailDOList
                .stream().collect(Collectors.groupingBy(o -> o.getUserId() + BusinessConstant.WELL_NO + o.getOcId() + BusinessConstant.WELL_NO + o.getVendorId()));

        Map<Long, UserInfoDO> userNameMap = userInfoDao.getByUserIds(userIdList)
                .stream()
                .collect(Collectors.toMap(UserInfoDO::getId, Function.identity(), (v1, v2) -> v1));

        List<Long> ocIdList = warehouseDetailDOS.stream()
                .map(o -> Arrays.asList(o.getOcId(), o.getUserOcId()))
                .flatMap(Collection::stream)
                .distinct()
                .collect(Collectors.toList());
        Map<Long, String> ocMap = deptService.listByDeptIds(ocIdList).stream().
                collect(Collectors.toMap(AttendanceDept::getId, AttendanceDept::getDeptNameEn, (v1, v2) -> v1));

        List<WarehousePcsReportDTO> collect = convertWarehousePcsReportDTOS(warehouseDetailDOS, userNameMap, ocMap, groupMap);
        return  RpcResult.ok(PageUtil.getPageResult(collect, param, (int) page.getTotal(), page.getPages()));
    }

    @Override
    public RpcResult<WarehousePcsMonthReportCountDTO> pcsDateReportCount(GetPcsReportCountByConditionParam param) {
        param.check();
        Date startDate = DateUtil.parseDate(DateUtil.formatDate(param.getStartDate()));
        Date endDate = DateUtil.parseDate(DateUtil.formatDate(param.getEndDate()));
        WarehouseDetailQuery warehouseDetailParam = new WarehouseDetailQuery();
        warehouseDetailParam.setOcIdList(param.getOcIdList());
        warehouseDetailParam.setVendorCodeList(Collections.singletonList(param.getVendorCode()));
        warehouseDetailParam.setStartDate(startDate);
        warehouseDetailParam.setEndDate(endDate);
        warehouseDetailParam.setCountry(param.getCountry());
        warehouseDetailParam.setCity(param.getCity());
        List<WarehouseDetailDO> list = warehouseDetailDao.selectPcsPage(warehouseDetailParam);
        WarehousePcsMonthReportCountDTO reportCount = new WarehousePcsMonthReportCountDTO();
        if (CollectionUtils.isEmpty(list)) {
            reportCount.setTotalCount(BusinessConstant.ZERO);
            reportCount.setNormalCount(BusinessConstant.ZERO);
            reportCount.setAbnormalCount(BusinessConstant.ZERO);
            return RpcResult.ok(reportCount);
        }
        reportCount.setTotalCount(list.size());
        reportCount.setNormalCount((int) list.stream().filter(warehouse -> Objects.equals(WarehouseAttendanceStatusEnum.NORMAL.getCode(), warehouse.getAttendanceStatus())).count());
        reportCount.setAbnormalCount((int) list.stream().filter(warehouse -> Objects.equals(WarehouseAttendanceStatusEnum.ABNORMAL.getCode(), warehouse.getAttendanceStatus())).count());
        reportCount.setWaitConfigClassesCount((int) list.stream().filter(warehouse -> Objects.equals(WarehouseAttendanceStatusEnum.PENDING_SHIFT_CONFIG.getCode(), warehouse.getAttendanceStatus())).count());
        return RpcResult.ok(reportCount);
    }

    @Override
    public RpcResult<WarehousePcsMonthReportPassRateDTO> pcsDateMonthReportPassRate(GetPcsReportCountByConditionParam param) {
        param.check();
        Date startDate = DateUtil.parseDate(DateUtil.formatDate(param.getStartDate()));
        Date endDate = DateUtil.parseDate(DateUtil.formatDate(param.getEndDate()));
        WarehouseDetailQuery warehouseDetailParam = new WarehouseDetailQuery();
        warehouseDetailParam.setOcIdList(param.getOcIdList());
        warehouseDetailParam.setVendorCodeList(Collections.singletonList(param.getVendorCode()));
        warehouseDetailParam.setStartDate(startDate);
        warehouseDetailParam.setEndDate(endDate);
        warehouseDetailParam.setCountry(param.getCountry());
        warehouseDetailParam.setCity(param.getCity());
        List<WarehouseDetailDO> list = warehouseDetailDao.selectPcsPage(warehouseDetailParam);
        WarehousePcsMonthReportPassRateDTO reportPassRate = new WarehousePcsMonthReportPassRateDTO();
        if (CollectionUtils.isEmpty(list)) {
            return RpcResult.ok(reportPassRate);
        }

        Map<Date, List<WarehouseDetailDO>> groupByDateMap = list.stream().collect(Collectors.groupingBy(WarehouseDetailDO::getWarehouseDate));

        for (int i = 0; i < 31; i++) {
            BiConsumer<WarehousePcsMonthReportPassRateDTO, BigDecimal> setter = reportPassRate.getSetter(i + 1);
            Date warehouseDate = DateUtil.offsetDay(startDate, i);
            List<WarehouseDetailDO> warehouseDetailDOS = groupByDateMap.get(warehouseDate);
            BigDecimal bigDecimal = BigDecimal.ZERO;
            if (CollectionUtils.isNotEmpty(warehouseDetailDOS)) {
                int normalCount = (int) warehouseDetailDOS.stream().filter(warehouse -> Objects.equals(WarehousePcsStatusEnum.NORMAL.getCode(), warehouse.getPcsStatus())).count();
                bigDecimal = new BigDecimal(normalCount).divide(new BigDecimal(warehouseDetailDOS.size()), 2, RoundingMode.HALF_UP);
            }
            setter.accept(reportPassRate, bigDecimal);
        }
        return RpcResult.ok(reportPassRate);
    }

    @Override
    public RpcResult<List<UserEmploymentFormDTO>> getEmploymentFormByUserCodes(UserEmploymentFormParam param) {
        log.info("query EmploymentForm params:{}", param);
        if (Objects.isNull(param) || CollectionUtils.isEmpty(param.getUserCodes())) {
            return RpcResult.ok(Collections.emptyList());
        }
        List<UserInfoDO> userInfoDOList = userInfoDao.listByUserCodes(param.getUserCodes());
        if (CollectionUtils.isEmpty(userInfoDOList)) {
            return RpcResult.ok(Collections.emptyList());
        }
        List<UserEmploymentFormDTO> result = new ArrayList<>();
        for (UserInfoDO userInfoDO : userInfoDOList) {
            UserEmploymentFormDTO userEmploymentFormDTO = new UserEmploymentFormDTO();
            userEmploymentFormDTO.setUserCode(userInfoDO.getUserCode());
            userEmploymentFormDTO.setEmploymentForm(userInfoDO.getEmployeeForm());
            result.add(userEmploymentFormDTO);
        }
        return RpcResult.ok(result);
    }

    public List<OcDTO> getOcListByCondition(Date startDate, Date endDate, String vendorCode) {
        WarehouseDetailQuery warehouseDetailParam = new WarehouseDetailQuery();
        warehouseDetailParam.setStartTime(startDate);
        warehouseDetailParam.setEndTime(endDate);
        warehouseDetailParam.setVendorCode(vendorCode);
        List<WarehouseDetailDO> warehouseDetailDOList = warehouseDetailDao.selectByCondition(warehouseDetailParam);
        if (CollectionUtils.isEmpty(warehouseDetailDOList)) {
            return Collections.emptyList();
        }
        List<Long> ocIds = warehouseDetailDOList.stream().map(WarehouseDetailDO::getOcId).distinct().collect(Collectors.toList());
        return deptService.listByDeptIds(ocIds).stream().map(dept -> {
            OcDTO oc = new OcDTO();
            oc.setOcId(dept.getId());
            oc.setOcCode(dept.getDeptCode());
            oc.setOcName(dept.getDeptNameEn());
            return oc;
        }).collect(Collectors.toList());
    }

    @NotNull
    private static List<WarehousePcsReportDTO> convertWarehousePcsDateReportDTOS(List<WarehouseDetailDO> list,
                                                                                 Map<Long, UserInfoDO> userNameMap,
                                                                                 Map<Long, String> ocMap,
                                                                                 List<Long> workMissCardWarehouseDetailList,
                                                                                 Map<String, UserCertificateDTO> certificateCodeMap) {
        return list.stream()
                .map(o -> {
                    WarehousePcsReportDTO pcsReportDto = new WarehousePcsReportDTO();
                    pcsReportDto.setId(o.getId());
                    pcsReportDto.setWarehouseDate(o.getWarehouseDate());
                    pcsReportDto.setCountry(o.getCountry());
                    pcsReportDto.setCity(o.getCity());
                    pcsReportDto.setInOcId(o.getOcId());
                    pcsReportDto.setInOcName(ocMap.get(o.getOcId()));
                    pcsReportDto.setInVendorCode(o.getVendorCode());
                    UserInfoDO user = userNameMap.getOrDefault(o.getUserId(), new UserInfoDO());
                    pcsReportDto.setOcId(o.getUserOcId());
                    pcsReportDto.setOcName(ocMap.get(o.getUserOcId()));
                    pcsReportDto.setUserCode(user.getUserCode());
                    pcsReportDto.setUserName(user.getUserName());
                    pcsReportDto.setUserNameEn(user.getUserNameEn());
                    pcsReportDto.setPcsStatus(o.getPcsStatus());
                    pcsReportDto.setClockIn(!workMissCardWarehouseDetailList.contains(o.getId()));
                    pcsReportDto.setClockOut(Objects.equals(WarehousePcsStatusEnum.NORMAL.getCode(), o.getPcsStatus()));
                    pcsReportDto.setClassesName(o.getClassesName());
                    pcsReportDto.setClassesType(o.getClassesType());
                    pcsReportDto.setAttendanceStatus(o.getAttendanceStatus());
                    pcsReportDto.setActualAttendanceTime(o.getActualAttendanceTime());
                    if (Objects.nonNull(certificateCodeMap.get(o.getUserCode()))) {
                        pcsReportDto.setCertificateCode(certificateCodeMap.get(o.getUserCode()).getCertificateCode());
                    }
                    return pcsReportDto;
                }).collect(Collectors.toList());
    }

    @NotNull
    private static List<WarehousePcsReportDTO> convertWarehousePcsReportDTOS(List<WarehouseDetailDO> list,
                                                                             Map<Long, UserInfoDO> userNameMap,
                                                                             Map<Long, String> ocMap,
                                                                             Map<String, List<WarehouseDetailDO>> groupMap) {
        return list.stream()
                .map(o -> {
                    WarehousePcsReportDTO pcsReportDto = new WarehousePcsReportDTO();
                    pcsReportDto.setId(o.getId());
                    pcsReportDto.setWarehouseDate(o.getWarehouseDate());
                    pcsReportDto.setCountry(o.getCountry());
                    pcsReportDto.setCity(o.getCity());
                    pcsReportDto.setInOcId(o.getOcId());
                    pcsReportDto.setInOcName(ocMap.get(o.getOcId()));
                    pcsReportDto.setInVendorCode(o.getVendorCode());
                    UserInfoDO user = userNameMap.getOrDefault(o.getUserId(), new UserInfoDO());
                    pcsReportDto.setOcId(o.getUserOcId());
                    pcsReportDto.setOcName(ocMap.get(o.getUserOcId()));
                    pcsReportDto.setUserCode(user.getUserCode());
                    pcsReportDto.setUserName(user.getUserName());
                    pcsReportDto.setUserNameEn(user.getUserNameEn());
                    List<WarehouseDetailDO> warehouseDetailDOList = groupMap.get(o.getUserId() + BusinessConstant.WELL_NO + o.getOcId() + BusinessConstant.WELL_NO + o.getVendorId());
                    if (CollectionUtils.isNotEmpty(warehouseDetailDOList)) {
                        pcsReportDto.setDays(warehouseDetailDOList.size());
                        pcsReportDto.setNormalDays((int) warehouseDetailDOList.stream().filter(warehouse -> Objects.equals(WarehousePcsStatusEnum.NORMAL.getCode(), warehouse.getPcsStatus())).count());
                        pcsReportDto.setAbnormalDays((int) warehouseDetailDOList.stream().filter(warehouse -> Objects.equals(WarehousePcsStatusEnum.ABNORMAL.getCode(), warehouse.getPcsStatus())).count());
                    }
                    return pcsReportDto;
                }).collect(Collectors.toList());
    }
}
