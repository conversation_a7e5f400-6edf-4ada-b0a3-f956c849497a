package com.imile.attendance.warehouse;

import com.imile.attendance.warehouse.param.GetOcListByVendorCodeParam;
import com.imile.attendance.warehouse.param.GetVendorListByOcListParam;
import com.imile.attendance.warehouse.param.WarehouseOcParam;
import com.imile.attendance.warehouse.vo.OcVO;
import com.imile.attendance.warehouse.vo.WarehouseOcCountryVO;
import com.imile.hermes.enterprise.dto.EntOcApiDTO;

import java.util.List;

/**
 * todo
 *
 * <AUTHOR> chen
 * @Date 2025/07/09
 * @Time 19:58
 * @Description
 */
public interface WarehouseOcService {

    /**
     * 查询当前登录人已授权的网点
     * 根据用工类型会进行特殊网点过滤
     */
    List<OcVO> getAuthOcList(String employeeType);

    /**
     * 查询指定用户拥有的网点权限
     */
    List<OcVO> getAuthOcListByUserId(Long userId);

    /**
     * 指定条件查询网点列表
     */
    List<OcVO> getOcListByCondition(GetVendorListByOcListParam param);

    /**
     * 获取部门主数据权限
     */
    List<OcVO> getDeptAuthVOList(String userCode);

    /**
     * 查询指定网点
     */
    EntOcApiDTO getOc(Long id);

    /**
     * 查询待配置班次网点下拉列表
     */
    List<OcVO> getNoBingShiftOcList();


    /**
     * 查询当前登录人WPM网点权限
     */
    List<OcVO> getOcVOList();


    /**
     * 根据供应商编码查询服务网点
     */
    List<OcVO> getOcListByVendorCode(GetOcListByVendorCodeParam param);

    /**
     * 网点级连筛选
     */
    List<WarehouseOcCountryVO> getWarehouseOcTree(WarehouseOcParam param);
}
