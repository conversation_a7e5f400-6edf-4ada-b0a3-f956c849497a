package com.imile.attendance.warehouse.impl;

import com.imile.attendance.apollo.AttendanceProperties;
import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.enums.EmploymentTypeEnum;
import com.imile.attendance.infrastructure.repository.common.dto.AttendanceUser;
import com.imile.attendance.warehouse.WarehouseUserService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @since 2025/8/1
 */
@Service
public class WarehouseUserServiceImpl implements WarehouseUserService {
    @Resource
    private AttendanceProperties attendanceProperties;

    @Override
    public boolean isWarehouseLaborSupport(AttendanceUser attendanceUser) {
        return Objects.equals(BusinessConstant.Y, attendanceUser.getIsWarehouseStaff())
                && Objects.equals(EmploymentTypeEnum.OS_FIXED_SALARY.getCode(), attendanceUser.getEmployeeType())
                && getWpmApplicationCountry().contains(attendanceUser.getLocationCountry())
                && Objects.equals(BusinessConstant.N, attendanceUser.getIsDriver());
    }

    @Override
    public List<String> getWpmApplicationCountry() {
        return Optional.ofNullable(attendanceProperties.getAttendance().getWpmApplicationCountry())
                .map(str -> str.split(BusinessConstant.DEFAULT_DELIMITER))
                .map(Arrays::stream)
                .orElseGet(Stream::empty)
                .collect(Collectors.toList());
    }
}
