package com.imile.attendance.warehouse.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/2/17
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WarehouseOcRegionVO {

    /**
     * 区域code(上级部门编码)
     */
    private String parentDeptCode;

    /**
     * 区域名称（上级部门名称（中文））
     */
    private String parentDeptNameCn;

    /**
     * 区域名称（上级部门名称（英文））
     */
    private String parentDeptNameEn;

    private List<WarehouseOcVO> ocList;
}
