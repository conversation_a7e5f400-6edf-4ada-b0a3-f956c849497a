package com.imile.attendance.warehouse;


import com.imile.attendance.infrastructure.repository.warehouse.query.WarehouseDetailQuery;
import com.imile.attendance.warehouse.param.BingShiftParam;
import com.imile.attendance.warehouse.param.CheckVendorConfirmStatusParam;
import com.imile.attendance.warehouse.param.InOrOutParam;
import com.imile.attendance.warehouse.param.InV2Param;
import com.imile.attendance.warehouse.param.OutParam;
import com.imile.attendance.warehouse.param.QuickOutParam;
import com.imile.attendance.warehouse.param.UpdateWarehouseWorkClassesParam;
import com.imile.attendance.warehouse.param.UpdateWarehouseWorkOcParam;
import com.imile.attendance.warehouse.param.UpdateWarehouseWorkVendorParam;
import com.imile.attendance.warehouse.param.VendorConfirmClassesParam;
import com.imile.attendance.warehouse.vo.CheckVendorConfirmStatusResultVO;
import com.imile.attendance.warehouse.vo.OutVO;

/**
 * <AUTHOR>
 * @since 2025/7/22
 */
public interface WarehouseService {
    /**
     * 入仓
     */
    void in(InOrOutParam param);

    /**
     * 入仓扩展
     * 老帐号停用
     * 证件号更新
     */
    void inV2(InV2Param param);

    /**
     * 出仓
     */
    OutVO out(OutParam param);

    /**
     * 快速出仓
     * 无需人脸识别检查
     */
//    void quickOut(QuickOutParam param);

    /**
     * 更新仓内日报工作供应商
     */
    void updateWorkVendor(UpdateWarehouseWorkVendorParam param);

    /**
     * 更新仓内日报工作网点
     */
    void updateWorkOc(UpdateWarehouseWorkOcParam param);

    /**
     * 更新仓内日报工作班次
     */
    void updateWorkClasses(UpdateWarehouseWorkClassesParam param);

    /**
     * 绑定班次
     */
    Boolean bingShift(BingShiftParam param);

    /**
     * 补推财务考勤流水
     */
    void retryPushFin(WarehouseDetailQuery param);

    /**
     * 供应商确认人员班次出勤结果
     */
    Long vendorClassedConfirm(VendorConfirmClassesParam param);

    /**
     * 检查供应商确认状态
     */
    CheckVendorConfirmStatusResultVO checkVendorConfirmStatus(CheckVendorConfirmStatusParam param);
}
