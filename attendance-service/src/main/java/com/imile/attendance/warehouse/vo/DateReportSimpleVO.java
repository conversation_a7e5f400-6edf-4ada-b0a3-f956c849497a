package com.imile.attendance.warehouse.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @since 2024/11/26
 */
@Data
public class DateReportSimpleVO {

    /**
     * 考勤结果表ID
     */
    private Long id;

    /**
     * 工作网点ID
     */
    private Long ocId;

    /**
     * 工作网点名称
     */
    private String ocName;

    /**
     * 工作供应商ID
     */
    private Long vendorId;

    /**
     * 工作供应商编码
     */
    private String vendorCode;

    /**
     * 工作供应商名称
     */
    private String vendorName;

    /**
     * 用户名称
     */
    private String userName;

    /**
     * 用户编码
     */
    private String userCode;

    /**
     * 拼接用户名称
     */
    private String unifiedUserName;

    /**
     * 考勤日
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date warehouseDate;

    /**
     * 班次名称
     */
    private String className;

    /**
     * 入仓时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date inTime;

    /**
     * 离仓时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date outTime;
}
