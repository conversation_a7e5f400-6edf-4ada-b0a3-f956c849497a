package com.imile.attendance.warehouse.impl;

import cn.hutool.core.date.DateUtil;
import com.google.common.collect.Maps;
import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.enums.warehouse.WarehouseStatusEnum;
import com.imile.attendance.enums.warehouse.WarehouseTypeEnum;
import com.imile.attendance.gensis.support.RpcUserCertificateSupport;
import com.imile.attendance.infrastructure.repository.common.dto.AttendanceDept;
import com.imile.attendance.infrastructure.repository.common.dto.AttendanceUser;
import com.imile.attendance.infrastructure.repository.warehouse.dao.WarehouseDetailDao;
import com.imile.attendance.infrastructure.repository.warehouse.dao.WarehouseRecordDao;
import com.imile.attendance.infrastructure.repository.warehouse.model.WarehouseDetailDO;
import com.imile.attendance.infrastructure.repository.warehouse.model.WarehouseRecordDO;
import com.imile.attendance.infrastructure.repository.warehouse.query.WarehouseDetailQuery;
import com.imile.attendance.util.BusinessFieldUtils;
import com.imile.attendance.util.ExcelUtil;
import com.imile.attendance.warehouse.WarehouseBaseService;
import com.imile.attendance.warehouse.WarehouseExportService;
import com.imile.attendance.warehouse.WarehouseSupplierService;
import com.imile.attendance.warehouse.param.StatisticVendorExportParam;
import com.imile.attendance.warehouse.vo.VendorClassAttendanceDetailImportVO;
import com.imile.genesis.api.enums.EmploymentTypeEnum;
import com.imile.genesis.api.model.result.user.UserCertificateDTO;
import com.imile.util.user.UserEvnHolder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 仓内导出服务
 *
 * <AUTHOR>
 * @since 2025/1/22
 */
@Slf4j
@Service
public class WarehouseExportServiceImpl extends WarehouseBaseService implements WarehouseExportService {

    @Resource
    private WarehouseSupplierService warehouseSupplierService;

    @Resource
    private RpcUserCertificateSupport userCertificateSupport;

    @Resource
    private WarehouseRecordDao warehouseRecordDao;

    @Resource
    private WarehouseDetailDao warehouseDetailDao;

    @Override
    public void vendorAttendanceDetailExport(HttpServletResponse response, StatisticVendorExportParam param) {
        // key: sheet页名称  value: 第一行标题头列表
        UserEvnHolder.setLocale(param.getLang());
        String fileName = String.format(BusinessConstant.VENDOR_ATTENDANCE_DETAIL_EXPORT_FILE_NAME, DateUtil.formatDate(param.getWarehouseDate()));
        Map<String, List<String>> headMap = matchSheetHead();
        Set<String> sheetNameSet = headMap.keySet();
        Optional<String> sheetNameOptional = sheetNameSet.stream().findFirst();
        String sheetName = "人员出勤";
        if (sheetNameOptional.isPresent()) {
            sheetName = sheetNameOptional.get();
        }
        XSSFWorkbook book = new XSSFWorkbook();
        ExcelUtil cascadeSelectTool = new ExcelUtil(book)
                .createSheet(sheetName)
                .createMultipleHeads(headMap);

        List<WarehouseDetailDO> warehouseDetailList = getWarehouseDetailList(param);

        Map<String, List<?>> sheetMap = new HashMap<>();
        if (CollectionUtils.isEmpty(warehouseDetailList)) {
            cascadeSelectTool.writeData(sheetMap);
            cascadeSelectTool.writeOutputStream(response, fileName + ".xlsx");
            return;
        }

        List<String> userCodeList = warehouseDetailList.stream().map(WarehouseDetailDO::getUserCode).distinct().collect(Collectors.toList());
        Map<String, AttendanceUser> userNameMap = userService.listByUserCodes(userCodeList)
                .stream()
                .collect(Collectors.toMap(AttendanceUser::getUserCode, Function.identity(), (v1, v2) -> v1));

        AttendanceDept attendanceDept = deptService.getByDeptId(param.getOcId());
        String certificateTypeCode = getCertificateType(attendanceDept.getCountry());
        Map<String, UserCertificateDTO> certificateCodeMap = userCertificateSupport.listUserCertificateList(userCodeList, certificateTypeCode)
                .stream()
                .collect(Collectors.toMap(UserCertificateDTO::getUserCode, Function.identity(), (v1, v2) -> v1));

        List<Long> ocIdList = warehouseDetailList.stream()
                .map(o -> Arrays.asList(o.getOcId(), o.getUserOcId()))
                .flatMap(Collection::stream)
                .distinct()
                .collect(Collectors.toList());
        Map<Long, String> ocMap = getOcMap(ocIdList);

        List<String> vendorCodeList = warehouseDetailList.stream()
                .map(o -> Arrays.asList(o.getVendorCode(), o.getUserVendorCode()))
                .flatMap(Collection::stream)
                .distinct()
                .collect(Collectors.toList());
        Map<String, String> vendorMap = warehouseSupplierService.getSupplierByCodes(vendorCodeList);

        // 出入仓时间
        List<Long> detailIdList = warehouseDetailList.stream().map(WarehouseDetailDO::getId).collect(Collectors.toList());
        Map<Long, Date> inRecordMap = Maps.newHashMap();
        Map<Long, Date> outRecordMap = Maps.newHashMap();
        List<WarehouseRecordDO> warehouseRecords = warehouseRecordDao.selectByWarehouseDetailIds(detailIdList);
        if (CollectionUtils.isNotEmpty(warehouseRecords)) {
            Map<Integer, List<WarehouseRecordDO>> recordTypeMap = warehouseRecords.stream().collect(Collectors.groupingBy(WarehouseRecordDO::getRecordType));
            List<WarehouseRecordDO> outRecords = recordTypeMap.get(WarehouseTypeEnum.OUT.getCode());
            if (CollectionUtils.isNotEmpty(outRecords)) {
                outRecordMap = outRecords.stream().collect(Collectors.toMap(WarehouseRecordDO::getWarehouseDetailId,
                        WarehouseRecordDO::getWarehouseTime,
                        (k1, k2) -> k1.after(k2) ? k1 : k2));
            }
            List<WarehouseRecordDO> inRecords = recordTypeMap.get(WarehouseTypeEnum.IN.getCode());
            if (CollectionUtils.isNotEmpty(inRecords)) {
                inRecordMap = inRecords.stream().collect(Collectors.toMap(WarehouseRecordDO::getWarehouseDetailId,
                        WarehouseRecordDO::getWarehouseTime,
                        (k1, k2) -> k1.before(k2) ? k1 : k2));
            }
        }
        List<VendorClassAttendanceDetailImportVO> result = new ArrayList<>();
        for (WarehouseDetailDO warehouseDetailDO : warehouseDetailList) {
            VendorClassAttendanceDetailImportVO vo = new VendorClassAttendanceDetailImportVO();
            vo.setWarehouseDate(DateUtil.formatDate(warehouseDetailDO.getWarehouseDate()));
            vo.setClassesName(warehouseDetailDO.getClassesName());
            vo.setOcName(ocMap.get(warehouseDetailDO.getOcId()));
            vo.setVendorName(vendorMap.get(warehouseDetailDO.getVendorCode()));
            AttendanceUser user = userNameMap.get(warehouseDetailDO.getUserCode());
            if (Objects.nonNull(user)) {
                vo.setUserName(BusinessFieldUtils.getUnifiedUserName(user.getUserName(), user.getUserNameEn()) + BusinessConstant.LEFT_BRACKET + user.getUserCode() + BusinessConstant.RIGHT_BRACKET);
            }
            if (Objects.nonNull(certificateCodeMap.get(warehouseDetailDO.getUserCode()))) {
                vo.setCertificatesCode(certificateCodeMap.get(warehouseDetailDO.getUserCode()).getCertificateCode());
            }
            vo.setInTime(DateUtil.formatDateTime(inRecordMap.get(warehouseDetailDO.getId())));
            if (Objects.nonNull(outRecordMap.get(warehouseDetailDO.getId()))) {
                vo.setOutTime(DateUtil.formatDateTime(outRecordMap.get(warehouseDetailDO.getId())));
            }
            vo.setRequiredAttendanceTime(warehouseDetailDO.getRequiredAttendanceTime().toString());
            if (Objects.nonNull(warehouseDetailDO.getActualAttendanceTime())) {
                vo.setActualAttendanceTime(warehouseDetailDO.getActualAttendanceTime().toString());
            }
            result.add(vo);
        }
        sheetMap.put(sheetName, result);
        cascadeSelectTool.writeData(sheetMap);
        cascadeSelectTool.writeOutputStream(response, fileName + ".xlsx");
    }

    private List<WarehouseDetailDO> getWarehouseDetailList(StatisticVendorExportParam param) {
        WarehouseDetailQuery detailParam = new WarehouseDetailQuery();
        detailParam.setWarehouseDate(param.getWarehouseDate());
        detailParam.setOcId(param.getOcId());
        detailParam.setVendorCode(param.getVendorCode());
        detailParam.setClassesId(param.getClassesId());
        detailParam.setEmployeeTypeList(Collections.singletonList(EmploymentTypeEnum.OS_FIXED_SALARY.getCode()));
        Set<Long> recordWarehouseIdList = new HashSet<>();
        if (StringUtils.isNotEmpty(param.getWarehouseType())) {
            if (Objects.equals(BusinessConstant.IN, param.getWarehouseType())) {
                detailParam.setRecordType(WarehouseTypeEnum.IN.getCode());
                List<WarehouseDetailDO> warehouseDetailList = warehouseDetailDao.selectJoinRecordList(detailParam);
                if (CollectionUtils.isEmpty(warehouseDetailList)) {
                    return Collections.emptyList();
                }
                recordWarehouseIdList.addAll(warehouseDetailList.stream().map(WarehouseDetailDO::getId).collect(Collectors.toSet()));
            } else {
                detailParam.setWarehouseStatus(covertWarehouseStatus(param.getWarehouseType()));
            }
        }
        if (CollectionUtils.isNotEmpty(recordWarehouseIdList)) {
            detailParam.setIds(recordWarehouseIdList);
        }
        return warehouseDetailDao.selectDataStatisticsPage(detailParam);
    }

    /**
     * 匹配sheet页模版头
     *
     * @return 模版头
     */
    private Map<String, List<String>> matchSheetHead() {
        Map<String, List<String>> headMap = new HashMap<>();
        if (Locale.CHINA.equals(UserEvnHolder.getLocal())) {
            headMap.put(BusinessConstant.WAREHOUSE_CLASS_ATTENDANCE_CHN_HEAD_SHEET_NAME, BusinessConstant.WAREHOUSE_CLASS_ATTENDANCE_CHN_HEAD_SHEET);
        } else if (Locale.US.equals(UserEvnHolder.getLocal())) {
            headMap.put(BusinessConstant.WAREHOUSE_CLASS_ATTENDANCE_EN_HEAD_SHEET_NAME, BusinessConstant.WAREHOUSE_CLASS_ATTENDANCE_EN_HEAD_SHEET);
        } else if (BusinessConstant.PT_BR.equals(UserEvnHolder.getLocal())) {
            headMap.put(BusinessConstant.WAREHOUSE_CLASS_ATTENDANCE_BR_HEAD_SHEET_NAME, BusinessConstant.WAREHOUSE_CLASS_ATTENDANCE_BR_HEAD_SHEET);
        } else {
            headMap.put(BusinessConstant.WAREHOUSE_CLASS_ATTENDANCE_MX_HEAD_SHEET_NAME, BusinessConstant.WAREHOUSE_CLASS_ATTENDANCE_MX_HEAD_SHEET);
        }
        return headMap;
    }

    private Integer covertWarehouseStatus(String warehouseType) {
        if (Objects.equals(BusinessConstant.WAIT_OUT, warehouseType)) {
            return WarehouseStatusEnum.WAIT_OUT.getCode();
        }
        return WarehouseStatusEnum.OUT.getCode();
    }
}
