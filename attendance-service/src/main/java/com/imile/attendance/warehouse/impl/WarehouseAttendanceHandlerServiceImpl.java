package com.imile.attendance.warehouse.impl;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.base.Throwables;
import com.imile.attendance.abnormal.EmployeeAbnormalAttendanceManage;
import com.imile.attendance.abnormal.dto.AttendanceCalculateHandlerDTO;
import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.enums.AttendanceConcreteTypeEnum;
import com.imile.attendance.enums.AttendanceDataSourceEnum;
import com.imile.attendance.enums.ErrorCodeEnum;
import com.imile.attendance.enums.abnormal.AbnormalAttendanceStatusEnum;
import com.imile.attendance.enums.abnormal.AbnormalOperationTypeEnum;
import com.imile.attendance.enums.abnormal.AttendanceAbnormalTypeEnum;
import com.imile.attendance.enums.form.ApplicationFormAttrKeyEnum;
import com.imile.attendance.enums.form.ApplicationRelationTypeEnum;
import com.imile.attendance.enums.form.FormStatusEnum;
import com.imile.attendance.enums.warehouse.PunchStatusEnum;
import com.imile.attendance.enums.warehouse.WarehouseAbnormalStatusEnum;
import com.imile.attendance.enums.warehouse.WarehouseAttendanceStatusEnum;
import com.imile.attendance.enums.warehouse.WarehousePcsStatusEnum;
import com.imile.attendance.enums.warehouse.WarehouseStatusEnum;
import com.imile.attendance.form.AttendanceApprovalManage;
import com.imile.attendance.form.bo.AttendanceFormDetailBO;
import com.imile.attendance.infrastructure.idwork.DefaultIdWorker;
import com.imile.attendance.infrastructure.repository.abnormal.adapter.WarehouseDetailAbnormalAdapter;
import com.imile.attendance.infrastructure.repository.abnormal.adapter.WarehouseDetailAdapter;
import com.imile.attendance.infrastructure.repository.abnormal.dao.AttendanceEmployeeDetailDao;
import com.imile.attendance.infrastructure.repository.abnormal.dao.EmployeeAbnormalAttendanceDao;
import com.imile.attendance.infrastructure.repository.abnormal.dao.EmployeeAbnormalOperationRecordDao;
import com.imile.attendance.infrastructure.repository.abnormal.model.AttendanceEmployeeDetailDO;
import com.imile.attendance.infrastructure.repository.abnormal.model.EmployeeAbnormalAttendanceDO;
import com.imile.attendance.infrastructure.repository.abnormal.model.EmployeeAbnormalOperationRecordDO;
import com.imile.attendance.infrastructure.repository.common.AttendanceDeptService;
import com.imile.attendance.infrastructure.repository.common.AttendanceUserService;
import com.imile.attendance.infrastructure.repository.common.dto.AttendanceDept;
import com.imile.attendance.infrastructure.repository.common.dto.AttendanceUser;
import com.imile.attendance.infrastructure.repository.common.mapstruct.CommonMapstruct;
import com.imile.attendance.infrastructure.repository.employee.dao.UserInfoDao;
import com.imile.attendance.infrastructure.repository.employee.modle.UserInfoDO;
import com.imile.attendance.infrastructure.repository.form.model.AttendanceFormAttrDO;
import com.imile.attendance.infrastructure.repository.form.model.AttendanceFormDO;
import com.imile.attendance.infrastructure.repository.form.model.AttendanceFormRelationDO;
import com.imile.attendance.infrastructure.repository.rule.dao.PunchClassConfigDao;
import com.imile.attendance.infrastructure.repository.rule.model.PunchClassConfigDO;
import com.imile.attendance.infrastructure.repository.shift.dao.UserShiftConfigDao;
import com.imile.attendance.infrastructure.repository.shift.model.UserShiftConfigDO;
import com.imile.attendance.infrastructure.repository.warehouse.dao.WarehouseDetailAbnormalDao;
import com.imile.attendance.infrastructure.repository.warehouse.dao.WarehouseDetailDao;
import com.imile.attendance.infrastructure.repository.warehouse.model.WarehouseDetailAbnormalDO;
import com.imile.attendance.infrastructure.repository.warehouse.model.WarehouseDetailDO;
import com.imile.attendance.infrastructure.repository.warehouse.model.WarehousePunchPeriodDO;
import com.imile.attendance.infrastructure.repository.warehouse.query.WarehouseDetailQuery;
import com.imile.attendance.migration.MigrationService;
import com.imile.attendance.util.BaseDOUtil;
import com.imile.attendance.util.DateHelper;
import com.imile.attendance.warehouse.WarehouseAttendanceCalculateService;
import com.imile.attendance.warehouse.WarehouseAttendanceDurationCalculateService;
import com.imile.attendance.warehouse.WarehouseAttendanceHandlerService;
import com.imile.attendance.warehouse.WarehouseAttendancePushFinService;
import com.imile.attendance.warehouse.WarehouseManage;
import com.imile.attendance.warehouse.WarehouseUserService;
import com.imile.attendance.warehouse.dto.AttendanceAbnormalCheckDTO;
import com.imile.common.exception.BusinessException;
import com.imile.genesis.api.enums.EmploymentTypeEnum;
import com.imile.util.lang.I18nUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 仓内考勤结果处理服务
 *
 * <AUTHOR>
 * @since 2025/1/23
 */
@Slf4j
@Service
public class WarehouseAttendanceHandlerServiceImpl implements WarehouseAttendanceHandlerService {

    @Resource
    private WarehouseAttendanceCalculateService attendanceCalculateService;

    @Resource
    private WarehouseUserService warehouseUserService;

    @Resource
    private AttendanceUserService userService;

    @Resource
    private AttendanceDeptService deptService;

    @Resource
    private UserInfoDao userInfoDao;

    @Resource
    private WarehouseAttendancePushFinService attendancePushFinService;

    @Resource
    private WarehouseAttendanceCalculateService warehouseAttendanceCalculateService;

    @Resource
    private WarehouseAttendanceDurationCalculateService attendanceDurationCalculateService;

    @Resource
    private EmployeeAbnormalAttendanceDao abnormalAttendanceDao;

    @Resource
    private EmployeeAbnormalOperationRecordDao abnormalOperationRecordDao;

    @Resource
    private WarehouseDetailDao warehouseDetailDao;

    @Resource
    private WarehouseDetailAdapter warehouseDetailAdapter;

    @Resource
    private WarehouseDetailAbnormalDao warehouseDetailAbnormalDao;

    @Resource
    private WarehouseDetailAbnormalAdapter warehouseDetailAbnormalAdapter;

    @Resource
    private DefaultIdWorker defaultIdWorker;

    @Resource
    private WarehouseManage warehouseManage;

    @Resource
    private AttendanceApprovalManage attendanceApprovalManage;

    @Resource
    private UserShiftConfigDao userShiftConfigDao;

    @Resource
    private PunchClassConfigDao punchClassConfigDao;

    @Resource
    private AttendanceEmployeeDetailDao attendanceEmployeeDetailDao;

    @Resource
    private EmployeeAbnormalAttendanceManage abnormalAttendanceManage;

    @Resource
    private MigrationService migrationService;


    @Override
    public void warehouseAttendanceResultHandler(WarehouseDetailDO warehouseDetail, Long dayId, String stateType) {
        //计算考勤时长
        List<WarehousePunchPeriodDO> warehousePunchPeriodDOList = attendanceDurationCalculateService.calculateAttendanceHours(warehouseDetail, false);

        //计算考勤结果
        AttendanceCalculateHandlerDTO attendanceHandlerDTO = new AttendanceCalculateHandlerDTO();
        attendanceHandlerDTO.setUserCodes(warehouseDetail.getUserCode());
        attendanceHandlerDTO.setAttendanceDayId(dayId);
        attendanceHandlerDTO.setOcId(warehouseDetail.getOcId());
        attendanceHandlerDTO.setClassId(warehouseDetail.getClassesId());
        attendanceHandlerDTO.setWarehouseAttendanceConfigId(warehouseDetail.getAttendanceConfigId());
        attendanceHandlerDTO.setActualAttendanceTime(warehouseDetail.getActualAttendanceTime());
        attendanceHandlerDTO.setActualWorkingHours(warehouseDetail.getActualWorkingHours());
        warehouseAttendanceCalculateService.warehouseAttendanceCalculate(attendanceHandlerDTO);

        //考勤结果处理
        attendanceAbnormalHandler(warehouseDetail, dayId, true, warehousePunchPeriodDOList);

        //仓内考勤同步财务系统
        if (Objects.equals(EmploymentTypeEnum.OS_FIXED_SALARY.getCode(), warehouseDetail.getEmployeeType())) {
            //仓内考勤同步财务系统
            attendancePushFinService.asyncPushFin(Collections.singletonList(warehouseDetail.getId()), stateType);
        }
    }

    @Override
    public AttendanceAbnormalCheckDTO checkExistAttendanceException(Long dayId, Long userId, Long classId) {
        List<EmployeeAbnormalAttendanceDO> abnormalAttendanceList = abnormalAttendanceDao.selectAbnormalAttendanceByDayIdList(Collections.singletonList(userId), Collections.singletonList(dayId))
                .stream()
                .filter(abnormal -> Objects.isNull(classId)
                        || Objects.equals(classId, abnormal.getPunchClassConfigId())).
                collect(Collectors.toList());

        AttendanceAbnormalCheckDTO result = new AttendanceAbnormalCheckDTO();
        boolean attendanceExceptionFlag = false;
        List<AttendanceAbnormalCheckDTO.AttendanceAbnormalRecord> attendanceAbnormalRecords = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(abnormalAttendanceList)) {
            //过滤未通过的，包含待处理+已过期
            List<EmployeeAbnormalAttendanceDO> filterNoPassList = abnormalAttendanceList
                    .stream()
                    .filter(record -> !AbnormalAttendanceStatusEnum.PASS.getCode().equals(record.getStatus()))
                    .collect(Collectors.toList());

            if (CollectionUtils.isNotEmpty(filterNoPassList)) {
                attendanceExceptionFlag = true;
                //过滤已过期
                List<EmployeeAbnormalAttendanceDO> filterExpiredList = filterNoPassList
                        .stream()
                        .filter(record -> AbnormalAttendanceStatusEnum.EXPIRED.getCode().equals(record.getStatus()))
                        .collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(filterExpiredList)) {
                    attendanceAbnormalRecords.addAll(filterExpiredList.stream().map(noPass -> convertAttendanceAbnormalRecord(noPass, WarehouseAbnormalStatusEnum.EXPIRED.getCode())).collect(Collectors.toList()));
                    List<Long> expiredIds = filterExpiredList.stream().map(EmployeeAbnormalAttendanceDO::getId).distinct().collect(Collectors.toList());
                    List<EmployeeAbnormalAttendanceDO> filterProcessingList = abnormalAttendanceList
                            .stream()
                            .filter(record -> !expiredIds.contains(record.getId()))
                            .collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(filterProcessingList)) {
                        attendanceAbnormalRecords.addAll(filterProcessingList.stream().map(noPass -> convertAttendanceAbnormalRecord(noPass, WarehouseAbnormalStatusEnum.PENDING_PROCESSING.getCode())).collect(Collectors.toList()));
                    }
                } else {
                    attendanceAbnormalRecords.addAll(filterNoPassList.stream().map(noPass -> convertAttendanceAbnormalRecord(noPass, WarehouseAbnormalStatusEnum.PENDING_PROCESSING.getCode())).collect(Collectors.toList()));
                }
            }

            //过滤已通过 确认异常也是已通过，可通过操作记录表判断
            List<EmployeeAbnormalAttendanceDO> filterPassList = abnormalAttendanceList
                    .stream()
                    .filter(record -> AbnormalAttendanceStatusEnum.PASS.getCode().equals(record.getStatus()))
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(filterPassList)) {
                List<Long> abnormalIds = filterPassList.stream().map(EmployeeAbnormalAttendanceDO::getId).distinct().collect(Collectors.toList());
                List<EmployeeAbnormalOperationRecordDO> abnormalOperationRecordDOList = abnormalOperationRecordDao.selectByAbnormalList(abnormalIds);
                List<EmployeeAbnormalOperationRecordDO> abnormalOperationRecordList = abnormalOperationRecordDOList
                        .stream()
                        .filter(abnormalOperation -> AbnormalOperationTypeEnum.ABNORMAL_CONFIRM.getCode().equals(abnormalOperation.getOperationType())).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(abnormalOperationRecordList)) {
                    attendanceExceptionFlag = true;
                    List<Long> abnormalIdList = abnormalOperationRecordList
                            .stream()
                            .map(EmployeeAbnormalOperationRecordDO::getAbnormalId)
                            .distinct()
                            .collect(Collectors.toList());
                    List<AttendanceAbnormalCheckDTO.AttendanceAbnormalRecord> confirmAbnormalList = filterPassList
                            .stream()
                            .filter(pass -> abnormalIdList.contains(pass.getId()))
                            .collect(Collectors.toList())
                            .stream()
                            .map(pass -> convertAttendanceAbnormalRecord(pass, WarehouseAbnormalStatusEnum.CONFIRM_ABNORMAL.getCode())).collect(Collectors.toList());

                    List<AttendanceAbnormalCheckDTO.AttendanceAbnormalRecord> passAbnormalList = filterPassList
                            .stream()
                            .filter(pass -> !abnormalIdList.contains(pass.getId()))
                            .collect(Collectors.toList())
                            .stream()
                            .map(pass -> convertAttendanceAbnormalRecord(pass, WarehouseAbnormalStatusEnum.PROCESSED.getCode())).collect(Collectors.toList());

                    attendanceAbnormalRecords.addAll(confirmAbnormalList);
                    if (CollectionUtils.isNotEmpty(passAbnormalList)) {
                        attendanceAbnormalRecords.addAll(passAbnormalList);
                    }
                } else {
                    attendanceAbnormalRecords.addAll(filterPassList.stream().map(noPass -> convertAttendanceAbnormalRecord(noPass, WarehouseAbnormalStatusEnum.PROCESSED.getCode())).collect(Collectors.toList()));
                }
            }
        } else {
            List<AttendanceEmployeeDetailDO> employeeDetailDOList = attendanceEmployeeDetailDao.selectAttendanceByDayIdList(userId, Collections.singletonList(dayId));
            if (CollectionUtils.isEmpty(employeeDetailDOList)) {
                log.error("用户相关考勤配置异常,userId: {},dayId: {}", userId, dayId);
                attendanceExceptionFlag = true;
            }
        }
        result.setExistException(attendanceExceptionFlag);
        if (CollectionUtils.isNotEmpty(attendanceAbnormalRecords)) {
            result.setAttendanceAbnormalRecords(attendanceAbnormalRecords);
        }
        return result;
    }

    @Override
    public void attendanceAbnormalHandler(WarehouseDetailDO warehouseDetail,
                                          Long dayId,
                                          Boolean updateFaceRecord,
                                          List<WarehousePunchPeriodDO> warehousePunchPeriodDOList) {
        AttendanceAbnormalCheckDTO attendanceAbnormalCheck = checkExistAttendanceException(dayId, warehouseDetail.getUserId(), warehouseDetail.getClassesId());
        log.info("考勤异常计算, userCode: {} ,dayId: {} ,结果: {}", warehouseDetail.getUserCode(), dayId, attendanceAbnormalCheck.getExistException());

        List<WarehouseDetailAbnormalDO> warehouseDetailAbnormalList = new ArrayList<>();
        if (attendanceAbnormalCheck.getExistException()) {
            if (CollectionUtils.isNotEmpty(attendanceAbnormalCheck.getAttendanceAbnormalRecords())) {
                Map<Integer, List<AttendanceAbnormalCheckDTO.AttendanceAbnormalRecord>> abnormalRecordMap = attendanceAbnormalCheck.getAttendanceAbnormalRecords()
                        .stream()
                        .collect(Collectors.groupingBy(AttendanceAbnormalCheckDTO.AttendanceAbnormalRecord::getStatus));
                //存在待处理异常
                convertWarehouseDetailAbnormal(warehouseDetailAbnormalList, warehouseDetail, abnormalRecordMap.get(WarehouseAbnormalStatusEnum.PENDING_PROCESSING.getCode()), BusinessConstant.N);
                //通过
                convertWarehouseDetailAbnormal(warehouseDetailAbnormalList, warehouseDetail, abnormalRecordMap.get(WarehouseAbnormalStatusEnum.PROCESSED.getCode()), BusinessConstant.Y);
                //确认异常
                convertWarehouseDetailAbnormal(warehouseDetailAbnormalList, warehouseDetail, abnormalRecordMap.get(WarehouseAbnormalStatusEnum.CONFIRM_ABNORMAL.getCode()), BusinessConstant.Y);
                //已过期
                convertWarehouseDetailAbnormal(warehouseDetailAbnormalList, warehouseDetail, abnormalRecordMap.get(WarehouseAbnormalStatusEnum.EXPIRED.getCode()), BusinessConstant.Y);
            }
            warehouseDetail.setAttendanceStatus(WarehouseAttendanceStatusEnum.ABNORMAL.getCode());
        } else {
            if (CollectionUtils.isNotEmpty(attendanceAbnormalCheck.getAttendanceAbnormalRecords())) {
                convertWarehouseDetailAbnormal(warehouseDetailAbnormalList, warehouseDetail, attendanceAbnormalCheck.getAttendanceAbnormalRecords(), BusinessConstant.Y);
            } else {
                warehouseDetailAbnormalAdapter.deleteByWarehouseDetailId(warehouseDetail.getId());
            }
            //不存在异常
            warehouseDetail.setAttendanceStatus(WarehouseAttendanceStatusEnum.NORMAL.getCode());
        }
        setPcsStatus(warehouseDetail);
        BaseDOUtil.fillDOUpdateFromUCenter(warehouseDetail);
        warehouseManage.warehouseAttendanceAbnormalUpdate(warehouseDetail, warehouseDetailAbnormalList, warehousePunchPeriodDOList, updateFaceRecord);
    }

    @Override
    public void warehouseAttendanceLeaveAuditPassHandler(AttendanceFormDO formDO, AttendanceCalculateHandlerDTO calculateHandlerDTO) {
        AttendanceUser userInfoDO = userService.getByUserId(formDO.getUserId());
        if (!warehouseUserService.isWarehouseSupportUser(userInfoDO)) {
            return;
        }

        //请假通过且无其他异常 生成考勤结果数据
        Date warehouseDate = DateHelper.transferDayIdToDate(calculateHandlerDTO.getAttendanceDayId());
        List<WarehouseDetailDO> warehouseDetailList = warehouseDetailDao.selectByWarehouseDateAndUserId(warehouseDate, userInfoDO.getId());
        if (CollectionUtils.isEmpty(warehouseDetailList)) {
            //提前请假仓内没生成日报时不处理
            return;
        }

        //更新
        WarehouseDetailDO warehouseDetailDO = warehouseDetailList.get(0);
        //计算考勤时长
        List<WarehousePunchPeriodDO> warehousePunchPeriodDOList = attendanceDurationCalculateService.calculateAttendanceHours(warehouseDetailDO, false);

        AttendanceCalculateHandlerDTO attendanceHandlerDTO = new AttendanceCalculateHandlerDTO();
        attendanceHandlerDTO.setAttendanceDayId(calculateHandlerDTO.getAttendanceDayId());
        attendanceHandlerDTO.setUserCodes(warehouseDetailDO.getUserCode());
        attendanceHandlerDTO.setWarehouseAttendanceConfigId(warehouseDetailDO.getAttendanceConfigId());
        attendanceHandlerDTO.setOcId(warehouseDetailDO.getOcId());
        attendanceHandlerDTO.setClassId(warehouseDetailDO.getClassesId());
        attendanceHandlerDTO.setActualAttendanceTime(warehouseDetailDO.getActualAttendanceTime());
        attendanceHandlerDTO.setActualWorkingHours(warehouseDetailDO.getActualWorkingHours());
        attendanceCalculateService.warehouseEmployeeAttendanceHandler(attendanceHandlerDTO);

        //查询考勤结果是否还存在异常记录
        AttendanceAbnormalCheckDTO attendanceAbnormalCheck = checkExistAttendanceException(calculateHandlerDTO.getAttendanceDayId(), userInfoDO.getId(), warehouseDetailDO.getClassesId());

        List<WarehouseDetailAbnormalDO> warehouseDetailAbnormalList = new ArrayList<>();
        if (attendanceAbnormalCheck.getExistException()) {
            if (CollectionUtils.isNotEmpty(attendanceAbnormalCheck.getAttendanceAbnormalRecords())) {
                Map<Integer, List<AttendanceAbnormalCheckDTO.AttendanceAbnormalRecord>> abnormalRecordMap = attendanceAbnormalCheck.getAttendanceAbnormalRecords()
                        .stream()
                        .collect(Collectors.groupingBy(AttendanceAbnormalCheckDTO.AttendanceAbnormalRecord::getStatus));
                //存在待处理异常
                convertWarehouseDetailAbnormal(warehouseDetailAbnormalList, warehouseDetailDO, abnormalRecordMap.get(WarehouseAbnormalStatusEnum.PENDING_PROCESSING.getCode()), BusinessConstant.N);
                //通过
                convertWarehouseDetailAbnormal(warehouseDetailAbnormalList, warehouseDetailDO, abnormalRecordMap.get(WarehouseAbnormalStatusEnum.PROCESSED.getCode()), BusinessConstant.Y);
                //确认异常
                convertWarehouseDetailAbnormal(warehouseDetailAbnormalList, warehouseDetailDO, abnormalRecordMap.get(WarehouseAbnormalStatusEnum.CONFIRM_ABNORMAL.getCode()), BusinessConstant.Y);
                //已过期
                convertWarehouseDetailAbnormal(warehouseDetailAbnormalList, warehouseDetailDO, abnormalRecordMap.get(WarehouseAbnormalStatusEnum.EXPIRED.getCode()), BusinessConstant.Y);
            }
            warehouseDetailDO.setAttendanceStatus(WarehouseAttendanceStatusEnum.ABNORMAL.getCode());
        } else {
            if (CollectionUtils.isNotEmpty(attendanceAbnormalCheck.getAttendanceAbnormalRecords())) {
                convertWarehouseDetailAbnormal(warehouseDetailAbnormalList, warehouseDetailDO, attendanceAbnormalCheck.getAttendanceAbnormalRecords(), BusinessConstant.Y);
            }
            //不存在异常
            warehouseDetailDO.setAttendanceStatus(WarehouseAttendanceStatusEnum.NORMAL.getCode());
        }
        BaseDOUtil.fillDOUpdateFromUCenter(warehouseDetailDO);
        warehouseManage.warehouseAttendanceAbnormalUpdate(warehouseDetailDO, warehouseDetailAbnormalList, warehousePunchPeriodDOList, false);
    }

    @Override
    public void warehouseAttendanceReissueAuditPassHandler(AttendanceUser userInfoDO, Long abnormalId, Long dayId) {
        if (!warehouseUserService.isWarehouseSupportUser(userInfoDO)) {
            return;
        }
        WarehouseDetailDO warehouseDetailDO = null;
        if (Objects.nonNull(abnormalId)) {
            WarehouseDetailAbnormalDO warehouseDetailAbnormalDO = warehouseDetailAbnormalDao.selectByAbnormalId(abnormalId);
            if (Objects.nonNull(warehouseDetailAbnormalDO)) {
                warehouseDetailDO = warehouseDetailDao.selectById(warehouseDetailAbnormalDO.getWarehouseDetailId());
                //补下班卡
                if (Objects.equals(AttendanceAbnormalTypeEnum.AFTER_OFFICE_LACK.getCode(), warehouseDetailAbnormalDO.getAbnormalType())) {
                    warehouseDetailDO.setWarehouseStatus(WarehouseStatusEnum.OUT.getCode());
                }
            }
        } else {
            List<WarehouseDetailDO> warehouseDetailList = warehouseDetailDao.selectByWarehouseDateAndUserId(DateHelper.transferDayIdToDate(dayId), userInfoDO.getId());
            if (CollectionUtils.isNotEmpty(warehouseDetailList)) {
                warehouseDetailDO = warehouseDetailList.get(BusinessConstant.FIRST_ELEMENT_INDEX);
            } else {
                warehouseDetailDO = new WarehouseDetailDO();
            }
        }

        //计算考勤时长
        List<WarehousePunchPeriodDO> warehousePunchPeriodDOList = attendanceDurationCalculateService.calculateAttendanceHours(warehouseDetailDO, false);

        //仓内计算异常
        AttendanceCalculateHandlerDTO attendanceHandlerDTO = new AttendanceCalculateHandlerDTO();
        attendanceHandlerDTO.setUserCodes(warehouseDetailDO.getUserCode());
        attendanceHandlerDTO.setAttendanceDayId(dayId);
        attendanceHandlerDTO.setOcId(warehouseDetailDO.getOcId());
        attendanceHandlerDTO.setClassId(warehouseDetailDO.getClassesId());
        attendanceHandlerDTO.setWarehouseAttendanceConfigId(warehouseDetailDO.getAttendanceConfigId());
        attendanceHandlerDTO.setActualAttendanceTime(warehouseDetailDO.getActualAttendanceTime());
        attendanceHandlerDTO.setActualWorkingHours(warehouseDetailDO.getActualWorkingHours());
        warehouseAttendanceCalculateService.warehouseAttendanceCalculate(attendanceHandlerDTO);

        attendanceAbnormalHandler(warehouseDetailDO, dayId, false, warehousePunchPeriodDOList);
        //仓内考勤同步财务系统
        attendancePushFinService.asyncPushFin(Collections.singletonList(warehouseDetailDO.getId()), BusinessConstant.OFF_WORK_PUNCH_REISSUE);
    }

    @Override
    public void warehouseAttendanceNoPunchHandler(List<UserInfoDO> noClassUserList,
                                                  List<UserInfoDO> offClassUserList,
                                                  List<UserInfoDO> userListPreDay,
                                                  List<UserInfoDO> userList,
                                                  Long preDayId,
                                                  Long dayId) {
        //排班了
        if (CollectionUtils.isNotEmpty(userListPreDay) && Objects.nonNull(preDayId)) {
            schedulingPlan(userListPreDay, preDayId);
        }

        //排班了
        if (CollectionUtils.isNotEmpty(userList) && Objects.nonNull(dayId)) {
            schedulingPlan(userList, dayId);
        }

        //排班PH/OFF
        if (CollectionUtils.isNotEmpty(offClassUserList)) {
            List<String> userCodeList = offClassUserList.stream().filter(user -> warehouseUserService.isWarehouseEmployeeSupport(CommonMapstruct.INSTANCE.mapToUser(user))).map(UserInfoDO::getUserCode).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(userCodeList)) {
                String userCacheCodes = StringUtils.join(userCodeList, BusinessConstant.DEFAULT_DELIMITER);
                AttendanceCalculateHandlerDTO attendanceHandlerDTO = new AttendanceCalculateHandlerDTO();
                attendanceHandlerDTO.setAttendanceDayId(dayId);
                attendanceHandlerDTO.setUserCodes(userCacheCodes);
                attendanceCalculateService.warehouseEmployeeAttendanceHandler(attendanceHandlerDTO);
            }
        }

        //未排班
        if (CollectionUtils.isNotEmpty(noClassUserList)) {
            if (CollectionUtils.isNotEmpty(offClassUserList)) {
                noClassUserList.removeAll(offClassUserList);
            }
            noSchedulingPlan(noClassUserList, dayId);
        }
    }

    @Override
    public void warehouseConfirmExceptionHandler(Long abnormalId, Long userId, Long dayId) {
        WarehouseDetailAbnormalDO warehouseDetailAbnormalDO = warehouseDetailAbnormalDao.selectByAbnormalId(abnormalId);
        if (Objects.nonNull(warehouseDetailAbnormalDO)) {
            WarehouseDetailDO warehouseDetailDO = warehouseDetailDao.selectById(warehouseDetailAbnormalDO.getWarehouseDetailId());
            attendanceAbnormalHandler(warehouseDetailDO, dayId, false, null);
        }
    }

    @Override
    public void warehouseAddDurationPassHandler(AttendanceFormDetailBO attendanceFormDetailBO) {
        log.info("补时长审批回调,params:{}", JSON.toJSONString(attendanceFormDetailBO));
        AttendanceFormDO formDO = attendanceFormDetailBO.getFormDO();
        formDO.setFormStatus(FormStatusEnum.PASS.getCode());
        List<AttendanceFormAttrDO> attrDOS = attendanceFormDetailBO.getAttrDOList();
        Map<String, AttendanceFormAttrDO> attrMap = attrDOS.stream().collect(Collectors.toMap(AttendanceFormAttrDO::getAttrKey, o -> o, (v1, v2) -> v1));

        AttendanceFormAttrDO actualAttendanceTimeAttrDO = attrMap.get(ApplicationFormAttrKeyEnum.actualAttendanceTime.getLowerCode());
        AttendanceFormAttrDO newActualAttendanceTimeAttrDO = attrMap.get(ApplicationFormAttrKeyEnum.newActualAttendanceTime.getLowerCode());
        AttendanceFormAttrDO actualWorkingHoursAttrDO = attrMap.get(ApplicationFormAttrKeyEnum.actualWorkingHours.getLowerCode());
        AttendanceFormAttrDO newActualWorkingHoursAttrDO = attrMap.get(ApplicationFormAttrKeyEnum.newActualWorkingHours.getLowerCode());

        if (Objects.isNull(newActualAttendanceTimeAttrDO) && Objects.isNull(newActualWorkingHoursAttrDO)) {
            log.error("仓内补时长实出勤时长和实工作时长均为空");
            throw BusinessException.get(ErrorCodeEnum.ATTENDANCE_APPROVAL_BASIC_INFO_EMPTY.getCode(), I18nUtils.getMessage(ErrorCodeEnum.ATTENDANCE_APPROVAL_BASIC_INFO_EMPTY.getDesc()));
        }
        AttendanceUser userInfoDO = userService.getByUserId(formDO.getUserId());
        if (userInfoDO == null) {
            throw BusinessException.get(ErrorCodeEnum.ACCOUNT_NOT_EXITS.getCode(), I18nUtils.getMessage(ErrorCodeEnum.ACCOUNT_NOT_EXITS.getDesc()));
        }

        List<AttendanceFormRelationDO> relationDOS = attendanceFormDetailBO.getRelationDOList();
        List<AttendanceFormRelationDO> abnormalRelationList = relationDOS.stream().filter(item -> StringUtils.equalsIgnoreCase(item.getRelationType(), ApplicationRelationTypeEnum.ABNORMAL.getCode())).collect(Collectors.toList());
        List<EmployeeAbnormalAttendanceDO> abnormalAttendanceDOList = abnormalAttendanceManage.selectByIdList(Collections.singletonList(abnormalRelationList.get(0).getRelationId()));
        if (CollectionUtils.isEmpty(abnormalAttendanceDOList)) {
            throw BusinessException.get(ErrorCodeEnum.USER_NOT_HAVE_ABNORMAL_ATTENDANCE_RECORD_ON_THE_CURRENT_DAY.getCode(), I18nUtils.getMessage(ErrorCodeEnum.USER_NOT_HAVE_ABNORMAL_ATTENDANCE_RECORD_ON_THE_CURRENT_DAY.getDesc()));
        }
        EmployeeAbnormalAttendanceDO abnormalAttendanceDO = abnormalAttendanceDOList.get(0);
        if (!StringUtils.equalsIgnoreCase(abnormalAttendanceDO.getStatus(), AbnormalAttendanceStatusEnum.IN_REVIEW.getCode())) {
            //说明单据状态已完结，不抛异常直接退出
            return;
        }
        abnormalAttendanceDO.setStatus(AbnormalAttendanceStatusEnum.PASS.getCode());
        BaseDOUtil.fillDOUpdate(abnormalAttendanceDO);

        WarehouseDetailAbnormalDO warehouseDetailAbnormalDO = warehouseDetailAbnormalDao.selectByAbnormalId(abnormalAttendanceDO.getId());
        warehouseDetailAbnormalDO.setProcessed(BusinessConstant.Y);
        BaseDOUtil.fillDOUpdate(warehouseDetailAbnormalDO);
        WarehouseDetailDO warehouseDetailDO = warehouseDetailDao.selectById(warehouseDetailAbnormalDO.getWarehouseDetailId());

        if (Objects.isNull(warehouseDetailDO)) {
            log.error("仓内补时长审批通过关联查询仓内考勤统计表异常");
            return;
        }

        BigDecimal requiredAttendanceTime = warehouseDetailDO.getRequiredAttendanceTime();
        BigDecimal legalWorkingHours = warehouseDetailDO.getLegalWorkingHours();

        AttendanceEmployeeDetailDO attendanceEmployeeDetailDO = new AttendanceEmployeeDetailDO();
        Date date = abnormalAttendanceDO.getDate();
        attendanceEmployeeDetailDO.setId(defaultIdWorker.nextId());
        attendanceEmployeeDetailDO.setUserId(userInfoDO.getId());
        attendanceEmployeeDetailDO.setLocationCountry(userInfoDO.getLocationCountry());
        attendanceEmployeeDetailDO.setYear((long) DateUtil.year(date));
        attendanceEmployeeDetailDO.setMonth((long) (DateUtil.month(date) + 1));
        attendanceEmployeeDetailDO.setDay(DateUtil.dayOfMonth(date));
        attendanceEmployeeDetailDO.setDayId(abnormalAttendanceDO.getDayId());
        attendanceEmployeeDetailDO.setDate(DateUtil.beginOfDay(date));
        attendanceEmployeeDetailDO.setDataSource(AttendanceDataSourceEnum.APPROVAL.getCode());
        attendanceEmployeeDetailDO.setAttendanceType(abnormalAttendanceDO.getAttendanceType());
        attendanceEmployeeDetailDO.setConcreteType(AttendanceConcreteTypeEnum.P.getCode());
        attendanceEmployeeDetailDO.setIsAttendance(BusinessConstant.Y);
        attendanceEmployeeDetailDO.setDeptId(userInfoDO.getDeptId());
        attendanceEmployeeDetailDO.setPostId(userInfoDO.getPostId());
        attendanceEmployeeDetailDO.setLegalWorkingHours(legalWorkingHours);
        attendanceEmployeeDetailDO.setFormId(formDO.getId());
        attendanceEmployeeDetailDO.setClassId(abnormalAttendanceDO.getPunchClassConfigId());
        BaseDOUtil.fillDOInsert(attendanceEmployeeDetailDO);

        BigDecimal actualAttendanceTime = Objects.nonNull(actualAttendanceTimeAttrDO) ? Convert.toBigDecimal(actualAttendanceTimeAttrDO.getAttrValue()) : BigDecimal.ZERO;
        BigDecimal updateActualAttendanceTime = Objects.nonNull(newActualAttendanceTimeAttrDO) ? Convert.toBigDecimal(newActualAttendanceTimeAttrDO.getAttrValue()) : BigDecimal.ZERO;
        BigDecimal actualWorkingHours = Objects.nonNull(actualWorkingHoursAttrDO) ? Convert.toBigDecimal(actualWorkingHoursAttrDO.getAttrValue()) : BigDecimal.ZERO;
        BigDecimal updateActualWorkingHours = Objects.nonNull(newActualWorkingHoursAttrDO) ? Convert.toBigDecimal(newActualWorkingHoursAttrDO.getAttrValue()) : BigDecimal.ZERO;

        //查询当天正常考勤表的数据
        List<AttendanceEmployeeDetailDO> attendanceEmployeeList = attendanceEmployeeDetailDao.selectByUserId(Collections.singletonList(abnormalAttendanceDO.getUserId()), abnormalAttendanceDO.getDayId());
        //请假时长
        BigDecimal leaveMinutes = BigDecimal.ZERO;
        for (AttendanceEmployeeDetailDO detailDO : attendanceEmployeeList) {
            if (Objects.nonNull(detailDO.getLeaveMinutes())) {
                leaveMinutes = leaveMinutes.add(detailDO.getLeaveMinutes());
            }
        }

        if (updateActualWorkingHours.compareTo(BigDecimal.ZERO) > 0) {
            BigDecimal remainWorkingMinutes = legalWorkingHours.multiply(BusinessConstant.MINUTES).subtract(leaveMinutes);
            BigDecimal updateActualWorkingMinutes = updateActualWorkingHours.multiply(BusinessConstant.MINUTES);
            BigDecimal actualWorkingMinutes = actualWorkingHours.multiply(BusinessConstant.MINUTES);
            BigDecimal addActualWorkingMinutes = updateActualWorkingMinutes.subtract(actualWorkingMinutes);
            if (addActualWorkingMinutes.compareTo(remainWorkingMinutes) > -1) {
                attendanceEmployeeDetailDO.setAddDurationMinutes(remainWorkingMinutes);
            } else {
                attendanceEmployeeDetailDO.setAddDurationMinutes(addActualWorkingMinutes);
            }
            attendanceEmployeeDetailDO.setActualPunchMinutes(actualWorkingMinutes);
        } else {
            attendanceEmployeeDetailDO.setActualPunchMinutes(legalWorkingHours.multiply(BusinessConstant.MINUTES));
            attendanceEmployeeDetailDO.setAddDurationMinutes(BigDecimal.ZERO);
        }

        BigDecimal attendanceTimeMinutes = BigDecimal.ZERO;
        if (updateActualAttendanceTime.compareTo(BigDecimal.ZERO) > 0) {
            BigDecimal remainAttendanceMinutes = requiredAttendanceTime.multiply(BusinessConstant.MINUTES).subtract(leaveMinutes);
            BigDecimal updateActualAttendanceMinutes = updateActualAttendanceTime.multiply(BusinessConstant.MINUTES);
            BigDecimal actualAttendanceMinutes = actualAttendanceTime.multiply(BusinessConstant.MINUTES);
            BigDecimal addActualAttendanceMinutes = updateActualAttendanceMinutes.subtract(actualAttendanceMinutes);
            if (addActualAttendanceMinutes.compareTo(remainAttendanceMinutes) > -1) {
                attendanceTimeMinutes = remainAttendanceMinutes;
            } else {
                attendanceTimeMinutes = addActualAttendanceMinutes;
            }
        }

        warehouseDetailDO.setActualAttendanceTime((warehouseDetailDO.getActualAttendanceTime().multiply(BusinessConstant.MINUTES).add(attendanceTimeMinutes)).divide(BusinessConstant.MINUTES, 2, RoundingMode.HALF_UP));
        warehouseDetailDO.setActualWorkingHours((attendanceEmployeeDetailDO.getActualPunchMinutes().add(attendanceEmployeeDetailDO.getAddDurationMinutes())).divide(BusinessConstant.MINUTES, 2, RoundingMode.HALF_UP));

        if (warehouseDetailDO.getActualAttendanceTime().compareTo(warehouseDetailDO.getRequiredAttendanceTime()) > -1
                && warehouseDetailDO.getActualWorkingHours().compareTo(warehouseDetailDO.getLegalWorkingHours()) > -1) {
            warehouseDetailDO.setAttendanceStatus(WarehouseAttendanceStatusEnum.NORMAL.getCode());
            warehouseDetailDO.setAbsenceTime(BigDecimal.ZERO);
        } else {
            warehouseDetailDO.setAbsenceTime(warehouseDetailDO.getRequiredAttendanceTime().subtract(warehouseDetailDO.getActualAttendanceTime()));
        }

        attendanceApprovalManage.addDurationUpdate(formDO, attendanceEmployeeDetailDO, abnormalAttendanceDO, warehouseDetailDO, warehouseDetailAbnormalDO);

        //仓内考勤同步财务系统
        attendancePushFinService.asyncPushFin(Collections.singletonList(warehouseDetailDO.getId()), BusinessConstant.OFF_WORK_PUNCH_REISSUE);
    }

    @Override
    public void retryCalculateAbnormal(WarehouseDetailQuery param) {
        if (CollectionUtils.isEmpty(param.getIds())) {
            return;
        }

        List<WarehouseDetailDO> warehouseDetailDOS = warehouseDetailDao.selectByIds(param.getIds());
        if (CollectionUtils.isEmpty(warehouseDetailDOS)) {
            return;
        }
        warehouseDetailDOS.forEach(warehouseDetail -> {
            Long dayId = DateHelper.getDayId(warehouseDetail.getWarehouseDate());
            warehouseAttendanceResultHandler(warehouseDetail, dayId, BusinessConstant.OFF_WORK_PUNCH_REISSUE);
        });
    }

    @Override
    public void calculateAbnormal(String userCode, Long dayId) {
        if (Objects.isNull(dayId) || StringUtils.isEmpty(userCode)) {
            return;
        }
        UserInfoDO userInfoDO = userInfoDao.getByUserCode(userCode);
        if (Objects.isNull(userInfoDO)) {
            return;
        }
        AttendanceCalculateHandlerDTO attendanceHandlerDTO = new AttendanceCalculateHandlerDTO();
        attendanceHandlerDTO.setUserCodes(userCode);
        attendanceHandlerDTO.setAttendanceDayId(dayId);
        attendanceHandlerDTO.setOcId(userInfoDO.getDeptId());
        warehouseAttendanceCalculateService.warehouseAttendanceCalculate(attendanceHandlerDTO);
    }

    private void schedulingPlan(List<UserInfoDO> userListPreDay, Long dayId) {
        //过滤得到仓内自有用户
        List<UserInfoDO> warehouseUserList = userListPreDay.stream()
                .filter(user -> warehouseUserService.isWarehouseEmployeeSupport(CommonMapstruct.INSTANCE.mapToUser(user))
                        && migrationService.verifyUserIsEnableNewAttendance(user.getId()))
                .collect(Collectors.toList());
        List<Long> userIds = warehouseUserList.stream().map(UserInfoDO::getId).distinct().collect(Collectors.toList());
        log.info("仓内员工排班生成异常入参, userIds: [{}] , dayId: {}", userIds, dayId);
        //查询用户排班的班次
        List<UserShiftConfigDO> userShiftConfigDOList = userShiftConfigDao.selectBatchUserRecord(userIds, Collections.singletonList(dayId));
        Map<Long, UserShiftConfigDO> daysConfigDTOMap = userShiftConfigDOList.stream().collect(Collectors.toMap(UserShiftConfigDO::getUserId, Function.identity(), (v1, v2) -> v1));
        List<Long> classIds = userShiftConfigDOList.stream().map(UserShiftConfigDO::getPunchClassConfigId).distinct().collect(Collectors.toList());
        //查询班次信息
        List<PunchClassConfigDO> punchClassConfigDOList = punchClassConfigDao.selectLatestAndActiveByIds(classIds);
        Map<Long, PunchClassConfigDO> attendancePunchClassConfigMap = punchClassConfigDOList.stream().collect(Collectors.toMap(PunchClassConfigDO::getId, Function.identity()));
        //查询网点部门
        List<String> ocCodes = warehouseUserList.stream().map(UserInfoDO::getOcCode).filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
        Map<String, Long> deptIdMap = deptService.listByOcCode(ocCodes).stream().collect(Collectors.toMap(AttendanceDept::getOcCode, AttendanceDept::getId, (v1, v2) -> v1));
        warehouseUserList.forEach(userInfoDO -> {
            try {

                UserShiftConfigDO userShiftConfigDO = daysConfigDTOMap.get(userInfoDO.getId());
                if (Objects.isNull(userShiftConfigDO)) {
                    return;
                }
                PunchClassConfigDO classConfigDO = attendancePunchClassConfigMap.get(userShiftConfigDO.getPunchClassConfigId());
                if (Objects.isNull(classConfigDO)) {
                    return;
                }
                List<WarehouseDetailDO> warehouseDetailDOS = warehouseDetailDao.selectByWarehouseDateAndUserId(DateHelper.transferDayIdToDate(dayId), userInfoDO.getId());

                if (CollectionUtils.isNotEmpty(warehouseDetailDOS)) {
                    return;
                }

                //计算考勤结果
                AttendanceCalculateHandlerDTO attendanceHandlerDTO = new AttendanceCalculateHandlerDTO();
                attendanceHandlerDTO.setAttendanceDayId(dayId);
                attendanceHandlerDTO.setUserCodes(userInfoDO.getUserCode());
                attendanceCalculateService.warehouseEmployeeAttendanceHandler(attendanceHandlerDTO);

                //查询考勤异常
                AttendanceAbnormalCheckDTO attendanceAbnormalCheck = checkExistAttendanceException(dayId, userInfoDO.getId(), classConfigDO.getId());

                WarehouseDetailDO warehouseDetailDO = new WarehouseDetailDO();
                warehouseDetailDO.setId(defaultIdWorker.nextId());
                warehouseDetailDO.setCountry(userInfoDO.getLocationCountry());
                warehouseDetailDO.setCity(userInfoDO.getLocationCity());
                warehouseDetailDO.setOcId(deptIdMap.get(userInfoDO.getOcCode()));
                warehouseDetailDO.setVendorId(userInfoDO.getVendorId());
                warehouseDetailDO.setVendorCode(userInfoDO.getVendorCode());
                warehouseDetailDO.setUserOcId(userInfoDO.getDeptId());
                warehouseDetailDO.setUserVendorId(userInfoDO.getVendorId());
                warehouseDetailDO.setUserVendorCode(userInfoDO.getVendorCode());
                warehouseDetailDO.setUserId(userInfoDO.getId());
                warehouseDetailDO.setUserCode(userInfoDO.getUserCode());
                warehouseDetailDO.setEmployeeType(userInfoDO.getEmployeeType());
                warehouseDetailDO.setWarehouseDate(DateHelper.transferDayIdToDate(dayId));
                warehouseDetailDO.setSalaryDate(warehouseDetailDO.getWarehouseDate());
                warehouseDetailDO.setAttendanceType("P");
                warehouseDetailDO.setAttendanceStatus(WarehouseAttendanceStatusEnum.ABNORMAL.getCode());
                warehouseDetailDO.setClassesId(userShiftConfigDO.getPunchClassConfigId());
                warehouseDetailDO.setClassesName(classConfigDO.getClassName());
                warehouseDetailDO.setClassesType(classConfigDO.getClassType());
                warehouseDetailDO.setLegalWorkingHours(classConfigDO.getLegalWorkingHours());
                warehouseDetailDO.setRequiredAttendanceTime(classConfigDO.getAttendanceHours());
                warehouseDetailDO.setPcsStatus(WarehousePcsStatusEnum.ABNORMAL.getCode());
                warehouseDetailDO.setPunchStatus(PunchStatusEnum.ABNORMAL_PUNCH.getCode());
                BaseDOUtil.fillDOInsertFromUCenter(warehouseDetailDO);
                warehouseDetailAdapter.saveOrUpdate(warehouseDetailDO);

                if (attendanceAbnormalCheck.getExistException() && CollectionUtils.isNotEmpty(attendanceAbnormalCheck.getAttendanceAbnormalRecords())) {
                    List<WarehouseDetailAbnormalDO> warehouseDetailAbnormalList = attendanceAbnormalCheck.getAttendanceAbnormalRecords().stream().map(abnormal -> {
                        WarehouseDetailAbnormalDO warehouseDetailAbnormalDO = new WarehouseDetailAbnormalDO();
                        warehouseDetailAbnormalDO.setWarehouseDetailId(warehouseDetailDO.getId());
                        warehouseDetailAbnormalDO.setAbnormalId(abnormal.getAbnormalId());
                        warehouseDetailAbnormalDO.setAbnormalType(abnormal.getAbnormalType());
                        warehouseDetailAbnormalDO.setOcId(warehouseDetailDO.getOcId());
                        warehouseDetailAbnormalDO.setVendorId(warehouseDetailDO.getVendorId());
                        BaseDOUtil.fillDOInsertFromUCenter(warehouseDetailAbnormalDO);
                        return warehouseDetailAbnormalDO;
                    }).collect(Collectors.toList());
                    warehouseDetailAbnormalAdapter.saveOrUpdateBatch(warehouseDetailAbnormalList);
                }
            } catch (Exception e) {
                log.error("仓内员工排班生成仓内考勤异常：{},userCode: {}, dayId:{}", Throwables.getStackTraceAsString(e), userInfoDO.getUserCode(), dayId);
            }
        });
    }

    private void noSchedulingPlan(List<UserInfoDO> noClassUserList, Long dayId) {
        //过滤得到仓内自有用户
        List<UserInfoDO> warehouseUserList = noClassUserList.stream()
                .filter(user -> warehouseUserService.isWarehouseEmployeeSupport(CommonMapstruct.INSTANCE.mapToUser(user))
                        && migrationService.verifyUserIsEnableNewAttendance(user.getId()))
                .collect(Collectors.toList());
        List<String> ocCodes = warehouseUserList.stream().map(UserInfoDO::getOcCode).filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
        Map<String, Long> deptIdMap = deptService.listByOcCode(ocCodes).stream().collect(Collectors.toMap(AttendanceDept::getOcCode, AttendanceDept::getId, (v1, v2) -> v1));
        warehouseUserList.forEach(userInfoDO -> {
            try {
                List<WarehouseDetailDO> warehouseDetailList = warehouseDetailDao.selectByWarehouseDateAndUserId(DateHelper.transferDayIdToDate(dayId), userInfoDO.getId());

                if (CollectionUtils.isNotEmpty(warehouseDetailList)) {
                    return;
                }

                //计算考勤结果
                AttendanceCalculateHandlerDTO attendanceHandlerDTO = new AttendanceCalculateHandlerDTO();
                attendanceHandlerDTO.setAttendanceDayId(dayId);
                attendanceHandlerDTO.setUserCodes(userInfoDO.getUserCode());
                attendanceCalculateService.warehouseEmployeeAttendanceHandler(attendanceHandlerDTO);

                AttendanceAbnormalCheckDTO attendanceAbnormalCheckDTO = checkExistAttendanceException(dayId, userInfoDO.getId(), null);

                WarehouseDetailDO warehouseDetailDO = new WarehouseDetailDO();
                warehouseDetailDO.setId(defaultIdWorker.nextId());
                warehouseDetailDO.setCountry(userInfoDO.getLocationCountry());
                warehouseDetailDO.setCity(userInfoDO.getLocationCity());
                warehouseDetailDO.setOcId(deptIdMap.get(userInfoDO.getOcCode()));
                warehouseDetailDO.setVendorId(userInfoDO.getVendorId());
                warehouseDetailDO.setVendorCode(userInfoDO.getVendorCode());
                warehouseDetailDO.setUserOcId(userInfoDO.getDeptId());
                warehouseDetailDO.setUserVendorId(userInfoDO.getVendorId());
                warehouseDetailDO.setUserVendorCode(userInfoDO.getVendorCode());
                warehouseDetailDO.setUserId(userInfoDO.getId());
                warehouseDetailDO.setUserCode(userInfoDO.getUserCode());
                warehouseDetailDO.setWarehouseDate(DateHelper.transferDayIdToDate(dayId));
                warehouseDetailDO.setSalaryDate(warehouseDetailDO.getWarehouseDate());
                warehouseDetailDO.setAttendanceStatus(WarehouseAttendanceStatusEnum.ABNORMAL.getCode());
                warehouseDetailDO.setPunchStatus(PunchStatusEnum.ABNORMAL_PUNCH.getCode());
                warehouseDetailDO.setPcsStatus(WarehousePcsStatusEnum.ABNORMAL.getCode());
                warehouseDetailDO.setEmployeeType(userInfoDO.getEmployeeType());
                BaseDOUtil.fillDOInsertFromUCenter(warehouseDetailDO);

                if (attendanceAbnormalCheckDTO.getExistException() && CollectionUtils.isNotEmpty(attendanceAbnormalCheckDTO.getAttendanceAbnormalRecords())) {
                    List<WarehouseDetailAbnormalDO> warehouseDetailAbnormalList = attendanceAbnormalCheckDTO.getAttendanceAbnormalRecords()
                            .stream()
                            .map(abnormal -> {
                                WarehouseDetailAbnormalDO warehouseDetailAbnormalDO = new WarehouseDetailAbnormalDO();
                                warehouseDetailAbnormalDO.setId(defaultIdWorker.nextId());
                                warehouseDetailAbnormalDO.setWarehouseDetailId(warehouseDetailDO.getId());
                                warehouseDetailAbnormalDO.setAbnormalId(abnormal.getAbnormalId());
                                warehouseDetailAbnormalDO.setAbnormalType(abnormal.getAbnormalType());
                                warehouseDetailAbnormalDO.setOcId(warehouseDetailDO.getOcId());
                                warehouseDetailAbnormalDO.setVendorId(warehouseDetailDO.getVendorId());
                                BaseDOUtil.fillDOInsertFromUCenter(warehouseDetailAbnormalDO);
                                return warehouseDetailAbnormalDO;
                            }).collect(Collectors.toList());

                    warehouseManage.warehouseAttendanceAbnormalAdd(warehouseDetailDO, warehouseDetailAbnormalList);
                }
            } catch (Exception e) {
                log.error("自有未排班生成仓内考勤异常：{},userCode: {}, dayId:{}", Throwables.getStackTraceAsString(e), userInfoDO.getUserCode(), dayId);
            }
        });
    }

    private static void setPcsStatus(WarehouseDetailDO warehouseDetail) {
        if (!Objects.equals(EmploymentTypeEnum.OS_FIXED_SALARY.getCode(), warehouseDetail.getEmployeeType())) {
            return;
        }
        if (Objects.isNull(warehouseDetail.getActualAttendanceTime())) {
            warehouseDetail.setPcsStatus(WarehousePcsStatusEnum.ABNORMAL.getCode());
        }
        if (warehouseDetail.getActualAttendanceTime().compareTo(BigDecimal.ZERO) > 0) {
            warehouseDetail.setPcsStatus(WarehousePcsStatusEnum.NORMAL.getCode());
        } else {
            warehouseDetail.setPcsStatus(WarehousePcsStatusEnum.ABNORMAL.getCode());
        }
    }

    private static AttendanceAbnormalCheckDTO.AttendanceAbnormalRecord convertAttendanceAbnormalRecord(EmployeeAbnormalAttendanceDO noPass, Integer status) {
        AttendanceAbnormalCheckDTO.AttendanceAbnormalRecord attendanceAbnormalRecord = new AttendanceAbnormalCheckDTO.AttendanceAbnormalRecord();
        attendanceAbnormalRecord.setAbnormalId(noPass.getId());
        attendanceAbnormalRecord.setAbnormalType(noPass.getAbnormalType());
        attendanceAbnormalRecord.setStatus(status);
        return attendanceAbnormalRecord;
    }

    private void convertWarehouseDetailAbnormal(List<WarehouseDetailAbnormalDO> warehouseDetailAbnormalDOS,
                                                WarehouseDetailDO warehouseDetailDO,
                                                List<AttendanceAbnormalCheckDTO.AttendanceAbnormalRecord> abnormalList,
                                                Integer processed) {
        if (CollectionUtils.isEmpty(abnormalList)) {
            return;
        }
        List<WarehouseDetailAbnormalDO> insertAbnormalList = abnormalList.stream().map(abnormal -> {
            WarehouseDetailAbnormalDO abnormalDO = new WarehouseDetailAbnormalDO();
            abnormalDO.setId(defaultIdWorker.nextId());
            abnormalDO.setAbnormalId(abnormal.getAbnormalId());
            abnormalDO.setAbnormalType(abnormal.getAbnormalType());
            abnormalDO.setWarehouseDetailId(warehouseDetailDO.getId());
            abnormalDO.setOcId(warehouseDetailDO.getOcId());
            abnormalDO.setVendorId(warehouseDetailDO.getVendorId());
            abnormalDO.setProcessed(processed);
            BaseDOUtil.fillDOInsertFromUCenter(abnormalDO);
            return abnormalDO;
        }).collect(Collectors.toList());
        warehouseDetailAbnormalDOS.addAll(insertAbnormalList);
    }

}
