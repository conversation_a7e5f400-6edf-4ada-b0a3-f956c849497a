package com.imile.attendance.warehouse;


import com.imile.attendance.warehouse.param.WarehouseOperatorListParam;
import com.imile.attendance.warehouse.param.WarehouseOperatorSaveParam;
import com.imile.attendance.warehouse.vo.WarehouseOperatorListVO;

import java.util.List;

/**
 * 仓内考勤操作员授权服务
 *
 * <AUTHOR>
 * @since 2025/1/22
 */
public interface WarehouseOperatorAuthService {

    /**
     * WPM操作员列表
     */
    List<WarehouseOperatorListVO> warehouseOperatorList(WarehouseOperatorListParam param);

    /**
     * 绑定WPM操作员角色
     */
    Boolean bindOperatorRole(WarehouseOperatorSaveParam param);

    /**
     * 删除WPM操作白名单
     */
    void removeOperatorRole(Long id);
}
