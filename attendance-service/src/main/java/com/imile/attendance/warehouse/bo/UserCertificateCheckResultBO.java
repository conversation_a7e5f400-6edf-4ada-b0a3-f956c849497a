package com.imile.attendance.warehouse.bo;

import com.imile.hrms.api.primary.model.parse.Certificate;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/2/5
 */
@Data
@AllArgsConstructor
public class UserCertificateCheckResultBO {

    /**
     * 是否重复
     */
    private Boolean isRepeat;

    /**
     * 重复证件列表
     */
    private List<RepeatCertificate> repeatCertificateList;

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class RepeatCertificate {

        /**
         * 重复证件
         */
        private Certificate certificate;

        /**
         * 重复证件类型描述
         */
        private String certificateTypeCodeDesc;

        /**
         * 重复证件所属人人员名称
         */
        private String ownerUserName;

        /**
         * 重复证件所属人人员编码
         */
        private String ownerUserCode;
    }

    public static UserCertificateCheckResultBO of(Boolean isRepeat, List<RepeatCertificate> repeatCertificateList) {
        return new UserCertificateCheckResultBO(isRepeat, repeatCertificateList);
    }
}
