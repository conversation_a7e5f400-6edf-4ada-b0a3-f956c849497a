package com.imile.attendance.warehouse.param;

import com.imile.common.query.BaseQuery;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/10/15
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class WpmDataStatisticsParam extends BaseQuery {

    /**
     * 作业日期
     */
    @NotNull(message = "warehouseDate cannot be empty")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date warehouseDate;

    /**
     * 当地时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date attendanceTime;

    /**
     * 网点
     */
    @NotNull(message = "ocId cannot be empty")
    private Long ocId;

    /**
     * 供应商编码
     */
    private String vendorCode;

    /**
     * 班次
     */
    private Long classesId;

    /**
     * 用工类型
     */
    private List<String> employeeTypeList;

    /**
     * 出入仓类型
     */
    private String warehouseType;

    /**
     * 异常类型
     */
    private String abnormalType;
}
