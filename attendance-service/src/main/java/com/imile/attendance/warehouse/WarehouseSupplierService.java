package com.imile.attendance.warehouse;

import com.imile.attendance.warehouse.param.GetVendorListByOcListParam;
import com.imile.attendance.warehouse.vo.QrCodeResultVO;
import com.imile.attendance.warehouse.vo.QrCodeVerifyResultVO;
import com.imile.attendance.warehouse.vo.VendorVO;

import java.util.List;
import java.util.Map;

/**
 * 仓内供应商服务 todo
 * <AUTHOR> chen
 * @Date 2025/07/09
 * @Time 17:12
 * @Description
 */
public interface WarehouseSupplierService {

    /**
     * 指定条件查询供应商
     */
    List<VendorVO> getVendorListByCondition(GetVendorListByOcListParam param);

    /**
     * 通过网点获取关联的供应商
     */
    List<VendorVO> getVendorList(List<Long> ocDeptIdList);

    /**
     * 供应商统计下拉列表
     */
    List<VendorVO> getNoLoginVendorListByOcList(List<Long> ocDeptIdList);

    /**
     * 根据供应商编码查询网点区域编码
     */
    List<String> getOcCodeList(List<String> supplierCodeList);

    /**
     * 根据供应商编码批量查询供应商名称
     */
    Map<String, String> getSupplierByCodes(List<String> supplierCodes);

    /**
     * 生成供应商二维码，先支持url
     */
    QrCodeResultVO generateQrCode(Long ocId, String vendorCode, Integer permission);

    /**
     * 验证二维码token，返回解析结果
     */
    QrCodeVerifyResultVO verifyToken(String token);
}
