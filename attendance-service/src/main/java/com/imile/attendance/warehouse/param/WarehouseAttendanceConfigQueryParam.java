package com.imile.attendance.warehouse.param;

import com.imile.attendance.query.ResourceQuery;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/11/30
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class WarehouseAttendanceConfigQueryParam extends ResourceQuery {

    /**
     * 国家
     */
    private String country;

    /**
     * 规则名称
     */
    private String attendanceConfigName;

    /**
     * 使用部门
     */
    private List<Long> deptIds;
}
