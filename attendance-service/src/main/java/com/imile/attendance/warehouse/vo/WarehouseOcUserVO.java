package com.imile.attendance.warehouse.vo;

import com.imile.attendance.annon.HyperLink;
import com.imile.attendance.annon.WithDict;
import lombok.Data;

import java.util.List;

/**
 * @description:
 * @author: taokang
 * @createDate: 2024/12/11 11:36
 * @version: 1.0
 */
@Data
public class WarehouseOcUserVO {

    private String country;


    private String city;

    /**
     * 入仓网点
     */
    private Long inOcId;

    /**
     * 入仓网点名称
     */
    private String inOcName;

    /**
     * 入仓供应商id
     */
    private Long inVendorId;

    /**
     * 入仓供应商编码
     */
    private String inVendorCode;

    /**
     * 入仓供应商名称
     */
    private String inVendorName;

    /**
     * 用户所属网点
     */
    private Long ocId;

    /**
     * 用户所属网点名称
     */
    private String ocName;

    /**
     * 用户所属供应商
     */
    private Long vendorId;

    /**
     * 用户所属供应商编码
     */
    private String vendorCode;

    /**
     * 用户所属供应商名称
     */
    private String vendorName;

    /**
     * 人脸key
     */
    @HyperLink(ref = "profilePhotoUrl")
    private String profilePhotoUrlKey;

    /**
     * 人脸url
     */
    private String profilePhotoUrl;

    /**
     * 证件号(用于列表展示)
     */
    private String certificatesCode;

    /**
     * 证件信息
     */
    private List<WarehouseUserCertificateVO> certificateVOList;

    /**
     * 员工id
     */
    private Long userId;

    /**
     * 员工账号
     */
    private String userCode;

    /**
     * 员工名称
     */
    private String userName;

    /**
     * 组合名称
     */
    private String unifiedUserName;

    /**
     * 用工类型
     */
    private String employeeType;

    /**
     * 1:男 2:女
     */
    private Integer sex;

    /**
     * 性别编码
     */
    @WithDict(typeCode = "sex", ref = "sexDesc")
    private String sexCode;

    /**
     * 性别描述
     */
    private String sexDesc;

    /**
     * 用工形式
     */
    @WithDict(typeCode = "EmploymentForm", ref = "employmentFormDesc")
    private String employmentForm;

    /**
     * 用工形式描述
     */
    private String employmentFormDesc;
}
