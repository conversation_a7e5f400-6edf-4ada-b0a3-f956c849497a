package com.imile.attendance.warehouse;


import com.imile.attendance.abnormal.dto.AttendanceCalculateHandlerDTO;

/**
 * 仓内考勤结果计算
 *
 * <AUTHOR>
 * @since 2025/1/6
 */
public interface WarehouseAttendanceCalculateService {

    /**
     * 仓内考勤计算
     */
    void warehouseAttendanceCalculate(AttendanceCalculateHandlerDTO calculateHandlerDTO);

    /**
     * 仓内劳务员工考勤计算
     */
    void warehouseLaborWorkerAttendanceHandler(AttendanceCalculateHandlerDTO calculateHandlerDTO);

    /**
     * 仓内自有员工考勤计算
     */
    void warehouseEmployeeAttendanceHandler(AttendanceCalculateHandlerDTO calculateHandlerDTO);
}
