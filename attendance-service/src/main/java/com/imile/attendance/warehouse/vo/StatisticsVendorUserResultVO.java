package com.imile.attendance.warehouse.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.imile.attendance.annon.WithDict;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/11/28
 */
@Data
public class StatisticsVendorUserResultVO {

    /**
     * 用户照片OSS短链
     */
    private String userPhotoKey;

    /**
     * 用户照片OSS完整路径
     */
    private String userPhoto;

    /**
     * 用户编码
     */
    private String userCode;

    /**
     * 用户名称
     */
    private String userName;

    /**
     * 入仓时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date clockInTime;

    /**
     * 离仓时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date clockOutTime;


    /**
     * 考勤状态
     */
    @WithDict(typeCode = "WarehouseDateReportResult", ref = "attendanceStatusDesc")
    private Integer attendanceStatusCode;

    /**
     * 考勤状态描述
     */
    private String attendanceStatusDesc;


    /**
     * 实际出勤时长 单位:小时
     */
    private BigDecimal actualAttendanceTime;

    /**
     * 满勤标识 0:否 1:是
     */
    private Integer fullAttendance;

    /**
     * 异常类型列表
     */
    private List<DataStatisticsDetailsVO.AbnormalVO> abnormalVOList;

}
