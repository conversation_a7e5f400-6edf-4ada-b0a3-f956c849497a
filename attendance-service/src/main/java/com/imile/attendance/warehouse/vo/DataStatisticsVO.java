package com.imile.attendance.warehouse.vo;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/10/15
 */
@Data
public class DataStatisticsVO {

    /**
     * 总计人数
     */
    private DataCountVO total = new DataCountVO();

    /**
     * 员工人数
     */
    private DataCountVO employee = new DataCountVO();

    /**
     * 劳务派遣人数
     */
    private DataCountVO laborDispatch = new DataCountVO();

    /**
     * 供应商明细
     */
    private List<VendorDataCountVO> vendorDetailList;

    /**
     * 是否分段计算工时
     */
    private Boolean isSegmentedCalculation = Boolean.FALSE;
}
