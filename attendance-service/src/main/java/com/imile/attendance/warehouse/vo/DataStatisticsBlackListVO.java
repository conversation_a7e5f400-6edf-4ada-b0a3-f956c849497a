package com.imile.attendance.warehouse.vo;

import com.imile.attendance.annon.HyperLink;
import com.imile.attendance.annon.WithDict;
import com.imile.attendance.constants.BusinessConstant;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2025/2/18
 */
@Data
public class DataStatisticsBlackListVO {

    private Long id;

    /**
     * 用户编码
     */
    private String userCode;

    /**
     * 用户名称
     */
    private String userName;

    /**
     * 用户照片OSS短链
     */
    @HyperLink(ref = "userPhoto")
    private String userPhotoKey;

    /**
     * 用户照片OSS完整路径
     */
    private String userPhoto;

    /**
     * 证件类型编码
     */
    private String certificateTypeCode;
    /**
     * 证件号码
     */
    private String certificateCode;

    /**
     * 类型（识别环节）
     */
    @WithDict(typeCode = BusinessConstant.SysDictDataTypeConstant.WAREHOUSE_BLACK_TYPE, ref = "typeDesc")
    private Integer type;

    private String typeDesc;

    /**
     * 供应商名称
     */
    private String vendorName;

    /**
     * 原因
     */
    private String reason;

}
