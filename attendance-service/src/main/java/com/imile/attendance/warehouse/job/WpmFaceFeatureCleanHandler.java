package com.imile.attendance.warehouse.job;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.imile.attendance.apollo.AttendanceProperties;
import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.enums.EmploymentTypeEnum;
import com.imile.attendance.enums.WorkStatusEnum;
import com.imile.attendance.infrastructure.repository.common.AttendanceUserService;
import com.imile.attendance.infrastructure.repository.common.dto.AttendanceUser;
import com.imile.attendance.infrastructure.repository.warehouse.dao.FaceFeatureDao;
import com.imile.attendance.infrastructure.repository.warehouse.dao.FaceFeatureEncDao;
import com.imile.attendance.infrastructure.repository.warehouse.dao.WarehouseDetailDao;
import com.imile.attendance.infrastructure.repository.warehouse.model.FaceFeatureDO;
import com.imile.attendance.infrastructure.repository.warehouse.model.FaceFeatureEncDO;
import com.imile.attendance.infrastructure.repository.warehouse.model.WarehouseDetailDO;
import com.imile.attendance.infrastructure.repository.warehouse.query.FaceFeatureQuery;
import com.imile.attendance.recognition.RpcRecognitionClient;
import com.imile.common.enums.StatusEnum;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;


/**
 * WPM自定义清理人脸特征
 *
 * <AUTHOR>
 * @since 2025/7/18
 */
@Slf4j
@Component
public class WpmFaceFeatureCleanHandler {

    @Resource
    private FaceFeatureDao faceFeatureDao;
    @Resource
    private FaceFeatureEncDao faceFeatureEncDao;
    @Resource
    private AttendanceUserService attendanceUserService;
    @Resource
    private RpcRecognitionClient recognitionClient;
    @Resource
    private WarehouseDetailDao warehouseDetailDao;
    @Resource
    private AttendanceProperties attendanceProperties;

    /**
     * 30天转换毫秒值
     */
    private static final long MILLIS_PER_30_DAYS = 30L * 24 * 60 * 60 * 1000;


    @XxlJob(BusinessConstant.JobHandler.WPM_FACE_FEATURE_CLEAN_HANDLER)
    public ReturnT<String> faceFeatureCleanHandler(String content) {
        FaceFeatureCleanHandlerParam param = StringUtils.isNotBlank(content) ? JSON.parseObject(content, FaceFeatureCleanHandlerParam.class) : new FaceFeatureCleanHandlerParam();
        XxlJobLogger.log("XXL-JOB,  {} Start.The Param:{}", BusinessConstant.JobHandler.WPM_FACE_FEATURE_CLEAN_HANDLER, param);

        FaceFeatureQuery query = new FaceFeatureQuery();
        if (StringUtils.isNotBlank(param.getUserCodeList())) {
            query.setUserCodeList(Arrays.asList(param.getUserCodeList().split(BusinessConstant.DEFAULT_DELIMITER)));
        }
        if (StringUtils.isNotBlank(param.getCountryList())) {
            query.setCountryList(Arrays.asList(param.getCountryList().split(BusinessConstant.DEFAULT_DELIMITER)));
        }
        if (StringUtils.isNotBlank(param.getEmployeeTypeList())) {
            query.setEmployeeTypeList(Arrays.asList(param.getEmployeeTypeList().split(BusinessConstant.DEFAULT_DELIMITER)));
        }
        //普通人脸特征库清理
        faceFeatureCleanHandler(query);

        //隐私安全人脸特征库清理
        faceFeaturePrivacyCleanHandler(query);

        XxlJobLogger.log("XXL-JOB,  {} 执行结束", BusinessConstant.JobHandler.WPM_FACE_FEATURE_CLEAN_HANDLER);
        return ReturnT.SUCCESS;
    }

    private void faceFeatureCleanHandler(FaceFeatureQuery query) {
        log.info("XXL-JOB: {},faceFeatureCleanHandler start", BusinessConstant.JobHandler.WPM_FACE_FEATURE_CLEAN_HANDLER);
        Set<Long> totalUserIdList = new HashSet<>();
        int currentPage = 1;
        int pageSize = 1000;
        Date currentDate = new Date();
        PageInfo<FaceFeatureDO> pageInfo;
        do {
            Page<FaceFeatureDO> page = PageHelper.startPage(currentPage, pageSize, true);
            pageInfo = page.doSelectPageInfo(() -> faceFeatureDao.selectByCondition(query));
            // 总记录数
            List<FaceFeatureDO> faceFeatureList = pageInfo.getList();
            if (CollectionUtils.isEmpty(faceFeatureList)) {
                currentPage++;
                continue;
            }
            log.info("XXL-JOB: {},faceFeatureList size:{}", BusinessConstant.JobHandler.WPM_FACE_FEATURE_CLEAN_HANDLER, faceFeatureList.size());
            //过滤得到所有劳务用户ID集合
            List<Long> userIdList = faceFeatureList.stream()
                    .filter(face -> Objects.equals(EmploymentTypeEnum.OS_FIXED_SALARY.getCode(), face.getEmployeeType()) && Objects.nonNull(face.getUserId()))
                    .map(FaceFeatureDO::getUserId)
                    .distinct()
                    .collect(Collectors.toList());

            List<AttendanceUser> userInfoDOList = attendanceUserService.listUsersByIds(userIdList);
            List<Long> activeUserIds = userInfoDOList.stream()
                    .filter(user -> Objects.equals(WorkStatusEnum.ON_JOB.getCode(), user.getWorkStatus())
                            && Objects.equals(StatusEnum.ACTIVE.getCode(), user.getStatus()))
                    .map(AttendanceUser::getId)
                    .collect(Collectors.toList());

            if (CollectionUtils.isEmpty(activeUserIds)) {
                currentPage++;
                continue;
            }

            List<WarehouseDetailDO> warehouseDetailDOList = warehouseDetailDao.selectLatestByUserIds(activeUserIds);
            if (CollectionUtils.isEmpty(warehouseDetailDOList)) {
                totalUserIdList.addAll(activeUserIds);
                currentPage++;
                continue;
            }

            Map<Long, List<WarehouseDetailDO>> userGroup = warehouseDetailDOList.stream().collect(Collectors.groupingBy(WarehouseDetailDO::getUserId));

            List<Long> clearUserIdList = activeUserIds.stream()
                    .filter(userId -> {
                        List<WarehouseDetailDO> userList = userGroup.get(userId);
                        if (CollectionUtils.isEmpty(userList)) {
                            return true;
                        }
                        return checkDiffer30Days(currentDate, userList.get(0).getWarehouseDate());
                    })
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(clearUserIdList)) {
                totalUserIdList.addAll(clearUserIdList);
            }
            currentPage++;
        } while (currentPage <= pageInfo.getPages());

        if (CollectionUtils.isNotEmpty(totalUserIdList)) {
            List<List<Long>> partitionList = Lists.partition(new ArrayList<>(totalUserIdList), 200);
            partitionList.forEach(recognitionClient::clearFacePhotoByUserIds);
        }

        log.info("XXL-JOB: {},faceFeatureCleanHandler 执行结束", BusinessConstant.JobHandler.WPM_FACE_FEATURE_CLEAN_HANDLER);
    }

    private void faceFeaturePrivacyCleanHandler(FaceFeatureQuery query) {
        log.info("XXL-JOB: {},faceFeaturePrivacyCleanHandler start", BusinessConstant.JobHandler.WPM_FACE_FEATURE_CLEAN_HANDLER);
        Set<Long> totalUserIdList = new HashSet<>();
        int currentPage = 1;
        int pageSize = 1000;
        Date currentDate = new Date();
        PageInfo<FaceFeatureEncDO> pageInfo;
        do {
            Page<FaceFeatureEncDO> page = PageHelper.startPage(currentPage, pageSize, true);
            pageInfo = page.doSelectPageInfo(() -> faceFeatureEncDao.selectByCondition(query));
            // 总记录数
            List<FaceFeatureEncDO> faceFeatureList = pageInfo.getList();
            if (CollectionUtils.isEmpty(faceFeatureList)) {
                currentPage++;
                continue;
            }
            log.info("XXL-JOB: {},faceFeatureList size:{}", BusinessConstant.JobHandler.WPM_FACE_FEATURE_CLEAN_HANDLER, faceFeatureList.size());
            //过滤得到所有劳务用户ID集合
            List<Long> userIdList = faceFeatureList.stream()
                    .filter(face -> Objects.equals(EmploymentTypeEnum.OS_FIXED_SALARY.getCode(), face.getEmployeeType()) && Objects.nonNull(face.getUserId()))
                    .map(FaceFeatureEncDO::getUserId)
                    .distinct()
                    .collect(Collectors.toList());

            List<AttendanceUser> userInfoDOList = attendanceUserService.listUsersByIds(userIdList);
            List<Long> activeUserIds = userInfoDOList.stream()
                    .filter(user -> Objects.equals(WorkStatusEnum.ON_JOB.getCode(), user.getWorkStatus())
                            && Objects.equals(StatusEnum.ACTIVE.getCode(), user.getStatus()))
                    .map(AttendanceUser::getId)
                    .collect(Collectors.toList());

            if (CollectionUtils.isEmpty(activeUserIds)) {
                currentPage++;
                continue;
            }

            List<WarehouseDetailDO> warehouseDetailDOList = warehouseDetailDao.selectLatestByUserIds(activeUserIds);
            if (CollectionUtils.isEmpty(warehouseDetailDOList)) {
                totalUserIdList.addAll(activeUserIds);
                currentPage++;
                continue;
            }

            Map<Long, List<WarehouseDetailDO>> userGroup = warehouseDetailDOList.stream().collect(Collectors.groupingBy(WarehouseDetailDO::getUserId));

            List<Long> clearUserIdList = activeUserIds.stream()
                    .filter(userId -> {
                        List<WarehouseDetailDO> userList = userGroup.get(userId);
                        if (CollectionUtils.isEmpty(userList)) {
                            return true;
                        }
                        return checkDiffer30Days(currentDate, userList.get(0).getWarehouseDate());
                    })
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(clearUserIdList)) {
                totalUserIdList.addAll(clearUserIdList);
            }
            currentPage++;
        } while (currentPage <= pageInfo.getPages());

        if (CollectionUtils.isNotEmpty(totalUserIdList)) {
            List<List<Long>> partitionList = Lists.partition(new ArrayList<>(totalUserIdList), 200);
            partitionList.forEach(recognitionClient::clearFacePhotoByUserIds);
        }

        log.info("XXL-JOB: {},faceFeaturePrivacyCleanHandler 执行结束", BusinessConstant.JobHandler.WPM_FACE_FEATURE_CLEAN_HANDLER);
    }

    private boolean checkDiffer30Days(Date currentDate, Date disabledDate) {
        if (Objects.isNull(currentDate) || Objects.isNull(disabledDate)) {
            return false;
        }
        long differ = currentDate.getTime() - disabledDate.getTime();
        return differ > getCleanDayDiffer();
    }

    @Data
    public static class FaceFeatureCleanHandlerParam {
        /**
         * 国家
         */
        private String countryList;

        /**
         * 用户编码
         */
        private String userCodeList;

        /**
         * 用工类型
         */
        private String employeeTypeList;
    }

    private long getCleanDayDiffer() {
        Long wpmLaborWorkersFaceCleanDay = attendanceProperties.getAttendance().getWpmLaborWorkersFaceCleanDay();
        if (Objects.isNull(wpmLaborWorkersFaceCleanDay)) {
            return MILLIS_PER_30_DAYS;
        }
        return wpmLaborWorkersFaceCleanDay * 24 * 60 * 60 * 1000;
    }
}
