package com.imile.attendance.warehouse.param;

import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.util.Date;


/**
 * <AUTHOR>
 * @since 2024/10/15
 */
@Data
public class GetClassesByConditionParam {
    @NotNull(message = "ocId cannot be empty")
    private Long ocId;

    @NotNull(message = "warehouseDate cannot be empty")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date warehouseDate;

    /**
     * 供应商编码
     */
    private String vendorCode;
}
