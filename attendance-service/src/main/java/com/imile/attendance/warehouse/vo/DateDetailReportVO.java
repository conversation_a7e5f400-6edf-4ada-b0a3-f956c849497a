package com.imile.attendance.warehouse.vo;

import com.imile.attendance.annon.WithDict;
import com.imile.attendance.constants.BusinessConstant;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 日报详情展示
 *
 * <AUTHOR>
 * @since 2024/8/22
 */
@Data
public class DateDetailReportVO {

    /**
     * 考勤结果
     */
    private AttendanceResult attendanceResult;

    /**
     * 考勤异常处理
     */
    private List<AttendanceAbnormal> attendanceAbnormalList;

    /**
     * 出入仓详情列表
     */
    private List<WarehouseRecordVO> warehouseRecordList;

    /**
     * 请假详情列表
     */
    private List<LeaveDetail> leaveDetailList;

    /**
     * 是否历史数据
     */
    private Boolean historyData = Boolean.FALSE;

    /**
     * 去处理是否展示
     */
    private Boolean show;


    @Data
    public static class AttendanceResult {
        /**
         * 考勤结果ID
         */
        private Long id;

        /**
         * 网点ID
         */
        private Long ocId;

        /**
         * 班次名称
         */
        private String classesName;

        /**
         * 班次类型
         */
        @WithDict(typeCode = "PunchClassType", ref = "classesTypeDesc")
        private Integer classesType;

        /**
         * 班次类型描述
         */
        private String classesTypeDesc;

        /**
         * 当日结果：1正常 2异常 3待配置班次
         */
        @WithDict(typeCode = "WarehouseDateReportResult", ref = "resultCodeDesc")
        private Integer resultCode;

        /**
         * 当日结果描述
         */
        private String resultCodeDesc;

        /**
         * 实际出勤时长
         */
        private BigDecimal actualAttendanceTime;

        /**
         * 实际工作时长
         */
        private BigDecimal actualWorkingHours;

        /**
         * 用户账号
         */
        private String userCode;

        /**
         * 考勤异常类型
         */
        private List<AttendanceAbnormalType> attendanceAbnormalTypes;
    }

    @Data
    public static class AttendanceAbnormalType {
        /**
         * 异常ID
         */
        private Long abnormalId;

        /**
         * 异常类型
         */
        @WithDict(typeCode = BusinessConstant.SysDictDataTypeConstant.WAREHOUSE_ABNORMAL_TYPE, ref = "abnormalTypeDesc")
        private String abnormalType;

        /**
         * 异常类型描述
         */
        private String abnormalTypeDesc;

        /**
         * 处理状态 0: 待处理 1：已处理
         */
        private Integer processed;
    }

    @Data
    public static class AttendanceAbnormal {
        /**
         * 异常操作记录表ID
         */
        private Long id;

        /**
         * 异常ID
         */
        private Long abnormalId;

        /**
         * 异常类型
         */
        @WithDict(typeCode = BusinessConstant.SysDictDataTypeConstant.WAREHOUSE_ABNORMAL_TYPE, ref = "abnormalTypeDesc")
        private String abnormalType;

        /**
         * 异常类型描述
         */
        private String abnormalTypeDesc;

        /**
         * 处理类型
         */
        @WithDict(typeCode = "AttendanceAbnormalOperationTypeEnum", ref = "handleTypeDesc")
        private String handleType;

        /**
         * 处理类型描述
         */
        private String handleTypeDesc;

        /**
         * 异常处理状态
         */
        @WithDict(typeCode = "AbnormalAttendanceStatusEnum", ref = "abnormalStatusDesc")
        private String abnormalStatus;

        /**
         * 异常处理状态描述
         */
        private String abnormalStatusDesc;
    }

    @Data
    public static class LeaveDetail {

        /**
         * 请假单据表id
         */
        private Long id;

        /**
         * 请假类型
         */
        private String leaveType;

        /**
         * 请假开始时间
         */
        private Date leaveStartTime;

        /**
         * 请假结束时间
         */
        private Date leaveEndTime;

        /**
         * 审批状态
         */
        private String formStatus;
    }
}
