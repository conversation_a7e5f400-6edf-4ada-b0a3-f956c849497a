package com.imile.attendance.warehouse.job;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.enums.warehouse.WarehouseAttendanceStatusEnum;
import com.imile.attendance.infrastructure.repository.common.CountryService;
import com.imile.attendance.infrastructure.repository.rule.dao.PunchClassItemConfigDao;
import com.imile.attendance.infrastructure.repository.rule.model.PunchClassItemConfigDO;
import com.imile.attendance.infrastructure.repository.warehouse.dao.WarehouseDetailDao;
import com.imile.attendance.infrastructure.repository.warehouse.dao.WarehouseRecordDao;
import com.imile.attendance.infrastructure.repository.warehouse.model.WarehouseDetailDO;
import com.imile.attendance.infrastructure.repository.warehouse.model.WarehouseRecordDO;
import com.imile.attendance.infrastructure.repository.warehouse.query.WarehouseDetailQuery;
import com.imile.attendance.rule.dto.DayPunchTimeDTO;
import com.imile.attendance.rule.service.PunchClassConfigQueryService;
import com.imile.attendance.util.CommonUtil;
import com.imile.attendance.util.DateFormatterUtil;
import com.imile.attendance.util.DateHelper;
import com.imile.attendance.warehouse.WarehouseAttendanceHandlerService;
import com.imile.hermes.business.dto.CountryConfigDTO;
import com.imile.hermes.business.query.CountryApiQuery;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description: 仓内考勤出仓缺卡扫描，每小时执行一次
 * @since 2024/8/28
 */
@Slf4j
@Component
public class AttendanceOutWarehouseMissPunchHandler {
    @Resource
    private CountryService countryService;
    @Resource
    private PunchClassConfigQueryService punchClassConfigQueryService;
    @Resource
    private WarehouseDetailDao warehouseDetailDao;
    @Resource
    private WarehouseRecordDao warehouseRecordDao;
    @Resource
    private WarehouseAttendanceHandlerService warehouseAttendanceHandlerService;
    @Resource
    private PunchClassItemConfigDao punchClassItemConfigDao;


    @XxlJob(BusinessConstant.JobHandler.ATTENDANCE_OUT_WAREHOUSE_MISS_PUNCH_HANDLER)
    public ReturnT<String> attendanceOutWarehouseMissPunchHandler(String param) {
        XxlJobLogger.log("XXL-JOB,  {} Start.The Param:{}", BusinessConstant.JobHandler.ATTENDANCE_OUT_WAREHOUSE_MISS_PUNCH_HANDLER, param);
        AttendanceOutWarehouseMissPunchHandlerParam handlerParam = StringUtils.isNotBlank(param) ? JSON.parseObject(param, AttendanceOutWarehouseMissPunchHandlerParam.class) : new AttendanceOutWarehouseMissPunchHandlerParam();
        if (StringUtils.isBlank(handlerParam.getCountryList())) {
            XxlJobLogger.log("XXL-JOB,  {} 传入国家不存在", BusinessConstant.JobHandler.ATTENDANCE_OUT_WAREHOUSE_MISS_PUNCH_HANDLER);
            return ReturnT.FAIL;
        }
        // 处理仓内未出仓打卡每日考勤
        List<String> countryList = Arrays.asList(handlerParam.getCountryList().split(BusinessConstant.DEFAULT_DELIMITER));
        // 获取对应国家的当前时间
        Map<String, Date> countryCurrentDate = getCountryCurrentDate(countryList, handlerParam);
        for (String country : countryList) {
            Date warehouseDate = countryCurrentDate.get(country);
            if (Objects.isNull(warehouseDate)) {
                XxlJobLogger.log("XXL-JOB,  {} 未获取到当前国家对应时间:{}", BusinessConstant.JobHandler.ATTENDANCE_OUT_WAREHOUSE_MISS_PUNCH_HANDLER, country);
                continue;
            }
            Date preWarehouseDate = DateUtil.beginOfDay(DateUtil.offsetDay(warehouseDate, -1));
            Long attendanceDayId = DateHelper.getDayId(warehouseDate);
            Long preDayId =  DateHelper.getDayId(preWarehouseDate);
            List<Long> dayIds = Arrays.asList(preDayId, attendanceDayId);
            log.info("XXL-JOB: {},country: {},warehouseDate:{},preWarehouseDate:{}", BusinessConstant.JobHandler.ATTENDANCE_OUT_WAREHOUSE_MISS_PUNCH_HANDLER, country, warehouseDate, preWarehouseDate);

            WarehouseDetailQuery warehouseDetailParam = new WarehouseDetailQuery();
            warehouseDetailParam.setCountry(country);
            warehouseDetailParam.setOcId(handlerParam.getOcId());
            warehouseDetailParam.setStartTime(preWarehouseDate);
            warehouseDetailParam.setEndTime(warehouseDate);
            if (StringUtils.isNotBlank(handlerParam.getUserCodes())) {
                warehouseDetailParam.setUserCodeList(Arrays.asList(handlerParam.getUserCodes().split(BusinessConstant.DEFAULT_DELIMITER)));
            }
            warehouseDetailParam.setAttendanceStatusList(Lists.newArrayList(WarehouseAttendanceStatusEnum.INIT.getCode()));
            int currentPage = 1;
            int pageSize = 1000;
            PageInfo<WarehouseDetailDO> pageInfo;
            do {
                Page<WarehouseDetailDO> page = PageHelper.startPage(currentPage, pageSize, true);
                pageInfo = page.doSelectPageInfo(() -> warehouseDetailDao.selectByCondition(warehouseDetailParam));
                // 总记录数
                List<WarehouseDetailDO> warehouseDetailList = pageInfo.getList();
                if (CollectionUtils.isEmpty(warehouseDetailList)) {
                    currentPage++;
                    continue;
                }
                log.info("XXL-JOB: {},country: {},warehouseDetailList size:{}", BusinessConstant.JobHandler.ATTENDANCE_OUT_WAREHOUSE_MISS_PUNCH_HANDLER, country, warehouseDetailList.size());
                //过滤无班次信息
                warehouseDetailList = warehouseDetailList
                        .stream()
                        .filter(warehouse -> Objects.nonNull(warehouse.getClassesId())).collect(Collectors.toList());
                if (CollectionUtils.isEmpty(warehouseDetailList)) {
                    currentPage++;
                    continue;
                }
                List<WarehouseDetailDO> allWarehouseDetailDOList = new ArrayList<>();
                //查询班次信息并分组
                List<Long> classIds = warehouseDetailList
                        .stream()
                        .map(WarehouseDetailDO::getClassesId)
                        .distinct()
                        .collect(Collectors.toList());

                List<PunchClassItemConfigDO> classItemConfigList = punchClassItemConfigDao.selectLatestByClassIds(classIds);
                Map<Long, List<PunchClassItemConfigDO>> classItemConfigMap = classItemConfigList.stream().collect(Collectors.groupingBy(PunchClassItemConfigDO::getPunchClassId));

                Set<Long> itemConfigListOnTimePreDay = new HashSet<>();
                Set<Long> itemConfigListOnTimeAttendanceDay = new HashSet<>();
                //查询满足下班时间条件的班次时段
                setOutOfWorkTimeClassIds(warehouseDate, preDayId, dayIds, classItemConfigList, classItemConfigMap, itemConfigListOnTimePreDay, itemConfigListOnTimeAttendanceDay);

                //当前不存在下班的记录
                getLackCardAfterWork(preWarehouseDate, warehouseDetailList, allWarehouseDetailDOList, itemConfigListOnTimePreDay);
                getLackCardAfterWork(DateUtil.beginOfDay(warehouseDate), warehouseDetailList, allWarehouseDetailDOList, itemConfigListOnTimeAttendanceDay);

                allWarehouseDetailDOList.forEach(warehouseDetailDO -> {
                    Long dayId = Long.valueOf(DateUtil.format(warehouseDetailDO.getWarehouseDate(), DateFormatterUtil.FORMAT_YYYYMMDD));
                    log.info("下班缺卡Job扫描计算,userCode:{},dayId:{}", warehouseDetailDO.getUserCode(), dayId);
                    //考勤计算
                    warehouseAttendanceHandlerService.warehouseAttendanceResultHandler(warehouseDetailDO, dayId, BusinessConstant.OFF_WORK_PUNCH_IN);
                });

                currentPage++;
            } while (currentPage <= pageInfo.getPages());

            log.info("XXL-JOB: {},country：{},currentPage {}，while循环结束", BusinessConstant.JobHandler.ATTENDANCE_OUT_WAREHOUSE_MISS_PUNCH_HANDLER, country, currentPage);
        }
        return ReturnT.SUCCESS;
    }

    private void getLackCardAfterWork(Date warehouseDate, List<WarehouseDetailDO> warehouseDetailList, List<WarehouseDetailDO> allWarehouseDetailDOList, Set<Long> itemConfigListOnTimeDay) {
        if (CollectionUtils.isEmpty(itemConfigListOnTimeDay)) {
            return;
        }
        List<Long> warehouseDetailIdList = new ArrayList<>();
        List<WarehouseDetailDO> calssesWarehouseDetailList = warehouseDetailList
                .stream()
                .filter(record -> Objects.nonNull(record.getClassesId())
                        && record.getWarehouseDate().equals(warehouseDate)
                        && itemConfigListOnTimeDay.contains(record.getClassesId()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(calssesWarehouseDetailList)) {
            return;
        }
        Map<Long, List<WarehouseRecordDO>> warehouseRecordMap = warehouseRecordDao.selectByWarehouseDetailIds(calssesWarehouseDetailList.stream().map(WarehouseDetailDO::getId).collect(Collectors.toList()))
                .stream()
                .collect(Collectors.groupingBy(WarehouseRecordDO::getWarehouseDetailId));

        for (Long warehouseDetailId : warehouseRecordMap.keySet()) {
            List<WarehouseRecordDO> warehouseRecordDOList = warehouseRecordMap.get(warehouseDetailId);
            List<WarehouseRecordDO> inList = warehouseRecordDOList.stream().filter(record -> Objects.equals(BusinessConstant.ONE, record.getRecordType())).collect(Collectors.toList());
            List<WarehouseRecordDO> outList = warehouseRecordDOList.stream().filter(record -> Objects.equals(BusinessConstant.TWO, record.getRecordType())).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(outList) || inList.size() > outList.size()) {
                warehouseDetailIdList.add(warehouseDetailId);
            }
        }
        //得到所有下班缺卡的记录
        List<WarehouseDetailDO> warehouseDetailDOS = warehouseDetailList
                .stream()
                .filter(record -> warehouseDetailIdList.contains(record.getId()))
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(warehouseDetailDOS)) {
            allWarehouseDetailDOList.addAll(warehouseDetailDOS);
        }
    }

    private void setOutOfWorkTimeClassIds(Date warehouseDate,
                                          Long preDayId,
                                          List<Long> dayIds,
                                          List<PunchClassItemConfigDO> classItemConfigList,
                                          Map<Long, List<PunchClassItemConfigDO>> classItemConfigMap,
                                          Set<Long> itemConfigListOnTime_preDay,
                                          Set<Long> itemConfigListOnTime_attendanceDay) {

        for (PunchClassItemConfigDO itemConfigDO : classItemConfigList) {
            for (Long dayId : dayIds) {
                List<PunchClassItemConfigDO> itemConfigDOS = classItemConfigMap.get(itemConfigDO.getPunchClassId());
                if (CollectionUtils.isEmpty(itemConfigDOS)) {
                    continue;
                }
                DayPunchTimeDTO dayPunchTimeDTO = punchClassConfigQueryService.getUserPunchClassItemDayTime(dayId, itemConfigDO.getId(), itemConfigDOS);
                if (Objects.isNull(dayPunchTimeDTO)) {
                    continue;
                }

                if (warehouseDate.compareTo(dayPunchTimeDTO.getDayPunchStartTime()) >= 0) {
                    BigDecimal elasticTime = itemConfigDO.getElasticTime();
                    if (Objects.isNull(elasticTime)) {
                        elasticTime = BigDecimal.valueOf(0);
                    }
                    //下班时间  默认和最晚下班时间同一天
                    Date punchOutTime = DateUtil.parse(DateUtil.format(dayPunchTimeDTO.getDayPunchEndTime(), "yyyy-MM-dd") + " " + DateUtil.format(itemConfigDO.getPunchOutTime(), "HH:mm:ss"), "yyyy-MM-dd HH:mm:ss");
                    if (itemConfigDO.getPunchOutTime().after(itemConfigDO.getLatestPunchOutTime())) {
                        punchOutTime = DateUtil.parse(DateUtil.format(DateUtil.offsetDay(dayPunchTimeDTO.getDayPunchEndTime(), -1), "yyyy-MM-dd") + " " + DateUtil.format(itemConfigDO.getPunchOutTime(), "HH:mm:ss"), "yyyy-MM-dd HH:mm:ss");
                    }
                    BigDecimal addMinutes = elasticTime.add(BigDecimal.valueOf(3)).multiply(new BigDecimal(60));
                    DateTime abnormalPunchOutTime = DateUtil.offsetMinute(punchOutTime, addMinutes.intValue());
                    // 判断当前时间是否大于等于下班时间+弹性时间+3小时
                    if (warehouseDate.compareTo(abnormalPunchOutTime) >= 0) {
                        if (preDayId.equals(dayId)) {
                            itemConfigListOnTime_preDay.add(itemConfigDO.getPunchClassId());
                            continue;
                        }
                        itemConfigListOnTime_attendanceDay.add(itemConfigDO.getPunchClassId());
                    }
                }
            }
        }
    }

    @Data
    public static class AttendanceOutWarehouseMissPunchHandlerParam {
        /**
         * 国家
         */
        private String countryList;
        /**
         * 用户编码
         */
        private String userCodes;

        /**
         * 网点
         */
        private Long ocId;

        /**
         * 供应商
         */
        private Long vendorId;

        /**
         * 考勤日期 格式: yyyy-MM-dd
         */
        private String warehouseDate;

        /**
         * 考勤时间 格式: yyyy-MM-dd HH:mm:ss
         */
        private String warehouseTime;
    }

    private Map<String, Date> getCountryCurrentDate(List<String> country, AttendanceOutWarehouseMissPunchHandlerParam handlerParam) {
        CountryApiQuery countryQuery = new CountryApiQuery();
        countryQuery.setCountryNames(country);
        countryQuery.setOrgId(BusinessConstant.DEFAULT_ORG_ID);
        List<CountryConfigDTO> countryConfigList = countryService.selectCountryConfigList(countryQuery);
        Map<String, String> countryConfigMap = countryConfigList.stream()
                .collect(Collectors.toMap(CountryConfigDTO::getCountryName, CountryConfigDTO::getTimeZone));
        Map<String, Date> dateCountryMap = new HashMap<>();
        Date now = StringUtils.isNotBlank(handlerParam.getWarehouseTime()) ?
                DateUtil.parse(handlerParam.getWarehouseTime(), DateFormatterUtil.FORMAT_YYYY_MM_DD_HH_MM_SS) :
                StringUtils.isBlank(handlerParam.getWarehouseDate()) ?
                        new Date() :
                        DateUtil.parse(handlerParam.getWarehouseDate(), DateFormatterUtil.FORMAT_YYYYMMDD);
        // 获取国家对应时区
        for (String countryName : countryConfigMap.keySet()) {
            String timeZone = countryConfigMap.getOrDefault(countryName, "");
            if (ObjectUtil.equal(timeZone, "")) {
                continue;
            }
            // 获取国家当地时间
            Date dateTime = CommonUtil.convertDateByTimeZonePlus(timeZone, now);
            XxlJobLogger.log("ATTENDANCE_DAY_GENERATE_HANDLER: 北京时间:{},当前国家:{},当地时间:{}", now, country, dateTime);
            dateCountryMap.put(countryName, dateTime);
        }
        return dateCountryMap;
    }

}
