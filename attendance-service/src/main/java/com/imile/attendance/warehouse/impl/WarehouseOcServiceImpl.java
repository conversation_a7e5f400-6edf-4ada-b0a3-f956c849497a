package com.imile.attendance.warehouse.impl;

import com.google.common.collect.Lists;
import com.imile.attendance.apollo.AttendanceProperties;
import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.context.RequestInfoHolder;
import com.imile.attendance.enums.ErrorCodeEnum;
import com.imile.attendance.hermes.support.RpcHermesEntOcClientSupport;
import com.imile.attendance.infrastructure.repository.common.AttendanceDeptService;
import com.imile.attendance.infrastructure.repository.common.AttendanceUserService;
import com.imile.attendance.infrastructure.repository.common.EntOcService;
import com.imile.attendance.infrastructure.repository.common.dto.AttendanceDept;
import com.imile.attendance.infrastructure.repository.common.dto.AttendanceUser;
import com.imile.attendance.infrastructure.repository.warehouse.dao.WarehouseDetailDao;
import com.imile.attendance.infrastructure.repository.warehouse.model.WarehouseDetailDO;
import com.imile.attendance.infrastructure.repository.warehouse.query.WarehouseDetailQuery;
import com.imile.attendance.permission.WpmPermissionService;
import com.imile.attendance.warehouse.WarehouseBaseService;
import com.imile.attendance.warehouse.WarehouseOcService;
import com.imile.attendance.warehouse.WarehouseSupplierService;
import com.imile.attendance.warehouse.param.GetOcListByVendorCodeParam;
import com.imile.attendance.warehouse.param.GetVendorListByOcListParam;
import com.imile.attendance.warehouse.param.WarehouseOcParam;
import com.imile.attendance.warehouse.vo.OcVO;
import com.imile.attendance.warehouse.vo.WarehouseOcCountryVO;
import com.imile.attendance.warehouse.vo.WarehouseOcRegionVO;
import com.imile.attendance.warehouse.vo.WarehouseOcVO;
import com.imile.common.exception.BusinessException;
import com.imile.hermes.enterprise.dto.EntOcApiDTO;
import com.imile.util.lang.I18nUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR> chen
 * @Date 2025/07/09
 * @Time 19:59
 * @Description
 */
@Slf4j
@Service
public class WarehouseOcServiceImpl extends WarehouseBaseService implements WarehouseOcService {

    @Resource
    private WarehouseDetailDao warehouseDetailDao;
    @Resource
    private AttendanceUserService userService;
    @Resource
    private AttendanceDeptService deptService;
    @Resource
    private EntOcService entOcService;
    @Resource
    private WarehouseSupplierService warehouseSupplierService;
    @Resource
    private WpmPermissionService wpmPermissionService;
    @Resource
    private AttendanceProperties attendanceProperties;
    @Resource
    private RpcHermesEntOcClientSupport hermesEntOcClientSupport;

    @Override
    public List<OcVO> getAuthOcList(String employeeType) {
        List<OcVO> ocList = getOcVOList();
        if (StringUtils.isEmpty(employeeType) || !Objects.equals(BusinessConstant.LABOR_DISPATCH, employeeType)) {
            return ocList;
        }
        List<String> ocIdList = Arrays.stream(attendanceProperties.getVendor().getFilterOcId()
                .split(BusinessConstant.DEFAULT_DELIMITER)).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(ocIdList)) {
            return ocList.stream().filter(oc -> !ocIdList.contains(oc.getOcId().toString()))
                    .collect(Collectors.toList());
        }
        return ocList;
    }

    @Override
    public List<OcVO> getAuthOcListByUserId(Long userId) {
        if (Objects.isNull(userId)) {
            return Collections.emptyList();
        }
        AttendanceUser userInfoDO = Optional.ofNullable(userService.getByUserId(userId))
                .orElseThrow(() -> BusinessException.get(ErrorCodeEnum.ACCOUNT_NOT_EXITS.getCode(),
                        I18nUtils.getMessage(ErrorCodeEnum.ACCOUNT_NOT_EXITS.getDesc())));
        // 查询用户网点权限
        List<AttendanceDept> entDeptList = wpmPermissionService.getRegionStationAuthDeptList(userInfoDO.getUserCode());
        return this.convertOcVOList(entDeptList);
    }

    @Override
    public List<OcVO> getOcListByCondition(GetVendorListByOcListParam param) {
        if (StringUtils.isEmpty(param.getCountry())
                && CollectionUtils.isEmpty(param.getCityList())
                && CollectionUtils.isEmpty(param.getOcIdList())
        ) {
            return getDeptAuthVOList(RequestInfoHolder.getUserCode());
        }
        List<AttendanceDept> deptOcList = wpmPermissionService.getAuthDeptList(RequestInfoHolder.getUserCode(), null);
        if (StringUtils.isNotEmpty(param.getCountry())) {
            deptOcList = deptOcList.stream()
                    .filter(oc -> param.getCountry().equals(oc.getCountry()))
                    .collect(Collectors.toList());
        }
        if (CollectionUtils.isNotEmpty(param.getCityList())) {
            deptOcList = deptOcList.stream()
                    .filter(oc -> StringUtils.isNotEmpty(oc.getCity()) && param.getCityList().contains(oc.getCity()))
                    .collect(Collectors.toList());
        }
        return convertOcVOList(deptOcList);
    }

    @Override
    public List<OcVO> getDeptAuthVOList(String userCode) {
        List<AttendanceDept> entDeptInfoList = wpmPermissionService.getAuthDeptList(userCode, null);
        return this.convertOcVOList(entDeptInfoList);
    }

    @Override
    public EntOcApiDTO getOc(Long id) {
        //id就是deptId
        AttendanceDept attendanceDept = deptService.getByDeptId(id);
        if (Objects.isNull(attendanceDept)) {
            throw BusinessException.get(ErrorCodeEnum.STATION_NOT_EXITS.getCode(), I18nUtils.getMessage(ErrorCodeEnum.STATION_NOT_EXITS.getDesc()));
        }
        EntOcApiDTO oc = entOcService.getOcById(BusinessConstant.DEFAULT_ORG_ID, attendanceDept.getOcId());
        if (Objects.isNull(oc)) {
            throw BusinessException.get(ErrorCodeEnum.STATION_NOT_EXITS.getCode(), I18nUtils.getMessage(ErrorCodeEnum.STATION_NOT_EXITS.getDesc()));
        }
        return oc;
    }

    @Override
    public List<OcVO> getNoBingShiftOcList() {
        List<OcVO> ocVOList = getDeptAuthVOList(RequestInfoHolder.getUserCode());
        if (CollectionUtils.isEmpty(ocVOList)) {
            return Collections.emptyList();
        }

        List<WarehouseDetailDO> warehouseDetailList = warehouseDetailDao.selectNoBindShiftByCondition(WarehouseDetailQuery
                .builder()
                .ocIdList(ocVOList.stream()
                        .map(OcVO::getOcId)
                        .distinct()
                        .collect(Collectors.toList()))
                .build());
        if (CollectionUtils.isEmpty(warehouseDetailList)) {
            return Collections.emptyList();
        }
        List<Long> ocIds = warehouseDetailList.stream().map(WarehouseDetailDO::getOcId).distinct().collect(Collectors.toList());
        return ocVOList.stream().filter(oc -> ocIds.contains(oc.getOcId())).collect(Collectors.toList());
    }

    @Override
    public List<OcVO> getOcVOList() {
        List<AttendanceDept> entDeptDOList = wpmPermissionService.getRegionStationAuthDeptList(RequestInfoHolder.getUserCode());
        return convertOcVOList(entDeptDOList);
    }

    @Override
    public List<OcVO> getOcListByVendorCode(GetOcListByVendorCodeParam param) {
        if (Objects.isNull(param) || CollectionUtils.isEmpty(param.getVendorCodeList())) {
            return Collections.emptyList();
        }

        //查询供应商区域中心编码
        List<String> ocCenterCodeList = warehouseSupplierService.getOcCodeList(param.getVendorCodeList());
        List<EntOcApiDTO> totalEntOcList = new ArrayList<>();
        for (String ocCenterCode : ocCenterCodeList) {
            List<EntOcApiDTO> entOcApiList = hermesEntOcClientSupport.listOcByOcCenterCode(ocCenterCode);
            if (CollectionUtils.isEmpty(entOcApiList)) {
                continue;
            }
            totalEntOcList.addAll(entOcApiList);
        }
        if (CollectionUtils.isEmpty(totalEntOcList)) {
            return Collections.emptyList();
        }

        List<String> ocCodeList = totalEntOcList.stream().map(EntOcApiDTO::getOcCode).filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
        List<AttendanceDept> entDeptInfoList = deptService.listByOcCode(ocCodeList).stream()
                .filter(this::filterDept)
                .collect(Collectors.toList());
        return convertOcVOList(entDeptInfoList);
    }

    @Override
    public List<WarehouseOcCountryVO> getWarehouseOcTree(WarehouseOcParam param) {
        // 查询用户组织树权限
        List<AttendanceDept> deptList = wpmPermissionService.getAuthDeptList(RequestInfoHolder.getUserCode(), param.getOcName());

        if (CollectionUtils.isEmpty(deptList)) {
            return Collections.emptyList();
        }

        Set<String> parentDeptCodeList = new HashSet<>();
        for (AttendanceDept deptDO : deptList) {
            if (StringUtils.isNotBlank(deptDO.getParentDeptCode())) {
                parentDeptCodeList.add(deptDO.getParentDeptCode());
            }
        }

        // 查询上级部门的部门名称
        Map<String, AttendanceDept> parentMap = deptService.listByDeptCodes(parentDeptCodeList).stream().collect(Collectors.toMap(AttendanceDept::getDeptCode, Function.identity()));

        // 组装返回值
        List<WarehouseOcCountryVO> result = Lists.newArrayList();
        deptList.stream()
                .filter(dept -> StringUtils.isNotBlank(dept.getCountry())).collect(Collectors.groupingBy(AttendanceDept::getCountry))
                .forEach((k, v) -> {
                    WarehouseOcCountryVO countryVO = new WarehouseOcCountryVO();
                    countryVO.setCountry(k);
                    countryVO.setRegionList(Lists.newArrayList());
                    v.stream().filter(x2 -> x2.getParentDeptCode() != null).collect(Collectors.groupingBy(AttendanceDept::getParentDeptCode)).forEach((k1, v1) -> {
                        if (!parentMap.containsKey(k1)) {
                            return;
                        }
                        AttendanceDept parent = parentMap.get(k1);
                        WarehouseOcRegionVO regionVO = new WarehouseOcRegionVO();
                        regionVO.setParentDeptNameCn(parent.getDeptNameCn());
                        regionVO.setParentDeptNameEn(parent.getDeptNameEn());
                        regionVO.setParentDeptCode(parent.getDeptCode());
                        regionVO.setOcList(CollectionUtils.isEmpty(v1) ? Lists.newArrayList() : v1.stream().map(v2 -> {
                            WarehouseOcVO warehouseOcVO = new WarehouseOcVO();
                            warehouseOcVO.setDeptCode(v2.getDeptCode());
                            warehouseOcVO.setOcId(v2.getOcId());
                            warehouseOcVO.setOcNameCn(v2.getDeptNameCn());
                            warehouseOcVO.setOcNameEn(v2.getDeptNameEn());
                            warehouseOcVO.setOcCode(v2.getOcCode());
                            return warehouseOcVO;
                        }).collect(Collectors.toList()));
                        countryVO.getRegionList().add(regionVO);
                    });
                    result.add(countryVO);
                });
        return result;
    }

}
