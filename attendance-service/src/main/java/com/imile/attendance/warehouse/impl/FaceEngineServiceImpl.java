package com.imile.attendance.warehouse.impl;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import com.google.common.io.BaseEncoding;
import com.imile.attendance.apollo.AttendanceProperties;
import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.enums.BlacklistBanStatusEnum;
import com.imile.attendance.enums.ClassNatureEnum;
import com.imile.attendance.enums.CountryCodeEnum;
import com.imile.attendance.enums.EmploymentTypeEnum;
import com.imile.attendance.enums.ErrorCodeEnum;
import com.imile.attendance.enums.FaceErrorCodeEnum;
import com.imile.attendance.enums.warehouse.FaceRecordStatusEnum;
import com.imile.attendance.exception.BusinessLogicException;
import com.imile.attendance.gensis.support.RpcUserCertificateSupport;
import com.imile.attendance.hrms.RpcBlacklistClient;
import com.imile.attendance.hrms.RpcUserBaseClient;
import com.imile.attendance.infrastructure.idwork.DefaultIdWorker;
import com.imile.attendance.infrastructure.repository.common.AttendanceDeptService;
import com.imile.attendance.infrastructure.repository.common.AttendanceUserService;
import com.imile.attendance.infrastructure.repository.common.EntOcService;
import com.imile.attendance.infrastructure.repository.common.dto.AttendanceDept;
import com.imile.attendance.infrastructure.repository.common.dto.AttendanceUser;
import com.imile.attendance.infrastructure.repository.warehouse.dao.UserFaceMarkRecordDao;
import com.imile.attendance.infrastructure.repository.warehouse.dao.WarehouseFaceRecordDao;
import com.imile.attendance.infrastructure.repository.warehouse.model.UserFaceMarkRecordDO;
import com.imile.attendance.infrastructure.repository.warehouse.model.WarehouseFaceRecordDO;
import com.imile.attendance.ipep.support.RpcIpepClientSupport;
import com.imile.attendance.recognition.RpcRecognitionClient;
import com.imile.attendance.rule.WarehouseRuleHandler;
import com.imile.attendance.util.BaseDOUtil;
import com.imile.attendance.warehouse.FaceEngineService;
import com.imile.attendance.warehouse.WarehouseSupplierService;
import com.imile.attendance.warehouse.param.FaceCheckParam;
import com.imile.attendance.warehouse.param.FaceSaveParam;
import com.imile.attendance.warehouse.param.FaceSearchParam;
import com.imile.attendance.warehouse.param.FaceSearchRepeatParam;
import com.imile.attendance.warehouse.vo.UserFaceSearchRepeatVO;
import com.imile.attendance.warehouse.vo.UserFaceSearchVO;
import com.imile.common.enums.StatusEnum;
import com.imile.common.exception.BusinessException;
import com.imile.genesis.api.enums.CertificateTypeEnum;
import com.imile.genesis.api.model.result.user.UserCertificateDTO;
import com.imile.hermes.enterprise.dto.EntOcApiDTO;
import com.imile.hrms.api.blacklist.dto.BlacklistInfoDTO;
import com.imile.hrms.api.primary.enums.OperationSceneEnum;
import com.imile.hrms.api.primary.model.param.user.UserUpdateParam;
import com.imile.recognition.api.face.model.dto.UserFaceSearchDTO;
import com.imile.util.BeanUtils;
import com.imile.util.lang.I18nUtils;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.rpc.RpcException;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 人脸识别服务Service
 *
 * <AUTHOR> chen
 * @Date 2025/07/09
 * @Time 16:25
 * @Description
 */
@Slf4j
@Service
public class FaceEngineServiceImpl implements FaceEngineService {

    @Resource
    private AttendanceUserService userService;
    @Resource
    private RpcIpepClientSupport rpcIpepClientSupport;
    @Resource
    private RpcRecognitionClient rpcRecognitionClient;
    @Resource
    private WarehouseFaceRecordDao warehouseFaceRecordDao;
    @Resource
    private AttendanceDeptService deptService;
    @Resource
    private EntOcService entOcService;
    @Resource
    private WarehouseSupplierService warehouseSupplierService;
    @Resource
    private RpcBlacklistClient rpcBlacklistClient;
    @Resource
    private WarehouseRuleHandler warehouseRuleHandler;
    @Resource
    private RpcUserCertificateSupport rpcUserCertificateSupport;
    @Resource
    private RpcUserBaseClient userBaseClient;
    @Resource
    private AttendanceProperties attendanceProperties;
    @Resource
    private UserFaceMarkRecordDao userFaceMarkRecordDao;
    @Resource
    protected DefaultIdWorker defaultIdWorker;

    @SneakyThrows
    @Override
    public void faceInput(FaceSaveParam param) {
        AttendanceUser attendanceUser = Optional.ofNullable(userService.getByUserCode(param.getUserCode()))
                .orElseThrow(() -> BusinessLogicException.getException(ErrorCodeEnum.ACCOUNT_NOT_EXITS));
        String faceUrl = rpcIpepClientSupport.upload(BusinessConstant.FACE_UPLOAD_FILE_PATH_PREFIX,
                param.getFile().getOriginalFilename(), param.getFile().getBytes());
        String imageBase64 = BaseEncoding.base64().encode(param.getFile().getBytes());
        faceInput(param.getFaceTime(), attendanceUser, faceUrl, imageBase64);
        if (StringUtils.isNotBlank(param.getSimilarUserCode())) {
            markUserFaceRecord(param, attendanceUser);
        }
    }

    @SneakyThrows
    @Override
    public UserFaceSearchVO faceCheck(FaceCheckParam param) {
        checkFaceBaseParams(param.getEmployeeType(), param.getOcId(), param.getVendorId());
        AttendanceUser attendanceUser = Optional.ofNullable(userService.getByUserCode(param.getUserCode()))
                .orElseThrow(() -> BusinessException.get(ErrorCodeEnum.ACCOUNT_NOT_EXITS.getCode(), I18nUtils.getMessage((ErrorCodeEnum.ACCOUNT_NOT_EXITS.getDesc()))));
        checkUser(attendanceUser);
        String faceUrl = rpcIpepClientSupport.upload(BusinessConstant.FACE_UPLOAD_FILE_PATH_PREFIX, param.getFile().getOriginalFilename(), param.getFile().getBytes());
        String imageBase64 = BaseEncoding.base64().encode(param.getFile().getBytes());
        UserFaceSearchDTO userFaceSearchDTO;
        boolean facialPrivacyControl = checkFacialPrivacyControl(attendanceUser.getLocationCountry());
        try {
            userFaceSearchDTO = rpcRecognitionClient.faceSingleCheck(attendanceUser.getUserCode(), faceUrl, imageBase64, facialPrivacyControl);
        } catch (RpcException e) {
            log.info("人脸一对一检查失败,RPC{}", Throwables.getStackTraceAsString(e));
            throw BusinessException.get(String.valueOf(e.getCode()), e.getMessage());
        }
        saveFaceRecord(attendanceUser, userFaceSearchDTO.getScore(), userFaceSearchDTO.getPass(), faceUrl, userFaceSearchDTO.getFaceKey(), userFaceSearchDTO.getEncFaceKey(), param.getFaceTime(), facialPrivacyControl);
        return convert(attendanceUser, userFaceSearchDTO.getScore(), userFaceSearchDTO.getPass(), userFaceSearchDTO.getFaceKey(), param.getVendorId(), false);
    }

    @SneakyThrows
    @Override
    public UserFaceSearchVO faceRecognition(FaceSearchParam param) {
        checkFaceBaseParams(param.getEmployeeType(), param.getOcId(), param.getVendorId());
        AttendanceDept attendanceDept = Optional.ofNullable(deptService.getByDeptId(param.getOcId()))
                .orElseThrow(() -> BusinessException.get(ErrorCodeEnum.STATION_NOT_EXITS.getCode(),
                        I18nUtils.getMessage(ErrorCodeEnum.STATION_NOT_EXITS.getDesc())));
        //人脸识别照上传
        String faceUrl = rpcIpepClientSupport.upload(BusinessConstant.FACE_UPLOAD_FILE_PATH_PREFIX,
                param.getFile().getOriginalFilename(), param.getFile().getBytes());
        String imageBase64 = BaseEncoding.base64().encode(param.getFile().getBytes());
        boolean facialPrivacyControl = checkFacialPrivacyControl(attendanceDept.getCountry());
        List<UserFaceSearchDTO> userFaceSearchDTOList;
        try {
            userFaceSearchDTOList = rpcRecognitionClient.faceRecognitionPlus(faceUrl, imageBase64, attendanceDept.getCountry(), EmploymentTypeEnum.WAREHOUSE_EMPLOYEE_TYPE, facialPrivacyControl);
            log.info("人脸识别结果 faceRecognitionPlus: {}", JSON.toJSONString(userFaceSearchDTOList));
            if (CollectionUtils.isEmpty(userFaceSearchDTOList)) {
                return convertDefaultUserFaceSearchVO();
            }
        } catch (RpcException e) {
            log.info("人脸识别失败,faceRecognitionPlus RPC{}", Throwables.getStackTraceAsString(e));
            throw BusinessException.get(String.valueOf(e.getCode()), e.getMessage());
        }
        if (Objects.equals(BusinessConstant.FORMAL, param.getEmployeeType())) {
            return formalEmployeeFaceSearch(param, faceUrl, facialPrivacyControl, userFaceSearchDTOList);
        } else {
            return laborEmployeeFaceSearch(param, faceUrl, facialPrivacyControl, userFaceSearchDTOList);
        }
    }

    @SneakyThrows
    @Override
    public UserFaceSearchRepeatVO faceRecognitionV2(FaceSearchRepeatParam param) {
        AttendanceUser attendanceUser = Optional.ofNullable(userService.getByUserId(param.getUserId()))
                .orElseThrow(() -> BusinessException.get(ErrorCodeEnum.ACCOUNT_NOT_EXITS.getCode(), I18nUtils.getMessage(ErrorCodeEnum.ACCOUNT_NOT_EXITS.getDesc())));
        //人脸识别照上传
        String faceUrl = rpcIpepClientSupport.upload(BusinessConstant.FACE_UPLOAD_FILE_PATH_PREFIX, param.getFile().getOriginalFilename(), param.getFile().getBytes());
        String imageBase64 = BaseEncoding.base64().encode(param.getFile().getBytes());
        UserFaceSearchDTO userFaceSearchDTO;
        boolean facialPrivacyControl = checkFacialPrivacyControl(attendanceUser.getLocationCountry());
        try {
            userFaceSearchDTO = rpcRecognitionClient.faceRecognition(faceUrl, imageBase64, attendanceUser.getLocationCountry(), EmploymentTypeEnum.WAREHOUSE_EMPLOYEE_TYPE, facialPrivacyControl);
        } catch (RpcException e) {
            log.info("人脸识别失败,RPC{}", Throwables.getStackTraceAsString(e));
            throw BusinessException.get(String.valueOf(e.getCode()), e.getMessage());
        }

        log.info("人脸识别结果: {}", JSON.toJSONString(userFaceSearchDTO));
        if (Objects.isNull(userFaceSearchDTO) || StringUtils.isEmpty(userFaceSearchDTO.getUserCode())) {
            //人脸录入
            return faceInput(param.getFaceTime(), attendanceUser, faceUrl, imageBase64);
        }
        AttendanceUser similarUser = userService.getByUserCode(userFaceSearchDTO.getUserCode());
        if (Objects.isNull(similarUser)
                || !userFaceSearchDTO.getPass()
                || !Objects.equals(StatusEnum.ACTIVE.getCode(), similarUser.getStatus())
                || Objects.equals(BusinessConstant.Y, similarUser.getIsDriver())
                || Objects.equals(BusinessConstant.N, similarUser.getIsWarehouseStaff())
                || Objects.equals(ClassNatureEnum.FIXED_CLASS.name(), similarUser.getClassNature())) {
            //人脸录入
            return faceInput(param.getFaceTime(), attendanceUser, faceUrl, imageBase64);
        }

        //用户人脸打标记录过滤
        Set<String> filterUserCodeList = filterUserFaceMarkRecord(similarUser);
        if (CollectionUtils.isNotEmpty(filterUserCodeList)) {
            if (filterUserCodeList.contains(similarUser.getUserCode())) {
                //人脸录入
                faceInput(param.getFaceTime(), similarUser, faceUrl, imageBase64);
                return convertDefaultResult();
            }
        }

        saveFaceRecord(similarUser, userFaceSearchDTO.getScore(), userFaceSearchDTO.getPass(), faceUrl,
                userFaceSearchDTO.getFaceKey(), userFaceSearchDTO.getEncFaceKey(), param.getFaceTime(), facialPrivacyControl);

        String certificateTypeCode = getCertificateType(similarUser.getLocationCountry());
        Map<String, UserCertificateDTO> certificateDTOMap = rpcUserCertificateSupport.listUserCertificateList(Lists.newArrayList(userFaceSearchDTO.getUserCode(), similarUser.getUserCode()), certificateTypeCode)
                .stream()
                .collect(Collectors.toMap(UserCertificateDTO::getUserCode, Function.identity(), (v1, v2) -> v1));

        return convertUserFaceSearchRepeatVO(attendanceUser, userFaceSearchDTO, similarUser, certificateDTOMap);
    }


    @Override
    public List<String> getFacePrivacySecurityCountry() {
        return Optional.ofNullable(attendanceProperties.getAttendance().getFacePrivacySecurityCountry())
                .map(str -> str.split(BusinessConstant.DEFAULT_DELIMITER))
                .map(Arrays::stream)
                .orElseGet(Stream::empty)
                .collect(Collectors.toList());
    }


    private UserFaceSearchRepeatVO faceInput(Date faceTime,
                                             AttendanceUser attendanceUser,
                                             String faceUrl,
                                             String imageBase64) {
        checkUser(attendanceUser);
        boolean facialPrivacyControl = checkFacialPrivacyControl(attendanceUser.getLocationCountry());
        try {
            rpcRecognitionClient.faceFeatureSave(attendanceUser.getUserCode(), faceUrl, imageBase64, facialPrivacyControl);
        } catch (RpcException e) {
            log.error("人脸特征录入异常：{}", Throwables.getStackTraceAsString(e));
            throw BusinessException.get(String.valueOf(e.getCode()), e.getMessage());
        }
        saveFaceRecord(attendanceUser, 1, true, faceUrl, faceUrl, null, faceTime, facialPrivacyControl);
        return convertDefaultResult();
    }

    private void saveFaceRecord(AttendanceUser attendanceUser,
                                float score,
                                boolean pass,
                                String recognitionPhoto,
                                String facePhoto,
                                String encFacePhoto,
                                Date faceTime,
                                boolean facialPrivacyControl) {
        WarehouseFaceRecordDO faceRecordDO = new WarehouseFaceRecordDO();
        faceRecordDO.setUserId(attendanceUser.getId());
        faceRecordDO.setUserCode(attendanceUser.getUserCode());
        faceRecordDO.setFaceRecordStatus(pass ? FaceRecordStatusEnum.EFFECTIVE.getCode() : FaceRecordStatusEnum.INVALID.getCode());
        faceRecordDO.setFaceRecordTime(faceTime);
        faceRecordDO.setWarehouseDate(DateUtil.parseDate(DateUtil.formatDate(faceTime)));
        faceRecordDO.setRecognitionScore(Convert.toBigDecimal(score));
        if (facialPrivacyControl) {
            //人脸录入时为空
            if (Objects.isNull(encFacePhoto)) {
                faceRecordDO.setFacePhoto(rpcRecognitionClient.faceEncryption(recognitionPhoto, true));
                faceRecordDO.setRecognitionPhoto(faceRecordDO.getFacePhoto());
            } else {
                faceRecordDO.setFacePhoto(encFacePhoto);
                faceRecordDO.setRecognitionPhoto(rpcRecognitionClient.faceEncryption(recognitionPhoto, true));
            }
        } else {
            faceRecordDO.setFacePhoto(facePhoto);
            faceRecordDO.setRecognitionPhoto(recognitionPhoto);
        }
        BaseDOUtil.fillDOInsert(faceRecordDO);
        warehouseFaceRecordDao.save(faceRecordDO);
    }

    private UserFaceSearchRepeatVO convertDefaultResult() {
        UserFaceSearchRepeatVO result = new UserFaceSearchRepeatVO();
        result.setScore(0f);
        result.setPass(false);
        return result;
    }

    private void checkFaceBaseParams(String employeeType, Long ocId, Long vendorId) {
        BusinessLogicException.checkTrue(!BusinessConstant.WAREHOUSE_EMPLOYEE_TYPE.contains(employeeType), ErrorCodeEnum.PARAM_VALID_ERROR);
        if (BusinessConstant.LABOR_DISPATCH.equals(employeeType) && (Objects.isNull(ocId) || Objects.isNull(vendorId))) {
            throw BusinessException.get(ErrorCodeEnum.PARAM_VALID_ERROR.getCode(), I18nUtils.getMessage(ErrorCodeEnum.PARAM_VALID_ERROR.getDesc()));
        }
    }

    private UserFaceSearchVO convert(AttendanceUser attendanceUser,
                                     Float score,
                                     boolean pass,
                                     String fileUrl,
                                     Long vendorId,
                                     boolean faceRepeat) {
        if (StringUtils.isBlank(attendanceUser.getUserCode()) || Objects.isNull(score)) {
            UserFaceSearchVO userFaceSearchVO = new UserFaceSearchVO();
            userFaceSearchVO.setScore(0f);
            userFaceSearchVO.setPass(false);
            return userFaceSearchVO;
        }
        UserFaceSearchVO faceSearchVO = new UserFaceSearchVO();
        faceSearchVO.setUserCode(attendanceUser.getUserCode());
        faceSearchVO.setScore(score);
        faceSearchVO.setPass(pass);
        faceSearchVO.setFaceUrl(fileUrl);

        AttendanceDept oc = deptService.getByDeptId(attendanceUser.getDeptId());
        Map<String, String> supplierMap = warehouseSupplierService.getSupplierByCodes(Collections.singletonList(attendanceUser.getVendorCode()));
        faceSearchVO.setSex(attendanceUser.getSex());
        faceSearchVO.setOcId(attendanceUser.getDeptId());
        faceSearchVO.setOcName(Objects.nonNull(oc) ? oc.getDeptNameEn() : null);
        faceSearchVO.setUserName(attendanceUser.getUserName());
        faceSearchVO.setVendorId(attendanceUser.getVendorId());
        faceSearchVO.setVendorCode(attendanceUser.getVendorCode());
        faceSearchVO.setVendorName(supplierMap.get(attendanceUser.getVendorCode()));
        faceSearchVO.setUserId(attendanceUser.getId());
        faceSearchVO.setVendorChange(Objects.nonNull(vendorId) &&
                !Objects.equals(faceSearchVO.getVendorId(), vendorId) &&
                !Objects.equals(CountryCodeEnum.BRA.getCode(), attendanceUser.getLocationCountry()));
        faceSearchVO.setFaceRepeat(faceRepeat);
        return faceSearchVO;
    }

    private UserFaceSearchVO convertDefaultUserFaceSearchVO() {
        UserFaceSearchVO userFaceSearchVO = new UserFaceSearchVO();
        userFaceSearchVO.setScore(0f);
        userFaceSearchVO.setPass(false);
        return userFaceSearchVO;
    }

    private void checkUser(AttendanceUser user) {
        if (!Objects.equals(StatusEnum.ACTIVE.getCode(), user.getStatus())) {
            //黑名单用户检查
            BlacklistInfoDTO blacklistInfo = rpcBlacklistClient.getBlacklistInfo(user.getUserCode());
            if (!BlacklistBanStatusEnum.getBanStatusList().contains(blacklistInfo.getBanStatus())) {
                //非黑名单导致的账号停用
                throw BusinessException.get(ErrorCodeEnum.ACCOUNT_HAS_BEEN_DEACTIVATED.getCode(),
                        I18nUtils.getMessage(ErrorCodeEnum.ACCOUNT_HAS_BEEN_DEACTIVATED.getDesc()));
            }
        }

        //限制司机使用
        if (Objects.equals(BusinessConstant.Y, user.getIsDriver())) {
            throw BusinessException.get(FaceErrorCodeEnum.DRIVER_CANT_USE_WPM.getCode(),
                    I18nUtils.getMessage(FaceErrorCodeEnum.DRIVER_CANT_USE_WPM.getDesc()));
        }

        //限制非仓内人员使用
        if (Objects.equals(BusinessConstant.N, user.getIsWarehouseStaff())) {
            throw BusinessException.get(FaceErrorCodeEnum.NON_WAREHOUSE_EMPLOYEES_CANT_USE_WPM.getCode(),
                    I18nUtils.getMessage(FaceErrorCodeEnum.NON_WAREHOUSE_EMPLOYEES_CANT_USE_WPM.getDesc()));
        }
        //限制固定班次类型人员使用
        if (StringUtils.isNotBlank(user.getClassNature()) && Objects.equals(ClassNatureEnum.FIXED_CLASS.name(), user.getClassNature())) {
            throw BusinessException.get(ErrorCodeEnum.USER_CLASS_NATURE_CONFIG_ERROR.getCode(),
                    I18nUtils.getMessage(ErrorCodeEnum.USER_CLASS_NATURE_CONFIG_ERROR.getDesc()));
        }
    }


    private UserFaceSearchVO laborEmployeeFaceSearch(FaceSearchParam param,
                                                     String faceUrl,
                                                     boolean facialPrivacyControl,
                                                     List<UserFaceSearchDTO> userFaceSearchDTOList) {

        List<String> userCodeList = userFaceSearchDTOList.stream().map(UserFaceSearchDTO::getUserCode).distinct().collect(Collectors.toList());
        List<AttendanceUser> userList = userService.listByUserCodes(userCodeList);
        if (CollectionUtils.isEmpty(userList)) {
            return convertDefaultUserFaceSearchVO();
        }

        Optional<UserFaceSearchDTO> userFaceSearchOptional = userFaceSearchDTOList.stream()
                .filter(userFaceSearch -> EmploymentTypeEnum.TYPE_OF_EMPLOYEE_WAREHOUSE.contains(userFaceSearch.getEmployeeType()))
                .findFirst();
        if (userFaceSearchOptional.isPresent()) {
            UserFaceSearchDTO userFaceSearchDTO = userFaceSearchOptional.get();
            AttendanceUser userInfoDO = userList.stream().filter(user -> Objects.equals(user.getUserCode(), userFaceSearchDTO.getUserCode())).findFirst().orElse(new AttendanceUser());
            //人脸打标记录过滤
            Set<String> filterUserCodeList = filterUserFaceMarkRecord(userInfoDO);
            if (CollectionUtils.isEmpty(filterUserCodeList)) {
                throw BusinessException.get(ErrorCodeEnum.EMPLOYEE_FACE_RECOGNITION_ENTRANCE_SELECT_INCORRECT.getCode(),
                        I18nUtils.getMessage(ErrorCodeEnum.EMPLOYEE_FACE_RECOGNITION_ENTRANCE_SELECT_INCORRECT.getDesc()));
            }

            userFaceSearchDTOList = userFaceSearchDTOList.stream()
                    .filter(userFace -> filterUserCodeList.contains(userFace.getUserCode())
                            && Objects.equals(EmploymentTypeEnum.OS_FIXED_SALARY.getCode(), userFace.getEmployeeType()))
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(userFaceSearchDTOList)) {
                log.info("劳务用户人脸打标记录不包含匹配到的用户");
                throw BusinessException.get(ErrorCodeEnum.EMPLOYEE_FACE_RECOGNITION_ENTRANCE_SELECT_INCORRECT.getCode(),
                        I18nUtils.getMessage(ErrorCodeEnum.EMPLOYEE_FACE_RECOGNITION_ENTRANCE_SELECT_INCORRECT.getDesc()));
            }
        }

        UserFaceSearchDTO userFaceSearchDTO = userFaceSearchDTOList.get(0);
        AttendanceUser user = userList.stream().filter(userInfoDO -> Objects.equals(userInfoDO.getUserCode(), userFaceSearchDTO.getUserCode())).findFirst().orElse(new AttendanceUser());

        //检测用户：1：黑名单、用户状态; 2:用户群体是否符合WPM打卡
        checkUser(user);
        boolean pass = userFaceSearchDTO.getPass();
        saveFaceRecord(user, userFaceSearchDTO.getScore(), pass, faceUrl, userFaceSearchDTO.getFaceKey(), userFaceSearchDTO.getEncFaceKey(), param.getFaceTime(), facialPrivacyControl);
        if (pass && !Objects.equals(user.getDeptId(), param.getOcId())) {
            //劳务员工更新网点信息
            AttendanceDept attendanceDept = deptService.getByDeptId(param.getOcId());
            if (Objects.isNull(attendanceDept)) {
                throw BusinessException.get(ErrorCodeEnum.STATION_NOT_EXITS.getCode(), I18nUtils.getMessage(ErrorCodeEnum.STATION_NOT_EXITS.getDesc()));
            }
            EntOcApiDTO oc = entOcService.getOcById(BusinessConstant.DEFAULT_ORG_ID, attendanceDept.getOcId());
            if (Objects.isNull(oc)) {
                throw BusinessException.get(ErrorCodeEnum.STATION_NOT_EXITS.getCode(), I18nUtils.getMessage(ErrorCodeEnum.STATION_NOT_EXITS.getDesc()));
            }
            UserUpdateParam build = UserUpdateParam.builder()
                    .userCode(user.getUserCode())
                    .leaderUserCode(oc.getOcPrincipalCode())
                    .deptCode(attendanceDept.getDeptCode())
                    .ocCode(oc.getOcCode())
                    .locationCountry(oc.getCountry())
                    .locationCity(oc.getCity())
                    .locationProvince(oc.getProvince())
                    .operationScene(OperationSceneEnum.WPM)
                    .build();
            userBaseClient.updateUser(build);

            //人员消息的同步需要时间,这里转换对象并设置更新值
            AttendanceUser updateUser = BeanUtils.convert(user, AttendanceUser.class);
            updateUser.setDeptId(attendanceDept.getId());
            updateUser.setOcCode(oc.getOcCode());
            updateUser.setLocationCountry(oc.getCountry());
            warehouseRuleHandler.warehouseCalendarAndPunchConfigHandler(updateUser, user);
        }
        return convert(user, userFaceSearchDTO.getScore(), userFaceSearchDTO.getPass(), userFaceSearchDTO.getFaceKey(), param.getVendorId(), false);
    }

    private UserFaceSearchVO formalEmployeeFaceSearch(FaceSearchParam param,
                                                      String faceUrl,
                                                      boolean facialPrivacyControl,
                                                      List<UserFaceSearchDTO> userFaceSearchDTOList) {
        List<String> userCodeList = userFaceSearchDTOList.stream().map(UserFaceSearchDTO::getUserCode).distinct().collect(Collectors.toList());
        List<AttendanceUser> userList = userService.listByUserCodes(userCodeList);
        if (CollectionUtils.isEmpty(userList)) {
            return convertDefaultUserFaceSearchVO();
        }

        Optional<UserFaceSearchDTO> userFaceSearchOptional = userFaceSearchDTOList.stream()
                .filter(userFaceSearch -> Objects.equals(EmploymentTypeEnum.OS_FIXED_SALARY.getCode(), userFaceSearch.getEmployeeType()))
                .findFirst();
        if (userFaceSearchOptional.isPresent()) {
            UserFaceSearchDTO userFaceSearchDTO = userFaceSearchOptional.get();
            AttendanceUser userInfoDO = userList.stream().filter(user -> Objects.equals(user.getUserCode(), userFaceSearchDTO.getUserCode())).findFirst().orElse(new AttendanceUser());
            //人脸打标记录过滤
            Set<String> filterUserCodeList = filterUserFaceMarkRecord(userInfoDO);
            if (CollectionUtils.isEmpty(filterUserCodeList)) {
                return convert(userInfoDO, userFaceSearchDTO.getScore(), userFaceSearchDTO.getPass(), userFaceSearchDTO.getFaceKey(), param.getVendorId(), true);
            }
            userFaceSearchDTOList = userFaceSearchDTOList.stream()
                    .filter(userFace -> filterUserCodeList.contains(userFace.getUserCode())
                            && EmploymentTypeEnum.TYPE_OF_EMPLOYEE_WAREHOUSE.contains(userFace.getEmployeeType()))
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(userFaceSearchDTOList)) {
                log.info("自有用户人脸打标记录不包含匹配到的用户");
                return convert(userInfoDO, userFaceSearchDTO.getScore(), userFaceSearchDTO.getPass(), userFaceSearchDTO.getFaceKey(), param.getVendorId(), true);
            }
        }

        UserFaceSearchDTO userFaceSearchDTO = userFaceSearchDTOList.get(0);
        AttendanceUser userInfoDO = userList.stream().filter(user -> Objects.equals(user.getUserCode(), userFaceSearchDTO.getUserCode())).findFirst().orElse(new AttendanceUser());
        //检测用户：1：黑名单、用户状态; 2:用户群体是否符合WPM打卡
        checkUser(userInfoDO);
        saveFaceRecord(userInfoDO, userFaceSearchDTO.getScore(), userFaceSearchDTO.getPass(), faceUrl, userFaceSearchDTO.getFaceKey(), userFaceSearchDTO.getEncFaceKey(), param.getFaceTime(), facialPrivacyControl);
        return convert(userInfoDO, userFaceSearchDTO.getScore(), userFaceSearchDTO.getPass(), userFaceSearchDTO.getFaceKey(), param.getVendorId(), false);
    }

    private boolean checkFacialPrivacyControl(String country) {
        if (StringUtils.isBlank(country)) {
            return false;
        }
        return getFacePrivacySecurityCountry().contains(country);
    }

    private void markUserFaceRecord(FaceSaveParam param, AttendanceUser attendanceUser) {
        UserFaceMarkRecordDO userFaceMarkRecordDO = new UserFaceMarkRecordDO();
        userFaceMarkRecordDO.setId(defaultIdWorker.nextId());
        userFaceMarkRecordDO.setUserCode(attendanceUser.getUserCode());
        userFaceMarkRecordDO.setMarkUserCode(param.getSimilarUserCode());
        BaseDOUtil.fillDOInsertByUsrOrSystem(userFaceMarkRecordDO);
        userFaceMarkRecordDao.save(userFaceMarkRecordDO);
    }

    private Set<String> filterUserFaceMarkRecord(AttendanceUser userInfoDO) {
        if (StringUtils.isBlank(userInfoDO.getUserCode())) {
            return new HashSet<>();
        }
        List<UserFaceMarkRecordDO> userFaceMarkRecordDOList = userFaceMarkRecordDao.getByUserCode(userInfoDO.getUserCode());
        if (CollectionUtils.isEmpty(userFaceMarkRecordDOList)) {
            return new HashSet<>();
        }

        Set<String> filterUserCodeList = new HashSet<>();
        for (UserFaceMarkRecordDO userFaceMarkRecordDO : userFaceMarkRecordDOList) {
            if (userFaceMarkRecordDO.getUserCode().equals(userInfoDO.getUserCode())) {
                filterUserCodeList.add(userFaceMarkRecordDO.getMarkUserCode());
            }
            if (userFaceMarkRecordDO.getMarkUserCode().equals(userInfoDO.getUserCode())) {
                filterUserCodeList.add(userFaceMarkRecordDO.getUserCode());
            }
        }
        return filterUserCodeList;
    }

    /**
     * 获取证件类型
     * 用于证件匹配
     *
     * @param country 国家三字码
     * @return 证件类型
     */
    public String getCertificateType(String country) {
        if (StringUtils.equalsAnyIgnoreCase(CountryCodeEnum.MEX.getCode(), country)) {
            return CertificateTypeEnum.INE.getCode();
        }
        if (Lists.newArrayList(CountryCodeEnum.COL.getCode(), CountryCodeEnum.PER.getCode()).contains(country)) {
            return CertificateTypeEnum.ID_CARD.getCode();
        }
        return CertificateTypeEnum.INE.getCode();
    }

    private static UserFaceSearchRepeatVO convertUserFaceSearchRepeatVO(AttendanceUser attendanceUser,
                                                                        UserFaceSearchDTO userFaceSearchDTO,
                                                                        AttendanceUser similarUser,
                                                                        Map<String, UserCertificateDTO> certificateDTOMap) {
        UserFaceSearchRepeatVO result = new UserFaceSearchRepeatVO();
        result.setUserId(attendanceUser.getId());
        result.setUserCode(attendanceUser.getUserCode());
        result.setUserName(attendanceUser.getUserName());
        if (Objects.nonNull(certificateDTOMap.get(attendanceUser.getUserCode()))) {
            result.setCertificatesCode(certificateDTOMap.get(attendanceUser.getUserCode()).getCertificateCode());
        }
        result.setSimilarUserId(similarUser.getId());
        result.setSimilarUserCode(similarUser.getUserCode());
        result.setSimilarUserName(similarUser.getUserName());
        if (Objects.nonNull(certificateDTOMap.get(similarUser.getUserCode()))) {
            result.setSimilarCertificatesCode(certificateDTOMap.get(similarUser.getUserCode()).getCertificateCode());
        }
        result.setPass(userFaceSearchDTO.getPass());
        result.setScore(userFaceSearchDTO.getScore());
        result.setFaceUrl(userFaceSearchDTO.getFaceKey());
        return result;
    }
}
