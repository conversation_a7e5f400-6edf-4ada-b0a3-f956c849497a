package com.imile.attendance.warehouse.vo;


import com.imile.attendance.annon.HyperLink;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2024/11/18
 */
@Data
public class UserFaceSearchRepeatVO {

    /**
     * 新员工id
     */
    private Long userId;

    /**
     * 新员工Code
     */
    private String userCode;

    /**
     * 新员工名称
     */
    private String userName;

    /**
     * 新员工证件号
     */
    private String certificatesCode;

    /**
     * 相似员工id
     */
    private Long similarUserId;

    /**
     * 相似员工Code
     */
    private String similarUserCode;

    /**
     * 相似员工名称
     */
    private String similarUserName;

    /**
     * 相似员工证件号
     */
    private String similarCertificatesCode;

    /**
     * 人脸照key
     */
    @HyperLink(ref = "facePhoto")
    private String faceUrl;

    /**
     * 人脸照地址
     */
    private String facePhoto;

    /**
     * 相似度得分
     */
    private Float score;

    /**
     * 人脸比对通过标识
     */
    private Boolean pass;
}
