package com.imile.attendance.warehouse.impl;

import cn.hutool.core.date.DateUtil;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.Lists;
import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.enums.ErrorCodeEnum;
import com.imile.attendance.enums.abnormal.AttendanceAbnormalTypeEnum;
import com.imile.attendance.enums.warehouse.WarehouseAttendanceStatusEnum;
import com.imile.attendance.enums.warehouse.WarehousePcsStatusEnum;
import com.imile.attendance.exception.BusinessLogicException;
import com.imile.attendance.infrastructure.repository.common.AttendanceDeptService;
import com.imile.attendance.infrastructure.repository.common.dto.AttendanceDept;
import com.imile.attendance.infrastructure.repository.employee.dao.UserInfoDao;
import com.imile.attendance.infrastructure.repository.employee.modle.UserInfoDO;
import com.imile.attendance.infrastructure.repository.hrms.modle.HrmsUserInfoDO;
import com.imile.attendance.infrastructure.repository.warehouse.dao.WarehouseDetailAbnormalDao;
import com.imile.attendance.infrastructure.repository.warehouse.dao.WarehouseDetailDao;
import com.imile.attendance.infrastructure.repository.warehouse.model.WarehouseDetailAbnormalDO;
import com.imile.attendance.infrastructure.repository.warehouse.model.WarehouseDetailDO;
import com.imile.attendance.infrastructure.repository.warehouse.query.WarehouseDetailQuery;
import com.imile.attendance.util.PageUtil;
import com.imile.attendance.warehouse.WarehouseExternalService;
import com.imile.attendance.warehouse.param.GetOcListByConditionParam;
import com.imile.attendance.warehouse.vo.OcVO;
import com.imile.attendance.warehouse.vo.WarehousePcsMonthReportCountVO;
import com.imile.attendance.warehouse.vo.WarehousePcsMonthReportPassRateVO;
import com.imile.attendance.warehouse.vo.WarehousePcsReportVO;
import com.imile.common.page.PaginationResult;
import com.imile.hrms.api.warehouse.param.GetPcsReportByConditionParam;
import com.imile.hrms.api.warehouse.param.GetPcsReportCountByConditionParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.BiConsumer;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024/9/27
 */
@Slf4j
@Service
public class WarehouseExternalServiceImpl implements WarehouseExternalService {

    @Resource
    private WarehouseDetailDao warehouseDetailDao;

    @Resource
    private WarehouseDetailAbnormalDao warehouseDetailAbnormalDao;

    @Resource
    private AttendanceDeptService deptService;

    @Resource
    private UserInfoDao userInfoDao;

    @Override
    public List<OcVO> getOcListByCondition(GetOcListByConditionParam param) {
        WarehouseDetailQuery warehouseDetailParam = new WarehouseDetailQuery();
        warehouseDetailParam.setStartTime(param.getStartDate());
        warehouseDetailParam.setEndTime(param.getEndDate());
        warehouseDetailParam.setVendorCode(param.getVendorCode());
        warehouseDetailParam.setCity(param.getCity());
        List<WarehouseDetailDO> warehouseDetailDOList = warehouseDetailDao.selectByCondition(warehouseDetailParam);
        if (CollectionUtils.isEmpty(warehouseDetailDOList)) {
            return Collections.emptyList();
        }
        List<Long> ocIds = warehouseDetailDOList.stream().map(WarehouseDetailDO::getOcId).distinct().collect(Collectors.toList());
        return deptService.listByDeptIds(ocIds).stream().map(dept -> {
            OcVO ocVO = new OcVO();
            ocVO.setOcId(dept.getId());
            ocVO.setOcCode(dept.getDeptCode());
            ocVO.setOcName(dept.getDeptNameEn());
            return ocVO;
        }).collect(Collectors.toList());
    }

    @Override
    public PaginationResult<WarehousePcsReportVO> pcsDateReport(GetPcsReportByConditionParam param) {
        List<Long> ocList = param.getOcIdList();
        if (CollectionUtils.isEmpty(ocList)) {
            GetOcListByConditionParam getOcListByConditionParam = new GetOcListByConditionParam();
            getOcListByConditionParam.setVendorCode(param.getVendorCode());
            getOcListByConditionParam.setStartDate(param.getStartDate());
            getOcListByConditionParam.setEndDate(param.getEndDate());
            List<OcVO> ocVOList = getOcListByCondition(getOcListByConditionParam);
            if (CollectionUtils.isEmpty(ocVOList)) {
                return PaginationResult.get(Collections.emptyList(), param);
            }
            ocList = ocVOList.stream().map(OcVO::getOcId).collect(Collectors.toList());
        }

        List<Long> searchKeyUserIdList = new ArrayList<>();
        boolean searchFlag = false;
        if (StringUtils.isNotBlank(param.getSearchUserKey())) {
            List<UserInfoDO> userInfoDOList = userInfoDao.selectBySearchCode(param.getSearchUserKey());
            searchKeyUserIdList = userInfoDOList.stream().map(UserInfoDO::getId).collect(Collectors.toList());
            searchFlag = true;
        }
        if (searchFlag && CollectionUtils.isEmpty(searchKeyUserIdList)) {
            return PaginationResult.get(Collections.emptyList(), param);
        }

        WarehouseDetailQuery warehouseDetailParam = new WarehouseDetailQuery();
        warehouseDetailParam.setCountry(param.getCountry());
        warehouseDetailParam.setCity(param.getCity());
        warehouseDetailParam.setOcIdList(ocList);
        warehouseDetailParam.setVendorCodeList(Collections.singletonList(param.getVendorCode()));
        warehouseDetailParam.setUserIdList(searchKeyUserIdList);
        warehouseDetailParam.setStartDate(param.getStartDate());
        warehouseDetailParam.setEndDate(param.getEndDate());
        warehouseDetailParam.setPcsStatus(param.getPcsStatus());
        Page<WarehouseDetailDO> page = PageHelper.startPage(param.getCurrentPage(), param.getShowCount());
        List<WarehouseDetailDO> list = warehouseDetailDao.selectPcsPage(warehouseDetailParam);
        if (CollectionUtils.isEmpty(list)) {
            return PaginationResult.get(Collections.emptyList(), param);
        }

        List<Long> userIdList = list.stream().map(WarehouseDetailDO::getUserId).collect(Collectors.toList());
        Map<Long, UserInfoDO> userNameMap = userInfoDao.getByUserIds(userIdList)
                .stream()
                .collect(Collectors.toMap(UserInfoDO::getId, Function.identity(), (v1, v2) -> v1));

        List<Long> ocIdList = list.stream()
                .map(o -> Arrays.asList(o.getOcId(), o.getUserOcId()))
                .flatMap(Collection::stream)
                .distinct()
                .collect(Collectors.toList());
        Map<Long, String> ocMap = deptService.listByDeptIds(ocIdList).stream().
                collect(Collectors.toMap(AttendanceDept::getId, AttendanceDept::getDeptNameEn, (v1, v2) -> v1));

        List<Long> warehouseDetailIds = list
                .stream()
                .filter(warehouse -> Objects.equals(WarehouseAttendanceStatusEnum.ABNORMAL.getCode(), warehouse.getAttendanceStatus()))
                .map(WarehouseDetailDO::getId)
                .collect(Collectors.toList());


        List<Long> workMissCardWarehouseDetailList = warehouseDetailAbnormalDao.selectByWarehouseDetailIds(warehouseDetailIds)
                .stream()
                .filter(warehouseAbnormal -> Objects.equals(AttendanceAbnormalTypeEnum.BEFORE_OFFICE_LACK.getCode(), warehouseAbnormal.getAbnormalType()))
                .map(WarehouseDetailAbnormalDO::getWarehouseDetailId)
                .distinct().collect(Collectors.toList());


        List<WarehousePcsReportVO> collect = convertWarehousePcsDateReportVOS(list, userNameMap, ocMap, workMissCardWarehouseDetailList);
        return PageUtil.getPageResult(collect, param, (int) page.getTotal(), page.getPages());
    }

    @Override
    public PaginationResult<WarehousePcsReportVO> pcsWeekOrMonthReport(GetPcsReportByConditionParam param) {
        BusinessLogicException.checkTrue(StringUtils.isEmpty(param.getReportType()), ErrorCodeEnum.PARAM_VALID_ERROR.getCode(), ErrorCodeEnum.PARAM_VALID_ERROR.getDesc(), "reportType");
        BusinessLogicException.checkTrue(!Lists.newArrayList(BusinessConstant.WEEK, BusinessConstant.MONTH).contains(param.getReportType()), ErrorCodeEnum.PARAM_VALID_ERROR.getCode(), ErrorCodeEnum.PARAM_VALID_ERROR.getDesc(), "reportType");

        List<Long> ocList = param.getOcIdList();
        if (CollectionUtils.isEmpty(ocList)) {
            GetOcListByConditionParam getOcListByConditionParam = new GetOcListByConditionParam();
            getOcListByConditionParam.setVendorCode(param.getVendorCode());
            getOcListByConditionParam.setStartDate(param.getStartDate());
            getOcListByConditionParam.setEndDate(param.getEndDate());
            List<OcVO> ocVOList = getOcListByCondition(getOcListByConditionParam);
            if (CollectionUtils.isEmpty(ocVOList)) {
                return PaginationResult.get(Collections.emptyList(), param);
            }
            ocList = ocVOList.stream().map(OcVO::getOcId).collect(Collectors.toList());
        }

        List<Long> searchKeyUserIdList = new ArrayList<>();
        boolean searchFlag = false;
        if (StringUtils.isNotBlank(param.getSearchUserKey())) {
            List<UserInfoDO> userInfoDOList = userInfoDao.selectBySearchCode(param.getSearchUserKey());
            searchKeyUserIdList = userInfoDOList.stream().map(UserInfoDO::getId).collect(Collectors.toList());
            searchFlag = true;
        }
        if (searchFlag && CollectionUtils.isEmpty(searchKeyUserIdList)) {
            return PaginationResult.get(Collections.emptyList(), param);
        }

        WarehouseDetailQuery warehouseDetailParam = new WarehouseDetailQuery();
        warehouseDetailParam.setCountry(param.getCountry());
        warehouseDetailParam.setCity(param.getCity());
        warehouseDetailParam.setOcIdList(ocList);
        warehouseDetailParam.setVendorCodeList(Collections.singletonList(param.getVendorCode()));
        warehouseDetailParam.setUserIdList(searchKeyUserIdList);
        warehouseDetailParam.setStartDate(param.getStartDate());
        warehouseDetailParam.setEndDate(param.getEndDate());
        warehouseDetailParam.setPcsStatus(param.getPcsStatus());
        warehouseDetailParam.setReportType(param.getReportType());
        Page<WarehouseDetailDO> page = PageHelper.startPage(param.getCurrentPage(), param.getShowCount());
        List<WarehouseDetailDO> warehouseDetailDOS = warehouseDetailDao.selectPcsPage(warehouseDetailParam);
        if (CollectionUtils.isEmpty(warehouseDetailDOS)) {
            return PaginationResult.get(Collections.emptyList(), param);
        }

        List<Long> userIdList = warehouseDetailDOS.stream()
                .map(WarehouseDetailDO::getUserId)
                .collect(Collectors.toList());

        WarehouseDetailQuery detailParam = new WarehouseDetailQuery();
        detailParam.setStartDate(param.getStartDate());
        detailParam.setEndDate(param.getEndDate());
        detailParam.setUserIdList(userIdList);
        List<WarehouseDetailDO> warehouseDetailDOList = warehouseDetailDao.selectPcsPage(detailParam);

        Map<String, List<WarehouseDetailDO>> groupMap = warehouseDetailDOList
                .stream().collect(Collectors.groupingBy(o -> o.getUserId() + BusinessConstant.WELL_NO + o.getOcId() + BusinessConstant.WELL_NO + o.getVendorId()));

        Map<Long, UserInfoDO> userNameMap = userInfoDao.getByUserIds(userIdList)
                .stream()
                .collect(Collectors.toMap(UserInfoDO::getId, Function.identity(), (v1, v2) -> v1));

        List<Long> ocIdList = warehouseDetailDOS.stream()
                .map(o -> Arrays.asList(o.getOcId(), o.getUserOcId()))
                .flatMap(Collection::stream)
                .distinct()
                .collect(Collectors.toList());
        Map<Long, String> ocMap = deptService.listByDeptIds(ocIdList).stream().
                collect(Collectors.toMap(AttendanceDept::getId, AttendanceDept::getDeptNameEn, (v1, v2) -> v1));

        List<WarehousePcsReportVO> collect = convertWarehousePcsReportVOS(warehouseDetailDOS, userNameMap, ocMap, groupMap);
        return PageUtil.getPageResult(collect, param, (int) page.getTotal(), page.getPages());
    }

    @Override
    public WarehousePcsMonthReportCountVO pcsDateReportCount(GetPcsReportCountByConditionParam param) {
        WarehouseDetailQuery warehouseDetailParam = new WarehouseDetailQuery();
        warehouseDetailParam.setOcIdList(param.getOcIdList());
        warehouseDetailParam.setVendorCodeList(Collections.singletonList(param.getVendorCode()));
        warehouseDetailParam.setStartDate(param.getStartDate());
        warehouseDetailParam.setEndDate(param.getEndDate());
        List<WarehouseDetailDO> list = warehouseDetailDao.selectPcsPage(warehouseDetailParam);
        WarehousePcsMonthReportCountVO reportCountVO = new WarehousePcsMonthReportCountVO();
        if (CollectionUtils.isEmpty(list)) {
            reportCountVO.setTotalCount(BusinessConstant.ZERO);
            reportCountVO.setNormalCount(BusinessConstant.ZERO);
            reportCountVO.setAbnormalCount(BusinessConstant.ZERO);
            return reportCountVO;
        }
        reportCountVO.setTotalCount(list.size());
        reportCountVO.setNormalCount((int) list.stream().filter(warehouse -> Objects.equals(WarehousePcsStatusEnum.NORMAL.getCode(), warehouse.getPcsStatus())).count());
        reportCountVO.setAbnormalCount((int) list.stream().filter(warehouse -> Objects.equals(WarehousePcsStatusEnum.ABNORMAL.getCode(), warehouse.getPcsStatus())).count());
        return reportCountVO;
    }

    @Override
    public WarehousePcsMonthReportPassRateVO pcsDateMonthReportPassRate(GetPcsReportCountByConditionParam param) {
        WarehouseDetailQuery warehouseDetailParam = new WarehouseDetailQuery();
        warehouseDetailParam.setOcIdList(param.getOcIdList());
        warehouseDetailParam.setVendorCodeList(Collections.singletonList(param.getVendorCode()));
        warehouseDetailParam.setStartDate(param.getStartDate());
        warehouseDetailParam.setEndDate(param.getEndDate());
        List<WarehouseDetailDO> list = warehouseDetailDao.selectPcsPage(warehouseDetailParam);
        WarehousePcsMonthReportPassRateVO reportPassRateVO = new WarehousePcsMonthReportPassRateVO();
        if (CollectionUtils.isEmpty(list)) {
            return reportPassRateVO;
        }

        Map<Date, List<WarehouseDetailDO>> groupByDateMap = list.stream().collect(Collectors.groupingBy(WarehouseDetailDO::getWarehouseDate));

        for (int i = 0; i < 31; i++) {
            BiConsumer<WarehousePcsMonthReportPassRateVO, BigDecimal> setter = reportPassRateVO.getSetter(i + 1);
            Date warehouseDate = DateUtil.offsetDay(param.getStartDate(), i);
            List<WarehouseDetailDO> warehouseDetailDOS = groupByDateMap.get(warehouseDate);
            BigDecimal bigDecimal = BigDecimal.ZERO;
            if (CollectionUtils.isNotEmpty(warehouseDetailDOS)) {
                int normalCount = (int) warehouseDetailDOS.stream().filter(warehouse -> Objects.equals(WarehousePcsStatusEnum.NORMAL.getCode(), warehouse.getPcsStatus())).count();
                bigDecimal = new BigDecimal(normalCount).divide(new BigDecimal(warehouseDetailDOS.size()), 2, RoundingMode.HALF_UP);
            }
            setter.accept(reportPassRateVO, bigDecimal);
        }
        return reportPassRateVO;
    }

    @NotNull
    private static List<WarehousePcsReportVO> convertWarehousePcsDateReportVOS(List<WarehouseDetailDO> list,
                                                                               Map<Long, UserInfoDO> userNameMap,
                                                                               Map<Long, String> ocMap,
                                                                               List<Long> workMissCardWarehouseDetailList) {
        return list.stream()
                .map(o -> {
                    WarehousePcsReportVO pcsReportVO = new WarehousePcsReportVO();
                    pcsReportVO.setId(o.getId());
                    pcsReportVO.setWarehouseDate(o.getWarehouseDate());
                    pcsReportVO.setCountry(o.getCountry());
                    pcsReportVO.setCity(o.getCity());
                    pcsReportVO.setInOcId(o.getOcId());
                    pcsReportVO.setInOcName(ocMap.get(o.getOcId()));
                    pcsReportVO.setInVendorCode(o.getVendorCode());
                    UserInfoDO user = userNameMap.getOrDefault(o.getUserId(), new UserInfoDO());
                    pcsReportVO.setOcId(o.getUserOcId());
                    pcsReportVO.setOcName(ocMap.get(o.getUserOcId()));
                    pcsReportVO.setUserCode(user.getUserCode());
                    pcsReportVO.setUserName(user.getUserName());
                    pcsReportVO.setUserNameEn(user.getUserNameEn());
                    pcsReportVO.setPcsStatus(o.getPcsStatus());
                    pcsReportVO.setClockIn(!workMissCardWarehouseDetailList.contains(o.getId()));
                    pcsReportVO.setClockOut(Objects.equals(WarehousePcsStatusEnum.NORMAL.getCode(), o.getPcsStatus()));
                    return pcsReportVO;
                }).collect(Collectors.toList());
    }

    @NotNull
    private static List<WarehousePcsReportVO> convertWarehousePcsReportVOS(List<WarehouseDetailDO> list,
                                                                           Map<Long, UserInfoDO> userNameMap,
                                                                           Map<Long, String> ocMap,
                                                                           Map<String, List<WarehouseDetailDO>> groupMap) {
        return list.stream()
                .map(o -> {
                    WarehousePcsReportVO pcsReportVO = new WarehousePcsReportVO();
                    pcsReportVO.setId(o.getId());
                    pcsReportVO.setWarehouseDate(o.getWarehouseDate());
                    pcsReportVO.setCountry(o.getCountry());
                    pcsReportVO.setCity(o.getCity());
                    pcsReportVO.setInOcId(o.getOcId());
                    pcsReportVO.setInOcName(ocMap.get(o.getOcId()));
                    pcsReportVO.setInVendorCode(o.getVendorCode());
                    UserInfoDO user = userNameMap.getOrDefault(o.getUserId(), new UserInfoDO());
                    pcsReportVO.setOcId(o.getUserOcId());
                    pcsReportVO.setOcName(ocMap.get(o.getUserOcId()));
                    pcsReportVO.setUserCode(user.getUserCode());
                    pcsReportVO.setUserName(user.getUserName());
                    pcsReportVO.setUserNameEn(user.getUserNameEn());
                    List<WarehouseDetailDO> warehouseDetailDOList = groupMap.get(o.getUserId() + BusinessConstant.WELL_NO + o.getOcId() + BusinessConstant.WELL_NO + o.getVendorId());
                    if (CollectionUtils.isNotEmpty(warehouseDetailDOList)) {
                        pcsReportVO.setDays(warehouseDetailDOList.size());
                        pcsReportVO.setNormalDays((int) warehouseDetailDOList.stream().filter(warehouse -> Objects.equals(WarehousePcsStatusEnum.NORMAL.getCode(), warehouse.getPcsStatus())).count());
                        pcsReportVO.setAbnormalDays((int) warehouseDetailDOList.stream().filter(warehouse -> Objects.equals(WarehousePcsStatusEnum.ABNORMAL.getCode(), warehouse.getPcsStatus())).count());
                    }
                    return pcsReportVO;
                }).collect(Collectors.toList());
    }
}
