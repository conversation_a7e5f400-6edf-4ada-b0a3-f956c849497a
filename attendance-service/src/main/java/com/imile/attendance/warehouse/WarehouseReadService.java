package com.imile.attendance.warehouse;

import com.imile.attendance.ipep.dto.OssApiVo;
import com.imile.attendance.warehouse.param.GetVendorConfirmContentParam;
import com.imile.attendance.warehouse.param.NoBingShiftReportParam;
import com.imile.attendance.warehouse.param.ReportParam;
import com.imile.attendance.warehouse.param.SimpleReportParam;
import com.imile.attendance.warehouse.param.StatisticVendorParam;
import com.imile.attendance.warehouse.param.WpmDataStatisticsParam;
import com.imile.attendance.warehouse.vo.BusZoneListVO;
import com.imile.attendance.warehouse.vo.DataStatisticsBlackListVO;
import com.imile.attendance.warehouse.vo.DataStatisticsDetailsVO;
import com.imile.attendance.warehouse.vo.DataStatisticsVO;
import com.imile.attendance.warehouse.vo.DateDetailReportVO;
import com.imile.attendance.warehouse.vo.DateReportSimpleVO;
import com.imile.attendance.warehouse.vo.DateReportVO;
import com.imile.attendance.warehouse.vo.GetVendorConfirmContentResultVO;
import com.imile.attendance.warehouse.vo.MonthReportVO;
import com.imile.attendance.warehouse.vo.StatisticsVendorResultVO;
import com.imile.attendance.warehouse.vo.StatisticsVendorUserResultVO;
import com.imile.attendance.warehouse.vo.VendorClassesConfirmVO;
import com.imile.common.page.PaginationResult;

import java.util.List;

/**
 * 仓内考勤读服务
 *
 * <AUTHOR>
 * @since 2025/1/21
 */
public interface WarehouseReadService {

    /**
     * 查询当前登录人国家列表
     */
    List<BusZoneListVO> getCountryList();

    /**
     * 查询账号网点权限下待配置班次考勤记录数量
     */
    Integer noBindShiftCount();

    /**
     * 简易日报列表
     * 批量更新网点&供应商&班次场景使用
     */
    List<DateReportSimpleVO> simpleDateReport(SimpleReportParam param);

    /**
     * 仓内日报
     */
    PaginationResult<DateReportVO> dateReport(ReportParam param);

    /**
     * 仓内日报详情
     */
    DateDetailReportVO dateReportDetail(Long id);

    /**
     * 仓内月报
     */
    PaginationResult<MonthReportVO> monthReport(ReportParam param);

    /**
     * 仓内无绑定班次日报列表
     */
    List<DateReportVO> dateReportNoBingShift(NoBingShiftReportParam param);

    /**
     * 查询WPM数据统计
     */
    DataStatisticsVO dataStatistics(WpmDataStatisticsParam param);

    /**
     * 查询WPM数据统计人员明细
     */
    PaginationResult<DataStatisticsDetailsVO> dataStatisticsDetails(WpmDataStatisticsParam param);

    /**
     * 查询WPM数据统计人员明细
     */
    PaginationResult<DataStatisticsBlackListVO> dataStatisticsBlackList(WpmDataStatisticsParam param);

    /**
     * 统计供应商视角下人员数量出勤数据
     */
    StatisticsVendorResultVO statisticVendor(StatisticVendorParam param);

    /**
     * 统计供应商视角下人员明细出勤数据
     */
    PaginationResult<StatisticsVendorUserResultVO> statisticVendorUser(StatisticVendorParam param);

    /**
     * H5供应商确认人员班次出勤明细
     */
    VendorClassesConfirmVO vendorClassedConfirmDetailH5(Long id);

    /**
     * Web供应商确认人员班次出勤明细
     */
    VendorClassesConfirmVO vendorClassedConfirmDetailWeb(Long id);

    /**
     * 获取供应商待确定内容
     */
    GetVendorConfirmContentResultVO getVendorConfirmContent(GetVendorConfirmContentParam param);

    /**
     * 查询OSS文件信息
     */
    OssApiVo getOssFileUrl(String fileKey);
}
