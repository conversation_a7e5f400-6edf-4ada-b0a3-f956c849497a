package com.imile.attendance.warehouse.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;


/**
 * <AUTHOR>
 * @since 2024/8/23
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ClassesDetailVO {
    /**
     * 时段1、2、3
     */
    private Integer sortNo;

    /**
     * 上班时间 仅有时分秒信息，形如：09:00:00
     */
    private String punchInTime;

    /**
     * 下班时间 仅有时分秒信息，形如：18:00:00
     */
    private String punchOutTime;

    /**
     * 最早上班打卡时间 仅有时分秒信息，形如：09:00:00
     */
    private String earliestPunchInTime;
    /**
     * 最晚下班打卡时间 仅有时分秒信息，形如：19:00:00
     */
    private String latestPunchOutTime;

    /**
     * 休息开始时间 仅有时分秒信息，形如：09:00:00
     */
    private String restStartTime;

    /**
     * 休息结束时间 仅有时分秒信息，形如：09:00:00
     */
    private String restEndTime;

    /**
     * 弹性时间
     */
    private BigDecimal elasticTime;
}
