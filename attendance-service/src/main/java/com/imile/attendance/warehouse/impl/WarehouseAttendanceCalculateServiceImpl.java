package com.imile.attendance.warehouse.impl;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.imile.attendance.abnormal.AttendanceCalculateContext;
import com.imile.attendance.abnormal.AttendanceEmployeeDetailManage;
import com.imile.attendance.abnormal.AttendanceGenerateService;
import com.imile.attendance.abnormal.EmployeeAbnormalAttendanceManage;
import com.imile.attendance.abnormal.dto.AttendanceCalculateHandlerDTO;
import com.imile.attendance.abnormal.dto.UserAttendancePunchConfigDTO;
import com.imile.attendance.abnormal.service.AttendanceCalculateService;
import com.imile.attendance.calendar.CalendarManage;
import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.employee.UserDimissionRecordManage;
import com.imile.attendance.employee.UserEntryRecordManage;
import com.imile.attendance.enums.EmploymentTypeEnum;
import com.imile.attendance.enums.WorkStatusEnum;
import com.imile.attendance.enums.form.FormStatusEnum;
import com.imile.attendance.enums.form.FormTypeEnum;
import com.imile.attendance.enums.rule.PunchConfigTypeEnum;
import com.imile.attendance.form.AttendanceFormManage;
import com.imile.attendance.form.bo.AttendanceFormDetailBO;
import com.imile.attendance.infrastructure.idwork.DefaultIdWorker;
import com.imile.attendance.infrastructure.repository.abnormal.model.AttendanceEmployeeDetailDO;
import com.imile.attendance.infrastructure.repository.abnormal.model.EmployeeAbnormalAttendanceDO;
import com.imile.attendance.infrastructure.repository.calendar.model.CalendarConfigDO;
import com.imile.attendance.infrastructure.repository.calendar.model.CalendarConfigDetailDO;
import com.imile.attendance.infrastructure.repository.common.AttendanceUserService;
import com.imile.attendance.infrastructure.repository.common.dto.AttendanceUser;
import com.imile.attendance.infrastructure.repository.employee.dao.UserInfoDao;
import com.imile.attendance.infrastructure.repository.employee.dao.UserLeaveStageDetailDao;
import com.imile.attendance.infrastructure.repository.employee.modle.UserDimissionRecordDO;
import com.imile.attendance.infrastructure.repository.employee.modle.UserEntryRecordDO;
import com.imile.attendance.infrastructure.repository.employee.modle.UserInfoDO;
import com.imile.attendance.infrastructure.repository.employee.modle.UserLeaveDetailDO;
import com.imile.attendance.infrastructure.repository.employee.modle.UserLeaveStageDetailDO;
import com.imile.attendance.infrastructure.repository.form.model.AttendanceFormDO;
import com.imile.attendance.infrastructure.repository.rule.dto.PunchClassConfigDTO;
import com.imile.attendance.infrastructure.repository.shift.model.UserShiftConfigDO;
import com.imile.attendance.infrastructure.repository.vacation.model.CompanyLeaveConfigDO;
import com.imile.attendance.infrastructure.repository.warehouse.dao.WarehouseAttendanceConfigDao;
import com.imile.attendance.infrastructure.repository.warehouse.model.WarehouseAttendanceConfigDO;
import com.imile.attendance.loader.StrategyLoader;
import com.imile.attendance.migration.MigrationService;
import com.imile.attendance.punch.EmployeePunchRecordManage;
import com.imile.attendance.punch.bo.UserPunchRecordBO;
import com.imile.attendance.rule.PunchClassConfigManage;
import com.imile.attendance.shift.UserShiftConfigManage;
import com.imile.attendance.vacation.CompanyLeaveConfigManage;
import com.imile.attendance.vacation.UserLeaveDetailManage;
import com.imile.attendance.warehouse.WarehouseAttendanceCalculateService;
import com.imile.attendance.warehouse.WarehouseUserService;
import com.imile.common.enums.StatusEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 仓内考勤结果计算服务
 *
 * <AUTHOR>
 * @since 2025/1/6
 */
@Slf4j
@Service
public class WarehouseAttendanceCalculateServiceImpl implements WarehouseAttendanceCalculateService {

    @Resource
    protected DefaultIdWorker defaultIdWorker;
    @Resource
    private WarehouseAttendanceConfigDao warehouseAttendanceConfigDao;
    @Resource
    private WarehouseUserService warehouseUserService;
    @Resource
    private UserInfoDao userInfoDao;
    @Resource
    private AttendanceUserService userService;
    @Resource
    private EmployeePunchRecordManage employeePunchRecordManage;
    @Resource
    private UserEntryRecordManage userEntryRecordManage;
    @Resource
    private UserDimissionRecordManage userDimissionRecordManage;
    @Resource
    private AttendanceEmployeeDetailManage employeeDetailManage;
    @Resource
    private EmployeeAbnormalAttendanceManage abnormalAttendanceManage;
    @Resource
    private CalendarManage calendarManage;
    @Resource
    private PunchClassConfigManage punchClassConfigManage;
    @Resource
    private UserLeaveDetailManage userLeaveDetailManage;
    @Resource
    private CompanyLeaveConfigManage companyLeaveConfigManage;
    @Resource
    private UserShiftConfigManage userShiftConfigManage;
    @Resource
    private UserLeaveStageDetailDao userLeaveStageDetailDao;
    @Resource
    private AttendanceFormManage attendanceFormManage;
    @Resource
    private AttendanceGenerateService attendanceGenerateService;
    @Resource
    private MigrationService migrationService;

    @Override
    public void warehouseAttendanceCalculate(AttendanceCalculateHandlerDTO calculateHandlerDTO) {
        if (Objects.isNull(calculateHandlerDTO.getAttendanceDayId()) || StringUtils.isEmpty(calculateHandlerDTO.getUserCodes())) {
            return;
        }
        List<AttendanceUser> userInfoList = userService.listByUserCodes(Arrays.asList(calculateHandlerDTO.getUserCodes().split(BusinessConstant.DEFAULT_DELIMITER)));
        List<String> laborUserCodeList = new ArrayList<>();
        List<String> employeeUserCodeList = new ArrayList<>();
        for (AttendanceUser userInfo : userInfoList) {
            if (warehouseUserService.isWarehouseLaborSupport(userInfo)) {
                laborUserCodeList.add(userInfo.getUserCode());
            }

            if (warehouseUserService.isWarehouseEmployeeSupport(userInfo)) {
                employeeUserCodeList.add(userInfo.getUserCode());
            }
        }
        if (CollectionUtils.isNotEmpty(laborUserCodeList)) {
            calculateHandlerDTO.setUserCodes(StringUtils.join(laborUserCodeList, BusinessConstant.DEFAULT_DELIMITER));
            warehouseLaborWorkerAttendanceHandler(calculateHandlerDTO);
        }

        if (CollectionUtils.isNotEmpty(employeeUserCodeList)) {
            calculateHandlerDTO.setUserCodes(StringUtils.join(employeeUserCodeList, BusinessConstant.DEFAULT_DELIMITER));
            warehouseEmployeeAttendanceHandler(calculateHandlerDTO);
        }
    }

    /**
     * 仓内劳务员工考勤异常计算
     */
    @Override
    public void warehouseLaborWorkerAttendanceHandler(AttendanceCalculateHandlerDTO calculateHandlerDTO) {
        log.info("warehouse labor attendance handler: {}", JSON.toJSONString(calculateHandlerDTO));
        if (Objects.isNull(calculateHandlerDTO)
                || Objects.isNull(calculateHandlerDTO.getAttendanceDayId())
                || StringUtils.isEmpty(calculateHandlerDTO.getUserCodes())
                || Objects.isNull(calculateHandlerDTO.getOcId())
                || Objects.isNull(calculateHandlerDTO.getClassId())
        ) {
            log.info("attendanceHandlerDTO | 入参对象为空OR必填项信息不全");
            return;
        }
        List<String> userCodes = Arrays.asList(calculateHandlerDTO.getUserCodes().split(BusinessConstant.DEFAULT_DELIMITER));
        //过滤得到仓内启用在职且非司机员工
        List<UserInfoDO> userInfoDOList = userInfoDao.listByUserCodes(userCodes)
                .stream()
                .filter(item -> Objects.equals(EmploymentTypeEnum.OS_FIXED_SALARY.getCode(), item.getEmployeeType())
                        && Objects.equals(StatusEnum.ACTIVE.getCode(), item.getStatus())
                        && Objects.equals(WorkStatusEnum.ON_JOB.getCode(), item.getWorkStatus())
                        && Objects.equals(BusinessConstant.Y, item.getIsWarehouseStaff())
                        && Objects.equals(BusinessConstant.N, item.getIsDriver())
                        && migrationService.verifyUserIsEnableNewAttendance(item.getId()))
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(userInfoDOList)) {
            log.info("warehouseLaborWorkerAttendanceHandler | 员工不符合条件,{}", userCodes);
            return;
        }

        List<String> userCodeList = userInfoDOList.stream().map(UserInfoDO::getUserCode).distinct().collect(Collectors.toList());
        List<Long> userIds = userInfoDOList.stream().map(UserInfoDO::getId).distinct().collect(Collectors.toList());

        //设置考勤计算范围时间段
        attendanceGenerateService.buildAttendanceCalculateRangeTime(calculateHandlerDTO);

        Date finalDateNow = DateUtil.endOfDay(calculateHandlerDTO.getAttendanceTime());

        //是否允许上多班次
        boolean isMultipleShifts = false;
        //查询仓内考勤管理规则
        WarehouseAttendanceConfigDO warehouseAttendanceConfigDO = warehouseAttendanceConfigDao.selectActiveById(calculateHandlerDTO.getWarehouseAttendanceConfigId());
        if (Objects.nonNull(warehouseAttendanceConfigDO) && Objects.equals(BusinessConstant.Y, warehouseAttendanceConfigDO.getIsMultipleShifts())) {
            isMultipleShifts = true;
        }

        //查询用户打卡记录
        Map<String, List<UserPunchRecordBO>> userPunchCardGroup = employeePunchRecordManage.mapByUserCodesAndTimeRange(calculateHandlerDTO.getStartTime(), calculateHandlerDTO.getEndTime(), userCodeList,
                calculateHandlerDTO.getOcId(), calculateHandlerDTO.getClassId(), calculateHandlerDTO.getAttendanceDayId(), isMultipleShifts);
        //查询考勤正常出勤明细
        Map<Long, List<AttendanceEmployeeDetailDO>> userAttendanceEmployeeMap = employeeDetailManage.mapByUserIdsAndDayId(userIds, calculateHandlerDTO.getAttendanceDayId());
        //查询考勤当天未处理的异常数据表(有些可能已经被发起审批了，在审批中)
        Map<Long, List<EmployeeAbnormalAttendanceDO>> userAbnormalMap = abnormalAttendanceManage.mapByUserIdsAndDayIds(userIds, Collections.singletonList(calculateHandlerDTO.getAttendanceDayId()));
        //查询用户所在日历
        Map<Long, List<CalendarConfigDO>> userCalendarConfigMap = calendarManage.mapByUserIds(userIds, finalDateNow);
        //查询用户考勤日历配置明细
        List<Long> calendarConfigIds = userCalendarConfigMap.values().stream()
                .flatMap(List::stream)
                .map(CalendarConfigDO::getId)
                .distinct()
                .collect(Collectors.toList());
        Map<Long, List<CalendarConfigDetailDO>> attendanceConfigDetailMap = calendarManage.selectCalendarDetailsByConfigIds(calendarConfigIds)
                .stream().collect(Collectors.groupingBy(CalendarConfigDetailDO::getAttendanceConfigId));
        //查询用户班次规则
        Map<Long, List<PunchClassConfigDTO>> punchClassConfigMap = punchClassConfigManage.mapByUserIds(userIds, finalDateNow);
        //查询员工周期内的排班数据(3天)
        Map<Long, List<UserShiftConfigDO>> userShiftConfigMap = userShiftConfigManage.selectRecordByUserIdList(userIds, calculateHandlerDTO.getStartDayId(), calculateHandlerDTO.getEndDayId())
                .stream().collect(Collectors.groupingBy(UserShiftConfigDO::getUserId));
        //查询排班中的班次信息
        List<Long> punchClassIdList = userShiftConfigMap.values().stream()
                .flatMap(List::stream)
                .map(UserShiftConfigDO::getPunchClassConfigId)
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());
        List<PunchClassConfigDTO> userShiftPunchClassConfigList = new ArrayList<>(punchClassConfigManage.selectByIds(punchClassIdList).values());

        //遍历员工
        for (UserInfoDO user : userInfoDOList) {
            //构建用户考勤计算上下文参数
            AttendanceCalculateContext calculateContext = attendanceGenerateService.buildAttendanceCalculateContext(true, calculateHandlerDTO, user, Collections.emptyList(),
                    new HashMap<>(), new HashMap<>(), userPunchCardGroup, userAttendanceEmployeeMap, userAbnormalMap, userCalendarConfigMap, attendanceConfigDetailMap, new HashMap<>(),
                    userShiftPunchClassConfigList, punchClassConfigMap, new HashMap<>(), Collections.emptyList(), Collections.emptyList(), userShiftConfigMap, Collections.emptyList());
            if (Objects.isNull(calculateContext)) {
                continue;
            }

            //查询仓内考勤管理规则
            calculateContext.setWarehouseAttendanceConfigDO(warehouseAttendanceConfigDO);

            //查询用户当天的是否有排班
            List<UserAttendancePunchConfigDTO> userAttendancePunchConfigList = calculateContext.getUserAttendancePunchConfigDTOList()
                    .stream().filter(item -> item.getDayId().equals(calculateHandlerDTO.getAttendanceDayId())).collect(Collectors.toList());
            calculateContext.setUserAttendancePunchConfigDTOList(userAttendancePunchConfigList);

            AttendanceCalculateService calculateService = StrategyLoader.load(AttendanceCalculateService.class, t -> t.isMatch(userAttendancePunchConfigList, PunchConfigTypeEnum.FIXED_WORK.getCode(), true, user.getEmployeeType()));
            calculateService.execute(calculateContext);
        }
    }

    /**
     * 仓内自有员工考勤异常计算
     */
    @Override
    public void warehouseEmployeeAttendanceHandler(AttendanceCalculateHandlerDTO calculateHandlerDTO) {
        log.info("warehouse employee attendance handler: {}", JSON.toJSONString(calculateHandlerDTO));
        if (Objects.isNull(calculateHandlerDTO)
                || Objects.isNull(calculateHandlerDTO.getAttendanceDayId())
                || StringUtils.isEmpty(calculateHandlerDTO.getUserCodes())
        ) {
            log.info("warehouseAttendanceHandlerDTO | 入参对象为空OR必填项信息不全");
            return;
        }

        List<String> userCodes = Arrays.asList(calculateHandlerDTO.getUserCodes().split(BusinessConstant.DEFAULT_DELIMITER));
        //过滤得到仓内自有和挂靠员工
        List<UserInfoDO> userInfoDOList = userInfoDao.listByUserCodes(userCodes)
                .stream()
                .filter(item -> EmploymentTypeEnum.TYPE_OF_EMPLOYEE_WAREHOUSE.contains(item.getEmployeeType())
                        && Objects.equals(BusinessConstant.Y, item.getIsWarehouseStaff())
                        && Objects.equals(BusinessConstant.N, item.getIsDriver())
                        && migrationService.verifyUserIsEnableNewAttendance(item.getId()))
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(userInfoDOList)) {
            log.info("warehouseAttendanceHandler | 员工不符合条件,{}", userCodes);
            return;
        }

        attendanceGenerateService.buildAttendanceCalculateRangeTime(calculateHandlerDTO);

        //查询仓内考勤管理规则
        WarehouseAttendanceConfigDO warehouseAttendanceConfigDO = warehouseAttendanceConfigDao.selectActiveById(calculateHandlerDTO.getWarehouseAttendanceConfigId());

        Date finalDateNow = DateUtil.endOfDay(calculateHandlerDTO.getAttendanceTime());

        List<String> userCodeList = userInfoDOList.stream().map(UserInfoDO::getUserCode).distinct().collect(Collectors.toList());
        List<Long> userIdList = userInfoDOList.stream().map(UserInfoDO::getId).distinct().collect(Collectors.toList());

        //查询所有员工的入职日期
        Map<Long, UserEntryRecordDO> userEntryMap = userEntryRecordManage.mapByUserIds(userIdList);
        //查询所有员工的离职信息
        Map<Long, UserDimissionRecordDO> userDimissionMap = userDimissionRecordManage.mapByUserIds(userIdList);
        //查询用户打卡记录
        Map<String, List<UserPunchRecordBO>> userPunchCardGroup = employeePunchRecordManage.mapByUserCodesAndTimeRange(calculateHandlerDTO.getStartTime(), calculateHandlerDTO.getEndTime(), userCodeList);
        //查询考勤正常出勤明细
        Map<Long, List<AttendanceEmployeeDetailDO>> userAttendanceEmployeeMap = employeeDetailManage.mapByUserIdsAndDayId(userIdList, calculateHandlerDTO.getAttendanceDayId());
        //查询考勤当天未处理的异常数据表(有些可能已经被发起审批了，在审批中)
        Map<Long, List<EmployeeAbnormalAttendanceDO>> userAbnormalMap = abnormalAttendanceManage.mapByUserIdsAndDayIds(userIdList, Collections.singletonList(calculateHandlerDTO.getAttendanceDayId()));
        //查询用户所在日历
        Map<Long, List<CalendarConfigDO>> userCalendarConfigMap = calendarManage.mapByUserIds(userIdList, finalDateNow);
        //查询用户考勤日历配置明细
        List<Long> calendarConfigIds = userCalendarConfigMap.values().stream()
                .flatMap(List::stream)
                .map(CalendarConfigDO::getId)
                .distinct()
                .collect(Collectors.toList());
        Map<Long, List<CalendarConfigDetailDO>> attendanceConfigDetailMap = calendarManage.selectCalendarDetailsByConfigIds(calendarConfigIds)
                .stream().collect(Collectors.groupingBy(CalendarConfigDetailDO::getAttendanceConfigId));
        //查询用户班次规则
        Map<Long, List<PunchClassConfigDTO>> punchClassConfigMap = punchClassConfigManage.mapByUserIds(userIdList, finalDateNow);
        //查询用户假期详情
        List<UserLeaveDetailDO> userLeaveDetailList = userLeaveDetailManage.listByUserId(userIdList);
        Map<Long, List<UserLeaveDetailDO>> leaveDetailMap = userLeaveDetailList.stream().collect(Collectors.groupingBy(UserLeaveDetailDO::getUserId));
        //查询用户假期余额
        List<Long> leaveIdList = userLeaveDetailList.stream().map(UserLeaveDetailDO::getId).distinct().collect(Collectors.toList());
        List<UserLeaveStageDetailDO> leaveStageDetailList = userLeaveStageDetailDao.selectByLeaveId(leaveIdList);
        // 获取所有假期
        List<CompanyLeaveConfigDO> companyLeaveConfigList = companyLeaveConfigManage.selectAllActive();
        //查询员工周期内的排班数据(3天)
        Map<Long, List<UserShiftConfigDO>> userShiftConfigMap = userShiftConfigManage.selectRecordByUserIdList(userIdList, calculateHandlerDTO.getStartDayId(), calculateHandlerDTO.getEndDayId())
                .stream().collect(Collectors.groupingBy(UserShiftConfigDO::getUserId));
        //查询排班中的班次信息
        List<Long> punchClassIdList = userShiftConfigMap.values().stream()
                .flatMap(List::stream)
                .map(UserShiftConfigDO::getPunchClassConfigId)
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());
        List<PunchClassConfigDTO> userShiftPunchClassConfigList = new ArrayList<>(punchClassConfigManage.selectByIds(punchClassIdList).values());
        //查询用户审批通过的请假/外勤单据
        List<AttendanceFormDetailBO> attendanceFormDetailBOList = attendanceFormManage.listByUserIds(userIdList, Collections.singletonList(FormStatusEnum.PASS.getCode()), FormTypeEnum.getLeaveAndOutOfOfficeCodeList());
        //获取有效的审批单据ID
        List<AttendanceFormDO> effectFormList = attendanceFormManage.getEffectFormList(calculateHandlerDTO.getStartDayId(),
                calculateHandlerDTO.getEndDayId(), attendanceFormDetailBOList);
        List<Long> effectFormIdList = effectFormList.stream().map(AttendanceFormDO::getId).collect(Collectors.toList());

        //循环遍历员工
        for (UserInfoDO user : userInfoDOList) {
            //构建用户考勤计算上下文参数
            AttendanceCalculateContext calculateContext = attendanceGenerateService.buildAttendanceCalculateContext(true, calculateHandlerDTO, user, effectFormIdList, userEntryMap, userDimissionMap,
                    userPunchCardGroup, userAttendanceEmployeeMap, userAbnormalMap, userCalendarConfigMap, attendanceConfigDetailMap, new HashMap<>(), userShiftPunchClassConfigList,
                    punchClassConfigMap, leaveDetailMap, leaveStageDetailList, companyLeaveConfigList, userShiftConfigMap, attendanceFormDetailBOList);
            if (Objects.isNull(calculateContext)) {
                continue;
            }

            calculateContext.setWarehouseAttendanceConfigDO(warehouseAttendanceConfigDO);

            //查询用户当天的是否有排班
            List<UserAttendancePunchConfigDTO> userAttendancePunchConfigList = calculateContext.getUserAttendancePunchConfigDTOList()
                    .stream().filter(item -> item.getDayId().equals(calculateHandlerDTO.getAttendanceDayId())).collect(Collectors.toList());
            calculateContext.setUserAttendancePunchConfigDTOList(userAttendancePunchConfigList);

            AttendanceCalculateService calculateService = StrategyLoader.load(AttendanceCalculateService.class, t -> t.isMatch(userAttendancePunchConfigList, PunchConfigTypeEnum.FIXED_WORK.getCode(), true, user.getEmployeeType()));
            calculateService.execute(calculateContext);
        }

    }
}
