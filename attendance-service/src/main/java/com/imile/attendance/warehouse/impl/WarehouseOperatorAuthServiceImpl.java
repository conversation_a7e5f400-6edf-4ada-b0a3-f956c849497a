package com.imile.attendance.warehouse.impl;

import com.alibaba.fastjson.JSON;
import com.imile.attendance.apollo.AttendanceProperties;
import com.imile.attendance.context.RequestInfoHolder;
import com.imile.attendance.enums.ErrorCodeEnum;
import com.imile.attendance.enums.WorkStatusEnum;
import com.imile.attendance.infrastructure.repository.common.AttendanceUserService;
import com.imile.attendance.infrastructure.repository.common.dto.AttendanceDept;
import com.imile.attendance.infrastructure.repository.common.dto.AttendanceUser;
import com.imile.attendance.permission.WpmPermissionService;
import com.imile.attendance.warehouse.WarehouseOperatorAuthService;
import com.imile.attendance.warehouse.param.WarehouseOperatorListParam;
import com.imile.attendance.warehouse.param.WarehouseOperatorSaveParam;
import com.imile.attendance.warehouse.vo.WarehouseOperatorListVO;
import com.imile.common.enums.StatusEnum;
import com.imile.common.exception.BusinessException;
import com.imile.util.lang.I18nUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 仓内考勤操作员授权服务
 *
 * <AUTHOR>
 * @since 2025/1/22
 */
@Slf4j
@Service
public class WarehouseOperatorAuthServiceImpl implements WarehouseOperatorAuthService {
    @Resource
    private AttendanceUserService userService;
    @Resource
    private WpmPermissionService wpmPermissionService;
    @Resource
    private AttendanceProperties attendanceProperties;

    @Override
    public List<WarehouseOperatorListVO> warehouseOperatorList(WarehouseOperatorListParam param) {
        Long operatorRoleId = attendanceProperties.getAttendance().getWpmOperatorRoleId();
        if (Objects.isNull(operatorRoleId)) {
            return Collections.emptyList();
        }

        Map<String, List<String>> userCodeMap = wpmPermissionService.selectUserCodeByRoleId(Collections.singletonList(operatorRoleId));
        if (MapUtils.isEmpty(userCodeMap)) {
            return Collections.emptyList();
        }
        String userCode = RequestInfoHolder.getUserCode();
        List<Long> deptIdList = wpmPermissionService.getRegionStationAuthDeptList(userCode).stream().map(AttendanceDept::getId).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(deptIdList)) {
            return Collections.emptyList();
        }

        log.info("WPM网点权限,userCode:{},部门ID:{}", userCode, JSON.toJSONString(deptIdList));
        List<AttendanceUser> userInfoDOList = userService.listByUserCodes(new ArrayList<>(userCodeMap.keySet())).stream()
                .filter(user -> deptIdList.contains(user.getDeptId())
                        && Objects.equals(WorkStatusEnum.ON_JOB.getCode(), user.getWorkStatus())
                        && Objects.equals(StatusEnum.ACTIVE.getCode(), user.getStatus()))
                .collect(Collectors.toList());

        return userInfoDOList.stream().map(user -> {
            WarehouseOperatorListVO whiteListVO = new WarehouseOperatorListVO();
            whiteListVO.setId(user.getId());
            whiteListVO.setUserId(user.getId());
            whiteListVO.setUserCode(user.getUserCode());
            whiteListVO.setUserName(user.getUserName());
            whiteListVO.setProfilePhotoKey(user.getProfilePhotoUrl());
            return whiteListVO;
        }).collect(Collectors.toList());
    }

    @Override
    public Boolean bindOperatorRole(WarehouseOperatorSaveParam param) {
        AttendanceUser userInfoDO = Optional.ofNullable(userService.getByUserId(param.getUserId()))
                .orElseThrow(() -> BusinessException.get(ErrorCodeEnum.ACCOUNT_NOT_EXITS.getCode(), I18nUtils.getMessage(ErrorCodeEnum.ACCOUNT_NOT_EXITS.getDesc())));
        if (!StringUtils.equalsIgnoreCase(userInfoDO.getWorkStatus(),WorkStatusEnum.ON_JOB.getCode())) {
            throw BusinessException.get(ErrorCodeEnum.PERSON_HAS_RESIGNED.getCode(), I18nUtils.getMessage(ErrorCodeEnum.PERSON_HAS_RESIGNED.getDesc()));
        }
        if (!StringUtils.equalsIgnoreCase(userInfoDO.getStatus(), StatusEnum.ACTIVE.getCode())) {
            throw BusinessException.get(ErrorCodeEnum.ACCOUNT_HAS_BEEN_DEACTIVATED.getCode(), I18nUtils.getMessage(ErrorCodeEnum.ACCOUNT_HAS_BEEN_DEACTIVATED.getDesc()));
        }
        Long operatorRoleId = attendanceProperties.getAttendance().getWpmOperatorRoleId();
        if (Objects.isNull(operatorRoleId)) {
            return Boolean.FALSE;
        }
        return wpmPermissionService.addUserRole(userInfoDO.getUserCode(), operatorRoleId);
    }


    @Override
    public void removeOperatorRole(Long id) {
        if (Objects.isNull(id)) {
            return;
        }
        AttendanceUser userInfoDO = Optional.ofNullable(userService.getByUserId(id))
                .orElseThrow(() -> BusinessException.get(ErrorCodeEnum.ACCOUNT_NOT_EXITS.getCode(), I18nUtils.getMessage(ErrorCodeEnum.ACCOUNT_NOT_EXITS.getDesc())));
        Long operatorRoleId = attendanceProperties.getAttendance().getWpmOperatorRoleId();
        if (Objects.isNull(operatorRoleId)) {
            return;
        }
        wpmPermissionService.removeUserRole(userInfoDO.getUserCode(), operatorRoleId);
    }
}
