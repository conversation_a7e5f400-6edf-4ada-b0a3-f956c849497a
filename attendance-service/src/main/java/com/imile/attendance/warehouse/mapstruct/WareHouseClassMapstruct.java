package com.imile.attendance.warehouse.mapstruct;


import com.imile.attendance.infrastructure.config.MapperConfiguration;
import com.imile.attendance.infrastructure.repository.rule.model.PunchClassConfigDO;
import com.imile.attendance.infrastructure.repository.warehouse.dto.WarehousePunchClassConfigDTO;
import com.imile.attendance.infrastructure.repository.warehouse.dto.WarehousePunchConfigDTO;
import com.imile.attendance.warehouse.vo.ClassesVO;
import com.imile.attendance.warehouse.vo.ClassesWebVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/7/14
 * @Description
 */
@Mapper(config = MapperConfiguration.class)
public interface WareHouseClassMapstruct {

    WareHouseClassMapstruct INSTANCE = Mappers.getMapper(WareHouseClassMapstruct.class);

    WarehousePunchClassConfigDTO mapToWareHouseClassDTO(PunchClassConfigDO punchClassConfigDO);

    List<WarehousePunchClassConfigDTO> mapToWareHouseClassDTO(List<PunchClassConfigDO> punchClassConfigDOList);

    ClassesWebVO mapToWareHouseClassWebVO(WarehousePunchClassConfigDTO warehousePunchClassConfigDTO);

    List<ClassesWebVO> mapToWareHouseClassWebVO(List<WarehousePunchClassConfigDTO> warehousePunchClassConfigDTOList);

    ClassesVO mapToWareHouseClassVO(WarehousePunchConfigDTO warehousePunchConfigDTO);

    List<ClassesVO> mapToWareHouseClassVO(List<WarehousePunchConfigDTO> warehousePunchConfigDTOList);


}
