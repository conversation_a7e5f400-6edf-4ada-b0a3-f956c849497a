package com.imile.attendance.warehouse.param;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;


/**
 * <AUTHOR>
 * @since 2024/12/06
 */
@Data
public class VendorConfirmClassesParam implements Serializable {

    @NotNull(message = "ocId cannot be empty")
    private Long ocId;

    @NotNull(message = "vendorCode cannot be empty")
    private String vendorCode;

    @NotNull(message = "classesId cannot be empty")
    private Long classesId;

    @NotNull(message = "warehouseDate cannot be empty")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date warehouseDate;

    @NotNull(message = "confirmDate cannot be empty")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date confirmDate;

    @NotNull(message = "signedFile cannot be empty")
    @JSONField(serialize = false)
    private MultipartFile signedFile;

    @NotNull(message = "faceFile cannot be empty")
    @JSONField(serialize = false)
    private MultipartFile faceFile;
}
