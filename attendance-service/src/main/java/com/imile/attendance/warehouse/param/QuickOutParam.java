package com.imile.attendance.warehouse.param;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;


/**
 * 快速离仓
 *
 * <AUTHOR>
 * @since 2024/11/11
 */
@Data
public class QuickOutParam {

    /**
     * 人脸拍摄文件流
     */
    @NotNull(message = "file cannot be empty")
    @JSONField(serialize = false)
    private MultipartFile file;

    /**
     * 仓内考勤结果表ID
     */
    @NotNull(message = "id cannot be empty")
    private Long id;

    /**
     * 当地打卡时间
     */
    @NotNull(message = "attendanceTime cannot be empty")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date attendanceTime;

    /**
     * 出入仓地理坐标精度
     */
    private BigDecimal longitude;

    /**
     * 出入仓地理坐标维度
     */
    private BigDecimal latitude;
}
