package com.imile.attendance.warehouse.param;

import lombok.Data;

import java.util.List;

/**
 * {@code @author:} allen
 * {@code @className:} WarehouseUserCardParam
 * {@code @since:} 2024-11-29 14:43
 * {@code @description:} 仓内绑定用户补卡记录参数
 */
@Data
public class WarehouseUserCardParam {
    /**
     * 所有userCode集合
     */
    List<String> allUserCodeList;

    /**
     * 按照天的用户列表
     */
    List<UserCardParam> userCardParamList;
}
