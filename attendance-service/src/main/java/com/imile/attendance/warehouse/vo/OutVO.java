package com.imile.attendance.warehouse.vo;

import lombok.Data;

/**
 * <AUTHOR>
 * @since 2024/10/15
 */
@Data
public class OutVO {

    private Boolean checkOutResult = Boolean.TRUE;

    private Boolean ocChange = Boolean.FALSE;

    private Boolean vendorChange = Boolean.FALSE;

    private Long inOcId;

    private String inOcName;

    private Long outOcId;

    private String outOcName;

    private Long inVendorId;

    private String inVendorCode;

    private String inVendorName;

    private Long outVendorId;

    private String outVendorCode;

    private String outVendorName;
}
