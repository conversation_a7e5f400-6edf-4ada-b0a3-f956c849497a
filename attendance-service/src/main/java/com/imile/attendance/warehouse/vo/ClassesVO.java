package com.imile.attendance.warehouse.vo;

import lombok.Data;

import java.math.BigDecimal;


/**
 * <AUTHOR>
 * @since 2024/8/22
 */
@Data
public class ClassesVO {

    /**
     * 打卡规则ID
     */
//    private Long punchConfigId;
//
//    /**
//     * 打卡规则名称
//     */
//    private String punchConfigName;
//
//    /**
//     * 打卡规则类型：固定上下班、班次上下班、自由上下班
//     */
//    private String punchConfigType;

    /**
     * dayPunchType：固定上下班、自由上下班取打卡规则名称，班次上下班取班次名称
     */
    private String dayPunchType;

    /**
     * 打卡规则no
     */
//    private String punchConfigNo;

    /**
     * 班次信息id
     */
    private Long classId;

    /**
     * 班次类型
     */
    private Integer classType;

    /**
     * 班次名称
     */
    private String className;

    /**
     * 出勤时间（法定时长 + 休息时长）
     */
    private BigDecimal attendanceHours;

    /**
     * 法定时长
     */
    private BigDecimal legalWorkingHours;
}
