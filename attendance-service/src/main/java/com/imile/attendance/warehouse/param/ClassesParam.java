package com.imile.attendance.warehouse.param;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;


/**
 * <AUTHOR>
 * @since 2024/8/22
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ClassesParam {

    /**
     * dayPunchType：固定上下班、自由上下班取打卡规则名称，班次上下班取班次名称
     */
    private String dayPunchType;

    /**
     * 班次信息id
     */
    private Long classId;

    /**
     * 班次信息编码
     */
    private String classNo;

    /**
     * 班次类型
     */
    private Integer classType;

    /**
     * 班次名称
     */
    private String className;

    /**
     * 出勤时间（法定时长 + 休息时长）
     */
    private BigDecimal attendanceHours;

    /**
     * 法定时长
     */
    private BigDecimal legalWorkingHours;
}
