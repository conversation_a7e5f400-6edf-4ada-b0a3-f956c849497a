package com.imile.attendance.warehouse.param;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR> chen
 * @Date 2025/07/09
 * @Time 16:20
 * @Description
 */
@Data
public class FaceSaveParam implements Serializable {

    @NotNull(message = "file cannot be empty")
    @JSONField(serialize = false)
    private MultipartFile file;

    @NotNull(message = "userCode cannot be empty")
    private String userCode;

    /**
     * 相似员工
     * 用于确认不同员工打标
     */
    private String similarUserCode;

    @NotNull(message = "faceTime cannot be empty")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date faceTime;

}
