package com.imile.attendance.warehouse.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2025/2/17
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WarehouseOcVO {


    /**
     * 网点code
     */
    private String ocCode;

    /**
     * 部门编码
     */
    private String deptCode;

    /**
     * 网点ID，对应hermes网点id
     */
    private Long ocId;

    /**
     * 网点名字（中文）
     */
    private String ocNameCn;

    /**
     * 网点名字（英文）
     */
    private String ocNameEn;
}
