package com.imile.attendance.warehouse.vo;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022/2/15
 */
@Data
public class UserVO implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 用户id
     */
    private Long userId;
    /**
     * 用户编码
     */
    private String userCode;

    /**
     * 员工姓名
     */
    private String userName;

    /**
     * 是否首次录入
     */
    private Boolean firstEnter = Boolean.FALSE;

}
