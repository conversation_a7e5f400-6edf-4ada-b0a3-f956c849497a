package com.imile.attendance.warehouse;

import com.imile.attendance.warehouse.param.FaceCheckParam;
import com.imile.attendance.warehouse.param.FaceSaveParam;
import com.imile.attendance.warehouse.param.FaceSearchParam;
import com.imile.attendance.warehouse.param.FaceSearchRepeatParam;
import com.imile.attendance.warehouse.vo.UserFaceSearchRepeatVO;
import com.imile.attendance.warehouse.vo.UserFaceSearchVO;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * <AUTHOR> chen
 * @Date 2025/07/09
 * @Time 16:17
 * @Description
 */
public interface FaceEngineService {

    /**
     * 人脸录入
     *
     * @param param 入参
     */
    void faceInput(FaceSaveParam param);

    /**
     * 人脸核对
     *
     * @param param 入参
     * @return 人脸搜索结果
     */
    UserFaceSearchVO faceCheck(FaceCheckParam param);


    /**
     * 人脸识别搜索
     *
     * @param param 入参
     * @return 人脸搜索结果
     */
    UserFaceSearchVO faceRecognition(FaceSearchParam param);

    /**
     * 人脸搜索
     *
     * @param param 入参
     * @return 人脸搜索结果
     */
    UserFaceSearchRepeatVO faceRecognitionV2(FaceSearchRepeatParam param);


    /**
     * 获取人脸隐私协议安全要求国家
     *
     * @return 国家三字码集合
     */
    List<String> getFacePrivacySecurityCountry();


}
