package com.imile.attendance.warehouse.vo;

import com.imile.attendance.annon.HyperLink;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;


/**
 * <AUTHOR>
 * @since 2024/12/06
 */
@Data
public class VendorClassesConfirmVO {

    private Long id;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date confirmDate;

    /**
     * 实际出勤人数
     */
    private Integer actualAttendanceNum = 0;

    /**
     * 下班缺卡人数
     */
    private Integer afterOfficeLackNum = 0;

    /**
     * 上班缺卡人数
     */
    private Integer beforeOfficeLackNum = 0;

    /**
     * 迟到人数
     */
    private Integer lateNum = 0;

    /**
     * 早退人数
     */
    private Integer leaveEarlyNum = 0;

    /**
     * 时长异常人数
     */
    private Integer abnormalDurationNum = 0;

    /**
     * 是否分段计算工时
     */
    private Boolean isSegmentedCalculation = Boolean.FALSE;

    /**
     * 签名照
     */
    @HyperLink(ref = "signedPhotoUrl")
    private String signedPhoto;

    /**
     * 签名照完整链接
     */
    private String signedPhotoUrl;

    /**
     * 人脸照
     */
    @HyperLink(ref = "facePhotoUrl")
    private String facePhoto;

    /**
     * 人脸照完整链接
     */
    private String facePhotoUrl;

    /**
     * 异常类型列表
     */
    private List<DataStatisticsDetailsVO.AbnormalVO> abnormalVOList;
}
