package com.imile.attendance.warehouse.param;

import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;
import java.util.List;


/**
 * 简易日报查询入参
 *
 * <AUTHOR>
 * @since 2025/01/21
 */
@Data
public class SimpleReportParam implements Serializable {
    /**
     * 网点id
     */
    private Long ocId;

    /**
     * 供应商编码List
     */
    private List<String> vendorCodeList;

    /**
     * 班次ID
     */
    private Long classId;

    /**
     * 开始日期
     */
    @NotNull(message = "startDate cannot be empty")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date startDate;

    /**
     * 结束日期
     */
    @NotNull(message = "endDate cannot be empty")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date endDate;

    /**
     * 搜索人员/工号关键词
     */
    private String searchUserKey;

    /**
     * 列表类型
     * 可选值
     * oc      网点
     * supplier  供应商
     * classes 班次
     */
    @NotNull(message = "type cannot be empty")
    private String type;
}
