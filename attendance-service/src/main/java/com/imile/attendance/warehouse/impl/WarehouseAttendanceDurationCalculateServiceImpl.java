package com.imile.attendance.warehouse.impl;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.enums.warehouse.PunchStatusEnum;
import com.imile.attendance.enums.warehouse.WarehouseTypeEnum;
import com.imile.attendance.infrastructure.idwork.IdWorkUtils;
import com.imile.attendance.infrastructure.repository.rule.dao.PunchClassItemConfigDao;
import com.imile.attendance.infrastructure.repository.rule.model.PunchClassItemConfigDO;
import com.imile.attendance.infrastructure.repository.warehouse.dao.WarehouseAttendanceConfigDao;
import com.imile.attendance.infrastructure.repository.warehouse.dao.WarehouseRecordDao;
import com.imile.attendance.infrastructure.repository.warehouse.model.WarehouseAttendanceConfigDO;
import com.imile.attendance.infrastructure.repository.warehouse.model.WarehouseDetailDO;
import com.imile.attendance.infrastructure.repository.warehouse.model.WarehousePunchPeriodDO;
import com.imile.attendance.infrastructure.repository.warehouse.model.WarehouseRecordDO;
import com.imile.attendance.rule.dto.DayPunchTimeDTO;
import com.imile.attendance.rule.service.PunchClassConfigQueryService;
import com.imile.attendance.util.BaseDOUtil;
import com.imile.attendance.util.DateHelper;
import com.imile.attendance.warehouse.WarehouseAttendanceDurationCalculateService;
import com.imile.genesis.api.enums.EmploymentTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 仓内出勤时长计算服务
 *
 * <AUTHOR>
 * @since 2025/1/23
 */
@Slf4j
@Service
public class WarehouseAttendanceDurationCalculateServiceImpl implements WarehouseAttendanceDurationCalculateService {
    @Resource
    private WarehouseRecordDao warehouseRecordDao;

    @Resource
    private WarehouseAttendanceConfigDao warehouseAttendanceConfigDao;

    @Resource
    private PunchClassItemConfigDao punchClassItemConfigDao;

    @Resource
    private PunchClassConfigQueryService punchClassConfigQueryService;

    @Resource
    private IdWorkUtils idWorkUtils;


    @Override
    public List<WarehousePunchPeriodDO> calculateAttendanceHours(WarehouseDetailDO warehouseDetailDO, boolean onlyUpdateWarehouseHours) {
        List<WarehouseRecordDO> warehouseRecordList = warehouseRecordDao.selectByWarehouseDetailIds(Collections.singletonList(warehouseDetailDO.getId()));
        if (CollectionUtils.isEmpty(warehouseRecordList)) {
            return Collections.emptyList();
        }

        //查询班次上班时间
        Date punchInTime = getPunchInTime(warehouseDetailDO.getWarehouseDate(), warehouseDetailDO.getClassesId());

        //打卡时间磨平秒数位
        for (WarehouseRecordDO warehouseRecordDO : warehouseRecordList) {
            warehouseRecordDO.setWarehouseTime(DateHelper.parseIgnoreSeconds(warehouseRecordDO.getWarehouseTime()));
            if (Objects.nonNull(punchInTime) && warehouseRecordDO.getWarehouseTime().compareTo(punchInTime) < 0) {
                warehouseRecordDO.setWarehouseTime(punchInTime);
            }
        }

        WarehouseAttendanceConfigDO warehouseAttendanceConfigDO = warehouseAttendanceConfigDao.selectActiveById(warehouseDetailDO.getAttendanceConfigId());
        if (Objects.isNull(warehouseAttendanceConfigDO) || Objects.equals(BusinessConstant.N, warehouseAttendanceConfigDO.getIsSegmentedCalculation())) {
            //计算总工时 最晚出仓时间 - 班次要求的上班时间开始
            calculateTotalWorkingHours(warehouseDetailDO, onlyUpdateWarehouseHours, warehouseRecordList, warehouseAttendanceConfigDO);
            return Collections.emptyList();
        }

        //分时段计算工时
        return segmentedCalculationWorkingHours(warehouseDetailDO, onlyUpdateWarehouseHours, warehouseRecordList, warehouseAttendanceConfigDO);
    }

    private void calculateTotalWorkingHours(WarehouseDetailDO warehouseDetailDO,
                                            boolean onlyUpdateWarehouseHours,
                                            List<WarehouseRecordDO> warehouseRecordList,
                                            WarehouseAttendanceConfigDO warehouseAttendanceConfigDO) {
        //最早入仓打卡时间
        Date firstInTime = warehouseRecordList
                .stream()
                .filter(record -> Objects.equals(WarehouseTypeEnum.IN.getCode(), record.getRecordType()))
                .findFirst()
                .orElse(new WarehouseRecordDO())
                .getWarehouseTime();

        //最晚出仓打卡时间
        Date lastOutTime = warehouseRecordList.stream()
                .filter(record -> Objects.equals(WarehouseTypeEnum.OUT.getCode(), record.getRecordType()))
                .max(Comparator.comparing(WarehouseRecordDO::getWarehouseTime))
                .orElse(new WarehouseRecordDO())
                .getWarehouseTime();

        BigDecimal minutes = BigDecimal.ZERO;
        if (Objects.nonNull(firstInTime) && Objects.nonNull(lastOutTime)) {
            minutes = Convert.toBigDecimal(DateUtil.between(firstInTime, lastOutTime, DateUnit.MINUTE));
        }

        Long punchInCount = warehouseRecordList.stream().filter(warehouseRecord -> Objects.equals(WarehouseTypeEnum.IN.getCode(), warehouseRecord.getRecordType())).count();
        Long punchOutCount = warehouseRecordList.stream().filter(warehouseRecord -> Objects.equals(WarehouseTypeEnum.OUT.getCode(), warehouseRecord.getRecordType())).count();

        if (Objects.equals(punchInCount, punchOutCount)) {
            warehouseDetailDO.setPunchStatus(PunchStatusEnum.NORMAL_PUNCH.getCode());
        } else {
            warehouseDetailDO.setPunchStatus(PunchStatusEnum.ABNORMAL_PUNCH.getCode());
        }

        //仅计算仓内实际出勤时长 根据仓内打卡记录计算 无关配置和异常处理
        if (onlyUpdateWarehouseHours) {
            warehouseDetailDO.setStayDuration(Convert.toLong(minutes));
            BigDecimal actualAttendanceTime = minutes.divide(BusinessConstant.MINUTES, 2, RoundingMode.HALF_UP);
            warehouseDetailDO.setWarehouseActualAttendanceTime(actualAttendanceTime);
            return;
        }
        //实际工作时长
        BigDecimal actualAttendanceHours = minutes.divide(BusinessConstant.MINUTES, 2, RoundingMode.HALF_UP);
        BigDecimal actualAttendanceTimeMinute = calculateActualAttendanceTime(warehouseRecordList);
        //实际出勤时长 单位:分钟
        BigDecimal actualAttendanceTime = actualAttendanceTimeMinute.divide(BusinessConstant.MINUTES, 2, RoundingMode.HALF_UP);

        //班次要求的工作时长和出勤时长
        BigDecimal legalWorkingMinutes = warehouseDetailDO.getLegalWorkingHours().multiply(BusinessConstant.MINUTES);
        BigDecimal requiredAttendanceMinutes = warehouseDetailDO.getRequiredAttendanceTime().multiply(BusinessConstant.MINUTES);
        //劳务派遣员工结合仓内考勤管理规则计算最终的工作时长和出勤时长
        if (Objects.equals(EmploymentTypeEnum.OS_FIXED_SALARY.getCode(), warehouseDetailDO.getEmployeeType())
                && Objects.nonNull(warehouseDetailDO.getAttendanceConfigId())
                && (minutes.compareTo(legalWorkingMinutes) < 0
                || actualAttendanceTimeMinute.compareTo(requiredAttendanceMinutes) < 0)) {
            if (Objects.nonNull(warehouseAttendanceConfigDO)) {
                if (actualAttendanceTimeMinute.compareTo(requiredAttendanceMinutes) < 0
                        && actualAttendanceTimeMinute.add(Convert.toBigDecimal(warehouseAttendanceConfigDO.getMinute())).compareTo(requiredAttendanceMinutes) > -1) {
                    actualAttendanceTime = warehouseDetailDO.getRequiredAttendanceTime();
                }
                if (minutes.compareTo(legalWorkingMinutes) < 0
                        && minutes.add(Convert.toBigDecimal(warehouseAttendanceConfigDO.getMinute())).compareTo(legalWorkingMinutes) > -1) {
                    actualAttendanceHours = warehouseDetailDO.getLegalWorkingHours();
                }
            }
        }
        warehouseDetailDO.setStayDuration(Convert.toLong(actualAttendanceHours.multiply(BusinessConstant.MINUTES)));
        warehouseDetailDO.setActualWorkingHours(actualAttendanceHours);
        warehouseDetailDO.setWarehouseActualAttendanceTime(actualAttendanceHours);
        warehouseDetailDO.setActualAttendanceTime(actualAttendanceTime);
        if (Objects.nonNull(warehouseDetailDO.getClassesId())) {
            if (actualAttendanceTime.compareTo(warehouseDetailDO.getRequiredAttendanceTime()) > 0) {
                warehouseDetailDO.setOvertimeHours(actualAttendanceTime.subtract(warehouseDetailDO.getRequiredAttendanceTime()));
                warehouseDetailDO.setAbsenceTime(BigDecimal.ZERO);
                return;
            }
            warehouseDetailDO.setAbsenceTime(warehouseDetailDO.getRequiredAttendanceTime().subtract(actualAttendanceTime));
        }
    }

    private List<WarehousePunchPeriodDO> segmentedCalculationWorkingHours(WarehouseDetailDO warehouseDetailDO,
                                                                          boolean onlyUpdateWarehouseHours,
                                                                          List<WarehouseRecordDO> warehouseRecordList,
                                                                          WarehouseAttendanceConfigDO warehouseAttendanceConfigDO) {
        List<WarehousePunchPeriodDO> warehousePunchPeriodDOList = new ArrayList<>();
        WarehouseRecordDO first = warehouseRecordList.get(0);
        Date firstTime = first.getWarehouseTime();
        Integer recordType = first.getRecordType();

        if (warehouseRecordList.size() == 1) {
            WarehousePunchPeriodDO warehousePunchPeriodDO = new WarehousePunchPeriodDO();
            warehousePunchPeriodDO.setId(idWorkUtils.nextId());
            warehousePunchPeriodDO.setWarehouseDetailId(warehouseDetailDO.getId());
            warehousePunchPeriodDO.setSortNo(BusinessConstant.ONE);
            warehousePunchPeriodDO.setPunchStatus(PunchStatusEnum.ABNORMAL_PUNCH.getCode());
            if (Objects.equals(WarehouseTypeEnum.IN.getCode(), recordType)) {
                warehousePunchPeriodDO.setInTime(firstTime);
            } else {
                warehousePunchPeriodDO.setOutTime(firstTime);
            }
            warehousePunchPeriodDO.setAttendanceHours(BigDecimal.ZERO);
            warehousePunchPeriodDOList.add(warehousePunchPeriodDO);
            BaseDOUtil.fillDOInsertByUsrOrSystem(warehousePunchPeriodDO);
            warehouseDetailDO.setPunchStatus(PunchStatusEnum.ABNORMAL_PUNCH.getCode());
            if (!onlyUpdateWarehouseHours) {
                warehouseDetailDO.setActualAttendanceTime(BigDecimal.ZERO);
                warehouseDetailDO.setActualWorkingHours(BigDecimal.ZERO);
            }
            return warehousePunchPeriodDOList;
        }

        Date lastInTime = null;
        Date lastOutTime = null;
        int punchInCount = 0;
        int punchOutCount = 0;
        if (Objects.equals(WarehouseTypeEnum.IN.getCode(), recordType)) {
            lastInTime = firstTime;
            punchInCount++;
        } else {
            lastOutTime = firstTime;
            punchOutCount++;
        }

        int sortNo = 0;
        for (int i = 1; i < warehouseRecordList.size(); i++) {
            WarehouseRecordDO record = warehouseRecordList.get(i);
            if (Objects.equals(WarehouseTypeEnum.IN.getCode(), record.getRecordType())) {
                if (lastOutTime != null) {
                    WarehousePunchPeriodDO warehousePunchPeriodDO = new WarehousePunchPeriodDO();
                    warehousePunchPeriodDO.setId(idWorkUtils.nextId());
                    warehousePunchPeriodDO.setWarehouseDetailId(warehouseDetailDO.getId());
                    warehousePunchPeriodDO.setSortNo(++sortNo);
                    warehousePunchPeriodDO.setOutTime(lastOutTime);
                    warehousePunchPeriodDO.setInTime(lastInTime);
                    if (lastInTime != null) {
                        if (punchInCount == punchOutCount) {
                            warehousePunchPeriodDO.setPunchStatus(PunchStatusEnum.NORMAL_PUNCH.getCode());
                        } else {
                            warehousePunchPeriodDO.setPunchStatus(PunchStatusEnum.ABNORMAL_PUNCH.getCode());
                        }
                        warehousePunchPeriodDO.setAttendanceHours(Convert.toBigDecimal(DateUtil.between(lastInTime, lastOutTime, DateUnit.MINUTE)));
                    } else {
                        warehousePunchPeriodDO.setPunchStatus(PunchStatusEnum.ABNORMAL_PUNCH.getCode());
                        warehousePunchPeriodDO.setAttendanceHours(BigDecimal.ZERO);
                    }
                    BaseDOUtil.fillDOInsertByUsrOrSystem(warehousePunchPeriodDO);
                    warehousePunchPeriodDOList.add(warehousePunchPeriodDO);
                    lastInTime = record.getWarehouseTime();
                    lastOutTime = null;
                    punchInCount = 1;
                    punchOutCount = 0;
                } else {
                    if (lastInTime == null) {
                        lastInTime = record.getWarehouseTime();
                    }
                    punchInCount++;
                }
            } else {
                punchOutCount++;
                lastOutTime = record.getWarehouseTime();
            }
        }

        if (lastOutTime != null) {
            WarehousePunchPeriodDO warehousePunchPeriodDO = new WarehousePunchPeriodDO();
            warehousePunchPeriodDO.setId(idWorkUtils.nextId());
            warehousePunchPeriodDO.setWarehouseDetailId(warehouseDetailDO.getId());
            warehousePunchPeriodDO.setSortNo(++sortNo);
            warehousePunchPeriodDO.setOutTime(lastOutTime);
            warehousePunchPeriodDO.setInTime(lastInTime);
            if (lastInTime != null) {
                if (punchInCount == punchOutCount) {
                    warehousePunchPeriodDO.setPunchStatus(PunchStatusEnum.NORMAL_PUNCH.getCode());
                } else {
                    warehousePunchPeriodDO.setPunchStatus(PunchStatusEnum.ABNORMAL_PUNCH.getCode());
                }
                warehousePunchPeriodDO.setAttendanceHours(Convert.toBigDecimal(DateUtil.between(lastInTime, lastOutTime, DateUnit.MINUTE)));
            } else {
                warehousePunchPeriodDO.setPunchStatus(PunchStatusEnum.ABNORMAL_PUNCH.getCode());
                warehousePunchPeriodDO.setAttendanceHours(BigDecimal.ZERO);
            }
            BaseDOUtil.fillDOInsertByUsrOrSystem(warehousePunchPeriodDO);
            warehousePunchPeriodDOList.add(warehousePunchPeriodDO);
        } else {
            WarehousePunchPeriodDO warehousePunchPeriodDO = new WarehousePunchPeriodDO();
            warehousePunchPeriodDO.setId(idWorkUtils.nextId());
            warehousePunchPeriodDO.setWarehouseDetailId(warehouseDetailDO.getId());
            warehousePunchPeriodDO.setSortNo(++sortNo);
            warehousePunchPeriodDO.setInTime(lastInTime);
            warehousePunchPeriodDO.setPunchStatus(PunchStatusEnum.ABNORMAL_PUNCH.getCode());
            warehousePunchPeriodDO.setAttendanceHours(BigDecimal.ZERO);
            BaseDOUtil.fillDOInsertByUsrOrSystem(warehousePunchPeriodDO);
            warehousePunchPeriodDOList.add(warehousePunchPeriodDO);
        }

        Optional<WarehousePunchPeriodDO> warehousePunchPeriodOptional = warehousePunchPeriodDOList
                .stream()
                .filter(punchPeriod -> Objects.equals(PunchStatusEnum.ABNORMAL_PUNCH.getCode(), punchPeriod.getPunchStatus()))
                .findFirst();
        warehouseDetailDO.setPunchStatus(warehousePunchPeriodOptional.isPresent() ? PunchStatusEnum.ABNORMAL_PUNCH.getCode() : PunchStatusEnum.NORMAL_PUNCH.getCode());
        BigDecimal totalAttendanceMinute = warehousePunchPeriodDOList.stream().map(WarehousePunchPeriodDO::getAttendanceHours).reduce(BigDecimal.ZERO, BigDecimal::add);

        if (onlyUpdateWarehouseHours) {
            warehouseDetailDO.setStayDuration(Convert.toLong(totalAttendanceMinute));
            warehouseDetailDO.setWarehouseActualAttendanceTime(totalAttendanceMinute.divide(BusinessConstant.MINUTES, 2, RoundingMode.HALF_UP));
            return warehousePunchPeriodDOList;
        }

        BigDecimal actualWorkingHours = totalAttendanceMinute.divide(BusinessConstant.MINUTES, 2, RoundingMode.HALF_UP);
        BigDecimal actualAttendanceTimeMinute = calculateActualAttendanceTime(warehouseRecordList);
        BigDecimal actualAttendanceTime = actualAttendanceTimeMinute.divide(BusinessConstant.MINUTES, 2, RoundingMode.HALF_UP);

        //班次要求的工作时长和出勤时长
        BigDecimal legalWorkingMinutes = warehouseDetailDO.getLegalWorkingHours().multiply(BusinessConstant.MINUTES);
        BigDecimal requiredAttendanceMinutes = warehouseDetailDO.getRequiredAttendanceTime().multiply(BusinessConstant.MINUTES);
        if (Objects.equals(EmploymentTypeEnum.OS_FIXED_SALARY.getCode(), warehouseDetailDO.getEmployeeType())
                && (totalAttendanceMinute.compareTo(legalWorkingMinutes) < 0
                || actualAttendanceTimeMinute.compareTo(requiredAttendanceMinutes) < 0)) {
            if (totalAttendanceMinute.compareTo(legalWorkingMinutes) < 0
                    && totalAttendanceMinute.add(Convert.toBigDecimal(warehouseAttendanceConfigDO.getMinute())).compareTo(legalWorkingMinutes) > -1) {
                actualWorkingHours = warehouseDetailDO.getLegalWorkingHours();
            }
            if (actualAttendanceTimeMinute.compareTo(requiredAttendanceMinutes) < 0
                    && actualAttendanceTimeMinute.add(Convert.toBigDecimal(warehouseAttendanceConfigDO.getMinute())).compareTo(requiredAttendanceMinutes) > -1) {
                actualAttendanceTime = warehouseDetailDO.getRequiredAttendanceTime();
            }
        }

        warehouseDetailDO.setStayDuration(Convert.toLong(actualWorkingHours.multiply(BusinessConstant.MINUTES)));
        warehouseDetailDO.setActualWorkingHours(actualWorkingHours);
        warehouseDetailDO.setWarehouseActualAttendanceTime(actualWorkingHours);
        warehouseDetailDO.setActualAttendanceTime(actualAttendanceTime);
        if (Objects.nonNull(warehouseDetailDO.getClassesId())) {
            if (warehouseDetailDO.getActualAttendanceTime().compareTo(warehouseDetailDO.getRequiredAttendanceTime()) > BusinessConstant.ZERO) {
                warehouseDetailDO.setOvertimeHours(warehouseDetailDO.getActualAttendanceTime().subtract(warehouseDetailDO.getRequiredAttendanceTime()));
                warehouseDetailDO.setAbsenceTime(BigDecimal.ZERO);
                return warehousePunchPeriodDOList;
            }
            warehouseDetailDO.setAbsenceTime(warehouseDetailDO.getRequiredAttendanceTime().subtract(warehouseDetailDO.getActualAttendanceTime()));
        }
        return warehousePunchPeriodDOList;
    }

    private Date getPunchInTime(Date warehouseDate, Long classId) {
        List<PunchClassItemConfigDO> classItemList = punchClassItemConfigDao.selectByClassIds(Collections.singletonList(classId));
        if (CollectionUtils.isEmpty(classItemList)) {
            return null;
        }
        PunchClassItemConfigDO item = classItemList.stream().filter(e -> e.getSortNo().equals(1)).findFirst().orElse(null);
        if (item == null) {
            return null;
        }
        Long dayId = DateHelper.getDayId(warehouseDate);

        DayPunchTimeDTO dayPunchTimeDTO = punchClassConfigQueryService.getUserPunchClassItemDayTime(dayId, item.getId(), classItemList);

        Date earliestPunchInTime = dayPunchTimeDTO.getDayPunchStartTime();
        //上班时间早于最早打卡时间，跨天
        Date punchInTime;
        if (item.getPunchInTime().before(item.getEarliestPunchInTime())) {
            punchInTime = DateHelper.concatDateAndTime(DateHelper.formatYYYYMMDD(DateHelper.pushDate(earliestPunchInTime, 1)), DateHelper.formatHHMMSS(item.getPunchInTime()));
        } else {
            punchInTime = DateHelper.concatDateAndTime(DateHelper.formatYYYYMMDD(earliestPunchInTime), DateHelper.formatHHMMSS(item.getPunchInTime()));
        }
        return punchInTime;
    }

    private BigDecimal calculateActualAttendanceTime(List<WarehouseRecordDO> warehouseRecordList) {
        List<WarehouseRecordDO> inList = warehouseRecordList.stream().filter(record -> Objects.equals(WarehouseTypeEnum.IN.getCode(), record.getRecordType())).collect(Collectors.toList());
        List<WarehouseRecordDO> outList = warehouseRecordList.stream().filter(record -> Objects.equals(WarehouseTypeEnum.OUT.getCode(), record.getRecordType())).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(outList) || CollectionUtils.isEmpty(inList)) {
            return BigDecimal.ZERO;
        }
        Date firstInTime = inList.get(BusinessConstant.FIRST_ELEMENT_INDEX).getWarehouseTime();
        Date lastOutTime = outList.get(outList.size() - 1).getWarehouseTime();

        return Convert.toBigDecimal(DateUtil.between(firstInTime, lastOutTime, DateUnit.MINUTE));
    }

}
