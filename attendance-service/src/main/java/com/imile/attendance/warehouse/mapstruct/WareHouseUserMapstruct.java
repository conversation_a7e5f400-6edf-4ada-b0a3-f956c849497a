package com.imile.attendance.warehouse.mapstruct;


import com.imile.attendance.infrastructure.config.MapperConfiguration;
import com.imile.attendance.warehouse.bo.UserCertificateBO;
import com.imile.attendance.warehouse.vo.CertificatesVO;
import com.imile.attendance.warehouse.vo.WarehouseUserCertificateVO;
import com.imile.genesis.api.model.result.user.UserCertificateDTO;
import com.imile.recognition.api.ocr.model.dto.CertificatesDTO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @Date 2025/7/14
 * @Description
 */
@Mapper(config = MapperConfiguration.class)
public interface WareHouseUserMapstruct {

    WareHouseUserMapstruct INSTANCE = Mappers.getMapper(WareHouseUserMapstruct.class);

    CertificatesVO mapToCertificatesVO(CertificatesDTO certificatesDTO);

    WarehouseUserCertificateVO mapToUserCertificateVO(UserCertificateDTO userCertificateDTO);


}
