package com.imile.attendance.warehouse.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.context.RequestInfoHolder;
import com.imile.attendance.enums.ErrorCodeEnum;
import com.imile.attendance.exception.BusinessLogicException;
import com.imile.attendance.hermes.dto.CountryDTO;
import com.imile.attendance.infrastructure.idwork.DefaultIdWorker;
import com.imile.attendance.infrastructure.repository.common.AttendanceDeptService;
import com.imile.attendance.infrastructure.repository.common.CountryService;
import com.imile.attendance.infrastructure.repository.common.dto.AttendanceDept;
import com.imile.attendance.infrastructure.repository.warehouse.dao.WarehouseAttendanceConfigDao;
import com.imile.attendance.infrastructure.repository.warehouse.model.WarehouseAttendanceConfigDO;
import com.imile.attendance.infrastructure.repository.warehouse.param.WarehouseAttendanceConfigParam;
import com.imile.attendance.util.BaseDOUtil;
import com.imile.attendance.util.CommonUtil;
import com.imile.attendance.util.PageUtil;
import com.imile.attendance.warehouse.WarehouseAttendanceConfigService;
import com.imile.attendance.warehouse.param.WarehouseAttendanceConfigQueryParam;
import com.imile.attendance.warehouse.param.WarehouseAttendanceConfigSaveParam;
import com.imile.attendance.warehouse.param.WarehouseAttendanceConfigUpdateStatusParam;
import com.imile.attendance.warehouse.vo.WarehouseAttendanceConfigVO;
import com.imile.common.enums.StatusEnum;
import com.imile.common.exception.BusinessException;
import com.imile.common.page.PaginationResult;
import com.imile.util.BeanUtils;
import com.imile.util.lang.I18nUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.beanutils.ConvertUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 仓内管理规则配置服务
 *
 * <AUTHOR>
 * @since 2024/11/30
 */
@Slf4j
@Service
public class WarehouseAttendanceConfigServiceImpl implements WarehouseAttendanceConfigService {

    @Resource
    private WarehouseAttendanceConfigDao warehouseAttendanceConfigDao;

    @Resource
    private AttendanceDeptService attendanceDeptService;

    @Resource
    private CountryService countryService;

    @Resource
    private DefaultIdWorker defaultIdWorker;


    @Override
    public PaginationResult<WarehouseAttendanceConfigVO> page(WarehouseAttendanceConfigQueryParam param) {

        WarehouseAttendanceConfigParam warehouseAttendanceConfigParam = BeanUtils.convert(param, WarehouseAttendanceConfigParam.class);
        PageInfo<WarehouseAttendanceConfigDO> pageInfo = PageHelper.startPage(param.getCurrentPage(), param.getShowCount(), param.getCount() && param.getShowCount() > 0)
                .doSelectPageInfo(() -> warehouseAttendanceConfigDao.list(warehouseAttendanceConfigParam));

        if (CollectionUtils.isEmpty(pageInfo.getList())) {
            return PaginationResult.get(Collections.emptyList(), param);
        }

        List<WarehouseAttendanceConfigVO> result = pageInfo.getList()
                .stream()
                .map(config -> {
                    WarehouseAttendanceConfigVO configVO = BeanUtils.convert(config, WarehouseAttendanceConfigVO.class);
                    // 查出部门
                    if (StringUtils.isNotEmpty(config.getDeptIds())) {
                        Long[] deptIdArr = (Long[]) ConvertUtils.convert(config.getDeptIds().split(BusinessConstant.DEFAULT_DELIMITER), Long.class);
                        List<AttendanceDept> entDeptList = attendanceDeptService.listByDeptIds(Arrays.asList(deptIdArr));
                        if (RequestInfoHolder.isChinese()) {
                            configVO.setDeptNameList(entDeptList.stream().map(AttendanceDept::getDeptNameCn).collect(Collectors.toList()));
                        } else {
                            configVO.setDeptNameList(entDeptList.stream().map(AttendanceDept::getDeptNameEn).collect(Collectors.toList()));
                        }
                        configVO.setDeptIds(Arrays.asList(deptIdArr));
                    }
                    return configVO;
                })
                .collect(Collectors.toList());

        return PageUtil.getPageResult(result, param, (int) pageInfo.getTotal(), pageInfo.getPages());
    }

    @Override
    public void saveOrUpdate(WarehouseAttendanceConfigSaveParam param) {
        checkSaveOrUpdateParam(param);
        CountryDTO countryDTO = countryService.queryCountry(param.getCountry());
        Date date = CommonUtil.convertDateByTimeZonePlus(countryDTO.getTimeZone(), new Date());
        if (Objects.nonNull(param.getId())) {
            //更新
            WarehouseAttendanceConfigDO exist = warehouseAttendanceConfigDao.selectLatestById(param.getId());
            if (Objects.equals(StatusEnum.DISABLED.getCode(), exist.getStatus())) {
                exist.setCountry(param.getCountry());
                exist.setAttendanceConfigName(param.getAttendanceConfigName());
                exist.setDeptIds(StringUtils.join(param.getDeptIds(), BusinessConstant.DEFAULT_DELIMITER));
                exist.setIsMultipleShifts(param.getIsMultipleShifts());
                exist.setMinute(param.getMinute());
                exist.setIsSegmentedCalculation(param.getIsSegmentedCalculation());
                BaseDOUtil.fillDOUpdate(exist);
                warehouseAttendanceConfigDao.updateById(exist);
                return;
            }
            exist.setIsLatest(BusinessConstant.N);
            exist.setExpireTime(date);
            BaseDOUtil.fillDOUpdate(exist);
            warehouseAttendanceConfigDao.updateById(exist);
        }
        //新增
        WarehouseAttendanceConfigDO warehouseAttendanceConfigDO = BeanUtils.convert(param, WarehouseAttendanceConfigDO.class);
        warehouseAttendanceConfigDO.setId(defaultIdWorker.nextId());
        warehouseAttendanceConfigDO.setStatus(StatusEnum.ACTIVE.getCode());
        warehouseAttendanceConfigDO.setIsLatest(BusinessConstant.Y);
        warehouseAttendanceConfigDO.setEffectTime(date);
        warehouseAttendanceConfigDO.setDeptIds(StringUtils.join(param.getDeptIds(), BusinessConstant.DEFAULT_DELIMITER));
        BaseDOUtil.fillDOInsert(warehouseAttendanceConfigDO);
        warehouseAttendanceConfigDao.saveOrUpdate(warehouseAttendanceConfigDO);
    }


    @Override
    public Boolean updateStatus(WarehouseAttendanceConfigUpdateStatusParam param) {
        BusinessLogicException.checkTrue(Objects.isNull(StatusEnum.getStatusEnum(param.getStatus())), ErrorCodeEnum.PARAM_VALID_ERROR.getCode(), ErrorCodeEnum.PARAM_VALID_ERROR.getDesc(), "status");

        WarehouseAttendanceConfigDO warehouseAttendanceConfigDO = warehouseAttendanceConfigDao.selectLatestById(param.getId());
        if (Objects.isNull(warehouseAttendanceConfigDO)) {
            return Boolean.FALSE;
        }
        warehouseAttendanceConfigDO.setStatus(param.getStatus());
        BaseDOUtil.fillDOUpdate(warehouseAttendanceConfigDO);
        return warehouseAttendanceConfigDao.updateById(warehouseAttendanceConfigDO);
    }

    private void checkSaveOrUpdateParam(WarehouseAttendanceConfigSaveParam param) {
        //查询国家下的所有仓内考勤关联规则
        List<WarehouseAttendanceConfigDO> attendanceConfigDOList = warehouseAttendanceConfigDao.selectByCountry(param.getCountry());
        if (CollectionUtils.isEmpty(attendanceConfigDOList)) {
            return;
        }
        Optional<WarehouseAttendanceConfigDO> warehouseAttendanceConfigDaoOptional = attendanceConfigDOList
                .stream()
                .filter(config -> Objects.equals(config.getAttendanceConfigName(), param.getAttendanceConfigName())
                        && !Objects.equals(config.getId(), param.getId()))
                .findFirst();
        if (warehouseAttendanceConfigDaoOptional.isPresent()) {
            throw BusinessException.get(ErrorCodeEnum.WAREHOUSE_ATTENDANCE_CONFIG_NAME_EXIST.getCode(),
                    I18nUtils.getMessage(ErrorCodeEnum.WAREHOUSE_ATTENDANCE_CONFIG_NAME_EXIST.getDesc()));
        }

        List<AttendanceDept> entDeptDOList = attendanceDeptService.listByDeptIds(param.getDeptIds());
        if (entDeptDOList.size() != param.getDeptIds().size()) {
            throw BusinessException.get(ErrorCodeEnum.DEPT_NOT_EXITS.getCode(), I18nUtils.getMessage(ErrorCodeEnum.DEPT_NOT_EXITS.getDesc()));
        }

        //判断部门是否被其他规则已经使用
        for (WarehouseAttendanceConfigDO configDO : attendanceConfigDOList) {
            List<Long> deptIdList = StringUtils.isBlank(configDO.getDeptIds()) ? new ArrayList<>() : Arrays.asList((Long[]) ConvertUtils.convert(configDO.getDeptIds().split(BusinessConstant.DEFAULT_DELIMITER), Long.class));
            for (Long deptId : deptIdList) {
                if (param.getDeptIds().contains(deptId) && !Objects.equals(configDO.getId(), param.getId())) {
                    throw BusinessException.get(ErrorCodeEnum.DEPT_EXIST_IN_OTHER_WAREHOUSE_ATTENDANCE_CONFIG.getCode(),
                            I18nUtils.getMessage(ErrorCodeEnum.DEPT_EXIST_IN_OTHER_WAREHOUSE_ATTENDANCE_CONFIG.getDesc()));
                }
            }
        }
    }
}
