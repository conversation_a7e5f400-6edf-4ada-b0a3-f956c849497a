package com.imile.attendance.warehouse;

import com.google.common.collect.Lists;
import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.enums.CountryCodeEnum;
import com.imile.attendance.enums.DeptTypeEnum;
import com.imile.attendance.infrastructure.repository.common.AttendanceDeptService;
import com.imile.attendance.infrastructure.repository.common.AttendanceUserService;
import com.imile.attendance.infrastructure.repository.common.CountryService;
import com.imile.attendance.infrastructure.repository.common.dto.AttendanceDept;
import com.imile.attendance.ipep.support.RpcIpepClientSupport;
import com.imile.attendance.recognition.RpcRecognitionClient;
import com.imile.attendance.warehouse.vo.OcVO;
import com.imile.common.enums.StatusEnum;
import com.imile.genesis.api.enums.CertificateTypeEnum;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 仓内考勤基础服务
 *
 * <AUTHOR>
 * @since 2025/1/21
 */
public abstract class WarehouseBaseService {

    @Resource
    protected AttendanceUserService userService;
    @Resource
    protected AttendanceDeptService deptService;
    @Resource
    protected CountryService countryService;
    @Resource
    protected RpcIpepClientSupport ipepIntegration;
    @Resource
    protected RpcRecognitionClient recognitionService;

    /**
     * 过滤加盟网点和非组织树的网点
     *
     * @param deptInfo 部门
     * @return Boolean
     */
    public boolean filterDept(AttendanceDept deptInfo) {
        return !Objects.equals(deptInfo.getType(), DeptTypeEnum.JOIN_STATION.getCode())
                && !Objects.equals(deptInfo.getTopId(), BusinessConstant.LONG_ZERO)
                && Objects.equals(deptInfo.getStatus(), StatusEnum.ACTIVE.getCode());
    }

    /**
     * 网点数据转换
     */
    public List<OcVO> convertOcVOList(List<AttendanceDept> deptInfoList) {
        if (CollectionUtils.isEmpty(deptInfoList)) {
            return Collections.emptyList();
        }
        return deptInfoList.stream().map(dept -> {
            OcVO ocVO = new OcVO();
            ocVO.setOcId(dept.getId());
            ocVO.setOcCode(dept.getDeptCode());
            ocVO.setOcName(dept.getDeptNameEn());
            ocVO.setCountry(dept.getCountry());
            return ocVO;
        }).collect(Collectors.toList());
    }

    /**
     * 查询部门名称
     */
    public Map<Long, String> getOcMap(List<Long> idList) {
        List<AttendanceDept> collect = deptService.selectDeptByIds(idList);
        return collect.stream().
                collect(Collectors.toMap(AttendanceDept::getId
                        , AttendanceDept::getDeptNameEn, (v1, v2) -> v1));
    }

    /**
     * 获取证件类型
     * 用于证件匹配
     *
     * @param country 国家三字码
     * @return 证件类型
     */
    public String getCertificateType(String country) {
        if (StringUtils.equalsAnyIgnoreCase(CountryCodeEnum.MEX.getCode(), country)) {
            return CertificateTypeEnum.INE.getCode();
        }
        if (StringUtils.equalsAnyIgnoreCase(CountryCodeEnum.BRA.getCode(), country)) {
            return CertificateTypeEnum.LABOUR_CARD_NO.getCode();
        }
        if (Lists.newArrayList(CountryCodeEnum.COL.getCode(), CountryCodeEnum.PER.getCode()).contains(country)) {
            return CertificateTypeEnum.ID_CARD.getCode();
        }
        return CertificateTypeEnum.INE.getCode();
    }

    /**
     * 获取证件类型
     * 用于列表展示
     *
     * @param country 国家三字码
     * @return 证件类型
     */
    public String getShowCertificateType(String country) {
        if (StringUtils.equalsAnyIgnoreCase(CountryCodeEnum.MEX.getCode(), country)) {
            return CertificateTypeEnum.INE.getCode();
        }
        return CertificateTypeEnum.ID_CARD.getCode();
    }
}
