package com.imile.attendance.warehouse.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.imile.attendance.annon.HyperLink;
import com.imile.attendance.annon.WithDict;
import lombok.Data;

import java.util.Date;

/**
 * @description:
 * @author: taokang
 * @createDate: 2024/12/11 11:37
 * @version: 1.0
 */
@Data
public class WarehouseUserCertificateVO {

    private Long id;

    /**
     * 证件类型编码
     */
    @WithDict(typeCode = "certificateTypeCode", ref = "certificateTypeCodeDesc")
    private String certificateTypeCode;

    /**
     * 证件类型描述
     */
    private String certificateTypeCodeDesc;

    /**
     * 证件号码
     */
    private String certificateCode;

    /**
     * 生效日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date certificateReceiptDate;

    /**
     * 失效日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date certificateExpireDate;

    /**
     * 证件正面照路径
     */
    @HyperLink(ref = "certificateFrontUrl")
    private String certificateFrontPath;

    /**
     * 证件正面照链接
     */
    private String certificateFrontUrl;

    /**
     * 证件背面照路径
     */
    @HyperLink(ref = "certificateBackUrl")
    private String certificateBackPath;

    /**
     * 证件背面照链接
     */
    private String certificateBackUrl;

}
