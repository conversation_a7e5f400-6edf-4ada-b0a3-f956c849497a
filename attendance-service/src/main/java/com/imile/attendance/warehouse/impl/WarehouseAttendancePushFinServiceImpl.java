package com.imile.attendance.warehouse.impl;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.imile.attendance.calendar.CalendarConfigService;
import com.imile.attendance.clock.query.WarehouseAttendanceConfigQuery;
import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.enums.warehouse.WarehouseAttendanceStatusEnum;
import com.imile.attendance.fms.RpcFmsClient;
import com.imile.attendance.infrastructure.mq.MqSend;
import com.imile.attendance.infrastructure.repository.abnormal.adapter.WarehouseDetailAdapter;
import com.imile.attendance.infrastructure.repository.common.AttendanceDeptService;
import com.imile.attendance.infrastructure.repository.common.dto.AttendanceDept;
import com.imile.attendance.infrastructure.repository.warehouse.dao.WarehouseDetailDao;
import com.imile.attendance.infrastructure.repository.warehouse.model.WarehouseDetailDO;
import com.imile.attendance.util.BaseDOUtil;
import com.imile.attendance.util.DateFormatterUtil;
import com.imile.attendance.warehouse.WarehouseAttendancePushFinService;
import com.imile.attendance.warehouse.dto.WarehouseAttendancePushFinDTO;
import com.imile.fms.api.bill.dto.BillingRecordDTO;
import com.imile.fms.api.bill.dto.FeeBillingRecordQuery;
import com.imile.fms.api.bill.enums.BusinessNoTypeApiEnum;
import com.imile.fms.api.bill.enums.SettlementObjectApiEnum;
import com.imile.genesis.api.enums.EmploymentTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 仓内考勤数据推送财务系统
 *
 * <AUTHOR>
 * @since 2024/9/12
 */
@Slf4j
@Service
public class WarehouseAttendancePushFinServiceImpl implements WarehouseAttendancePushFinService {

    @Resource
    private RpcFmsClient fmsClient;

    @Resource
    private WarehouseDetailDao warehouseDetailDao;

    @Resource
    private WarehouseDetailAdapter warehouseDetailAdapter;

    @Resource
    private AttendanceDeptService attendanceDeptService;

    @Resource
    private CalendarConfigService calendarConfigService;

    @Resource
    private MqSend mqSend;

    @Value("${rocket.mq.push.waukeen.attendance.topic:SAAS-WAUKEEN-BILL-TEST}")
    private String pushFinAttendanceTopic;

    @Async("bizTaskThreadPool")
    @Override
    public void asyncPushFin(List<Long> warehouseDetailIds, String stateType) {
        if (CollectionUtils.isEmpty(warehouseDetailIds)) {
            return;
        }
        List<WarehouseDetailDO> warehouseDetailList = warehouseDetailDao.selectByIds(warehouseDetailIds);
        if (CollectionUtils.isEmpty(warehouseDetailList)) {
            return;
        }

        //过滤得到所有劳务派遣且有考勤时长的记录
        warehouseDetailList = warehouseDetailList.stream()
                .filter(warehouse -> Objects.equals(EmploymentTypeEnum.OS_FIXED_SALARY.getCode(), warehouse.getEmployeeType())
                        && Lists.newArrayList(WarehouseAttendanceStatusEnum.NORMAL.getCode(), WarehouseAttendanceStatusEnum.ABNORMAL.getCode()).contains(warehouse.getAttendanceStatus()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(warehouseDetailList)) {
            log.info("无劳务派遣仓内考勤推送财务记录");
            return;
        }

        List<Long> ocIdList = warehouseDetailList.stream()
                .map(WarehouseDetailDO::getOcId)
                .distinct()
                .collect(Collectors.toList());
        Map<Long, String> ocCodeMap = attendanceDeptService.listByDeptIds(ocIdList).stream().collect(Collectors.toMap(AttendanceDept::getId, AttendanceDept::getOcCode));


        Date now = new Date();
        warehouseDetailList.forEach(warehouse -> {
            String warehouseAttendanceCode = buildWarehouseAttendanceCode(warehouse, ocCodeMap.get(warehouse.getOcId()));

            //查询账单是否已锁定
            FeeBillingRecordQuery query = new FeeBillingRecordQuery();
            query.setBusinessNo(warehouseAttendanceCode);
            query.setSourceSettlementType(SettlementObjectApiEnum.SETTLEMENT_CENTER.getCode());
            query.setTargetSettlementType(SettlementObjectApiEnum.VENDOR.getCode());
            query.setBusinessNoType(BusinessNoTypeApiEnum.LABOUR_PUNCH_NO.getCode());
            List<BillingRecordDTO> billingRecords = fmsClient.queryFeeBillingRecords(query);
            if (CollectionUtils.isNotEmpty(billingRecords)) {
                boolean immutable = billingRecords.stream().anyMatch(BillingRecordDTO::getImmutable);
                if (immutable) {
                    log.info("仓内考勤流水号: {} ,推送财务账单已锁定", warehouseAttendanceCode);
                    return;
                }
            }
            WarehouseAttendancePushFinDTO.AttendanceRecord attendanceRecord = new WarehouseAttendancePushFinDTO.AttendanceRecord();
            attendanceRecord.setWarehouseAttendanceCode(warehouseAttendanceCode);
            attendanceRecord.setBusinessCountry(warehouse.getCountry());
            attendanceRecord.setOcCode(ocCodeMap.get(warehouse.getOcId()));
            attendanceRecord.setVendorCode(warehouse.getVendorCode());
            attendanceRecord.setUserCode(warehouse.getUserCode());
            attendanceRecord.setClassesType(warehouse.getClassesType());
            attendanceRecord.setClassesId(warehouse.getClassesId());
            attendanceRecord.setClassesName(warehouse.getClassesName());
            attendanceRecord.setEmploymentForm(warehouse.getEmploymentForm());
            attendanceRecord.setSalaryDate(warehouse.getSalaryDate());
            if (Objects.equals(warehouse.getWarehouseDate().getTime(), warehouse.getSalaryDate().getTime())) {
                attendanceRecord.setAttendanceType(warehouse.getAttendanceType());
            } else {
                attendanceRecord.setAttendanceType(getCurrentDayType(warehouse.getSalaryDate(), warehouse));
            }

            if (warehouse.getRequiredAttendanceTime().compareTo(BigDecimal.ZERO) > 0) {
                if (warehouse.getActualAttendanceTime().compareTo(BigDecimal.ZERO) < 1) {
                    attendanceRecord.setActualAttendanceDay(BigDecimal.ZERO);
                } else if (warehouse.getActualAttendanceTime().compareTo(warehouse.getRequiredAttendanceTime()) > -1) {
                    attendanceRecord.setActualAttendanceDay(BigDecimal.ONE);
                } else {
                    attendanceRecord.setActualAttendanceDay(warehouse.getActualAttendanceTime().divide(warehouse.getRequiredAttendanceTime(), 3, RoundingMode.HALF_UP));
                }
            }

            if (warehouse.getLegalWorkingHours().compareTo(BigDecimal.ZERO) > 0) {
                if (warehouse.getActualWorkingHours().compareTo(BigDecimal.ZERO) < 1) {
                    attendanceRecord.setActualWorkingDay(BigDecimal.ZERO);
                } else if (warehouse.getActualWorkingHours().compareTo(warehouse.getLegalWorkingHours()) > -1) {
                    attendanceRecord.setActualWorkingDay(BigDecimal.ONE);
                } else {
                    attendanceRecord.setActualWorkingDay(warehouse.getActualWorkingHours().divide(warehouse.getLegalWorkingHours(), 3, RoundingMode.HALF_UP));
                }
            }

            attendanceRecord.setActualAttendanceTime(warehouse.getActualAttendanceTime());
            attendanceRecord.setActualWorkingHours(warehouse.getActualWorkingHours());
            attendanceRecord.setOvertimeHours(BigDecimal.ZERO);
            attendanceRecord.setWeek(getWeek(warehouse.getSalaryDate()));

            if (StringUtils.isNotEmpty(warehouse.getWarehouseAttendanceCode()) && !Objects.equals(warehouse.getWarehouseAttendanceCode(), warehouseAttendanceCode)) {
                attendanceRecord.setExpiredWarehouseAttendanceCode(warehouse.getWarehouseAttendanceCode());
                attendanceRecord.setLastSalaryDate(warehouse.getSalaryDate());
            }
            warehouse.setWarehouseAttendanceCode(warehouseAttendanceCode);
            BaseDOUtil.fillDOUpdateByUserOrSystem(warehouse);

            WarehouseAttendancePushFinDTO attendancePushFin = new WarehouseAttendancePushFinDTO();
            attendancePushFin.setStateType(stateType);
            attendancePushFin.setStateDate(now);
            attendancePushFin.setPayload(attendanceRecord);

            log.info("推送财务仓内考勤数据：{}", JSON.toJSONString(attendancePushFin));

            mqSend.send(pushFinAttendanceTopic, stateType, attendanceRecord.getWarehouseAttendanceCode(), JSON.toJSONString(attendancePushFin));
        });

        warehouseDetailAdapter.saveOrUpdateBatch(warehouseDetailList);
    }

    private String buildWarehouseAttendanceCode(WarehouseDetailDO warehouseDetailDO, String ocCode) {
        return DateUtil.format(warehouseDetailDO.getWarehouseDate(), DateFormatterUtil.FORMAT_YYYYMMDD) +
                BusinessConstant.DEFAULT_HYPHEN + "0" + warehouseDetailDO.getClassesType() +
                BusinessConstant.DEFAULT_HYPHEN + warehouseDetailDO.getClassesId() +
                BusinessConstant.DEFAULT_HYPHEN + ocCode +
                BusinessConstant.DEFAULT_HYPHEN + warehouseDetailDO.getVendorCode() +
                BusinessConstant.DEFAULT_HYPHEN + warehouseDetailDO.getUserCode();
    }

    private Integer getWeek(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        return calendar.get(Calendar.DAY_OF_WEEK) - 1;
    }

    private String getCurrentDayType(Date attendanceTime, WarehouseDetailDO warehouseDetailDO) {
        WarehouseAttendanceConfigQuery query = new WarehouseAttendanceConfigQuery();
        query.setUserId(warehouseDetailDO.getUserId());
        query.setLocationCountry(warehouseDetailDO.getCountry());
        query.setNow(attendanceTime);
        return calendarConfigService.selectAttendanceDayType(query);
    }

}
