package com.imile.attendance.warehouse.vo;

import com.imile.attendance.annon.HyperLink;
import com.imile.attendance.infrastructure.repository.warehouse.param.WarehouseUserCertificateParam;
import lombok.Data;

import java.util.List;

/**
 * @description:
 * @author: taokang
 * @createDate: 2024/12/10 20:56
 * @version: 1.0
 */
@Data
public class SubmitCheckVO {

    private String userCode;

    /**
     * 证件正面照路径
     */
    @HyperLink(ref = "certificateFrontUrl")
    private String certificateFrontPath;

    /**
     * 证件正面照链接
     */
    private String certificateFrontUrl;

    private List<WarehouseUserCertificateParam> certificateList;
}
