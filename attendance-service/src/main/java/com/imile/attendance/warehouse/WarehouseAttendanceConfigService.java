package com.imile.attendance.warehouse;

import com.imile.attendance.warehouse.param.WarehouseAttendanceConfigQueryParam;
import com.imile.attendance.warehouse.param.WarehouseAttendanceConfigSaveParam;
import com.imile.attendance.warehouse.param.WarehouseAttendanceConfigUpdateStatusParam;
import com.imile.attendance.warehouse.vo.WarehouseAttendanceConfigVO;
import com.imile.common.page.PaginationResult;
/**
 * <AUTHOR>
 * @since 2024/11/30
 */
public interface WarehouseAttendanceConfigService {

    PaginationResult<WarehouseAttendanceConfigVO> page(WarehouseAttendanceConfigQueryParam param);

    void saveOrUpdate(WarehouseAttendanceConfigSaveParam param);

    Boolean updateStatus(WarehouseAttendanceConfigUpdateStatusParam param);
}
