package com.imile.attendance.warehouse.dto;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 仓内考勤推送财务
 *
 * <AUTHOR>
 * @since 2024/9/21
 */
@Data
public class WarehouseAttendancePushFinDTO implements Serializable {

    private String isCancel = "N";

    /**
     * 推送时间
     */
    private Date stateDate;

    /**
     * 推送事件类型
     * OffWorkPunchIn : 下班打卡考勤正常推送事件
     * OffWorkPunchReissue : 考勤异常处理后补推事件
     */
    private String stateType;


    private AttendanceRecord payload;

    @Data
    public static class AttendanceRecord {

        /**
         * 考勤流水号
         */
        private String warehouseAttendanceCode;

        /**
         * 业务国家
         */
        private String businessCountry;

        /**
         * 工作网点编码
         */
        private String ocCode;

        /**
         * 工作供应商编码
         */
        private String vendorCode;

        /**
         * 人员编码
         */
        private String userCode;

        /**
         * 算薪日
         */
        private Date salaryDate;

        /**
         * 节假日标识 P:上班 WEEKEND:周末  HOLIDAY:节假日
         */
        private String attendanceType;

        /**
         * 出勤人天
         */
        private BigDecimal actualAttendanceDay;

        /**
         * 出勤小时
         */
        private BigDecimal actualAttendanceTime;

        /**
         * 实际工作人天
         */
        private BigDecimal actualWorkingDay;

        /**
         * 实际工作工时
         */
        private BigDecimal actualWorkingHours;

        /**
         * 加班小时
         */
        private BigDecimal overtimeHours;

        /**
         * 星期几
         */
        private Integer week;

        /**
         * 所属部门 默认仓内
         */
        private String dept = "warehouse";

        /**
         * 班次类型 1:白班 2晚班
         */
        private Integer classesType;

        /**
         * 班次id
         */
        private Long classesId;

        /**
         * 班次名称
         */
        private String classesName;

        /**
         * 已失效的考勤流水号
         */
        private String expiredWarehouseAttendanceCode;

        /**
         * 最近发薪日时间
         */
        private Date lastSalaryDate;

        /**
         * 用工形式
         */
        private String employmentForm;
    }
}
