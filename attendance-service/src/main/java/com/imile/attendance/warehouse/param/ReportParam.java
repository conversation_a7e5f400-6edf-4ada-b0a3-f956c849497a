package com.imile.attendance.warehouse.param;

import com.imile.common.query.BaseQuery;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @project hrms
 * @description 仓内报表搜索条件
 * @date 2024/6/29 20:25:44
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ReportParam extends BaseQuery {

    /**
     * 国家List
     */
    private String country;

    /**
     * 城市
     */
    private List<String> cityList;

    /**
     * 网点ID
     */
    private Long ocId;

    /**
     * 网点idList
     */
    private List<Long> ocIdList;

    /**
     * 供应商idList
     */
    private List<Long> vendorIdList;

    /**
     * 供应商编码List
     */
    private List<String> vendorCodeList;


    /**
     * 开始日期
     */
    @NotNull(message = "startDate cannot be empty")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date startDate;

    /**
     * 结束日期
     */
    @NotNull(message = "endDate cannot be empty")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date endDate;

    /**
     * 开始时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    /**
     * 结束时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    /**
     * 1正常 2异常
     */
    private Integer result;

    /**
     * 搜索人员/工号关键词
     */
    private String searchUserKey;

    /**
     * 用工类型列表
     */
    private List<String> employeeTypeList;

    /**
     * 考勤流水号
     */
    private String warehouseAttendanceCode;

    /**
     * 班次ID
     */
    private Long classId;

    /**
     * 班次确认状态
     */
    private List<Integer> confirmStatusList;

    /**
     * 打卡状态（1:正常 2:异常）
     */
    private Integer punchStatus;

    /**
     * 搜索用户ID列表
     */
    private List<Long> searchKeyUserIdList;

    /**
     * 用工形式
     */
    private String employmentForm;
}
