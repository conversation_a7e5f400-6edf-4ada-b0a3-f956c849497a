package com.imile.attendance.warehouse.vo;

import com.imile.attendance.annon.HyperLink;
import lombok.Data;

/**
 * <AUTHOR>
 * @project hrms
 * @description 待办任务列表
 * @date 2024/6/29 19:22:54
 */
@Data
public class RecordListVO {
    /**
     * id
     */
    private Long id;



    /**
     * 用户id
     */
    private Long userId;

    /**
     * 用户编码
     */
    private String userCode;

    /**
     * 用户名称
     */
    private String userName;

    /**
     * 人脸照key
     */
    @HyperLink(ref = "facePhoto")
    private String facePhotoKey;

    /**
     * 人脸照地址
     */
    private String facePhoto;

    /**
     * 供应商id
     */
    private Long vendorId;

    /**
     * 供应商编码
     */
    private String vendorCode;

    /**
     * 供应商名称
     */
    private String vendorName;


    /**
     * 是否进行过人脸识别
     */
    private Boolean haveRecognition;

    /**
     * 是否进行过人脸录入
     */
    private Boolean haveFace;

    /**
     * 用工类型
     */
    private String employeeType;

}
