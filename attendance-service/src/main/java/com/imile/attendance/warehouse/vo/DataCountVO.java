package com.imile.attendance.warehouse.vo;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/10/15
 */
@Data
public class DataCountVO {
    /**
     * 入仓
     */
    private Integer clockInCount = 0;

    /**
     * 入仓字典值编码
     */
    private String clockInDictCode = "in";

    /**
     * 已出仓
     */
    private Integer clockOutCount = 0;

    /**
     * 已出仓字典值编码
     */
    private String clockOutDictCode = "out";

    /**
     * 待出仓
     */
    private Integer waitClockOutCount = 0;

    /**
     * 待出仓字典值编码
     */
    private String waitClockOutDictCode = "waitOut";

    /**
     * 迟到
     */
    private Integer lateCount = 0;

    /**
     * 迟到字典值编码
     */
    private String lateDictCode = "LATE";

    /**
     * 早退
     */
    private Integer leaveEarlyCount = 0;

    /**
     * 早退字典值编码
     */
    private String leaveEarlyDictCode = "LEAVE_EARLY";

    /**
     * 上班缺卡
     */
    private Integer clockInLackCount = 0;

    /**
     * 上班缺卡字典值编码
     */
    private String clockInLackDictCode = "BEFORE_OFFICE_LACK";

    /**
     * 下班缺卡
     */
    private Integer clockOutLackCount = 0;

    /**
     * 下班缺卡字典值编码
     */
    private String clockOutLackDictCode = "AFTER_OFFICE_LACK";

    /**
     * 时长异常
     */
    private Integer abnormalDurationCount = 0;

    /**
     * 时长异常字典值编码
     */
    private String abnormalDurationDictCode = "ABNORMAL_DURATION";

    /**
     * 用工类型列表
     */
    private List<String> employeeTypeList;

    /**
     * 总异常数量
     */
    private Integer totalAbnormalCount = 0;

    /**
     * 黑名单人员数量
     */
    private Integer blackListCount = 0;

    /**
     * 计算总异常数量
     */
    public void calculateTotalAbnormalCount() {
        this.setTotalAbnormalCount(this.getLateCount() + this.getLeaveEarlyCount() + this.getClockInLackCount() + this.getClockOutLackCount() + this.getAbnormalDurationCount());
    }
}
