package com.imile.attendance.warehouse.param;

import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * <AUTHOR>
 * @since 2024/9/27
 */
@Data
public class GetOcListByConditionParam {

    /**
     * 城市
     */
    private String city;

    /**
     * 供应商编码
     */
    @NotNull(message = "vendorCode cannot be empty")
    private String vendorCode;

    /**
     * 开始日期
     */
    @NotNull(message = "startDate cannot be empty")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date startDate;

    /**
     * 结束日期
     */
    @NotNull(message = "endDate cannot be empty")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date endDate;

}
