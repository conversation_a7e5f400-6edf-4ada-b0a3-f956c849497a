package com.imile.attendance.warehouse.vo;

import lombok.Data;

/**
 * <AUTHOR>
 * @since 2024/12/6
 */
@Data
public class GetVendorConfirmContentResultVO {

    /**
     * 实际出勤人数
     */
    private Integer punchCount = 0;

    /**
     * 出仓未打卡人数
     */
    private Integer outWithoutPunchCount = 0;

    /**
     * 入仓未打卡人数
     */
    private Integer inWithoutPunchCount = 0;

    /**
     * 迟到人数
     */
    private Integer latePunchCount = 0;

    /**
     * 早退人数
     */
    private Integer leaveEarlyPunchCount = 0;

    /**
     * 时长异常人数
     */
    private Integer abnormalDurationNum = 0;

    /**
     * 是否分段计算工时
     */
    private Boolean isSegmentedCalculation = Boolean.FALSE;

}
