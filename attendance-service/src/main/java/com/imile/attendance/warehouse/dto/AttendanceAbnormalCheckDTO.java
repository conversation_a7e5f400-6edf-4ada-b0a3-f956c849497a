package com.imile.attendance.warehouse.dto;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/8/29
 */
@Data
public class AttendanceAbnormalCheckDTO {

    private Boolean existException;

    private List<AttendanceAbnormalRecord> attendanceAbnormalRecords;

    @Data
    public static class AttendanceAbnormalRecord {
        /**
         * 异常id
         */
        private Long abnormalId;

        /**
         * 异常类型
         */
        private String abnormalType;

        /**
         * 处理状态 0:待处理 1:已处理 2:确认异常
         */
        private Integer status;

    }
}
