package com.imile.attendance.warehouse.impl;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import com.aliyun.opensearch.sdk.dependencies.com.google.common.collect.Lists;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Maps;
import com.imile.attendance.apollo.AttendanceProperties;
import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.context.RequestInfoHolder;
import com.imile.attendance.enums.CountryCodeEnum;
import com.imile.attendance.enums.EmploymentTypeEnum;
import com.imile.attendance.enums.abnormal.AbnormalAttendanceStatusEnum;
import com.imile.attendance.enums.abnormal.AbnormalOperationTypeEnum;
import com.imile.attendance.enums.abnormal.AttendanceAbnormalTypeEnum;
import com.imile.attendance.enums.form.ApplicationFormAttrKeyEnum;
import com.imile.attendance.enums.form.FormStatusEnum;
import com.imile.attendance.enums.form.FormTypeEnum;
import com.imile.attendance.enums.warehouse.WarehouseAbnormalStatusEnum;
import com.imile.attendance.enums.warehouse.WarehouseAttendanceStatusEnum;
import com.imile.attendance.enums.warehouse.WarehouseStatusEnum;
import com.imile.attendance.enums.warehouse.WarehouseTypeEnum;
import com.imile.attendance.gensis.support.RpcUserCertificateSupport;
import com.imile.attendance.hermes.dto.CountryDTO;
import com.imile.attendance.infrastructure.repository.abnormal.dao.EmployeeAbnormalAttendanceDao;
import com.imile.attendance.infrastructure.repository.abnormal.dao.EmployeeAbnormalOperationRecordDao;
import com.imile.attendance.infrastructure.repository.abnormal.model.EmployeeAbnormalAttendanceDO;
import com.imile.attendance.infrastructure.repository.abnormal.model.EmployeeAbnormalOperationRecordDO;
import com.imile.attendance.infrastructure.repository.common.CountryService;
import com.imile.attendance.infrastructure.repository.common.ZoneService;
import com.imile.attendance.infrastructure.repository.common.dto.AttendanceDept;
import com.imile.attendance.infrastructure.repository.employee.dao.UserInfoDao;
import com.imile.attendance.infrastructure.repository.employee.modle.UserInfoDO;
import com.imile.attendance.infrastructure.repository.form.dao.AttendanceFormAttrDao;
import com.imile.attendance.infrastructure.repository.form.dao.AttendanceFormDao;
import com.imile.attendance.infrastructure.repository.form.model.AttendanceFormAttrDO;
import com.imile.attendance.infrastructure.repository.form.model.AttendanceFormDO;
import com.imile.attendance.infrastructure.repository.form.query.ApplicationFormQuery;
import com.imile.attendance.infrastructure.repository.warehouse.dao.WarehouseAttendanceConfigDao;
import com.imile.attendance.infrastructure.repository.warehouse.dao.WarehouseBlackListDao;
import com.imile.attendance.infrastructure.repository.warehouse.dao.WarehouseDetailAbnormalDao;
import com.imile.attendance.infrastructure.repository.warehouse.dao.WarehouseDetailDao;
import com.imile.attendance.infrastructure.repository.warehouse.dao.WarehouseFaceRecordDao;
import com.imile.attendance.infrastructure.repository.warehouse.dao.WarehouseRecordDao;
import com.imile.attendance.infrastructure.repository.warehouse.dao.WarehouseVendorClassesConfirmDao;
import com.imile.attendance.infrastructure.repository.warehouse.model.WarehouseAttendanceConfigDO;
import com.imile.attendance.infrastructure.repository.warehouse.model.WarehouseBlackListDO;
import com.imile.attendance.infrastructure.repository.warehouse.model.WarehouseDetailAbnormalDO;
import com.imile.attendance.infrastructure.repository.warehouse.model.WarehouseDetailDO;
import com.imile.attendance.infrastructure.repository.warehouse.model.WarehouseFaceRecordDO;
import com.imile.attendance.infrastructure.repository.warehouse.model.WarehouseRecordDO;
import com.imile.attendance.infrastructure.repository.warehouse.model.WarehouseVendorClassesConfirmDO;
import com.imile.attendance.infrastructure.repository.warehouse.param.WarehouseRecordParam;
import com.imile.attendance.infrastructure.repository.warehouse.param.WarehouseVendorClassesConfirmParam;
import com.imile.attendance.infrastructure.repository.warehouse.query.WarehouseDetailQuery;
import com.imile.attendance.ipep.dto.OssApiVo;
import com.imile.attendance.permission.WpmPermissionService;
import com.imile.attendance.util.BusinessFieldUtils;
import com.imile.attendance.util.CommonUtil;
import com.imile.attendance.util.DateFormatterUtil;
import com.imile.attendance.util.DateHelper;
import com.imile.attendance.util.PageUtil;
import com.imile.attendance.warehouse.WarehouseBaseService;
import com.imile.attendance.warehouse.WarehouseOcService;
import com.imile.attendance.warehouse.WarehouseReadService;
import com.imile.attendance.warehouse.WarehouseSupplierService;
import com.imile.attendance.warehouse.param.GetVendorConfirmContentParam;
import com.imile.attendance.warehouse.param.GetVendorListByOcListParam;
import com.imile.attendance.warehouse.param.NoBingShiftReportParam;
import com.imile.attendance.warehouse.param.ReportParam;
import com.imile.attendance.warehouse.param.SimpleReportParam;
import com.imile.attendance.warehouse.param.StatisticVendorParam;
import com.imile.attendance.warehouse.param.WpmDataStatisticsParam;
import com.imile.attendance.warehouse.vo.BusZoneListVO;
import com.imile.attendance.warehouse.vo.DataCountVO;
import com.imile.attendance.warehouse.vo.DataStatisticsBlackListVO;
import com.imile.attendance.warehouse.vo.DataStatisticsDetailsVO;
import com.imile.attendance.warehouse.vo.DataStatisticsVO;
import com.imile.attendance.warehouse.vo.DateDetailReportVO;
import com.imile.attendance.warehouse.vo.DateReportSimpleVO;
import com.imile.attendance.warehouse.vo.DateReportVO;
import com.imile.attendance.warehouse.vo.GetVendorConfirmContentResultVO;
import com.imile.attendance.warehouse.vo.MonthReportVO;
import com.imile.attendance.warehouse.vo.OcVO;
import com.imile.attendance.warehouse.vo.StatisticsVendorResultVO;
import com.imile.attendance.warehouse.vo.StatisticsVendorUserResultVO;
import com.imile.attendance.warehouse.vo.VendorClassesConfirmVO;
import com.imile.attendance.warehouse.vo.VendorDataCountVO;
import com.imile.attendance.warehouse.vo.VendorVO;
import com.imile.attendance.warehouse.vo.WarehouseRecordVO;
import com.imile.common.enums.IsDeleteEnum;
import com.imile.common.page.PaginationResult;
import com.imile.genesis.api.enums.CertificateTypeEnum;
import com.imile.genesis.api.model.result.user.UserCertificateDTO;
import com.imile.hermes.business.dto.BusZoneListDTO;
import com.imile.recognition.api.face.model.dto.FaceDecryptDTO;
import com.imile.recognition.api.face.model.dto.UserFacePhotoDTO;
import com.imile.recognition.api.face.model.param.FaceDecryptApiParam;
import com.imile.util.BeanUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.BiConsumer;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 仓内考勤读服务
 *
 * <AUTHOR>
 * @since 2025/1/21
 */
@Slf4j
@Service
public class WarehouseReadServiceImpl extends WarehouseBaseService implements WarehouseReadService {

    @Resource
    private ZoneService zoneService;

    @Resource
    private CountryService countryService;

    @Resource
    private WarehouseOcService warehouseOcService;

    @Resource
    private WpmPermissionService wpmPermissionService;

    @Resource
    private WarehouseSupplierService warehouseSupplierService;

    @Resource
    private WarehouseDetailDao warehouseDetailDao;

    @Resource
    private WarehouseRecordDao warehouseRecordDao;

    @Resource
    private WarehouseDetailAbnormalDao warehouseDetailAbnormalDao;

    @Resource
    private WarehouseFaceRecordDao faceRecordDao;

    @Resource
    private AttendanceFormDao attendanceFormDao;

    @Resource
    private AttendanceFormAttrDao attendanceFormAttrDao;

    @Resource
    private EmployeeAbnormalAttendanceDao abnormalAttendanceDao;

    @Resource
    private EmployeeAbnormalOperationRecordDao abnormalOperationRecordDao;

    @Resource
    private WarehouseAttendanceConfigDao warehouseAttendanceConfigDao;

    @Resource
    private WarehouseVendorClassesConfirmDao warehouseVendorClassesConfirmDao;

    @Resource
    private WarehouseBlackListDao warehouseBlackListDao;

    @Resource
    private RpcUserCertificateSupport userCertificateSupport;

    @Resource
    private AttendanceProperties attendanceProperties;

    @Resource
    private UserInfoDao userInfoDao;


    @Override
    public List<BusZoneListVO> getCountryList() {
        List<AttendanceDept> attendanceDeptList = wpmPermissionService.getAuthDeptList(RequestInfoHolder.getUserCode(), null);
        if (CollectionUtils.isEmpty(attendanceDeptList)) {
            return Collections.emptyList();
        }
        List<String> countryList = attendanceDeptList
                .stream()
                .map(AttendanceDept::getCountry)
                .filter(StringUtils::isNotBlank)
                .distinct()
                .collect(Collectors.toList());
        List<BusZoneListDTO> busZoneListDTOS = zoneService.getBusZoneByRegionLevel(BusinessConstant.COUNTRY_LEVEL);
        if (CollectionUtils.isEmpty(busZoneListDTOS)) {
            return Collections.emptyList();
        }
        List<BusZoneListDTO> busZoneList = busZoneListDTOS.stream().filter(busZone -> countryList.contains(busZone.getZoneName())).collect(Collectors.toList());

        return BeanUtils.convert(BusZoneListVO.class, busZoneList);
    }

    @Override
    public Integer noBindShiftCount() {
        List<OcVO> ocVOList = warehouseOcService.getDeptAuthVOList(RequestInfoHolder.getUserCode());
        if (CollectionUtils.isEmpty(ocVOList)) {
            return BusinessConstant.ZERO;
        }
        return warehouseDetailDao.noBindShiftCount(ocVOList.stream().map(OcVO::getOcId).distinct().collect(Collectors.toList()));
    }

    /**
     * 简易日报列表（不分页）
     */
    @Override
    public List<DateReportSimpleVO> simpleDateReport(SimpleReportParam param) {
        if (Objects.isNull(param.getOcId())) {
            return Collections.emptyList();
        }

        //用户搜索 截取前200匹配用户
        List<Long> searchKeyUserIdList = new ArrayList<>();
        boolean searchFlag = false;
        if (StringUtils.isNotBlank(param.getSearchUserKey())) {
            List<UserInfoDO> userInfoDOList = userInfoDao.selectBySearchCode(param.getSearchUserKey());
            searchKeyUserIdList = userInfoDOList.stream().map(UserInfoDO::getId).distinct().collect(Collectors.toList());
            searchFlag = true;
        }

        if (searchFlag && CollectionUtils.isEmpty(searchKeyUserIdList)) {
            return Collections.emptyList();
        }

        //供应商级联网点处理
        if (CollectionUtils.isEmpty(param.getVendorCodeList())) {
            List<VendorVO> vendorList = warehouseSupplierService.getVendorList(Collections.singletonList(param.getOcId()));
            if (CollectionUtils.isNotEmpty(vendorList)) {
                param.setVendorCodeList(vendorList.stream().map(VendorVO::getVendorCode).distinct().collect(Collectors.toList()));
            }
        }

        WarehouseDetailQuery warehouseDetailQuery = new WarehouseDetailQuery();
        warehouseDetailQuery.setOcId(param.getOcId());
        warehouseDetailQuery.setClassId(param.getClassId());
        warehouseDetailQuery.setUserIdList(searchKeyUserIdList);
        warehouseDetailQuery.setStartTime(DateUtil.parseDate(DateUtil.formatDate(param.getStartDate())));
        warehouseDetailQuery.setEndTime(DateUtil.parseDate(DateUtil.formatDate(param.getEndDate())));
        warehouseDetailQuery.setEmployeeTypeList(Objects.equals(BusinessConstant.SUPPLIER, param.getType()) ? Collections.singletonList(EmploymentTypeEnum.OS_FIXED_SALARY.getCode()) : null);
        warehouseDetailQuery.setVendorCodeList(param.getVendorCodeList());

        List<WarehouseDetailDO> warehouseDetailList = warehouseDetailDao.selectByCondition(warehouseDetailQuery);
        if (CollectionUtils.isEmpty(warehouseDetailList)) {
            return Collections.emptyList();
        }
        return convertSimpleDateReport(warehouseDetailList);
    }

    @Override
    public PaginationResult<DateReportVO> dateReport(ReportParam param) {
        if (!buildReportQuery(param)) {
            return PaginationResult.get(Collections.emptyList(), param);
        }

        WarehouseDetailQuery query = new WarehouseDetailQuery();
        query.setCountry(param.getCountry());
        query.setCityList(param.getCityList());
        query.setOcIdList(param.getOcIdList());
        query.setVendorIdList(param.getVendorIdList());
        query.setStartDate(param.getStartDate());
        query.setEndDate(param.getEndDate());
        query.setResult(param.getResult());
        query.setEmployeeTypeList(param.getEmployeeTypeList());
        query.setUserIdList(param.getSearchKeyUserIdList());
        query.setWarehouseAttendanceCode(param.getWarehouseAttendanceCode());
        query.setClassesId(param.getClassId());
        query.setPunchStatus(param.getPunchStatus());
        query.setEmploymentForm(param.getEmploymentForm());
        query.setConfirmStatusList(param.getConfirmStatusList());

        PageInfo<WarehouseDetailDO> pageInfo = PageHelper.startPage(query.getCurrentPage(), query.getShowCount(), query.getCount() && query.getShowCount() > 0)
                .doSelectPageInfo(() -> warehouseDetailDao.selectPage(query));

        if (CollectionUtils.isEmpty(pageInfo.getList())) {
            return PaginationResult.get(Collections.emptyList(), param);
        }

        List<DateReportVO> collect = convertDateReport(pageInfo.getList());
        return PageUtil.getPageResult(collect, query, (int) pageInfo.getTotal(), pageInfo.getPages());
    }

    @Override
    public DateDetailReportVO dateReportDetail(Long id) {
        DateDetailReportVO result = new DateDetailReportVO();
        WarehouseDetailDO warehouseDetailDO = warehouseDetailDao.selectById(id);
        if (Objects.isNull(warehouseDetailDO)) {
            return result;
        }

        //出入仓刷脸记录
        convertWarehouseRecordList(warehouseDetailDO, result);

        //考勤计算结果
        convertAttendanceResult(warehouseDetailDO, result);

        //请假详情
        convertLeaveDetailList(warehouseDetailDO, result);

        //其他数据转换
        convertOther(result, warehouseDetailDO);
        return result;
    }

    @Override
    public PaginationResult<MonthReportVO> monthReport(ReportParam param) {
        if (!buildReportQuery(param)) {
            return PaginationResult.get(Collections.emptyList(), param);
        }

        WarehouseDetailQuery query = new WarehouseDetailQuery();
        query.setCountry(param.getCountry());
        query.setCityList(param.getCityList());
        query.setOcIdList(param.getOcIdList());
        query.setVendorIdList(param.getVendorIdList());
        query.setStartDate(param.getStartDate());
        query.setEndDate(param.getEndDate());
        query.setReportType(BusinessConstant.MONTH);
        query.setUserIdList(param.getSearchKeyUserIdList());
        query.setEmployeeTypeList(param.getEmployeeTypeList());
        query.setEmploymentForm(param.getEmploymentForm());

        PageInfo<WarehouseDetailDO> pageInfo = PageHelper.startPage(query.getCurrentPage(), query.getShowCount(), query.getCount() && query.getShowCount() > 0)
                .doSelectPageInfo(() -> warehouseDetailDao.selectPage(query));

        List<WarehouseDetailDO> list = pageInfo.getList();

        if (CollectionUtils.isEmpty(list)) {
            return PaginationResult.get(Collections.emptyList(), param);
        }

        List<MonthReportVO> collect = convertMonthReport(param, list);
        return PageUtil.getPageResult(collect, query, (int) pageInfo.getTotal(), pageInfo.getPages());
    }

    @Override
    public List<DateReportVO> dateReportNoBingShift(NoBingShiftReportParam param) {
        WarehouseRecordParam warehouseRecordParam = new WarehouseRecordParam();
        Date startTime = DateFormatterUtil.convertDateTime(param.getAttendanceDate(), param.getStartTime());
        Date endTime = DateFormatterUtil.convertDateTime(param.getAttendanceDate(), param.getEndTime());
        warehouseRecordParam.setStartTime(startTime);
        warehouseRecordParam.setEndTime(endTime);
        warehouseRecordParam.setOcId(param.getOcId());
        List<WarehouseRecordDO> warehouseRecordDOS = warehouseRecordDao.selectByCondition(warehouseRecordParam);
        if (CollectionUtils.isEmpty(warehouseRecordDOS)) {
            return Collections.emptyList();
        }

        WarehouseDetailQuery warehouseDetailParam = new WarehouseDetailQuery();
        warehouseDetailParam.setIds(warehouseRecordDOS.stream().map(WarehouseRecordDO::getWarehouseDetailId).filter(Objects::nonNull).distinct().collect(Collectors.toList()));
        List<WarehouseDetailDO> warehouseDetailDOS = warehouseDetailDao.selectNoBindShiftByCondition(warehouseDetailParam);
        if (CollectionUtils.isEmpty(warehouseDetailDOS)) {
            return Collections.emptyList();
        }

        return convertDateReportNoBingShift(warehouseRecordDOS, warehouseDetailDOS);
    }


    @Override
    public DataStatisticsVO dataStatistics(WpmDataStatisticsParam param) {
        DataStatisticsVO dataStatisticsVO = new DataStatisticsVO();
        List<WarehouseAttendanceConfigDO> warehouseAttendanceConfigDOList = warehouseAttendanceConfigDao.selectByDateRange(DateUtil.endOfDay(param.getWarehouseDate()), param.getOcId());
        if (CollectionUtils.isNotEmpty(warehouseAttendanceConfigDOList) && Objects.equals(BusinessConstant.Y, warehouseAttendanceConfigDOList.get(0).getIsSegmentedCalculation())) {
            dataStatisticsVO.setIsSegmentedCalculation(Boolean.TRUE);
        }
        WarehouseDetailQuery warehouseDetailParam = new WarehouseDetailQuery();
        warehouseDetailParam.setWarehouseDate(param.getWarehouseDate());
        warehouseDetailParam.setOcId(param.getOcId());
        warehouseDetailParam.setClassId(param.getClassesId());
        List<WarehouseDetailDO> warehouseDetailDOList = warehouseDetailDao.selectByCondition(warehouseDetailParam);
        if (CollectionUtils.isEmpty(warehouseDetailDOList)) {
            return dataStatisticsVO;
        }
        List<Long> warehouseDetailIds = warehouseDetailDOList
                .stream()
                .map(WarehouseDetailDO::getId)
                .collect(Collectors.toList());
        List<WarehouseRecordDO> warehouseRecordDOS = warehouseRecordDao.selectByWarehouseDetailIds(warehouseDetailIds);

        List<WarehouseDetailAbnormalDO> warehouseDetailAbnormalDOS = null;
        List<Long> abnormalWarehouseIds = warehouseDetailDOList
                .stream()
                .filter(warehouse -> Objects.equals(WarehouseAttendanceStatusEnum.ABNORMAL.getCode(), warehouse.getAttendanceStatus()))
                .map(WarehouseDetailDO::getId)
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(abnormalWarehouseIds)) {
            warehouseDetailAbnormalDOS = warehouseDetailAbnormalDao.selectByWarehouseDetailIds(abnormalWarehouseIds);
        }
        return convertDataStatisticsVO(dataStatisticsVO, warehouseDetailDOList, warehouseRecordDOS, warehouseDetailAbnormalDOS);
    }


    @Override
    public PaginationResult<DataStatisticsDetailsVO> dataStatisticsDetails(WpmDataStatisticsParam param) {
        WarehouseDetailQuery detailParam = new WarehouseDetailQuery();
        detailParam.setWarehouseDate(param.getWarehouseDate());
        detailParam.setOcId(param.getOcId());
        detailParam.setVendorCode(param.getVendorCode());
        detailParam.setClassesId(param.getClassesId());
        detailParam.setEmployeeTypeList(param.getEmployeeTypeList());
        Set<Long> warehouseIdList = new HashSet<>();
        Set<Long> recordWarehouseIdList = new HashSet<>();
        Set<Long> abnormalWarehouseIdList = new HashSet<>();
        if (StringUtils.isNotEmpty(param.getWarehouseType())) {
            if (Objects.equals(BusinessConstant.IN, param.getWarehouseType())) {
                detailParam.setRecordType(WarehouseTypeEnum.IN.getCode());
                List<WarehouseDetailDO> warehouseDetailDOS = warehouseDetailDao.selectJoinRecordList(detailParam);
                if (CollectionUtils.isEmpty(warehouseDetailDOS)) {
                    return PaginationResult.get(Collections.emptyList(), param);
                }
                recordWarehouseIdList.addAll(warehouseDetailDOS.stream().map(WarehouseDetailDO::getId).collect(Collectors.toSet()));
            } else {
                detailParam.setWarehouseStatus(covertWarehouseStatus(param.getWarehouseType()));
            }
        }

        if (StringUtils.isNotEmpty(param.getAbnormalType())) {
            detailParam.setAbnormalType(param.getAbnormalType());
            List<WarehouseDetailDO> warehouseDetailDOS = warehouseDetailDao.selectJoinAbnormalList(detailParam);
            if (CollectionUtils.isEmpty(warehouseDetailDOS)) {
                return PaginationResult.get(Collections.emptyList(), param);
            }
            List<Long> warehouseDetailIds = warehouseDetailDOS.stream().map(WarehouseDetailDO::getId).distinct().collect(Collectors.toList());
            Map<Integer, List<WarehouseDetailAbnormalDO>> warehouseAbnormalMap = warehouseDetailAbnormalDao.selectByWarehouseDetailIds(warehouseDetailIds)
                    .stream()
                    .filter(abnormal -> Objects.equals(param.getAbnormalType(), abnormal.getAbnormalType()))
                    .collect(Collectors.groupingBy(WarehouseDetailAbnormalDO::getProcessed));

            List<WarehouseDetailAbnormalDO> processedList = warehouseAbnormalMap.get(WarehouseAbnormalStatusEnum.PROCESSED.getCode());
            if (CollectionUtils.isNotEmpty(processedList)) {
                Map<Long, Long> abnormalIdMap = processedList
                        .stream()
                        .collect(Collectors.toMap(WarehouseDetailAbnormalDO::getAbnormalId, WarehouseDetailAbnormalDO::getWarehouseDetailId));
                if (CollectionUtils.isNotEmpty(abnormalIdMap.keySet())) {
                    List<EmployeeAbnormalOperationRecordDO> employeeAbnormalOperationRecordDOS = abnormalOperationRecordDao.selectByAbnormalList(new ArrayList<>(abnormalIdMap.keySet()));
                    List<EmployeeAbnormalOperationRecordDO> abnormalOperationRecordList = employeeAbnormalOperationRecordDOS
                            .stream()
                            .filter(abnormalOperation -> AbnormalOperationTypeEnum.ABNORMAL_CONFIRM.getCode().equals(abnormalOperation.getOperationType())).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(abnormalOperationRecordList)) {
                        List<Long> confirmAbnormalWarehouseDetailIds = new ArrayList<>();
                        for (EmployeeAbnormalOperationRecordDO abnormalOperationRecord : abnormalOperationRecordList) {
                            if (Objects.nonNull(abnormalIdMap.get(abnormalOperationRecord.getAbnormalId()))) {
                                confirmAbnormalWarehouseDetailIds.add(abnormalIdMap.get(abnormalOperationRecord.getAbnormalId()));
                            }
                        }
                        if (CollectionUtils.isNotEmpty(confirmAbnormalWarehouseDetailIds)) {
                            abnormalWarehouseIdList.addAll(confirmAbnormalWarehouseDetailIds);
                        }
                    }
                    Set<Long> durationAbnormalDetailIds = processedList
                            .stream()
                            .filter(abnormal -> Objects.equals(AttendanceAbnormalTypeEnum.ABNORMAL_DURATION.getCode(), abnormal.getAbnormalType()))
                            .map(WarehouseDetailAbnormalDO::getWarehouseDetailId)
                            .collect(Collectors.toSet());
                    if (CollectionUtils.isNotEmpty(durationAbnormalDetailIds)) {
                        abnormalWarehouseIdList.addAll(durationAbnormalDetailIds);
                    }
                }
            }

            List<WarehouseDetailAbnormalDO> processingList = warehouseAbnormalMap.get(WarehouseAbnormalStatusEnum.PENDING_PROCESSING.getCode());
            if (CollectionUtils.isNotEmpty(processingList)) {
                abnormalWarehouseIdList.addAll(processingList.stream().map(WarehouseDetailAbnormalDO::getWarehouseDetailId).distinct().collect(Collectors.toList()));
            }

            if (CollectionUtils.isEmpty(abnormalWarehouseIdList)) {
                return PaginationResult.get(Collections.emptyList(), param);
            }
        }

        if (CollectionUtils.isNotEmpty(recordWarehouseIdList) && CollectionUtils.isNotEmpty(abnormalWarehouseIdList)) {
            warehouseIdList.addAll(recordWarehouseIdList);
            warehouseIdList.retainAll(abnormalWarehouseIdList);
        } else if (CollectionUtils.isNotEmpty(recordWarehouseIdList)) {
            warehouseIdList.addAll(recordWarehouseIdList);
        } else {
            warehouseIdList.addAll(abnormalWarehouseIdList);
        }

        detailParam.setIds(warehouseIdList);
        PageInfo<WarehouseDetailDO> pageInfo = PageHelper.startPage(param.getCurrentPage(), param.getShowCount(), param.getCount() && param.getShowCount() > 0)
                .doSelectPageInfo(() -> warehouseDetailDao.selectDataStatisticsPage(detailParam));

        List<WarehouseDetailDO> list = pageInfo.getList();
        if (CollectionUtils.isEmpty(list)) {
            return PaginationResult.get(Collections.emptyList(), param);
        }

        List<Long> userIds = list.stream().map(WarehouseDetailDO::getUserId).distinct().collect(Collectors.toList());
        Map<Long, UserInfoDO> userNameMap = userInfoDao.getByUserIds(userIds)
                .stream()
                .collect(Collectors.toMap(UserInfoDO::getId, Function.identity(), (v1, v2) -> v1));

        List<String> userCodes = list.stream().map(WarehouseDetailDO::getUserCode).distinct().collect(Collectors.toList());
        Map<String, UserFacePhotoDTO> userFacePhotoMap = recognitionService.listFacePhotoByUserCodes(userCodes).stream().collect(Collectors.toMap(UserFacePhotoDTO::getUserCode, Function.identity()));

        List<Long> warehouseIds = list.stream().map(WarehouseDetailDO::getId).distinct().collect(Collectors.toList());
        Map<Long, List<WarehouseRecordDO>> recordMap = warehouseRecordDao.selectByWarehouseDetailIds(warehouseIds)
                .stream()
                .collect(Collectors.groupingBy(WarehouseRecordDO::getWarehouseDetailId));

        Map<Long, List<WarehouseDetailAbnormalDO>> warehouseAbnormalMap = warehouseDetailAbnormalDao.selectByWarehouseDetailIds(warehouseIds)
                .stream()
                .collect(Collectors.groupingBy(WarehouseDetailAbnormalDO::getWarehouseDetailId));
        List<DataStatisticsDetailsVO> result = list.stream().map(item -> {
            DataStatisticsDetailsVO statisticsDetailsVO = new DataStatisticsDetailsVO();
            statisticsDetailsVO.setId(item.getId());
            statisticsDetailsVO.setAttendanceStatusCode(item.getAttendanceStatus());
            UserInfoDO userInfoDO = userNameMap.getOrDefault(item.getUserId(), new UserInfoDO());
            statisticsDetailsVO.setUserCode(userInfoDO.getUserCode());
            statisticsDetailsVO.setUserName(userInfoDO.getUserName());
            UserFacePhotoDTO userFacePhotoDTO = userFacePhotoMap.get(userInfoDO.getUserCode());
            if (Objects.nonNull(userFacePhotoDTO)) {
                statisticsDetailsVO.setUserPhotoKey(userFacePhotoDTO.getFileKey());
                statisticsDetailsVO.setUserPhoto(userFacePhotoDTO.getFileUrl());
            }
            statisticsDetailsVO.setActualAttendanceTime(item.getActualAttendanceTime());
            if ((item.getActualAttendanceTime().compareTo(item.getRequiredAttendanceTime()) > -1 && item.getActualWorkingHours().compareTo(item.getLegalWorkingHours()) > -1)
                    || Objects.equals(WarehouseAttendanceStatusEnum.NORMAL.getCode(), item.getAttendanceStatus())) {
                statisticsDetailsVO.setFullAttendance(BusinessConstant.Y);
            } else {
                statisticsDetailsVO.setFullAttendance(BusinessConstant.N);
            }

            if (CollectionUtils.isNotEmpty(recordMap.get(item.getId()))) {
                List<WarehouseRecordDO> warehouseRecordDOList = recordMap.get(item.getId());
                Optional<WarehouseRecordDO> inOptional = warehouseRecordDOList.stream().filter(record -> Objects.equals(WarehouseTypeEnum.IN.getCode(), record.getRecordType())).findFirst();
                inOptional.ifPresent(warehouseRecordDO -> statisticsDetailsVO.setClockInTime(warehouseRecordDO.getWarehouseTime()));
                List<WarehouseRecordDO> outList = warehouseRecordDOList.stream().filter(record -> Objects.equals(WarehouseTypeEnum.OUT.getCode(), record.getRecordType()))
                        .sorted(Comparator.comparing(WarehouseRecordDO::getWarehouseTime).reversed()).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(outList)) {
                    statisticsDetailsVO.setClockOutTime(outList.get(BusinessConstant.FIRST_ELEMENT_INDEX).getWarehouseTime());
                }
            }

            if (Objects.equals(WarehouseAttendanceStatusEnum.ABNORMAL.getCode(), item.getAttendanceStatus()) && CollectionUtils.isNotEmpty(warehouseAbnormalMap.get(item.getId()))) {
                List<DataStatisticsDetailsVO.AbnormalVO> abnormalList = new ArrayList<>();
                List<DataStatisticsDetailsVO.AbnormalVO> abnormalVOList = warehouseAbnormalMap.get(item.getId())
                        .stream()
                        .filter(abnormal -> Objects.equals(WarehouseAbnormalStatusEnum.PENDING_PROCESSING.getCode(), abnormal.getProcessed())).map(abnormal -> {
                            DataStatisticsDetailsVO.AbnormalVO abnormalVO = new DataStatisticsDetailsVO.AbnormalVO();
                            abnormalVO.setAbnormalId(abnormal.getAbnormalId());
                            abnormalVO.setAbnormalType(abnormal.getAbnormalType());
                            return abnormalVO;
                        }).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(abnormalVOList)) {
                    abnormalList.addAll(abnormalVOList);
                }

                List<WarehouseDetailAbnormalDO> abnormalProcessedList = warehouseAbnormalMap.get(item.getId())
                        .stream()
                        .filter(abnormal -> Objects.equals(WarehouseAbnormalStatusEnum.PROCESSED.getCode(), abnormal.getProcessed()))
                        .collect(Collectors.toList());

                Map<Long, WarehouseDetailAbnormalDO> abnormalMap = abnormalProcessedList
                        .stream()
                        .collect(Collectors.toMap(WarehouseDetailAbnormalDO::getAbnormalId, Function.identity()));
                if (CollectionUtils.isNotEmpty(abnormalMap.keySet())) {
                    List<EmployeeAbnormalOperationRecordDO> employeeAbnormalOperationRecordDOS = abnormalOperationRecordDao.selectByAbnormalList(new ArrayList<>(abnormalMap.keySet()));
                    List<EmployeeAbnormalOperationRecordDO> abnormalOperationRecordList = employeeAbnormalOperationRecordDOS
                            .stream()
                            .filter(abnormalOperation -> AbnormalOperationTypeEnum.ABNORMAL_CONFIRM.getCode().equals(abnormalOperation.getOperationType())).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(abnormalOperationRecordList)) {
                        List<DataStatisticsDetailsVO.AbnormalVO> confirmAbnormalList = new ArrayList<>();
                        for (EmployeeAbnormalOperationRecordDO abnormalOperationRecord : abnormalOperationRecordList) {
                            WarehouseDetailAbnormalDO warehouseDetailAbnormalDO = abnormalMap.get(abnormalOperationRecord.getAbnormalId());
                            if (Objects.nonNull(warehouseDetailAbnormalDO)) {
                                DataStatisticsDetailsVO.AbnormalVO abnormalVO = new DataStatisticsDetailsVO.AbnormalVO();
                                abnormalVO.setAbnormalId(warehouseDetailAbnormalDO.getAbnormalId());
                                abnormalVO.setAbnormalType(warehouseDetailAbnormalDO.getAbnormalType());
                                confirmAbnormalList.add(abnormalVO);
                            }
                        }
                        if (CollectionUtils.isNotEmpty(confirmAbnormalList)) {
                            abnormalList.addAll(confirmAbnormalList);
                        }
                    } else {
                        List<DataStatisticsDetailsVO.AbnormalVO> abnormalDurationList = abnormalProcessedList
                                .stream()
                                .filter(abnormal -> Objects.equals(AttendanceAbnormalTypeEnum.ABNORMAL_DURATION.getCode(), abnormal.getAbnormalType()))
                                .map(abnormal -> {
                                    DataStatisticsDetailsVO.AbnormalVO abnormalVO = new DataStatisticsDetailsVO.AbnormalVO();
                                    abnormalVO.setAbnormalId(abnormal.getAbnormalId());
                                    abnormalVO.setAbnormalType(abnormal.getAbnormalType());
                                    return abnormalVO;
                                }).collect(Collectors.toList());
                        if (CollectionUtils.isNotEmpty(abnormalDurationList)) {
                            abnormalList.addAll(abnormalDurationList);
                        }
                    }
                }
                statisticsDetailsVO.setAbnormalVOList(abnormalList);
            }

            if (Lists.newArrayList(WarehouseAttendanceStatusEnum.INIT.getCode(), WarehouseAttendanceStatusEnum.PENDING_SHIFT_CONFIG.getCode()).contains(item.getAttendanceStatus())
                    && (Objects.nonNull(param.getAttendanceTime()) && DateUtil.between(param.getWarehouseDate(), param.getAttendanceTime(), DateUnit.HOUR) < 24)) {
                statisticsDetailsVO.setQuickOut(Boolean.TRUE);
            }

            return statisticsDetailsVO;
        }).collect(Collectors.toList());

        return PageUtil.getPageResult(result, param, (int) pageInfo.getTotal(), pageInfo.getPages());
    }

    @Override
    public PaginationResult<DataStatisticsBlackListVO> dataStatisticsBlackList(WpmDataStatisticsParam param) {
        // 黑名单数量计算
        LambdaQueryWrapper<WarehouseBlackListDO> queryWrapper = Wrappers.lambdaQuery(WarehouseBlackListDO.class);
        queryWrapper.eq(WarehouseBlackListDO::getIsDelete, BusinessConstant.N);
        queryWrapper.eq(param.getWarehouseDate() != null, WarehouseBlackListDO::getWarehouseDate, param.getWarehouseDate());
        queryWrapper.eq(param.getOcId() != null, WarehouseBlackListDO::getOcId, param.getOcId());
        queryWrapper.eq(param.getClassesId() != null, WarehouseBlackListDO::getClassesId, param.getClassesId());
        queryWrapper.in(CollectionUtils.isNotEmpty(param.getEmployeeTypeList()), WarehouseBlackListDO::getEmployeeType, param.getEmployeeTypeList());
        queryWrapper.in(StringUtils.isNotBlank(param.getVendorCode()), WarehouseBlackListDO::getVendorCode, param.getVendorCode());

        Page<WarehouseBlackListDO> page = PageHelper.startPage(param.getCurrentPage(), param.getShowCount());
        List<WarehouseBlackListDO> list = warehouseBlackListDao.list(queryWrapper);
        if (CollectionUtils.isEmpty(list)) {
            return PaginationResult.get(Collections.emptyList(), param);
        }
        List<Long> userIds = Lists.newArrayList();
        List<String> vendorCodes = Lists.newArrayList();
        for (WarehouseBlackListDO item : list) {
            userIds.add(item.getUserId());
            vendorCodes.add(item.getVendorCode());
        }
        List<UserInfoDO> userList = userInfoDao.getByUserIds(userIds);
        if (CollectionUtils.isEmpty(userList)) {
            return PaginationResult.get(Collections.emptyList(), param);
        }
        Map<Long, UserInfoDO> userNameMap = userList.stream().collect(Collectors.toMap(UserInfoDO::getId, Function.identity(), (v1, v2) -> v1));
        Map<String, String> vendorMap = warehouseSupplierService.getSupplierByCodes(vendorCodes);
        //获取用户所有的证件类型
        String certificateTypeCode = StringUtils.equalsAnyIgnoreCase(CountryCodeEnum.BRA.getCode(), userList.get(0).getLocationCountry()) ? CertificateTypeEnum.ID_CARD.getCode() : CertificateTypeEnum.INE.getCode();
        List<String> userCodeList = userList.stream().map(UserInfoDO::getUserCode).distinct().collect(Collectors.toList());
        List<UserCertificateDTO> userCertificateList = userCertificateSupport.listUserCertificateList(userCodeList, certificateTypeCode);
        Map<String, UserCertificateDTO> certificateMap = CollectionUtils.isEmpty(userCertificateList) ? Maps.newHashMap() :
                userCertificateList.stream().collect(Collectors.toMap(UserCertificateDTO::getUserCode, Function.identity(), (v1, v2) -> v1));
        List<DataStatisticsBlackListVO> result = list.stream().collect(Collectors.toMap(WarehouseBlackListDO::getUserCode, o -> o, (k1, k2) -> k1)).values().stream().map(item -> {
            DataStatisticsBlackListVO blackListVO = new DataStatisticsBlackListVO();
            blackListVO.setId(item.getId());
            if (userNameMap.containsKey(item.getUserId())) {
                UserInfoDO userInfoDO = userNameMap.get(item.getUserId());
                blackListVO.setUserCode(userInfoDO.getUserCode());
                blackListVO.setUserName(userInfoDO.getUserName());
                blackListVO.setUserPhotoKey(userInfoDO.getProfilePhotoUrl());

            }
            if (certificateMap.containsKey(item.getUserCode())) {
                UserCertificateDTO userCertificateBO = certificateMap.get(item.getUserCode());
                blackListVO.setCertificateTypeCode(userCertificateBO.getCertificateTypeCode());
                blackListVO.setCertificateCode(userCertificateBO.getCertificateCode());
            }
            if (vendorMap.containsKey(item.getVendorCode())) {
                blackListVO.setVendorName(vendorMap.get(item.getVendorCode()));
            }
            blackListVO.setReason(item.getReason());
            blackListVO.setType(item.getType());
            return blackListVO;
        }).sorted(Comparator.comparing(DataStatisticsBlackListVO::getId)).collect(Collectors.toList());
        return PageUtil.getPageResult(result, param, (int) page.getTotal(), page.getPages());
    }

    @Override
    public StatisticsVendorResultVO statisticVendor(StatisticVendorParam param) {
        if (param.getClassesId() == null || StringUtils.isBlank(param.getVendorCode())) {
            return new StatisticsVendorResultVO();
        }
        WarehouseDetailQuery warehouseDetailParam = new WarehouseDetailQuery();
        warehouseDetailParam.setWarehouseDate(param.getWarehouseDate());
        warehouseDetailParam.setOcId(param.getOcId());
        warehouseDetailParam.setClassId(param.getClassesId());
        warehouseDetailParam.setVendorCode(param.getVendorCode());
        List<WarehouseDetailDO> warehouseDetailDOList = warehouseDetailDao.selectByCondition(warehouseDetailParam);
        if (CollectionUtils.isEmpty(warehouseDetailDOList)) {
            return new StatisticsVendorResultVO();
        }
        List<Long> warehouseDetailIds = warehouseDetailDOList
                .stream()
                .map(WarehouseDetailDO::getId)
                .collect(Collectors.toList());
        WarehouseRecordParam recordParam = new WarehouseRecordParam();
        recordParam.setWarehouseDetailIds(warehouseDetailIds);
        List<WarehouseRecordDO> warehouseRecordDOS = warehouseRecordDao.selectByCondition(recordParam);
        return convertStatisticsVendorResultVO(warehouseDetailDOList, warehouseRecordDOS);
    }

    @Override
    public PaginationResult<StatisticsVendorUserResultVO> statisticVendorUser(StatisticVendorParam param) {
        if (param.getClassesId() == null || StringUtils.isBlank(param.getVendorCode()) || StringUtils.isBlank(param.getWarehouseType())) {
            return PaginationResult.get(Collections.emptyList(), param);
        }
        WarehouseDetailQuery detailParam = new WarehouseDetailQuery();
        detailParam.setWarehouseDate(param.getWarehouseDate());
        detailParam.setOcId(param.getOcId());
        detailParam.setVendorCode(param.getVendorCode());
        detailParam.setClassesId(param.getClassesId());
        detailParam.setEmployeeTypeList(Collections.singletonList(EmploymentTypeEnum.OS_FIXED_SALARY.getCode()));
        Set<Long> recordWarehouseIdList = new HashSet<>();
        if (StringUtils.isNotEmpty(param.getWarehouseType())) {
            if (Objects.equals(BusinessConstant.IN, param.getWarehouseType())) {
                detailParam.setRecordType(WarehouseTypeEnum.IN.getCode());
                List<WarehouseDetailDO> warehouseDetailDOS = warehouseDetailDao.selectJoinRecordList(detailParam);
                if (CollectionUtils.isEmpty(warehouseDetailDOS)) {
                    return PaginationResult.get(Collections.emptyList(), param);
                }
                recordWarehouseIdList.addAll(warehouseDetailDOS.stream().map(WarehouseDetailDO::getId).collect(Collectors.toSet()));
            } else {
                detailParam.setWarehouseStatus(covertWarehouseStatus(param.getWarehouseType()));
            }
        }
        if (CollectionUtils.isNotEmpty(recordWarehouseIdList)) {
            detailParam.setIds(recordWarehouseIdList);
        }

        PageInfo<WarehouseDetailDO> pageInfo = PageHelper.startPage(detailParam.getCurrentPage(), detailParam.getShowCount(), detailParam.getCount() && detailParam.getShowCount() > 0)
                .doSelectPageInfo(() -> warehouseDetailDao.selectDataStatisticsPage(detailParam));

        List<WarehouseDetailDO> list = pageInfo.getList();

        if (CollectionUtils.isEmpty(list)) {
            return PaginationResult.get(Collections.emptyList(), param);
        }

        List<Long> userIds = list.stream().map(WarehouseDetailDO::getUserId).distinct().collect(Collectors.toList());
        Map<Long, UserInfoDO> userNameMap = userInfoDao.getByUserIds(userIds)
                .stream()
                .collect(Collectors.toMap(UserInfoDO::getId, Function.identity(), (v1, v2) -> v1));

        List<String> userCodes = list.stream().map(WarehouseDetailDO::getUserCode).distinct().collect(Collectors.toList());
        Map<String, UserFacePhotoDTO> userFacePhotoMap = recognitionService.listFacePhotoByUserCodes(userCodes).stream().collect(Collectors.toMap(UserFacePhotoDTO::getUserCode, Function.identity()));

        List<Long> warehouseIds = list.stream().map(WarehouseDetailDO::getId).distinct().collect(Collectors.toList());
        Map<Long, List<WarehouseRecordDO>> recordMap = warehouseRecordDao.selectByWarehouseDetailIds(warehouseIds)
                .stream()
                .collect(Collectors.groupingBy(WarehouseRecordDO::getWarehouseDetailId));

        Map<Long, List<WarehouseDetailAbnormalDO>> warehouseAbnormalMap = warehouseDetailAbnormalDao.selectByWarehouseDetailIds(warehouseIds)
                .stream()
                .collect(Collectors.groupingBy(WarehouseDetailAbnormalDO::getWarehouseDetailId));

        List<StatisticsVendorUserResultVO> result = list.stream().map(item -> {
            StatisticsVendorUserResultVO detail = new StatisticsVendorUserResultVO();
            UserInfoDO userInfoDO = userNameMap.getOrDefault(item.getUserId(), new UserInfoDO());
            detail.setUserCode(userInfoDO.getUserCode());
            detail.setUserName(userInfoDO.getUserName());
            UserFacePhotoDTO userFacePhotoDTO = userFacePhotoMap.get(userInfoDO.getUserCode());
            if (Objects.nonNull(userFacePhotoDTO)) {
                detail.setUserPhotoKey(userFacePhotoDTO.getFileKey());
                detail.setUserPhoto(userFacePhotoDTO.getFileUrl());
            }
            detail.setActualAttendanceTime(item.getActualAttendanceTime());

            if ((item.getActualAttendanceTime().compareTo(item.getRequiredAttendanceTime()) > -1 && item.getActualWorkingHours().compareTo(item.getLegalWorkingHours()) > -1)
                    || Objects.equals(WarehouseAttendanceStatusEnum.NORMAL.getCode(), item.getAttendanceStatus())) {
                detail.setFullAttendance(BusinessConstant.Y);
            } else {
                detail.setFullAttendance(BusinessConstant.N);
            }

            if (CollectionUtils.isNotEmpty(recordMap.get(item.getId()))) {
                List<WarehouseRecordDO> warehouseRecordDOList = recordMap.get(item.getId());
                Optional<WarehouseRecordDO> inOptional = warehouseRecordDOList.stream().filter(record -> Objects.equals(WarehouseTypeEnum.IN.getCode(), record.getRecordType())).findFirst();
                inOptional.ifPresent(warehouseRecord -> detail.setClockInTime(warehouseRecord.getWarehouseTime()));
                List<WarehouseRecordDO> outList = warehouseRecordDOList.stream().filter(record -> Objects.equals(WarehouseTypeEnum.OUT.getCode(), record.getRecordType()))
                        .sorted(Comparator.comparing(WarehouseRecordDO::getWarehouseTime).reversed()).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(outList)) {
                    detail.setClockOutTime(outList.get(BusinessConstant.FIRST_ELEMENT_INDEX).getWarehouseTime());
                }
            }

            if (Objects.equals(WarehouseAttendanceStatusEnum.ABNORMAL.getCode(), item.getAttendanceStatus()) && CollectionUtils.isNotEmpty(warehouseAbnormalMap.get(item.getId()))) {
                List<DataStatisticsDetailsVO.AbnormalVO> abnormalList = new ArrayList<>();
                Set<Long> confirmAbnormalIds = new HashSet<>();
                List<DataStatisticsDetailsVO.AbnormalVO> abnormalVOList = warehouseAbnormalMap.get(item.getId())
                        .stream()
                        .filter(abnormal -> Objects.equals(WarehouseAbnormalStatusEnum.PENDING_PROCESSING.getCode(), abnormal.getProcessed())).map(abnormal -> {
                            DataStatisticsDetailsVO.AbnormalVO abnormalVO = new DataStatisticsDetailsVO.AbnormalVO();
                            abnormalVO.setAbnormalId(abnormal.getAbnormalId());
                            abnormalVO.setAbnormalType(abnormal.getAbnormalType());
                            return abnormalVO;
                        }).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(abnormalVOList)) {
                    abnormalList.addAll(abnormalVOList);
                }

                List<WarehouseDetailAbnormalDO> abnormalProcessedList = warehouseAbnormalMap.get(item.getId())
                        .stream()
                        .filter(abnormal -> Objects.equals(WarehouseAbnormalStatusEnum.PROCESSED.getCode(), abnormal.getProcessed()))
                        .collect(Collectors.toList());

                Map<Long, WarehouseDetailAbnormalDO> abnormalMap = abnormalProcessedList
                        .stream()
                        .collect(Collectors.toMap(WarehouseDetailAbnormalDO::getAbnormalId, Function.identity()));
                if (CollectionUtils.isNotEmpty(abnormalMap.keySet())) {
                    List<EmployeeAbnormalOperationRecordDO> employeeAbnormalOperationRecordDOS = abnormalOperationRecordDao.selectByAbnormalList(new ArrayList<>(abnormalMap.keySet()));
                    List<EmployeeAbnormalOperationRecordDO> abnormalOperationRecordList = employeeAbnormalOperationRecordDOS
                            .stream()
                            .filter(abnormalOperation -> AbnormalOperationTypeEnum.ABNORMAL_CONFIRM.getCode().equals(abnormalOperation.getOperationType())).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(abnormalOperationRecordList)) {
                        List<DataStatisticsDetailsVO.AbnormalVO> confirmAbnormalList = new ArrayList<>();
                        for (EmployeeAbnormalOperationRecordDO abnormalOperationRecord : abnormalOperationRecordList) {
                            WarehouseDetailAbnormalDO warehouseDetailAbnormalDO = abnormalMap.get(abnormalOperationRecord.getAbnormalId());
                            if (Objects.nonNull(warehouseDetailAbnormalDO)) {
                                DataStatisticsDetailsVO.AbnormalVO abnormalVO = new DataStatisticsDetailsVO.AbnormalVO();
                                abnormalVO.setAbnormalId(warehouseDetailAbnormalDO.getAbnormalId());
                                abnormalVO.setAbnormalType(warehouseDetailAbnormalDO.getAbnormalType());
                                confirmAbnormalList.add(abnormalVO);
                                confirmAbnormalIds.add(warehouseDetailAbnormalDO.getAbnormalId());
                            }
                        }
                        if (CollectionUtils.isNotEmpty(confirmAbnormalList)) {
                            abnormalList.addAll(confirmAbnormalList);
                        }
                    }
                }

                List<DataStatisticsDetailsVO.AbnormalVO> abnormalDurationList = abnormalProcessedList
                        .stream()
                        .filter(abnormal -> Objects.equals(AttendanceAbnormalTypeEnum.ABNORMAL_DURATION.getCode(), abnormal.getAbnormalType())
                                && !confirmAbnormalIds.contains(abnormal.getAbnormalId()))
                        .map(abnormal -> {
                            DataStatisticsDetailsVO.AbnormalVO abnormalVO = new DataStatisticsDetailsVO.AbnormalVO();
                            abnormalVO.setAbnormalId(abnormal.getAbnormalId());
                            abnormalVO.setAbnormalType(abnormal.getAbnormalType());
                            return abnormalVO;
                        }).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(abnormalDurationList)) {
                    abnormalList.addAll(abnormalDurationList);
                }
                detail.setAbnormalVOList(abnormalList);
            }

            return detail;
        }).collect(Collectors.toList());
        return PageUtil.getPageResult(result, detailParam, (int) pageInfo.getTotal(), pageInfo.getPages());
    }

    @Override
    public VendorClassesConfirmVO vendorClassedConfirmDetailH5(Long id) {
        WarehouseVendorClassesConfirmDO warehouseVendorClassesConfirmDO = warehouseVendorClassesConfirmDao.selectById(id);
        if (Objects.isNull(warehouseVendorClassesConfirmDO)) {
            return null;
        }
        VendorClassesConfirmVO vendorClassesConfirmVO = BeanUtils.convert(warehouseVendorClassesConfirmDO, VendorClassesConfirmVO.class);
        List<WarehouseAttendanceConfigDO> warehouseAttendanceConfigDOList = warehouseAttendanceConfigDao.selectByDateRange(DateUtil.endOfDay(warehouseVendorClassesConfirmDO.getWarehouseDate()), warehouseVendorClassesConfirmDO.getOcId());
        if (CollectionUtils.isNotEmpty(warehouseAttendanceConfigDOList) && Objects.equals(BusinessConstant.Y, warehouseAttendanceConfigDOList.get(0).getIsSegmentedCalculation())) {
            vendorClassesConfirmVO.setIsSegmentedCalculation(Boolean.TRUE);
        }

        return vendorClassesConfirmVO;
    }

    @Override
    public VendorClassesConfirmVO vendorClassedConfirmDetailWeb(Long id) {
        WarehouseDetailDO warehouseDetailDO = warehouseDetailDao.selectById(id);
        if (Objects.isNull(warehouseDetailDO) || !Objects.equals(EmploymentTypeEnum.OS_FIXED_SALARY.getCode(), warehouseDetailDO.getEmployeeType())) {
            return null;
        }

        WarehouseVendorClassesConfirmParam param = new WarehouseVendorClassesConfirmParam();
        param.setWarehouseDate(warehouseDetailDO.getWarehouseDate());
        param.setVendorCode(warehouseDetailDO.getVendorCode());
        param.setOcId(warehouseDetailDO.getOcId());
        param.setClassesId(warehouseDetailDO.getClassesId());
        List<WarehouseVendorClassesConfirmDO> warehouseVendorClassesConfirmDOList = warehouseVendorClassesConfirmDao.selectByContidition(param);
        if (CollectionUtils.isEmpty(warehouseVendorClassesConfirmDOList)) {
            return null;
        }
        WarehouseVendorClassesConfirmDO warehouseVendorClassesConfirmDO = warehouseVendorClassesConfirmDOList.get(BusinessConstant.FIRST_ELEMENT_INDEX);
        VendorClassesConfirmVO result = BeanUtils.convert(warehouseVendorClassesConfirmDO, VendorClassesConfirmVO.class);
        List<WarehouseAttendanceConfigDO> warehouseAttendanceConfigDOList = warehouseAttendanceConfigDao.selectByDateRange(DateUtil.endOfDay(warehouseDetailDO.getWarehouseDate()), warehouseDetailDO.getOcId());
        if (CollectionUtils.isNotEmpty(warehouseAttendanceConfigDOList) && Objects.equals(BusinessConstant.Y, warehouseAttendanceConfigDOList.get(0).getIsSegmentedCalculation())) {
            result.setIsSegmentedCalculation(Boolean.TRUE);
        }
        if (Objects.equals(WarehouseAttendanceStatusEnum.ABNORMAL.getCode(), warehouseDetailDO.getAttendanceStatus())) {
            List<WarehouseDetailAbnormalDO> warehouseDetailAbnormalList = warehouseDetailAbnormalDao.selectByWarehouseDetailId(warehouseDetailDO.getId(), WarehouseAbnormalStatusEnum.PROCESSED.getCode());
            if (CollectionUtils.isEmpty(warehouseDetailAbnormalList)) {
                return result;
            }
            List<DataStatisticsDetailsVO.AbnormalVO> abnormalVOList = warehouseDetailAbnormalList
                    .stream()
                    .map(abnormal -> {
                        DataStatisticsDetailsVO.AbnormalVO abnormalVO = new DataStatisticsDetailsVO.AbnormalVO();
                        abnormalVO.setAbnormalId(abnormal.getAbnormalId());
                        abnormalVO.setAbnormalType(abnormal.getAbnormalType());
                        return abnormalVO;
                    }).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(abnormalVOList)) {
                result.setAbnormalVOList(abnormalVOList);
            }
        }
        return result;
    }

    @Override
    public GetVendorConfirmContentResultVO getVendorConfirmContent(GetVendorConfirmContentParam param) {
        GetVendorConfirmContentResultVO result = new GetVendorConfirmContentResultVO();
        WarehouseDetailQuery warehouseDetailParam = new WarehouseDetailQuery();
        warehouseDetailParam.setOcId(param.getOcId());
        warehouseDetailParam.setVendorCode(param.getVendorCode());
        warehouseDetailParam.setClassId(param.getClassesId());
        warehouseDetailParam.setWarehouseDate(param.getWarehouseDate());
        List<WarehouseDetailDO> detailList = warehouseDetailDao.selectByCondition(warehouseDetailParam);
        if (CollectionUtils.isEmpty(detailList)) {
            return result;
        }

        List<Long> warehouseIdList = detailList
                .stream()
                .filter(warehouseDetail -> Objects.equals(WarehouseAttendanceStatusEnum.ABNORMAL.getCode(), warehouseDetail.getAttendanceStatus()))
                .map(WarehouseDetailDO::getId)
                .collect(Collectors.toList());
        List<WarehouseDetailAbnormalDO> abnormalList = warehouseDetailAbnormalDao.selectByWarehouseDetailIds(warehouseIdList);
        if (CollectionUtils.isEmpty(abnormalList)) {
            result.setPunchCount(detailList.size());
            return result;
        }

        Integer inWithoutPunchCount = abnormalCount(abnormalList, AttendanceAbnormalTypeEnum.BEFORE_OFFICE_LACK.getCode());
        int outWithoutPunchCount;
        List<WarehouseAttendanceConfigDO> warehouseAttendanceConfigDOList = warehouseAttendanceConfigDao.selectByDateRange(DateUtil.endOfDay(param.getWarehouseDate()), param.getOcId());
        if (CollectionUtils.isNotEmpty(warehouseAttendanceConfigDOList) && Objects.equals(BusinessConstant.Y, warehouseAttendanceConfigDOList.get(0).getIsSegmentedCalculation())) {
            Set<Long> warehouseIds = new HashSet<>();
            result.setIsSegmentedCalculation(Boolean.TRUE);
            Integer abnormalDurationCount = abnormalDurationCount(abnormalList, warehouseIds);
            result.setAbnormalDurationNum(abnormalDurationCount);
            outWithoutPunchCount = Math.toIntExact(detailList.stream()
                    .filter(warehouse -> Objects.equals(WarehouseStatusEnum.WAIT_OUT.getCode(), warehouse.getWarehouseStatus())
                            && !warehouseIds.contains(warehouse.getId())).count());
        } else {
            outWithoutPunchCount = Math.toIntExact(detailList.stream().filter(warehouse -> Objects.equals(WarehouseStatusEnum.WAIT_OUT.getCode(), warehouse.getWarehouseStatus())).count());
            Integer latePunchCount = abnormalCount(abnormalList, AttendanceAbnormalTypeEnum.LATE.getCode());
            Integer leaveEarlyPunchCount = abnormalCount(abnormalList, AttendanceAbnormalTypeEnum.LEAVE_EARLY.getCode());
            result.setLatePunchCount(latePunchCount);
            result.setLeaveEarlyPunchCount(leaveEarlyPunchCount);
        }

        // 实际出勤 = 总记录 - 出仓未打卡 - 入仓未打卡
        result.setPunchCount(Math.max(detailList.size() - outWithoutPunchCount - inWithoutPunchCount, 0));
        result.setOutWithoutPunchCount(outWithoutPunchCount);
        result.setInWithoutPunchCount(inWithoutPunchCount);
        return result;
    }

    @Override
    public OssApiVo getOssFileUrl(String fileKey) {
        if (StringUtils.isEmpty(fileKey)) {
            return null;
        }
        String userCode = RequestInfoHolder.getUserCode();
        List<String> userCodes = Arrays.stream(attendanceProperties.getVendor().getUserCodesAuthority().split(BusinessConstant.DEFAULT_DELIMITER)).collect(Collectors.toList());
        if (!userCodes.contains(userCode)) {
            return null;
        }
        return ipepIntegration.getUrlByFileKey(fileKey, BusinessConstant.OSS_PRIVATE_BUCKET_TYPE);
    }

    private boolean buildReportQuery(ReportParam param) {
        if (StringUtils.isBlank(param.getCountry())) {
            return false;
        }

        Set<Long> searchKeyUserIdList = new HashSet<>();
        boolean searchFlag = false;
        if (StringUtils.isNotBlank(param.getSearchUserKey())) {
            List<UserInfoDO> userInfoDOList = userInfoDao.selectBySearchCode(param.getSearchUserKey());
            searchFlag = true;
            if (CollectionUtils.isNotEmpty(userInfoDOList)) {
                searchKeyUserIdList.addAll(userInfoDOList.stream().map(UserInfoDO::getId).collect(Collectors.toSet()));
            }
        }
        if (searchFlag && CollectionUtils.isEmpty(searchKeyUserIdList)) {
            return false;
        }
        param.setSearchKeyUserIdList(new ArrayList<>(searchKeyUserIdList));

        //网点权限处理
        if (CollectionUtils.isEmpty(param.getOcIdList())) {
            GetVendorListByOcListParam ocListParam = new GetVendorListByOcListParam();
            ocListParam.setCountry(param.getCountry());
            ocListParam.setCityList(param.getCityList());
            List<OcVO> ocList = warehouseOcService.getOcListByCondition(ocListParam);
            if (CollectionUtils.isNotEmpty(ocList)) {
                param.setOcIdList(ocList.stream().map(OcVO::getOcId).distinct().collect(Collectors.toList()));
            }
        }

        //供应商级联网点处理
        List<Long> vendorIdList = new ArrayList<>();
        if (CollectionUtils.isEmpty(param.getVendorIdList())) {
            List<VendorVO> vendorList = warehouseSupplierService.getVendorList(param.getOcIdList());
            if (CollectionUtils.isNotEmpty(vendorList)) {
                vendorIdList.addAll(vendorList.stream().map(VendorVO::getVendorId).distinct().collect(Collectors.toList()));
            }
            //特殊逻辑 自有挂在虚拟imile供应商下
            vendorIdList.add(BusinessConstant.IMILE_VIRTUAL_VENDOR_ID);
            param.setVendorIdList(vendorIdList);
        }
        return true;
    }

    private StatisticsVendorResultVO convertStatisticsVendorResultVO(List<WarehouseDetailDO> warehouseDetailList, List<WarehouseRecordDO> warehouseRecords) {
        StatisticsVendorResultVO result = new StatisticsVendorResultVO();
        List<String> employeeTypeList = Collections.singletonList(EmploymentTypeEnum.OS_FIXED_SALARY.getCode());

        List<WarehouseDetailDO> warehouseDetailDOS = warehouseDetailList
                .stream()
                .filter(warehouseDetail -> employeeTypeList.contains(warehouseDetail.getEmployeeType()))
                .collect(Collectors.toList());

        List<Long> warehouseIds = warehouseDetailDOS
                .stream()
                .map(WarehouseDetailDO::getId)
                .collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(warehouseRecords)) {
            Map<Long, List<WarehouseRecordDO>> warehouseRecordMap = warehouseRecords
                    .stream()
                    .filter(warehouseRecord -> warehouseIds.contains(warehouseRecord.getWarehouseDetailId()))
                    .collect(Collectors.groupingBy(WarehouseRecordDO::getWarehouseDetailId));

            if (MapUtils.isNotEmpty(warehouseRecordMap)) {
                Integer clockInCount = 0;
                Integer clockOutCount = 0;
                Integer waitClockOutCount = 0;
                for (WarehouseDetailDO warehouseDetailDO : warehouseDetailDOS) {
                    List<WarehouseRecordDO> warehouseRecordDOList = warehouseRecordMap.get(warehouseDetailDO.getId());
                    if (Objects.equals(WarehouseStatusEnum.WAIT_OUT.getCode(), warehouseDetailDO.getWarehouseStatus())) {
                        waitClockOutCount++;
                    }
                    if (CollectionUtils.isNotEmpty(warehouseRecordDOList)) {
                        Optional<WarehouseRecordDO> inOptional = warehouseRecordDOList.stream().filter(item -> Objects.equals(WarehouseTypeEnum.IN.getCode(), item.getRecordType())).findFirst();
                        if (inOptional.isPresent()) {
                            clockInCount++;
                        }
                    }
                    if (Objects.equals(WarehouseStatusEnum.OUT.getCode(), warehouseDetailDO.getWarehouseStatus())) {
                        clockOutCount++;
                    }
                }
                result.setClockInCount(clockInCount);
                result.setClockOutCount(clockOutCount);
                result.setWaitClockOutCount(waitClockOutCount);
            }
        }
        return result;
    }

    private Integer abnormalCount(List<WarehouseDetailAbnormalDO> abnormalList, String abnormalType) {
        Map<Integer, List<WarehouseDetailAbnormalDO>> warehouseMap = abnormalList.stream().filter(e -> abnormalType.equals(e.getAbnormalType())).collect(Collectors.groupingBy(WarehouseDetailAbnormalDO::getProcessed));
        int result = warehouseMap.getOrDefault(WarehouseAbnormalStatusEnum.PENDING_PROCESSING.getCode(), Collections.emptyList()).size();
        List<WarehouseDetailAbnormalDO> processedList = warehouseMap.get(WarehouseAbnormalStatusEnum.PROCESSED.getCode());
        if (CollectionUtils.isEmpty(processedList)) {
            return result;
        }
        List<Long> abnormalIds = processedList.stream().map(WarehouseDetailAbnormalDO::getAbnormalId).collect(Collectors.toList());
        List<EmployeeAbnormalOperationRecordDO> employeeAbnormalOperationRecordDOS = abnormalOperationRecordDao.selectByAbnormalList(abnormalIds);
        List<Long> confirmAbnormalIds = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(employeeAbnormalOperationRecordDOS)) {
            List<EmployeeAbnormalOperationRecordDO> abnormalOperationRecordList = employeeAbnormalOperationRecordDOS
                    .stream()
                    .filter(abnormalOperation -> AbnormalOperationTypeEnum.ABNORMAL_CONFIRM.getCode().equals(abnormalOperation.getOperationType())).collect(Collectors.toList());
            confirmAbnormalIds = abnormalOperationRecordList.stream().map(EmployeeAbnormalOperationRecordDO::getAbnormalId).collect(Collectors.toList());
            result += abnormalOperationRecordList.size();
        }

        if (Objects.equals(AttendanceAbnormalTypeEnum.ABNORMAL_DURATION.getCode(), abnormalType)) {
            List<Long> finalConfirmAbnormalIds = confirmAbnormalIds;
            long abnormalDurationCount = processedList.stream().filter(abnormal -> Objects.equals(AttendanceAbnormalTypeEnum.ABNORMAL_DURATION.getCode(), abnormal.getAbnormalType())
                    && !finalConfirmAbnormalIds.contains(abnormal.getAbnormalId())).count();
            result += abnormalDurationCount;
        }

        return result;
    }

    private Integer abnormalDurationCount(List<WarehouseDetailAbnormalDO> abnormalList, Set<Long> warehouseIds) {
        String abnormalType = AttendanceAbnormalTypeEnum.ABNORMAL_DURATION.getCode();
        Map<Integer, List<WarehouseDetailAbnormalDO>> warehouseMap = abnormalList.stream().filter(e -> abnormalType.equals(e.getAbnormalType())).collect(Collectors.groupingBy(WarehouseDetailAbnormalDO::getProcessed));
        List<WarehouseDetailAbnormalDO> processingAbnormalList = warehouseMap.getOrDefault(WarehouseAbnormalStatusEnum.PENDING_PROCESSING.getCode(), Collections.emptyList());
        int result = processingAbnormalList.size();
        Set<Long> warehouseIdList = processingAbnormalList.stream().map(WarehouseDetailAbnormalDO::getWarehouseDetailId).collect(Collectors.toSet());
        if (CollectionUtils.isNotEmpty(warehouseIdList)) {
            warehouseIds.addAll(warehouseIdList);
        }
        List<WarehouseDetailAbnormalDO> processedList = warehouseMap.get(WarehouseAbnormalStatusEnum.PROCESSED.getCode());
        if (CollectionUtils.isEmpty(processedList)) {
            return result;
        }
        Map<Long, List<WarehouseDetailAbnormalDO>> processedMap = processedList.stream().collect(Collectors.groupingBy(WarehouseDetailAbnormalDO::getWarehouseDetailId));
        for (Long warehouseId : processedMap.keySet()) {
            List<WarehouseDetailAbnormalDO> warehouseDetailAbnormalDOS = processedMap.get(warehouseId);

            List<Long> abnormalIds = warehouseDetailAbnormalDOS.stream().map(WarehouseDetailAbnormalDO::getAbnormalId).collect(Collectors.toList());
            List<EmployeeAbnormalOperationRecordDO> employeeAbnormalOperationRecordDOS = abnormalOperationRecordDao.selectByAbnormalList(abnormalIds);
            List<Long> confirmAbnormalIds = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(employeeAbnormalOperationRecordDOS)) {
                List<EmployeeAbnormalOperationRecordDO> abnormalOperationRecordList = employeeAbnormalOperationRecordDOS
                        .stream()
                        .filter(abnormalOperation -> AbnormalOperationTypeEnum.ABNORMAL_CONFIRM.getCode().equals(abnormalOperation.getOperationType())).collect(Collectors.toList());
                confirmAbnormalIds = abnormalOperationRecordList.stream().map(EmployeeAbnormalOperationRecordDO::getAbnormalId).collect(Collectors.toList());
                result += abnormalOperationRecordList.size();
                warehouseIds.add(warehouseId);
            }

            if (Objects.equals(AttendanceAbnormalTypeEnum.ABNORMAL_DURATION.getCode(), abnormalType)) {
                List<Long> finalConfirmAbnormalIds = confirmAbnormalIds;
                int abnormalDurationCount = Math.toIntExact(warehouseDetailAbnormalDOS.stream().filter(abnormal -> Objects.equals(AttendanceAbnormalTypeEnum.ABNORMAL_DURATION.getCode(), abnormal.getAbnormalType())
                        && !finalConfirmAbnormalIds.contains(abnormal.getAbnormalId())).count());
                result += abnormalDurationCount;
                if (abnormalDurationCount > 0) {
                    warehouseIds.add(warehouseId);
                }
            }
        }

        return result;
    }


    private List<DateReportSimpleVO> convertSimpleDateReport(List<WarehouseDetailDO> warehouseDetailList) {
        Map<Long, UserInfoDO> userNameMap = userInfoDao.getByUserIds(warehouseDetailList.stream().map(WarehouseDetailDO::getUserId).distinct().collect(Collectors.toList()))
                .stream()
                .collect(Collectors.toMap(UserInfoDO::getId, Function.identity(), (v1, v2) -> v1));

        List<Long> ocIdList = warehouseDetailList.stream()
                .map(o -> Arrays.asList(o.getOcId(), o.getUserOcId()))
                .flatMap(Collection::stream)
                .distinct()
                .collect(Collectors.toList());
        Map<Long, String> deptMap = getOcMap(ocIdList);

        List<String> vendorCodeList = warehouseDetailList.stream()
                .map(o -> Arrays.asList(o.getVendorCode(), o.getUserVendorCode()))
                .flatMap(Collection::stream)
                .distinct()
                .collect(Collectors.toList());
        Map<String, String> vendorMap = warehouseSupplierService.getSupplierByCodes(vendorCodeList);

        Map<Long, List<WarehouseRecordDO>> warehouseRecordMap = warehouseRecordDao.selectByWarehouseDetailIds(warehouseDetailList.stream().map(WarehouseDetailDO::getId).distinct().collect(Collectors.toList()))
                .stream()
                .collect(Collectors.groupingBy(WarehouseRecordDO::getWarehouseDetailId));

        return warehouseDetailList.stream().map(warehouse -> {
            DateReportSimpleVO reportSimpleVO = new DateReportSimpleVO();
            reportSimpleVO.setId(warehouse.getId());
            reportSimpleVO.setOcId(warehouse.getOcId());
            reportSimpleVO.setOcName(deptMap.get(warehouse.getOcId()));
            reportSimpleVO.setVendorId(warehouse.getVendorId());
            reportSimpleVO.setVendorCode(warehouse.getVendorCode());
            reportSimpleVO.setVendorName(vendorMap.get(warehouse.getVendorCode()));
            reportSimpleVO.setUserCode(warehouse.getUserCode());
            UserInfoDO user = userNameMap.getOrDefault(warehouse.getUserId(), new UserInfoDO());
            reportSimpleVO.setUserName(user.getUserName());
            reportSimpleVO.setUnifiedUserName(BusinessFieldUtils.getUnifiedUserName(user.getUserName(), user.getUserNameEn()) + BusinessConstant.LEFT_BRACKET + user.getUserCode() + BusinessConstant.RIGHT_BRACKET);
            reportSimpleVO.setClassName(warehouse.getClassesName());
            reportSimpleVO.setWarehouseDate(warehouse.getWarehouseDate());
            if (CollectionUtils.isEmpty(warehouseRecordMap.get(warehouse.getId()))) {
                return reportSimpleVO;
            }
            List<WarehouseRecordDO> warehouseRecordDOList = warehouseRecordMap.get(warehouse.getId());
            Optional<WarehouseRecordDO> inOptional = warehouseRecordDOList.stream()
                    .filter(record -> Objects.equals(WarehouseTypeEnum.IN.getCode(), record.getRecordType()))
                    .findFirst();
            inOptional.ifPresent(warehouseRecord -> reportSimpleVO.setInTime(warehouseRecord.getWarehouseTime()));
            List<WarehouseRecordDO> outList = warehouseRecordDOList.stream()
                    .filter(record -> Objects.equals(WarehouseTypeEnum.OUT.getCode(), record.getRecordType()))
                    .sorted(Comparator.comparing(WarehouseRecordDO::getWarehouseTime).reversed())
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(outList)) {
                reportSimpleVO.setOutTime(outList.get(0).getWarehouseTime());
            }
            return reportSimpleVO;
        }).collect(Collectors.toList());
    }

    private List<DateReportVO> convertDateReport(List<WarehouseDetailDO> warehouseDetailDOList) {
        List<Long> userIdList = warehouseDetailDOList.stream().map(WarehouseDetailDO::getUserId).distinct().collect(Collectors.toList());
        Map<Long, UserInfoDO> userNameMap = userInfoDao.getByUserIds(userIdList)
                .stream()
                .collect(Collectors.toMap(UserInfoDO::getId, Function.identity(), (v1, v2) -> v1));

        List<Long> ocIdList = warehouseDetailDOList.stream()
                .map(o -> Arrays.asList(o.getOcId(), o.getUserOcId()))
                .flatMap(Collection::stream)
                .distinct()
                .collect(Collectors.toList());
        Map<Long, String> ocMap = getOcMap(ocIdList);

        List<String> vendorCodeList = warehouseDetailDOList.stream()
                .map(o -> Arrays.asList(o.getVendorCode(), o.getUserVendorCode()))
                .flatMap(Collection::stream)
                .distinct()
                .collect(Collectors.toList());
        Map<String, String> vendorMap = warehouseSupplierService.getSupplierByCodes(vendorCodeList);

        List<Long> detailIdList = warehouseDetailDOList.stream().map(WarehouseDetailDO::getId).collect(Collectors.toList());
        Map<Long, List<WarehouseRecordDO>> warehouseRecordMap = warehouseRecordDao.selectByWarehouseDetailIds(detailIdList)
                .stream()
                .collect(Collectors.groupingBy(WarehouseRecordDO::getWarehouseDetailId));

        return warehouseDetailDOList.stream()
                .map(o -> {
                    DateReportVO dateReportVO = new DateReportVO();
                    dateReportVO.setId(o.getId());
                    dateReportVO.setWarehouseDate(o.getWarehouseDate());
                    dateReportVO.setCountry(o.getCountry());
                    dateReportVO.setCity(o.getCity());
                    dateReportVO.setInOcId(o.getOcId());
                    dateReportVO.setInOcName(ocMap.get(o.getOcId()));
                    dateReportVO.setInVendorId(o.getVendorId());
                    dateReportVO.setInVendorCode(o.getVendorCode());
                    if (!BusinessConstant.IMILE_VIRTUAL_VENDOR_ID.equals(dateReportVO.getInVendorId())) {
                        dateReportVO.setInVendorName(vendorMap.get(o.getVendorCode()));
                    }
                    UserInfoDO user = userNameMap.getOrDefault(o.getUserId(), new UserInfoDO());
                    dateReportVO.setOcId(o.getUserOcId());
                    dateReportVO.setOcName(ocMap.get(o.getUserOcId()));
                    dateReportVO.setVendorId(o.getUserVendorId());
                    dateReportVO.setVendorCode(o.getUserVendorCode());
                    if (!BusinessConstant.IMILE_VIRTUAL_VENDOR_ID.equals(dateReportVO.getVendorId())) {
                        dateReportVO.setVendorName(vendorMap.get(o.getUserVendorCode()));
                    }
                    dateReportVO.setUserId(user.getId());
                    dateReportVO.setUserCode(user.getUserCode());
                    dateReportVO.setUserName(user.getUserName());
                    dateReportVO.setUnifiedUserName(BusinessFieldUtils.getUnifiedUserName(user.getUserName(), user.getUserNameEn()) + BusinessConstant.LEFT_BRACKET + user.getUserCode() + BusinessConstant.RIGHT_BRACKET);
                    EmploymentTypeEnum employmentTypeEnum = EmploymentTypeEnum.getByCode(o.getEmployeeType());
                    dateReportVO.setEmployeeType(RequestInfoHolder.isChinese() ? employmentTypeEnum.getDesc() : employmentTypeEnum.getDescEn());
                    dateReportVO.setEmployeeTypeCode(o.getEmployeeType());
                    dateReportVO.setSex(user.getSex());
                    dateReportVO.setSexCode(convertSexCode(user.getSex()));
                    dateReportVO.setSalaryDate(o.getSalaryDate());
                    dateReportVO.setClassType(o.getClassesType());
                    dateReportVO.setClassName(o.getClassesName());
                    dateReportVO.setRequiredAttendanceTime(o.getRequiredAttendanceTime());
                    dateReportVO.setLegalWorkingHours(o.getLegalWorkingHours());
                    dateReportVO.setActualAttendanceTime(o.getActualAttendanceTime());
                    dateReportVO.setActualWorkingHours(o.getActualWorkingHours());
                    dateReportVO.setAbsenceTime(o.getAbsenceTime());
                    dateReportVO.setOvertimeHours(o.getOvertimeHours());
                    dateReportVO.setWarehouseActualAttendanceTime(o.getWarehouseActualAttendanceTime());
                    dateReportVO.setResultCode(o.getAttendanceStatus());
                    dateReportVO.setOcLongitude(o.getOcLongitude());
                    dateReportVO.setOcLatitude(o.getOcLatitude());
                    dateReportVO.setWarehouseAttendanceCode(o.getWarehouseAttendanceCode());
                    dateReportVO.setConfirmStatus(o.getConfirmStatus());
                    dateReportVO.setPunchStatus(o.getPunchStatus());
                    dateReportVO.setEmploymentForm(o.getEmploymentForm());
                    List<WarehouseRecordDO> warehouseRecordDOList = warehouseRecordMap.get(o.getId());
                    if (CollectionUtils.isEmpty(warehouseRecordDOList)) {
                        return dateReportVO;
                    }

                    Optional<WarehouseRecordDO> inOptional = warehouseRecordDOList.stream()
                            .filter(record -> Objects.equals(WarehouseTypeEnum.IN.getCode(), record.getRecordType()))
                            .findFirst();
                    inOptional.ifPresent(warehouseRecord -> dateReportVO.setInTime(warehouseRecord.getWarehouseTime()));
                    List<WarehouseRecordDO> outList = warehouseRecordDOList.stream()
                            .filter(record -> Objects.equals(WarehouseTypeEnum.OUT.getCode(), record.getRecordType()))
                            .sorted(Comparator.comparing(WarehouseRecordDO::getWarehouseTime).reversed())
                            .collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(outList)) {
                        dateReportVO.setOutTime(outList.get(0).getWarehouseTime());
                    }
                    dateReportVO.setEntryAndExitTime(convertEntryAndExitTime(warehouseRecordDOList));
                    return dateReportVO;
                }).collect(Collectors.toList());
    }

    private List<MonthReportVO> convertMonthReport(ReportParam param, List<WarehouseDetailDO> list) {
        List<Long> ocIdList = list.stream()
                .map(o -> Arrays.asList(o.getOcId(), o.getUserOcId()))
                .flatMap(Collection::stream)
                .distinct()
                .collect(Collectors.toList());
        Map<Long, String> ocMap = getOcMap(ocIdList);

        List<String> vendorCodeList = list.stream()
                .map(o -> Arrays.asList(o.getVendorCode(), o.getUserVendorCode()))
                .flatMap(Collection::stream)
                .distinct()
                .collect(Collectors.toList());
        Map<String, String> vendorMap = warehouseSupplierService.getSupplierByCodes(vendorCodeList);

        List<Long> userIdList = list.stream()
                .map(WarehouseDetailDO::getUserId)
                .collect(Collectors.toList());
        Map<Long, UserInfoDO> userNameMap = userInfoDao.getByUserIds(userIdList)
                .stream()
                .collect(Collectors.toMap(UserInfoDO::getId, Function.identity(), (v1, v2) -> v1));

        WarehouseDetailQuery detailParam = new WarehouseDetailQuery();
        detailParam.setStartDate(param.getStartDate());
        detailParam.setEndDate(param.getEndDate());
        detailParam.setUserIdList(userIdList);
        List<WarehouseDetailDO> warehouseDetailDOList = warehouseDetailDao.selectPage(detailParam);

        Map<String, Map<Date, BigDecimal>> map = warehouseDetailDOList.stream()
                .collect(Collectors.groupingBy(o -> o.getUserId() + BusinessConstant.WELL_NO + o.getOcId() + BusinessConstant.WELL_NO + o.getVendorId(),
                        Collectors.toMap(WarehouseDetailDO::getWarehouseDate, o -> Convert.toBigDecimal(o.getStayDuration()).divide(BusinessConstant.MINUTES, 2, RoundingMode.HALF_UP), BigDecimal::add)));

        return list.stream()
                .map(o -> {
                    MonthReportVO monthReportVO = new MonthReportVO();
                    Map<Date, BigDecimal> orDefault = map.getOrDefault(o.getUserId() + BusinessConstant.WELL_NO + o.getOcId() + BusinessConstant.WELL_NO + o.getVendorId(), new HashMap<>());
                    for (int i = 0; i < 31; i++) {
                        BiConsumer<MonthReportVO, String> setter = monthReportVO.getSetter(i + 1);
                        Date localDate = DateUtil.offsetDay(param.getStartDate(), i);
                        BigDecimal bigDecimal = orDefault.get(localDate);
                        setter.accept(monthReportVO, Objects.isNull(bigDecimal) ? "0 H" : bigDecimal.stripTrailingZeros().toPlainString() + " H");
                    }
                    monthReportVO.setCountry(o.getCountry());
                    monthReportVO.setCity(o.getCity());
                    monthReportVO.setInOcId(o.getOcId());
                    monthReportVO.setInOcName(ocMap.get(o.getOcId()));
                    monthReportVO.setInVendorId(o.getVendorId());
                    if (!BusinessConstant.IMILE_VIRTUAL_VENDOR_ID.equals(monthReportVO.getInVendorId())) {
                        monthReportVO.setInVendorName(vendorMap.get(o.getVendorCode()));
                    }

                    UserInfoDO user = userNameMap.getOrDefault(o.getUserId(), new UserInfoDO());
                    monthReportVO.setOcId(o.getUserOcId());
                    monthReportVO.setOcName(ocMap.get(o.getUserOcId()));
                    monthReportVO.setVendorId(o.getUserVendorId());
                    monthReportVO.setVendorName(vendorMap.get(o.getUserVendorCode()));
                    if (!BusinessConstant.IMILE_VIRTUAL_VENDOR_ID.equals(monthReportVO.getVendorId())) {
                        monthReportVO.setVendorName(vendorMap.get(o.getUserVendorCode()));
                    }
                    monthReportVO.setUserId(user.getId());
                    monthReportVO.setUserCode(user.getUserCode());
                    monthReportVO.setUserName(user.getUserName());
                    monthReportVO.setUnifiedUserName(BusinessFieldUtils.getUnifiedUserName(user.getUserName(), user.getUserNameEn()) + BusinessConstant.LEFT_BRACKET + user.getUserCode() + BusinessConstant.RIGHT_BRACKET);
                    EmploymentTypeEnum employmentTypeEnum = EmploymentTypeEnum.getByCode(user.getEmployeeType());
                    monthReportVO.setEmployeeType(RequestInfoHolder.isChinese() ? employmentTypeEnum.getDesc() : employmentTypeEnum.getDescEn());
                    monthReportVO.setEmploymentForm(o.getEmploymentForm());
                    return monthReportVO;
                }).collect(Collectors.toList());
    }

    private List<DateReportVO> convertDateReportNoBingShift(List<WarehouseRecordDO> warehouseRecordDOS, List<WarehouseDetailDO> warehouseDetailDOS) {
        List<Long> userIdList = warehouseDetailDOS.stream().map(WarehouseDetailDO::getUserId).distinct().collect(Collectors.toList());
        Map<Long, UserInfoDO> userNameMap = userInfoDao.getByUserIds(userIdList)
                .stream().collect(Collectors.toMap(UserInfoDO::getId, Function.identity(), (v1, v2) -> v1));

        List<Long> ocIdList = warehouseDetailDOS.stream()
                .map(o -> Arrays.asList(o.getOcId(), o.getUserOcId()))
                .flatMap(Collection::stream)
                .distinct()
                .collect(Collectors.toList());
        Map<Long, String> ocMap = getOcMap(ocIdList);

        List<String> vendorCodeList = warehouseDetailDOS.stream()
                .map(o -> Arrays.asList(o.getVendorCode(), o.getUserVendorCode()))
                .flatMap(Collection::stream)
                .distinct()
                .collect(Collectors.toList());
        Map<String, String> vendorMap = warehouseSupplierService.getSupplierByCodes(vendorCodeList);

        List<Long> warehouseDetailIds = warehouseDetailDOS.stream().map(WarehouseDetailDO::getId).collect(Collectors.toList());
        Map<Long, List<WarehouseRecordDO>> warehouseRecordMap = warehouseRecordDOS
                .stream()
                .filter(record -> Objects.nonNull(record.getWarehouseDetailId())
                        && warehouseDetailIds.contains(record.getWarehouseDetailId()))
                .collect(Collectors.groupingBy(WarehouseRecordDO::getWarehouseDetailId));

        return warehouseDetailDOS
                .stream()
                .map(warehouseDetail -> {
                    DateReportVO dateReportVO = new DateReportVO();
                    dateReportVO.setId(warehouseDetail.getId());
                    dateReportVO.setInOcId(warehouseDetail.getOcId());
                    dateReportVO.setInOcName(ocMap.get(warehouseDetail.getOcId()));
                    dateReportVO.setInVendorId(warehouseDetail.getVendorId());
                    dateReportVO.setInVendorCode(warehouseDetail.getVendorCode());
                    dateReportVO.setInVendorName(vendorMap.get(warehouseDetail.getVendorCode()));
                    dateReportVO.setUserId(warehouseDetail.getUserId());
                    dateReportVO.setUserCode(warehouseDetail.getUserCode());
                    UserInfoDO user = userNameMap.getOrDefault(warehouseDetail.getUserId(), new UserInfoDO());
                    dateReportVO.setUserName(user.getUserName());
                    dateReportVO.setUnifiedUserName(BusinessFieldUtils.getUnifiedUserName(user.getUserName(), user.getUserNameEn()) + BusinessConstant.LEFT_BRACKET + user.getUserCode() + BusinessConstant.RIGHT_BRACKET);
                    dateReportVO.setWarehouseDate(warehouseDetail.getWarehouseDate());
                    List<WarehouseRecordDO> warehouseRecordDOList = warehouseRecordMap.get(warehouseDetail.getId());
                    if (CollectionUtils.isEmpty(warehouseRecordDOList)) {
                        return dateReportVO;
                    }
                    Optional<Date> inOptional = warehouseRecordDOList
                            .stream()
                            .filter(warehouseRecord -> Objects.equals(WarehouseTypeEnum.IN.getCode(), warehouseRecord.getRecordType()))
                            .map(WarehouseRecordDO::getWarehouseTime)
                            .findFirst();
                    inOptional.ifPresent(dateReportVO::setInTime);
                    List<WarehouseRecordDO> outRecordList = warehouseRecordDOList
                            .stream()
                            .filter(warehouseRecord -> Objects.equals(WarehouseTypeEnum.OUT.getCode(), warehouseRecord.getRecordType()))
                            .sorted(Comparator.comparing(WarehouseRecordDO::getWarehouseTime, Comparator.reverseOrder()))
                            .collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(outRecordList)) {
                        dateReportVO.setOutTime(outRecordList.get(0).getWarehouseTime());
                    }
                    return dateReportVO;
                }).collect(Collectors.toList());
    }


    private static String convertSexCode(Integer sex) {
        if (Objects.isNull(sex)) {
            return null;
        }
        return Objects.equals(BusinessConstant.ONE, sex) ? BusinessConstant.MALE : BusinessConstant.FEMALE;
    }

    private static String convertEntryAndExitTime(List<WarehouseRecordDO> warehouseRecordList) {
        List<Date> inRecordList = warehouseRecordList.stream()
                .filter(record -> Objects.equals(WarehouseTypeEnum.IN.getCode(), record.getRecordType()))
                .map(WarehouseRecordDO::getWarehouseTime)
                .sorted()
                .collect(Collectors.toList());
        List<Date> outRecordList = warehouseRecordList.stream()
                .filter(record -> Objects.equals(WarehouseTypeEnum.OUT.getCode(), record.getRecordType()))
                .map(WarehouseRecordDO::getWarehouseTime)
                .sorted()
                .collect(Collectors.toList());
        int length = inRecordList.size() + outRecordList.size();

        Iterator<Date> inIterator = inRecordList.iterator();
        Iterator<Date> outIterator = outRecordList.iterator();

        StringBuilder result = new StringBuilder();
        Date inDate = getNextDate(inIterator);
        Date outDate = getNextDate(outIterator);

        for (int i = 0; i < length; i++) {
            if (inDate != null && (outDate == null || inDate.before(outDate))) {
                result.append(formatDate(inDate)).append(" ~ ");
                inDate = getNextDate(inIterator);
                if (outDate != null && inDate == null || (outDate != null && inDate.after(outDate))) {
                    result.append(formatDate(outDate)).append("\n");
                    outDate = getNextDate(outIterator);
                } else {
                    result.append("-\n");
                }
            } else if (outDate != null) {
                result.append("         -          ~ ").append(formatDate(outDate)).append("\n");
                outDate = getNextDate(outIterator);
            }
        }

        return result.toString();
    }

    private static Date getNextDate(Iterator<Date> iterator) {
        return iterator.hasNext() ? iterator.next() : null;
    }

    private static String formatDate(Date date) {
        SimpleDateFormat sdf = new SimpleDateFormat(DateFormatterUtil.FORMAT_YYYY_MM_DD_HH_MM_SS);
        return (date != null) ? sdf.format(date) : null;
    }

    private void convertWarehouseRecordList(WarehouseDetailDO warehouseDetailDO, DateDetailReportVO result) {
        List<WarehouseRecordDO> warehouseRecordDOList = warehouseRecordDao.selectByWarehouseDetailIds(Collections.singletonList(warehouseDetailDO.getId()));
        if (CollectionUtils.isEmpty(warehouseRecordDOList)) {
            return;
        }
        List<Long> faceRecordIds = warehouseRecordDOList.stream().map(WarehouseRecordDO::getFaceRecordId).distinct().collect(Collectors.toList());
        List<WarehouseFaceRecordDO> faceRecordDOList = faceRecordDao.getByIdList(faceRecordIds);
        Map<Long, WarehouseFaceRecordDO> faceRecordMap = faceRecordDOList.stream().collect(Collectors.toMap(WarehouseFaceRecordDO::getId, Function.identity()));

        //获取短链加密的集合
        List<WarehouseFaceRecordDO> faceKeyEncList = faceRecordDOList.stream()
                .filter(faceRecord -> StringUtils.isNotBlank(faceRecord.getFacePhoto())
                        && !faceRecord.getFacePhoto().startsWith(BusinessConstant.FACE_UPLOAD_FILE_PATH_PREFIX))
                .collect(Collectors.toList());

        List<FaceDecryptDTO> faceDecryptDTOList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(faceKeyEncList)) {
            List<FaceDecryptApiParam> params = new ArrayList<>();
            for (WarehouseFaceRecordDO faceRecordDO : faceKeyEncList) {
                FaceDecryptApiParam facePhotoApiParam = new FaceDecryptApiParam();
                facePhotoApiParam.setEncFileKey(faceRecordDO.getFacePhoto());
                facePhotoApiParam.setCode(faceRecordDO.getId() + BusinessConstant.UNDER_LINE + BusinessConstant.FACE_PHOTO);
                params.add(facePhotoApiParam);

                FaceDecryptApiParam recognitionPhotoApiParam = new FaceDecryptApiParam();
                recognitionPhotoApiParam.setEncFileKey(faceRecordDO.getRecognitionPhoto());
                recognitionPhotoApiParam.setCode(faceRecordDO.getId() + BusinessConstant.UNDER_LINE + BusinessConstant.RECOGNITION_PHOTO);
                params.add(recognitionPhotoApiParam);
            }
            faceDecryptDTOList = recognitionService.faceBatchDecrypt(params);
        }

        List<FaceDecryptDTO> finalFaceDecryptDTOList = faceDecryptDTOList;
        List<WarehouseRecordVO> warehouseRecordList = warehouseRecordDOList.stream().map(record -> {
            WarehouseRecordVO warehouseRecordVO = new WarehouseRecordVO();
            warehouseRecordVO.setId(record.getId());
            warehouseRecordVO.setWarehouseTime(record.getWarehouseTime());
            warehouseRecordVO.setRecordType(record.getRecordType());
            warehouseRecordVO.setCreateUserName(record.getCreateUserName());
            warehouseRecordVO.setOcLongitude(record.getOcLongitude());
            warehouseRecordVO.setOcLatitude(record.getOcLatitude());
            warehouseRecordVO.setDistance(record.getDistance());
            WarehouseFaceRecordDO faceRecordDO = faceRecordMap.get(record.getFaceRecordId());
            if (Objects.isNull(faceRecordDO)) {
                return warehouseRecordVO;
            }
            List<FaceDecryptDTO> filterFaceDecryptList = finalFaceDecryptDTOList.stream()
                    .filter(faceDecrypt -> faceDecrypt.getCode().startsWith(faceRecordDO.getId().toString()))
                    .collect(Collectors.toList());
            WarehouseRecordVO.FaceRecognitionDetail faceRecognitionDetail = new WarehouseRecordVO.FaceRecognitionDetail();
            faceRecognitionDetail.setRecognitionScore(faceRecordDO.getRecognitionScore());
            faceRecognitionDetail.setPass(faceRecordDO.getRecognitionScore().compareTo(BusinessConstant.FACE_PASS) > -1);
            if (CollectionUtils.isEmpty(filterFaceDecryptList)) {
                faceRecognitionDetail.setFacePhotoKey(faceRecordDO.getFacePhoto());
                faceRecognitionDetail.setRecognitionPhotoKey(faceRecordDO.getRecognitionPhoto());
            } else {
                for (FaceDecryptDTO faceDecryptDTO : filterFaceDecryptList) {
                    String[] keyStr = faceDecryptDTO.getCode().split(BusinessConstant.UNDER_LINE);
                    if (BusinessConstant.FACE_PHOTO.equals(keyStr[1])) {
                        faceRecognitionDetail.setFacePhotoKey(faceDecryptDTO.getFileKey());
                    }
                    if (BusinessConstant.RECOGNITION_PHOTO.equals(keyStr[1])) {
                        faceRecognitionDetail.setRecognitionPhotoKey(faceDecryptDTO.getFileKey());
                    }
                }
            }
            warehouseRecordVO.setFaceRecognitionDetail(faceRecognitionDetail);
            return warehouseRecordVO;
        }).collect(Collectors.toList());
        result.setWarehouseRecordList(warehouseRecordList);
    }

    private void convertAttendanceResult(WarehouseDetailDO warehouseDetailDO,
                                         DateDetailReportVO result) {
        DateDetailReportVO.AttendanceResult attendanceResult = new DateDetailReportVO.AttendanceResult();
        result.setAttendanceResult(attendanceResult);

        attendanceResult.setId(warehouseDetailDO.getId());
        attendanceResult.setOcId(warehouseDetailDO.getOcId());
        attendanceResult.setClassesType(warehouseDetailDO.getClassesType());
        attendanceResult.setClassesName(warehouseDetailDO.getClassesName());
        attendanceResult.setResultCode(warehouseDetailDO.getAttendanceStatus());
        attendanceResult.setActualAttendanceTime(warehouseDetailDO.getActualAttendanceTime());
        attendanceResult.setActualWorkingHours(warehouseDetailDO.getActualWorkingHours());
        attendanceResult.setUserCode(warehouseDetailDO.getUserCode());

        List<WarehouseDetailAbnormalDO> warehouseDetailAbnormalList = warehouseDetailAbnormalDao.selectByWarehouseDetailIds(Collections.singletonList(warehouseDetailDO.getId()));
        if (CollectionUtils.isEmpty(warehouseDetailAbnormalList)) {
            return;
        }

        //异常处理
        List<Long> abnormalIds = warehouseDetailAbnormalList.stream().map(WarehouseDetailAbnormalDO::getAbnormalId).distinct().collect(Collectors.toList());
        List<EmployeeAbnormalAttendanceDO> abnormalAttendanceDOList = abnormalAttendanceDao.selectAbnormalByIdList(abnormalIds)
                .stream()
                .filter(abnormal -> Objects.equals(IsDeleteEnum.NO.getCode(), abnormal.getIsDelete()))
                .collect(Collectors.toList());
        List<Long> abnormalIdList = abnormalAttendanceDOList.stream().map(EmployeeAbnormalAttendanceDO::getId).distinct().collect(Collectors.toList());
        Map<Long, List<EmployeeAbnormalOperationRecordDO>> abnormalOperationRecordMap = abnormalOperationRecordDao.selectByAbnormalList(abnormalIdList)
                .stream().collect(Collectors.groupingBy(EmployeeAbnormalOperationRecordDO::getAbnormalId));

        List<DateDetailReportVO.AttendanceAbnormal> attendanceAbnormalList = new ArrayList<>();
        for (EmployeeAbnormalAttendanceDO abnormalAttendanceDO : abnormalAttendanceDOList) {
            List<EmployeeAbnormalOperationRecordDO> abnormalOperationRecordDOList = abnormalOperationRecordMap.get(abnormalAttendanceDO.getId());
            if (CollectionUtils.isEmpty(abnormalOperationRecordDOList)) {
                continue;
            }
            for (EmployeeAbnormalOperationRecordDO abnormalOperationRecordDO : abnormalOperationRecordDOList) {
                DateDetailReportVO.AttendanceAbnormal attendanceAbnormal = new DateDetailReportVO.AttendanceAbnormal();
                attendanceAbnormal.setAbnormalId(abnormalAttendanceDO.getId());
                attendanceAbnormal.setAbnormalType(abnormalAttendanceDO.getAbnormalType());
                attendanceAbnormal.setAbnormalStatus(abnormalAttendanceDO.getStatus());
                attendanceAbnormal.setHandleType(abnormalOperationRecordDO.getOperationType());
                attendanceAbnormal.setId(abnormalOperationRecordDO.getId());
                attendanceAbnormalList.add(attendanceAbnormal);
            }
        }

        result.setAttendanceAbnormalList(attendanceAbnormalList);

        List<DateDetailReportVO.AttendanceAbnormalType> allAttendanceAbnormalTypes = new ArrayList<>();

        //已过期
        List<EmployeeAbnormalAttendanceDO> expireAbnormalList = abnormalAttendanceDOList
                .stream()
                .filter(abnormal -> AbnormalAttendanceStatusEnum.EXPIRED.getCode().equals(abnormal.getStatus()))
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(expireAbnormalList)) {
            List<DateDetailReportVO.AttendanceAbnormalType> expireAttendanceAbnormalTypes = expireAbnormalList.stream()
                    .map(abnormal -> {
                        DateDetailReportVO.AttendanceAbnormalType abnormalType = new DateDetailReportVO.AttendanceAbnormalType();
                        abnormalType.setAbnormalId(abnormal.getId());
                        abnormalType.setAbnormalType(abnormal.getAbnormalType());
                        abnormalType.setProcessed(WarehouseAbnormalStatusEnum.PROCESSED.getCode());
                        return abnormalType;
                    }).collect(Collectors.toList());
            allAttendanceAbnormalTypes.addAll(expireAttendanceAbnormalTypes);
        }

        //已通过
        List<EmployeeAbnormalAttendanceDO> passAbnormalList = abnormalAttendanceDOList
                .stream()
                .filter(abnormal -> AbnormalAttendanceStatusEnum.PASS.getCode().equals(abnormal.getStatus()))
                .collect(Collectors.toList());
        List<Long> passAbnormalIdList = passAbnormalList.stream().map(EmployeeAbnormalAttendanceDO::getId).collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(passAbnormalIdList)) {
            List<DateDetailReportVO.AttendanceAbnormalType> confirmAttendanceAbnormalTypes = passAbnormalList
                    .stream()
                    .map(abnormal -> {
                        DateDetailReportVO.AttendanceAbnormalType abnormalType = new DateDetailReportVO.AttendanceAbnormalType();
                        abnormalType.setAbnormalId(abnormal.getId());
                        abnormalType.setAbnormalType(abnormal.getAbnormalType());
                        abnormalType.setProcessed(WarehouseAbnormalStatusEnum.PROCESSED.getCode());
                        return abnormalType;
                    }).collect(Collectors.toList());
            allAttendanceAbnormalTypes.addAll(confirmAttendanceAbnormalTypes);
        }

        //待处理的记录
        List<DateDetailReportVO.AttendanceAbnormalType> noProcessAttendanceAbnormalTypes = abnormalAttendanceDOList
                .stream()
                .filter(abnormal -> !AbnormalAttendanceStatusEnum.TYPE_OF_PASS_OR_EXPIRED.contains(abnormal.getStatus()))
                .map(abnormal -> {
                    DateDetailReportVO.AttendanceAbnormalType abnormalType = new DateDetailReportVO.AttendanceAbnormalType();
                    abnormalType.setAbnormalId(abnormal.getId());
                    abnormalType.setAbnormalType(abnormal.getAbnormalType());
                    abnormalType.setProcessed(WarehouseAbnormalStatusEnum.PENDING_PROCESSING.getCode());
                    return abnormalType;
                }).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(noProcessAttendanceAbnormalTypes)) {
            allAttendanceAbnormalTypes.addAll(noProcessAttendanceAbnormalTypes);
        }

        attendanceResult.setAttendanceAbnormalTypes(allAttendanceAbnormalTypes);
    }

    private void convertLeaveDetailList(WarehouseDetailDO warehouseDetailDO, DateDetailReportVO result) {
        ApplicationFormQuery query = new ApplicationFormQuery();
        query.setUserId(warehouseDetailDO.getUserId());
        query.setFromTypeList(Collections.singletonList(FormTypeEnum.LEAVE.getCode()));
        query.setStatusList(Lists.newArrayList(FormStatusEnum.REJECT.getCode(), FormStatusEnum.IN_REVIEW.getCode(), FormStatusEnum.PASS.getCode()));
        List<AttendanceFormDO> applicationFormDOList = attendanceFormDao.selectForm(query);
        if (CollectionUtils.isEmpty(applicationFormDOList)) {
            return;
        }
        List<Long> formIds = applicationFormDOList.stream().map(AttendanceFormDO::getId).collect(Collectors.toList());
        Map<Long, List<AttendanceFormAttrDO>> applicationFormAttrMap = attendanceFormAttrDao.selectFormAttrByFormIdList(formIds)
                .stream().collect(Collectors.groupingBy(AttendanceFormAttrDO::getFormId));

        Long dayId = DateHelper.getDayId(warehouseDetailDO.getWarehouseDate());

        List<DateDetailReportVO.LeaveDetail> leaveDetailList = applicationFormDOList.stream()
                .map(applicationForm -> {
                    DateDetailReportVO.LeaveDetail leaveDetail = new DateDetailReportVO.LeaveDetail();
                    leaveDetail.setFormStatus(applicationForm.getFormStatus());
                    List<AttendanceFormAttrDO> applicationFormAttrDOS = applicationFormAttrMap.get(applicationForm.getId());
                    Date leaveStartTime = null;
                    Date leaveEndTime = null;
                    String leaveType = null;
                    for (AttendanceFormAttrDO applicationFormAttrDO : applicationFormAttrDOS) {
                        if (applicationFormAttrDO.getAttrKey().equals(ApplicationFormAttrKeyEnum.leaveType.getLowerCode())) {
                            leaveType = applicationFormAttrDO.getAttrValue();
                        }
                        if (applicationFormAttrDO.getAttrKey().equals(ApplicationFormAttrKeyEnum.leaveStartDate.getLowerCode())) {
                            leaveStartTime = DateUtil.parseDateTime(applicationFormAttrDO.getAttrValue());
                        }
                        if (applicationFormAttrDO.getAttrKey().equals(ApplicationFormAttrKeyEnum.leaveEndDate.getLowerCode())) {
                            leaveEndTime = DateUtil.parseDateTime(applicationFormAttrDO.getAttrValue());
                        }
                    }
                    leaveDetail.setLeaveType(leaveType);
                    leaveDetail.setLeaveStartTime(leaveStartTime);
                    leaveDetail.setLeaveEndTime(leaveEndTime);
                    return leaveDetail;
                })
                .filter(leaveDetail -> Objects.nonNull(leaveDetail.getLeaveStartTime())
                        && Objects.nonNull(leaveDetail.getLeaveEndTime())
                        && dayId.compareTo(DateHelper.getDayId(leaveDetail.getLeaveStartTime())) > -1
                        && dayId.compareTo(DateHelper.getDayId(leaveDetail.getLeaveEndTime())) < 1)
                .collect(Collectors.toList());
        result.setLeaveDetailList(leaveDetailList);
    }

    private void convertOther(DateDetailReportVO result, WarehouseDetailDO warehouseDetailDO) {
        //历史数据判断
        result.setHistoryData(!Objects.equals(warehouseDetailDO.getInFaceRecordId(), BusinessConstant.LONG_ZERO));

        //去处理按钮展示判断
        CountryDTO countryDTO = countryService.queryCountry(warehouseDetailDO.getCountry());
        Date date = CommonUtil.convertDateByTimeZonePlus(countryDTO.getTimeZone(), new Date());
        Long currentDayId = DateHelper.getDayId(date);
        result.setShow(DateHelper.getDayId(warehouseDetailDO.getWarehouseDate()).compareTo(currentDayId) < 0);
    }


    private DataStatisticsVO convertDataStatisticsVO(DataStatisticsVO dataStatisticsVO,
                                                     List<WarehouseDetailDO> warehouseDetailDOList,
                                                     List<WarehouseRecordDO> warehouseRecordDOS,
                                                     List<WarehouseDetailAbnormalDO> warehouseDetailAbnormalDOS) {
        dataStatisticsVO.setEmployee(convertDataCountResult(warehouseDetailDOList, warehouseRecordDOS, warehouseDetailAbnormalDOS, Lists.newArrayList(EmploymentTypeEnum.EMPLOYEE.getCode(), EmploymentTypeEnum.SUB_EMPLOYEE.getCode())));
        dataStatisticsVO.setLaborDispatch(convertDataCountResult(warehouseDetailDOList, warehouseRecordDOS, warehouseDetailAbnormalDOS, Collections.singletonList(EmploymentTypeEnum.OS_FIXED_SALARY.getCode())));
        DataCountVO total = convertDataCountResult(warehouseDetailDOList, warehouseRecordDOS, warehouseDetailAbnormalDOS, Lists.newArrayList(EmploymentTypeEnum.EMPLOYEE.getCode(), EmploymentTypeEnum.SUB_EMPLOYEE.getCode(), EmploymentTypeEnum.OS_FIXED_SALARY.getCode()));
        total.setWaitClockOutCount(dataStatisticsVO.getEmployee().getWaitClockOutCount() + dataStatisticsVO.getLaborDispatch().getWaitClockOutCount());
        dataStatisticsVO.setTotal(total);
        // 黑名单数量计算
        LambdaQueryWrapper<WarehouseBlackListDO> queryWrapper = Wrappers.lambdaQuery(WarehouseBlackListDO.class);
        queryWrapper.eq(WarehouseBlackListDO::getIsDelete, BusinessConstant.N);
        queryWrapper.eq(WarehouseBlackListDO::getWarehouseDate, DateUtil.formatDate(warehouseDetailDOList.get(0).getWarehouseDate()));
        queryWrapper.eq(WarehouseBlackListDO::getOcId, warehouseDetailDOList.get(0).getOcId());
        queryWrapper.eq(warehouseDetailDOList.get(0).getClassesId() != null, WarehouseBlackListDO::getClassesId, warehouseDetailDOList.get(0).getClassesId());
        List<WarehouseBlackListDO> list = warehouseBlackListDao.list(queryWrapper);
        Map<String, List<WarehouseBlackListDO>> employeeTypeMap = CollectionUtils.isEmpty(list) ? Maps.newHashMap() : list.stream().collect(Collectors.groupingBy(WarehouseBlackListDO::getEmployeeType));
        if (CollectionUtils.isNotEmpty(list)) {
            total.setBlackListCount((int) list.stream().map(WarehouseBlackListDO::getUserCode).distinct().count());
            dataStatisticsVO.getEmployee().setBlackListCount(
                    (int) employeeTypeMap.getOrDefault(EmploymentTypeEnum.EMPLOYEE.getCode(), Lists.newArrayList()).stream().map(WarehouseBlackListDO::getUserCode).distinct().count() +
                            (int) employeeTypeMap.getOrDefault(EmploymentTypeEnum.SUB_EMPLOYEE.getCode(), Lists.newArrayList()).stream().map(WarehouseBlackListDO::getUserCode).distinct().count());
            dataStatisticsVO.getLaborDispatch().setBlackListCount(
                    (int) employeeTypeMap.getOrDefault(EmploymentTypeEnum.OS_FIXED_SALARY.getCode(), Lists.newArrayList()).stream().map(WarehouseBlackListDO::getUserCode).distinct().count());
        }
        List<WarehouseDetailDO> laborDispatchWarehouseList = warehouseDetailDOList.stream().filter(warehouse -> Objects.equals(EmploymentTypeEnum.OS_FIXED_SALARY.getCode(), warehouse.getEmployeeType())).collect(Collectors.toList());
        List<VendorDataCountVO> vendorDetailList = Lists.newArrayList();
        if (CollectionUtils.isEmpty(laborDispatchWarehouseList)) {
            // 供应商黑名单
            if (employeeTypeMap.isEmpty()) {
                return dataStatisticsVO;
            }
            List<WarehouseBlackListDO> fixSalaryBlackList = employeeTypeMap.getOrDefault(EmploymentTypeEnum.OS_FIXED_SALARY.getCode(), Lists.newArrayList());
            if (CollectionUtils.isEmpty(fixSalaryBlackList)) {
                return dataStatisticsVO;
            }
            Map<String, List<WarehouseBlackListDO>> vendorBlackListMap = fixSalaryBlackList.stream().collect(Collectors.groupingBy(WarehouseBlackListDO::getVendorCode));
            Map<String, String> vendorMap = warehouseSupplierService.getSupplierByCodes(new ArrayList<>(vendorBlackListMap.keySet()));
            addBlackListVendorDetailList(vendorBlackListMap, vendorMap, vendorDetailList);
            return dataStatisticsVO;
        }
        Map<String, List<WarehouseDetailDO>> warehouseMap = laborDispatchWarehouseList.stream().collect(Collectors.groupingBy(WarehouseDetailDO::getVendorCode));

        List<String> vendorCodeAllList = new ArrayList<>(warehouseMap.keySet());

        List<WarehouseBlackListDO> fixSalaryBlackList = employeeTypeMap.getOrDefault(EmploymentTypeEnum.OS_FIXED_SALARY.getCode(), Lists.newArrayList());
        Map<String, List<WarehouseBlackListDO>> vendorBlackListMap = CollectionUtils.isEmpty(fixSalaryBlackList) ? Maps.newHashMap() :
                fixSalaryBlackList.stream().collect(Collectors.groupingBy(WarehouseBlackListDO::getVendorCode));
        vendorCodeAllList.addAll(vendorBlackListMap.keySet());
        Map<String, String> vendorMap = warehouseSupplierService.getSupplierByCodes(vendorCodeAllList);
        for (Map.Entry<String, List<WarehouseDetailDO>> warehouseDetail : warehouseMap.entrySet()) {
            List<WarehouseDetailDO> warehouseDetailDOS = warehouseDetail.getValue();
            VendorDataCountVO dataCountResult = new VendorDataCountVO();
            dataCountResult.setVendorCode(warehouseDetail.getKey());
            dataCountResult.setVendorName(vendorMap.get(warehouseDetail.getKey()));
            dataCountResult.setVendorDetailCount(convertDataCountResult(warehouseDetailDOS, warehouseRecordDOS, warehouseDetailAbnormalDOS, Collections.singletonList(EmploymentTypeEnum.OS_FIXED_SALARY.getCode())));
            dataCountResult.setTotalAbnormalCount(dataCountResult.getVendorDetailCount().getTotalAbnormalCount());
            dataCountResult.getVendorDetailCount().setBlackListCount((int) vendorBlackListMap.getOrDefault(warehouseDetail.getKey(), Lists.newArrayList()).stream().map(WarehouseBlackListDO::getUserCode).distinct().count());
            vendorBlackListMap.remove(warehouseDetail.getKey());
            vendorDetailList.add(dataCountResult);
        }
        addBlackListVendorDetailList(vendorBlackListMap, vendorMap, vendorDetailList);
        dataStatisticsVO.setVendorDetailList(vendorDetailList.stream()
                .sorted(Comparator.comparing(VendorDataCountVO::getTotalAbnormalCount).reversed())
                .collect(Collectors.toList()));
        return dataStatisticsVO;
    }

    private void addBlackListVendorDetailList(Map<String, List<WarehouseBlackListDO>> vendorBlackListMap, Map<String, String> vendorMap, List<VendorDataCountVO> vendorDetailList) {
        if (vendorBlackListMap.isEmpty()) {
            return;
        }
        for (String vendorCode : vendorBlackListMap.keySet()) {
            VendorDataCountVO dataCountResult = new VendorDataCountVO();
            dataCountResult.setVendorCode(vendorCode);
            dataCountResult.setVendorName(vendorMap.get(vendorCode));
            DataCountVO vendorDataCount = new DataCountVO();
            vendorDataCount.setEmployeeTypeList(Collections.singletonList(EmploymentTypeEnum.OS_FIXED_SALARY.getCode()));
            vendorDataCount.setBlackListCount((int) vendorBlackListMap.getOrDefault(vendorCode, Lists.newArrayList()).stream().map(WarehouseBlackListDO::getUserCode).distinct().count());
            dataCountResult.setVendorDetailCount(vendorDataCount);
            dataCountResult.setTotalAbnormalCount(0);
            vendorDetailList.add(dataCountResult);
        }
    }

    private DataCountVO convertDataCountResult(List<WarehouseDetailDO> warehouseDetailDOList,
                                               List<WarehouseRecordDO> warehouseRecordDOS,
                                               List<WarehouseDetailAbnormalDO> warehouseDetailAbnormalDOS,
                                               List<String> employeeTypeList) {
        DataCountVO result = new DataCountVO();
        result.setEmployeeTypeList(employeeTypeList);
        List<WarehouseDetailDO> warehouseDetailDOS = warehouseDetailDOList
                .stream()
                .filter(warehouseDetail -> employeeTypeList.contains(warehouseDetail.getEmployeeType()))
                .collect(Collectors.toList());

        List<Long> warehouseIds = warehouseDetailDOS
                .stream()
                .map(WarehouseDetailDO::getId)
                .collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(warehouseRecordDOS)) {

            Map<Long, List<WarehouseRecordDO>> warehouseRecordMap = warehouseRecordDOS
                    .stream()
                    .filter(warehouseRecord -> warehouseIds.contains(warehouseRecord.getWarehouseDetailId()))
                    .collect(Collectors.groupingBy(WarehouseRecordDO::getWarehouseDetailId));

            if (MapUtils.isNotEmpty(warehouseRecordMap)) {
                Integer clockInCount = 0;
                Integer clockOutCount = 0;
                Integer waitClockOutCount = 0;
                for (WarehouseDetailDO warehouseDetailDO : warehouseDetailDOS) {
                    List<WarehouseRecordDO> warehouseRecordDOList = warehouseRecordMap.get(warehouseDetailDO.getId());
                    if (Objects.equals(WarehouseStatusEnum.WAIT_OUT.getCode(), warehouseDetailDO.getWarehouseStatus())) {
                        waitClockOutCount++;
                    }
                    if (CollectionUtils.isNotEmpty(warehouseRecordDOList)) {
                        Optional<WarehouseRecordDO> inOptional = warehouseRecordDOList.stream().filter(item -> Objects.equals(WarehouseTypeEnum.IN.getCode(), item.getRecordType())).findFirst();
                        if (inOptional.isPresent()) {
                            clockInCount++;
                        }
                    }
                    if (Objects.equals(WarehouseStatusEnum.OUT.getCode(), warehouseDetailDO.getWarehouseStatus())) {
                        clockOutCount++;
                    }
                }
                result.setClockInCount(clockInCount);
                result.setClockOutCount(clockOutCount);
                result.setWaitClockOutCount(waitClockOutCount);
            }
        }


        if (CollectionUtils.isNotEmpty(warehouseDetailAbnormalDOS)) {
            //未处理异常
            Map<String, List<WarehouseDetailAbnormalDO>> warehouseAbnormalMap = warehouseDetailAbnormalDOS
                    .stream()
                    .filter(warehouseAbnormal -> warehouseIds.contains(warehouseAbnormal.getWarehouseDetailId())
                            && Objects.equals(WarehouseAbnormalStatusEnum.PENDING_PROCESSING.getCode(), warehouseAbnormal.getProcessed()))
                    .collect(Collectors.groupingBy(WarehouseDetailAbnormalDO::getAbnormalType));

            int lateCount = CollectionUtils.isNotEmpty(warehouseAbnormalMap.get(result.getLateDictCode())) ? warehouseAbnormalMap.get(result.getLateDictCode()).size() : BusinessConstant.ZERO;
            int leaveEarlyCount = CollectionUtils.isNotEmpty(warehouseAbnormalMap.get(result.getLeaveEarlyDictCode())) ? warehouseAbnormalMap.get(result.getLeaveEarlyDictCode()).size() : BusinessConstant.ZERO;
            int clockInLackCount = CollectionUtils.isNotEmpty(warehouseAbnormalMap.get(result.getClockInLackDictCode())) ? warehouseAbnormalMap.get(result.getClockInLackDictCode()).size() : BusinessConstant.ZERO;
            int clockOutLackCount = CollectionUtils.isNotEmpty(warehouseAbnormalMap.get(result.getClockOutLackDictCode())) ? warehouseAbnormalMap.get(result.getClockOutLackDictCode()).size() : BusinessConstant.ZERO;
            int abnormalDurationCount = CollectionUtils.isNotEmpty(warehouseAbnormalMap.get(result.getAbnormalDurationDictCode())) ? warehouseAbnormalMap.get(result.getAbnormalDurationDictCode()).size() : BusinessConstant.ZERO;

            result.setLateCount(lateCount);
            result.setLeaveEarlyCount(leaveEarlyCount);
            result.setClockInLackCount(clockInLackCount);
            result.setClockOutLackCount(clockOutLackCount);
            result.setAbnormalDurationCount(abnormalDurationCount);
            result.calculateTotalAbnormalCount();

            //已处理异常 通过 or 确认异常
            List<WarehouseDetailAbnormalDO> processedList = warehouseDetailAbnormalDOS
                    .stream()
                    .filter(abnormal -> warehouseIds.contains(abnormal.getWarehouseDetailId())
                            && Objects.equals(WarehouseAbnormalStatusEnum.PROCESSED.getCode(), abnormal.getProcessed()))
                    .collect(Collectors.toList());

            Map<Long, WarehouseDetailAbnormalDO> abnormalMap = processedList
                    .stream()
                    .collect(Collectors.toMap(WarehouseDetailAbnormalDO::getAbnormalId, Function.identity()));

            if (CollectionUtils.isEmpty(abnormalMap.keySet())) {
                return result;
            }

            List<DataStatisticsDetailsVO.AbnormalVO> abnormalList = new ArrayList<>();
            Set<Long> confirmAbnormalIds = new HashSet<>();
            List<EmployeeAbnormalOperationRecordDO> employeeAbnormalOperationRecordDOS = abnormalOperationRecordDao.selectByAbnormalList(new ArrayList<>(abnormalMap.keySet()));
            if (CollectionUtils.isNotEmpty(employeeAbnormalOperationRecordDOS)) {
                List<EmployeeAbnormalOperationRecordDO> abnormalOperationRecordList = employeeAbnormalOperationRecordDOS
                        .stream()
                        .filter(abnormalOperation -> AbnormalOperationTypeEnum.ABNORMAL_CONFIRM.getCode().equals(abnormalOperation.getOperationType())).collect(Collectors.toList());

                if (CollectionUtils.isNotEmpty(abnormalOperationRecordList)) {
                    for (EmployeeAbnormalOperationRecordDO abnormalOperationRecord : abnormalOperationRecordList) {
                        WarehouseDetailAbnormalDO warehouseDetailAbnormalDO = abnormalMap.get(abnormalOperationRecord.getAbnormalId());
                        if (Objects.nonNull(warehouseDetailAbnormalDO)) {
                            DataStatisticsDetailsVO.AbnormalVO abnormalVO = new DataStatisticsDetailsVO.AbnormalVO();
                            abnormalVO.setAbnormalId(warehouseDetailAbnormalDO.getAbnormalId());
                            abnormalVO.setAbnormalType(warehouseDetailAbnormalDO.getAbnormalType());
                            abnormalList.add(abnormalVO);
                            confirmAbnormalIds.add(warehouseDetailAbnormalDO.getAbnormalId());
                        }
                    }
                }
            }

            List<DataStatisticsDetailsVO.AbnormalVO> durationAbnormalList = processedList
                    .stream()
                    .filter(abnormal -> Objects.equals(AttendanceAbnormalTypeEnum.ABNORMAL_DURATION.getCode(), abnormal.getAbnormalType())
                            && !confirmAbnormalIds.contains(abnormal.getAbnormalId()))
                    .map(abnormalRecord -> {
                        DataStatisticsDetailsVO.AbnormalVO durationAbnormal = new DataStatisticsDetailsVO.AbnormalVO();
                        durationAbnormal.setAbnormalId(abnormalRecord.getAbnormalId());
                        durationAbnormal.setAbnormalType(abnormalRecord.getAbnormalType());
                        return durationAbnormal;
                    }).collect(Collectors.toList());

            if (CollectionUtils.isNotEmpty(durationAbnormalList)) {
                abnormalList.addAll(durationAbnormalList);
            }

            Map<String, List<DataStatisticsDetailsVO.AbnormalVO>> confirmAbnormalMap = abnormalList
                    .stream()
                    .collect(Collectors.groupingBy(DataStatisticsDetailsVO.AbnormalVO::getAbnormalType));

            lateCount += CollectionUtils.isNotEmpty(confirmAbnormalMap.get(result.getLateDictCode())) ? confirmAbnormalMap.get(result.getLateDictCode()).size() : BusinessConstant.ZERO;
            leaveEarlyCount += CollectionUtils.isNotEmpty(confirmAbnormalMap.get(result.getLeaveEarlyDictCode())) ? confirmAbnormalMap.get(result.getLeaveEarlyDictCode()).size() : BusinessConstant.ZERO;
            clockInLackCount += CollectionUtils.isNotEmpty(confirmAbnormalMap.get(result.getClockInLackDictCode())) ? confirmAbnormalMap.get(result.getClockInLackDictCode()).size() : BusinessConstant.ZERO;
            clockOutLackCount += CollectionUtils.isNotEmpty(confirmAbnormalMap.get(result.getClockOutLackDictCode())) ? confirmAbnormalMap.get(result.getClockOutLackDictCode()).size() : BusinessConstant.ZERO;
            abnormalDurationCount += CollectionUtils.isNotEmpty(confirmAbnormalMap.get(result.getAbnormalDurationDictCode())) ? confirmAbnormalMap.get(result.getAbnormalDurationDictCode()).size() : BusinessConstant.ZERO;
            result.setLateCount(lateCount);
            result.setLeaveEarlyCount(leaveEarlyCount);
            result.setClockInLackCount(clockInLackCount);
            result.setClockOutLackCount(clockOutLackCount);
            result.setAbnormalDurationCount(abnormalDurationCount);
            result.calculateTotalAbnormalCount();
        }
        return result;
    }

    private Integer covertWarehouseStatus(String warehouseType) {
        if (Objects.equals(BusinessConstant.WAIT_OUT, warehouseType)) {
            return WarehouseStatusEnum.WAIT_OUT.getCode();
        }
        return WarehouseStatusEnum.OUT.getCode();
    }
}
