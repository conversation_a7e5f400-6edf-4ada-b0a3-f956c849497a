package com.imile.attendance.warehouse.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.imile.attendance.annon.WithDict;
import com.imile.attendance.constants.BusinessConstant;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/10/15
 */
@Data
public class DataStatisticsDetailsVO {

    /**
     * 考勤统计表Id
     */
    private Long id;

    /**
     * 用户编码
     */
    private String userCode;

    /**
     * 用户名称
     */
    private String userName;

    /**
     * 用户照片OSS短链
     */
    private String userPhotoKey;

    /**
     * 用户照片OSS完整路径
     */
    private String userPhoto;

    /**
     * 入仓时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date clockInTime;

    /**
     * 离仓时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date clockOutTime;


    /**
     * 考勤状态
     */
    @WithDict(typeCode = "WarehouseDateReportResult", ref = "attendanceStatusDesc")
    private Integer attendanceStatusCode;

    /**
     * 考勤状态描述
     */
    private String attendanceStatusDesc;

    /**
     * 异常类型列表
     */
    private List<AbnormalVO> abnormalVOList;

    /**
     * 是否快速出仓
     */
    private Boolean quickOut = Boolean.FALSE;

    /**
     * 实际出勤时长 单位:小时
     */
    private BigDecimal actualAttendanceTime;

    /**
     * 满潜标识 0:否 1:是
     */
    private Integer fullAttendance;

    @Data
    public static class AbnormalVO {

        /**
         * 异常ID
         */
        private Long abnormalId;

        /**
         * 异常类型
         */
        @WithDict(typeCode = BusinessConstant.SysDictDataTypeConstant.WAREHOUSE_ABNORMAL_TYPE, ref = "abnormalTypeDesc")
        private String abnormalType;

        /**
         * 异常类型描述
         */
        private String abnormalTypeDesc;


    }
}
