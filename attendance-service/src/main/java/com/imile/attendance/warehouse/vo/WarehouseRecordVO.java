package com.imile.attendance.warehouse.vo;

import com.imile.attendance.annon.HyperLink;
import com.imile.attendance.annon.WithDict;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @since 2024/8/22
 */
@Data
public class WarehouseRecordVO {

    /**
     * id
     */
    private Long id;

    /**
     * 记录类型（1:入仓 2离仓）
     */
    @WithDict(ref = "recordTypeDesc", typeCode = "faceRecordType")
    private Integer recordType;

    /**
     * 记录类型描述
     */
    private String recordTypeDesc;

    /**
     * 出入仓时间
     */
    private Date warehouseTime;

    /**
     * 操作人
     */
    private String createUserName;

    /**
     * 工作网点经度
     */
    private BigDecimal ocLongitude;

    /**
     * 工作网点纬度
     */
    private BigDecimal ocLatitude;

    /**
     * 工作网点距离
     */
    private BigDecimal distance;

    /**
     * 刷脸对比照
     */
    private FaceRecognitionDetail faceRecognitionDetail;

    @Data
    @NoArgsConstructor
    public static class FaceRecognitionDetail {

        /**
         * 识别得分
         */
        private BigDecimal recognitionScore;

        /**
         * 录入照片
         */
        @HyperLink(ref = "facePhoto")
        private String facePhotoKey;

        /**
         * 识别照片
         */
        @HyperLink(ref = "recognitionPhoto")
        private String recognitionPhotoKey;

        /**
         * 录入照片
         */
        private String facePhoto;

        /**
         * 识别照片
         */
        private String recognitionPhoto;

        /**
         * 识别时间
         */
        private Date faceRecordTime;

        /**
         * 识别成功
         */
        private Boolean pass;
    }
}
