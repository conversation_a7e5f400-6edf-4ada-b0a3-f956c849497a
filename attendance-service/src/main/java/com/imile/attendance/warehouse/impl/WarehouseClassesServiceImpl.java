package com.imile.attendance.warehouse.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.enums.ClassNatureEnum;
import com.imile.attendance.enums.ErrorCodeEnum;
import com.imile.attendance.hermes.support.RpcHermesEntOcClientSupport;
import com.imile.attendance.infrastructure.repository.common.AttendanceDeptService;
import com.imile.attendance.infrastructure.repository.common.dto.AttendanceDept;
import com.imile.attendance.infrastructure.repository.rule.model.PunchClassConfigDO;
import com.imile.attendance.infrastructure.repository.rule.model.PunchClassItemConfigDO;
import com.imile.attendance.infrastructure.repository.warehouse.dao.WarehouseDetailDao;
import com.imile.attendance.infrastructure.repository.warehouse.dto.WarehouseClassItemDTO;
import com.imile.attendance.infrastructure.repository.warehouse.dto.WarehousePunchClassConfigDTO;
import com.imile.attendance.infrastructure.repository.warehouse.dto.WarehousePunchConfigDTO;
import com.imile.attendance.infrastructure.repository.warehouse.model.WarehouseDetailDO;
import com.imile.attendance.infrastructure.repository.warehouse.query.WarehouseDetailQuery;
import com.imile.attendance.rule.PunchClassConfigManage;
import com.imile.attendance.rule.dto.DayPunchTimeDTO;
import com.imile.attendance.rule.service.PunchClassConfigQueryService;
import com.imile.attendance.util.DateConvertUtils;
import com.imile.attendance.util.DateHelper;
import com.imile.attendance.warehouse.WarehouseClassesService;
import com.imile.attendance.warehouse.mapstruct.WareHouseClassMapstruct;
import com.imile.attendance.warehouse.param.ClassesParam;
import com.imile.attendance.warehouse.param.GetClassListByConditionParam;
import com.imile.attendance.warehouse.param.GetClassesByConditionParam;
import com.imile.attendance.warehouse.vo.ClassesBaseVO;
import com.imile.attendance.warehouse.vo.ClassesDetailVO;
import com.imile.attendance.warehouse.vo.ClassesVO;
import com.imile.attendance.warehouse.vo.ClassesWebVO;
import com.imile.attendance.warehouse.vo.PunchConfigAndClassesVO;
import com.imile.common.exception.BusinessException;
import com.imile.hermes.enterprise.dto.EntOcApiDTO;
import com.imile.util.BeanUtils;
import com.imile.util.lang.I18nUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 仓内班次服务
 *
 * <AUTHOR>
 * @since 2025/1/23
 */
@Slf4j
@Service
public class WarehouseClassesServiceImpl implements WarehouseClassesService {

    @Resource
    private AttendanceDeptService deptService;
    @Resource
    private PunchClassConfigManage punchClassConfigManage;
    @Resource
    private PunchClassConfigQueryService punchClassConfigQueryService;
    @Resource
    private WarehouseDetailDao warehouseDetailDao;
    @Resource
    private RpcHermesEntOcClientSupport hermesEntOcClientSupport;


    @Override
    public List<ClassesWebVO> getClassesList(GetClassListByConditionParam param) {
        if (CollectionUtils.isEmpty(param.getOcIdList()) && Objects.isNull(param.getOcId())) {
            return Collections.emptyList();
        }
        List<Long> ocIdList = null;
        if (CollectionUtils.isNotEmpty(param.getOcIdList())) {
            ocIdList = param.getOcIdList();
        }
        if (Objects.nonNull(param.getOcId())) {
            ocIdList = Collections.singletonList(param.getOcId());
        }
        List<AttendanceDept> entDeptList = Optional.ofNullable(deptService.selectDeptByIds(ocIdList))
                .orElseThrow(() -> BusinessException.get(ErrorCodeEnum.STATION_NOT_EXITS.getCode(),
                        I18nUtils.getMessage(ErrorCodeEnum.STATION_NOT_EXITS.getDesc())));
        List<WarehousePunchClassConfigDTO> result = this.queryPunchClassConfigList(entDeptList
                .stream()
                .map(AttendanceDept::getId)
                .collect(Collectors.toList()), entDeptList.get(BusinessConstant.FIRST_ELEMENT_INDEX).getCountry());

        return WareHouseClassMapstruct.INSTANCE.mapToWareHouseClassWebVO(result);
    }

    @Override
    public List<ClassesVO> getClassesList(Long ocId, String currentTime, String warehouseDate) {
        if (Objects.isNull(ocId) || Objects.isNull(currentTime) || Objects.isNull(warehouseDate)) {
            return Collections.emptyList();
        }
        List<AttendanceDept> entDeptDOList = Optional.ofNullable(deptService.selectDeptByIds(Collections.singletonList(ocId)))
                .orElseThrow(() -> BusinessException.get(ErrorCodeEnum.STATION_NOT_EXITS.getCode(),
                        I18nUtils.getMessage(ErrorCodeEnum.STATION_NOT_EXITS.getDesc())));
        Date dateTime = DateUtil.parseDateTime(currentTime);
        Date date = DateUtil.parseDate(warehouseDate);
        List<WarehousePunchConfigDTO> warehousePunchConfigDTOList = this.queryWarehousePunchConfigList(ocId, dateTime, date
                , entDeptDOList.get(0).getCountry());
        if (DateUtil.between(DateUtil.beginOfDay(date), dateTime, DateUnit.HOUR) >= 24) {
            warehousePunchConfigDTOList = warehousePunchConfigDTOList
                    .stream()
                    .filter(item -> Objects.equals(BusinessConstant.Y, item.getIsAcross()))
                    .collect(Collectors.toList());
        }

        return WareHouseClassMapstruct.INSTANCE.mapToWareHouseClassVO(warehousePunchConfigDTOList);
    }

    @Override
    public List<ClassesBaseVO> getClassesByCondition(GetClassesByConditionParam param) {
        WarehouseDetailQuery detailQuery = WarehouseDetailQuery
                .builder()
                .warehouseDate(param.getWarehouseDate())
                .ocId(param.getOcId())
                .vendorCode(param.getVendorCode())
                .build();
        List<WarehouseDetailDO> warehouseDetailDOList = warehouseDetailDao.selectClassesByCondition(detailQuery);
        if (CollectionUtils.isEmpty(warehouseDetailDOList)) {
            return Collections.emptyList();
        }
        return warehouseDetailDOList
                .stream()
                .map(warehouse -> ClassesBaseVO
                        .builder()
                        .classId(warehouse.getClassesId())
                        .classType(warehouse.getClassesType())
                        .className(warehouse.getClassesName())
                        .build())
                .collect(Collectors.toList());
    }

    @Override
    public List<ClassesWebVO> getOuterClassesList(GetClassListByConditionParam param) {
        if (CollectionUtils.isEmpty(param.getOcIdList()) && Objects.isNull(param.getOcId())) {
            return Collections.emptyList();
        }
        List<Long> ocIdList = null;
        if (CollectionUtils.isNotEmpty(param.getOcIdList())) {
            ocIdList = param.getOcIdList();
        }
        if (Objects.nonNull(param.getOcId())) {
            ocIdList = Collections.singletonList(param.getOcId());
        }
        List<EntOcApiDTO> entOcApiDTOList = hermesEntOcClientSupport.listOcIds(ocIdList);
        if (CollectionUtils.isEmpty(entOcApiDTOList)) {
            return Collections.emptyList();
        }
        List<String> ocCodeList = entOcApiDTOList.stream().map(EntOcApiDTO::getOcCode).distinct().collect(Collectors.toList());
        List<AttendanceDept> attendanceDeptList = Optional.ofNullable(deptService.listByOcCode(ocCodeList))
                .orElseThrow(() -> BusinessException.get(ErrorCodeEnum.STATION_NOT_EXITS.getCode(), I18nUtils.getMessage(ErrorCodeEnum.STATION_NOT_EXITS.getDesc())));
        List<WarehousePunchClassConfigDTO> result = queryPunchClassConfigList(attendanceDeptList.stream().map(AttendanceDept::getId).collect(Collectors.toList()), attendanceDeptList.get(0).getCountry());
        return BeanUtils.convert(ClassesWebVO.class, result);
    }

    @Override
    public List<ClassesDetailVO> getClassesDetail(Long classesId) {
        if (Objects.isNull(classesId)) {
            log.info("selectPunchClassItemDetail punchClassId is null");
            return Collections.emptyList();
        }
        log.info("selectPunchClassItemDetail punchClassId : {}", classesId);
        List<PunchClassItemConfigDO> classItemConfigList = punchClassConfigManage.selectClassItemByClassIds(Collections.singletonList(classesId));
        return classItemConfigList
                .stream()
                .map(record -> ClassesDetailVO
                        .builder()
                        .sortNo(record.getSortNo())
                        .punchInTime(DateConvertUtils.getFormatHhMmSs(record.getPunchInTime()))
                        .punchOutTime(DateConvertUtils.getFormatHhMmSs(record.getPunchOutTime()))
                        .earliestPunchInTime(DateConvertUtils.getFormatHhMmSs(record.getEarliestPunchInTime()))
                        .latestPunchOutTime(DateConvertUtils.getFormatHhMmSs(record.getLatestPunchInTime()))
                        .restStartTime(DateConvertUtils.getFormatHhMmSs(record.getRestStartTime()))
                        .restEndTime(DateConvertUtils.getFormatHhMmSs(record.getRestEndTime()))
                        .elasticTime(record.getElasticTime())
                        .build())
                .collect(Collectors.toList());
    }

    @Override
    public PunchConfigAndClassesVO getPunchConfigAndClasses(Long ocId) {
        if (Objects.isNull(ocId)) {
            return null;
        }

        List<AttendanceDept> entDeptDOList = Optional.ofNullable(deptService.selectDeptByIds(Collections.singletonList(ocId)))
                .orElseThrow(() -> BusinessException.get(ErrorCodeEnum.STATION_NOT_EXITS.getCode(),
                        I18nUtils.getMessage(ErrorCodeEnum.STATION_NOT_EXITS.getDesc())));
        List<WarehousePunchConfigDTO> warehousePunchConfigDTOList = this.queryWarehousePunchConfigList(ocId,
                null, null, entDeptDOList.get(0).getCountry());
        if (CollectionUtils.isEmpty(warehousePunchConfigDTOList)) {
            return null;
        }

        List<ClassesVO> classesVOList = WareHouseClassMapstruct.INSTANCE.mapToWareHouseClassVO(warehousePunchConfigDTOList);
        return PunchConfigAndClassesVO
                .builder()
//                .punchConfigId(warehousePunchConfigDTOList.get(BusinessConstant.FIRST_ELEMENT_INDEX).getPunchConfigId())
//                .punchConfigName(warehousePunchConfigDTOList.get(BusinessConstant.FIRST_ELEMENT_INDEX).getPunchConfigName())
                .classesVOList(classesVOList)
                .build();
    }

    @Override
    public ClassesParam getClassesInfo(Long classesId) {

        PunchClassConfigDO punchClassConfigById = punchClassConfigManage.getPunchClassConfigById(classesId);
        if (Objects.isNull(punchClassConfigById)) {
            throw BusinessException.get(ErrorCodeEnum.CLASS_NOT_EXISTS.getCode(),
                    I18nUtils.getMessage(ErrorCodeEnum.CLASS_NOT_EXISTS.getMessage()));
        }
        // classId 新系统只能查询到班次
        return ClassesParam
                .builder()
                .classId(punchClassConfigById.getId())
                .dayPunchType(punchClassConfigById.getClassName())
                .className(punchClassConfigById.getClassName())
                .classType(punchClassConfigById.getClassType())
                .attendanceHours(punchClassConfigById.getAttendanceHours())
                .legalWorkingHours(punchClassConfigById.getLegalWorkingHours())
                .build();
    }

    @Override
    public List<String> getNoBindShiftDay(Long ocId) {
        if (Objects.isNull(ocId)) {
            return Collections.emptyList();
        }
        WarehouseDetailQuery query = WarehouseDetailQuery.builder().ocId(ocId).build();
        List<WarehouseDetailDO> warehouseDetailList = warehouseDetailDao.selectNoBindShiftByCondition(query);
        if (CollectionUtils.isEmpty(warehouseDetailList)) {
            return Collections.emptyList();
        }
        return warehouseDetailList
                .stream()
                .filter(item -> Objects.nonNull(item.getWarehouseDate()))
                .sorted(Comparator.comparing(WarehouseDetailDO::getWarehouseDate).reversed())
                .map(item -> DateHelper.formatYYYYMMDD(item.getWarehouseDate()))
                .distinct()
                .collect(Collectors.toList());
    }

    private List<WarehousePunchConfigDTO> queryWarehousePunchConfigList(Long ocId,
                                                                        Date currentTime,
                                                                        Date warehouseDate,
                                                                        String country) {
        List<WarehousePunchConfigDTO> targetResult = Lists.newArrayList();
        // 查询班次信息
        List<PunchClassConfigDO> punchClassConfigList = punchClassConfigManage.selectLatestAndActiveByCondition(country,
                ocId, ClassNatureEnum.MULTIPLE_CLASS.getCode());
        if (CollectionUtils.isEmpty(punchClassConfigList)) {
            log.info("selectPunchConfigByWarehouseCondition ocId:{},country:{} the query punch class information is empty"
                    , ocId, country);
            return targetResult;
        }

        // 获取班次id
        List<Long> punchClassIdList = punchClassConfigList.stream()
                .map(PunchClassConfigDO::getId)
                .collect(Collectors.toList());

        // 获取班次详情信息
        List<PunchClassItemConfigDO> punchClassItemConfigList = punchClassConfigManage.selectClassItemByClassIds(punchClassIdList);

        if (CollectionUtils.isEmpty(punchClassItemConfigList)) {
            log.info("selectPunchConfigByWarehouseCondition punchClassIdList : {} the query punch class item information is empty"
                    , JSON.toJSONString(punchClassIdList));
            return targetResult;
        }

        // 如果时间参数为null：直接返回班次列表（无任何操作）
        if (ObjectUtil.isNull(currentTime)) {
            for (PunchClassConfigDO punchClassConfig : punchClassConfigList) {
                targetResult.add(this.buildWareHouseConfigDTO(punchClassConfig, null));
            }
            return targetResult;
        }
        // 时间参数不为null：根据时间参数判断是在那一个时间段内的班次，该班次放在第一个位置
        Long nowDayId = Objects.nonNull(warehouseDate)
                ? DateHelper.getDayId(warehouseDate)
                : DateHelper.getDayId(currentTime);
        // 获取当前时间减一天的day_id
        Long beforeDayId = Long.parseLong(DateUtil.format(DateUtil.offsetDay(currentTime, -1), DatePattern.PURE_DATE_PATTERN));

        List<WarehouseClassItemDTO> warehouseClassItemList = Lists.newArrayList();

        // 遍历班次详情数据
        this.handlerClassItem(punchClassItemConfigList, nowDayId, currentTime, warehouseClassItemList);

        // 增加逻辑：获取前一天的班次，看是否有符合条件的
        if (CollUtil.isEmpty(warehouseClassItemList)) {
            // 遍历班次详情数据
            this.handlerClassItem(punchClassItemConfigList, beforeDayId, currentTime, warehouseClassItemList);
        }
        // 处理结果集 将warehouseClassItemList按照betweenMs 正排序
        warehouseClassItemList.sort(Comparator.comparing(WarehouseClassItemDTO::getBetweenMs));
        log.info("selectPunchConfigByWarehouseCondition warehouseClassItemList:{}", warehouseClassItemList);

        List<WarehousePunchConfigDTO> otherList = Lists.newArrayList();

        Map<Long, PunchClassConfigDO> punchClassConfigDOMap = punchClassConfigList.stream()
                .collect(Collectors.toMap(PunchClassConfigDO::getId, Function.identity()));
        Map<Long, PunchClassItemConfigDO> punchClassItemConfigMap = punchClassItemConfigList.stream()
                .collect(Collectors.toMap(PunchClassItemConfigDO::getPunchClassId, Function.identity(), (o1, o2) -> o1));

        for (WarehouseClassItemDTO warehouseClassItem : warehouseClassItemList) {
            PunchClassConfigDO punchClassConfig = punchClassConfigDOMap.get(warehouseClassItem.getClassId());
            if (Objects.isNull(punchClassConfig)) {
                continue;
            }
            PunchClassItemConfigDO punchClassItemConfig = punchClassItemConfigMap.get(warehouseClassItem.getClassId());
            targetResult.add(this.buildWareHouseConfigDTO(punchClassConfig, punchClassItemConfig));
        }

        List<Long> classIds = warehouseClassItemList.stream()
                .map(WarehouseClassItemDTO::getClassId)
                .distinct().collect(Collectors.toList());
        List<PunchClassConfigDO> filterPunchClassConfigList = punchClassConfigList.stream()
                .filter(punchClassConfig -> !classIds.contains(punchClassConfig.getId()))
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(filterPunchClassConfigList)) {
            return targetResult;
        }

        for (PunchClassConfigDO punchClassConfig : filterPunchClassConfigList) {
            PunchClassItemConfigDO punchClassItemConfig = punchClassItemConfigMap.get(punchClassConfig.getId());
            targetResult.add(this.buildWareHouseConfigDTO(punchClassConfig, punchClassItemConfig));
        }

        if (CollectionUtils.isNotEmpty(otherList)) {
            targetResult.addAll(otherList);
        }

        return targetResult;
    }

    /**
     * 根据网点查询班次信息
     */
    private List<WarehousePunchClassConfigDTO> queryPunchClassConfigList(List<Long> ocIdList, String country) {
        List<WarehousePunchClassConfigDTO> targetResult = Lists.newArrayList();

        // 如果参数都不存在，返回空集合
        if (StringUtils.isBlank(country) || CollectionUtils.isEmpty(ocIdList)) {
            log.info("selectPunchClassConfigByWarehouseCondition param is empty");
            return targetResult;
        }

        Long deptId = ocIdList.get(0);
        List<PunchClassConfigDO> punchClassConfigList = punchClassConfigManage.selectLatestAndActiveByCondition(country,
                deptId, ClassNatureEnum.MULTIPLE_CLASS.getCode());
        if (CollectionUtils.isEmpty(punchClassConfigList)) {
            return targetResult;
        }
        return WareHouseClassMapstruct.INSTANCE.mapToWareHouseClassDTO(punchClassConfigList);
    }

    /**
     * 处理班次信息：获取符合条件的班次
     *
     * @param punchClassItemConfigList 打卡规则详情数据
     * @param dayId                    dayId
     * @param now                      当前时间
     * @param warehouseClassItemList   结果
     */
    private void handlerClassItem(List<PunchClassItemConfigDO> punchClassItemConfigList,
                                  Long dayId,
                                  Date now,
                                  List<WarehouseClassItemDTO> warehouseClassItemList) {
        for (PunchClassItemConfigDO punchClassItemConfig : punchClassItemConfigList) {
            //获取当前时刻的正常时间
            DayPunchTimeDTO dayPunchTimeDTO = punchClassConfigQueryService.getUserPunchClassItemDayTime(dayId, punchClassItemConfig.getId(), punchClassItemConfigList);
            log.info("selectPunchConfigByWarehouseCondition classId : {} dayPunchTimeDTO : {}", dayPunchTimeDTO, punchClassItemConfig.getPunchClassId());
            if (ObjectUtil.isNull(dayPunchTimeDTO) || dayPunchTimeDTO.getDayPunchStartTime().compareTo(dayPunchTimeDTO.getDayPunchEndTime()) > -1) {
                log.info("selectPunchConfigByWarehouseCondition dayPunchTimeDTO data error");
                continue;
            }
            if (now.compareTo(dayPunchTimeDTO.getDayPunchStartTime()) >= 0 && now.compareTo(dayPunchTimeDTO.getDayPunchEndTime()) <= 0) {
                // 封装数据:获取符合当前时间条件的班次以及班次最早上班最晚下班时间
                warehouseClassItemList.add(WarehouseClassItemDTO
                        .builder()
                        .classId(punchClassItemConfig.getPunchClassId())
                        .dayPunchStartTime(dayPunchTimeDTO.getDayPunchStartTime())
                        .dayPunchEndTime(dayPunchTimeDTO.getDayPunchEndTime())
                        // 获取当前时间与班次最早上班时间的差值
                        .betweenMs(DateUtil.betweenMs(dayPunchTimeDTO.getDayPunchStartTime(), now))
                        .build());
            }
        }
    }

    private WarehousePunchConfigDTO buildWareHouseConfigDTO(PunchClassConfigDO punchClassConfig,
                                                            PunchClassItemConfigDO punchClassItemConfig) {
        return WarehousePunchConfigDTO
                .builder()
                .classId(punchClassConfig.getId())
                .classNo(punchClassConfig.getConfigNo())
                .className(punchClassConfig.getClassName())
                .classType(punchClassConfig.getClassType())
                .attendanceHours(punchClassConfig.getAttendanceHours())
                .legalWorkingHours(punchClassConfig.getLegalWorkingHours())
                .dayPunchType(punchClassConfig.getClassName())
                // 是否跨天
                .isAcross(Objects.isNull(punchClassItemConfig)
                        ? 0 : punchClassItemConfig.getIsAcross())
                .build();
    }
}
