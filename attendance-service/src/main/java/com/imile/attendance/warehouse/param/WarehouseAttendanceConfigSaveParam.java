package com.imile.attendance.warehouse.param;

import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;


/**
 * <AUTHOR>
 * @since 2024/11/30
 */
@Data
public class WarehouseAttendanceConfigSaveParam {

    private Long id;

    /**
     * 国家
     */
    @NotNull(message = "country cannot be empty")
    private String country;

    /**
     * 考勤规则名称
     */
    @NotNull(message = "attendanceConfigName cannot be empty")
    @Size(min = 1, max = 200, message = "attendanceConfigName maximum length must less 200 character")
    private String attendanceConfigName;

    /**
     * 部门列表
     */
    @NotEmpty(message = "deptIds cannot be empty")
    private List<Long> deptIds;

    /**
     * 是否允许上多班次
     */
    private Integer isMultipleShifts = 0;

    /**
     * 满勤配置,允许实际总工作时长小于班次总时长分钟内
     */
    @NotNull(message = "minute cannot be empty")
    @Min(value = 0, message = "minute minimum must greater than 0")
    @Max(value = 90, message = "minute maximum must less than 91")
    private Integer minute = 0;

    /**
     * 是否分段计算工时 0计算总工时 1分段计算工时
     */
    private Integer isSegmentedCalculation = 0;
}
