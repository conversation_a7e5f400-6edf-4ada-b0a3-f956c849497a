package com.imile.attendance.warehouse.param;

import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;


/**
 * <AUTHOR>
 * @since 2024/8/23
 */
@Data
public class NoBingShiftReportParam {

    /**
     * 网点id
     */
    @NotNull(message = "ocId cannot be empty")
    private Long ocId;

    /**
     * 考勤日
     */
    @NotNull(message = "attendanceDate cannot be empty")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date attendanceDate;

    /**
     * 入仓开始时间
     */
    @NotNull(message = "startTime cannot be empty")
    @DateTimeFormat(pattern = "HH:mm:ss")
    private Date startTime;

    /**
     * 入仓结束时间
     */
    @NotNull(message = "endTime cannot be empty")
    @DateTimeFormat(pattern = "HH:mm:ss")
    private Date endTime;

}
