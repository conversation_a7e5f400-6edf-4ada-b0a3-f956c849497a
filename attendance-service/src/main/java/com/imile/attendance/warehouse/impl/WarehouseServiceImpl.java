package com.imile.attendance.warehouse.impl;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import com.google.common.io.BaseEncoding;
import com.imile.attendance.abnormal.EmployeeAbnormalAttendanceService;
import com.imile.attendance.abnormal.dto.AttendanceCalculateHandlerDTO;
import com.imile.attendance.abnormal.param.AbnormalAttendanceBatchUpdateParam;
import com.imile.attendance.apollo.AttendanceProperties;
import com.imile.attendance.calendar.CalendarConfigService;
import com.imile.attendance.clock.query.WarehouseAttendanceConfigQuery;
import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.context.RequestInfoHolder;
import com.imile.attendance.enums.BlacklistBanStatusEnum;
import com.imile.attendance.enums.ClassNatureEnum;
import com.imile.attendance.enums.CountryCodeEnum;
import com.imile.attendance.enums.EmploymentTypeEnum;
import com.imile.attendance.enums.ErrorCodeEnum;
import com.imile.attendance.enums.abnormal.AbnormalAttendanceStatusEnum;
import com.imile.attendance.enums.abnormal.AbnormalOperationTypeEnum;
import com.imile.attendance.enums.abnormal.AttendanceAbnormalTypeEnum;
import com.imile.attendance.enums.punch.SourceTypeEnum;
import com.imile.attendance.enums.rule.RuleRangeTypeEnum;
import com.imile.attendance.enums.warehouse.ConfirmStatusEnum;
import com.imile.attendance.enums.warehouse.FaceRecordStatusEnum;
import com.imile.attendance.enums.warehouse.OcrErrorCodeEnum;
import com.imile.attendance.enums.warehouse.PunchCardTypeEnum;
import com.imile.attendance.enums.warehouse.PunchStatusEnum;
import com.imile.attendance.enums.warehouse.WarehouseAbnormalStatusEnum;
import com.imile.attendance.enums.warehouse.WarehouseAttendanceStatusEnum;
import com.imile.attendance.enums.warehouse.WarehouseBlackTypeEnum;
import com.imile.attendance.enums.warehouse.WarehouseClassConfirmStatusEnum;
import com.imile.attendance.enums.warehouse.WarehousePcsStatusEnum;
import com.imile.attendance.enums.warehouse.WarehouseStatusEnum;
import com.imile.attendance.enums.warehouse.WarehouseTypeEnum;
import com.imile.attendance.exception.BusinessLogicException;
import com.imile.attendance.form.biz.reissueCard.UserReissueCardConfigService;
import com.imile.attendance.gensis.support.RpcUserCertificateSupport;
import com.imile.attendance.hermes.dto.CountryDTO;
import com.imile.attendance.hrms.RpcUserBaseClient;
import com.imile.attendance.infrastructure.idwork.DefaultIdWorker;
import com.imile.attendance.infrastructure.repository.abnormal.dao.AttendanceEmployeeDetailDao;
import com.imile.attendance.infrastructure.repository.abnormal.dao.EmployeeAbnormalAttendanceDao;
import com.imile.attendance.infrastructure.repository.abnormal.dao.EmployeeAbnormalOperationRecordDao;
import com.imile.attendance.infrastructure.repository.abnormal.model.AttendanceEmployeeDetailDO;
import com.imile.attendance.infrastructure.repository.abnormal.model.EmployeeAbnormalAttendanceDO;
import com.imile.attendance.infrastructure.repository.abnormal.model.EmployeeAbnormalOperationRecordDO;
import com.imile.attendance.infrastructure.repository.abnormal.query.AbnormalAttendanceQuery;
import com.imile.attendance.infrastructure.repository.common.dto.AttendanceDept;
import com.imile.attendance.infrastructure.repository.employee.dao.UserInfoDao;
import com.imile.attendance.infrastructure.repository.employee.modle.UserInfoDO;
import com.imile.attendance.infrastructure.repository.punch.dao.EmployeePunchRecordDao;
import com.imile.attendance.infrastructure.repository.punch.model.EmployeePunchRecordDO;
import com.imile.attendance.infrastructure.repository.punch.query.EmployeePunchCardRecordQuery;
import com.imile.attendance.infrastructure.repository.rule.dao.PunchClassConfigRangeDao;
import com.imile.attendance.infrastructure.repository.rule.dao.PunchClassItemConfigDao;
import com.imile.attendance.infrastructure.repository.rule.dto.PunchClassConfigDTO;
import com.imile.attendance.infrastructure.repository.rule.model.PunchClassConfigRangeDO;
import com.imile.attendance.infrastructure.repository.rule.model.PunchClassItemConfigDO;
import com.imile.attendance.infrastructure.repository.warehouse.dao.WarehouseAttendanceConfigDao;
import com.imile.attendance.infrastructure.repository.warehouse.dao.WarehouseBlackListDao;
import com.imile.attendance.infrastructure.repository.warehouse.dao.WarehouseDetailAbnormalDao;
import com.imile.attendance.infrastructure.repository.warehouse.dao.WarehouseDetailDao;
import com.imile.attendance.infrastructure.repository.warehouse.dao.WarehouseFaceRecordDao;
import com.imile.attendance.infrastructure.repository.warehouse.dao.WarehouseRecordDao;
import com.imile.attendance.infrastructure.repository.warehouse.dao.WarehouseVendorClassesConfirmDao;
import com.imile.attendance.infrastructure.repository.warehouse.model.WarehouseAttendanceConfigDO;
import com.imile.attendance.infrastructure.repository.warehouse.model.WarehouseBlackListDO;
import com.imile.attendance.infrastructure.repository.warehouse.model.WarehouseDetailAbnormalDO;
import com.imile.attendance.infrastructure.repository.warehouse.model.WarehouseDetailDO;
import com.imile.attendance.infrastructure.repository.warehouse.model.WarehouseFaceRecordDO;
import com.imile.attendance.infrastructure.repository.warehouse.model.WarehousePunchPeriodDO;
import com.imile.attendance.infrastructure.repository.warehouse.model.WarehouseRecordDO;
import com.imile.attendance.infrastructure.repository.warehouse.model.WarehouseVendorClassesConfirmDO;
import com.imile.attendance.infrastructure.repository.warehouse.param.WarehouseVendorClassesConfirmParam;
import com.imile.attendance.infrastructure.repository.warehouse.query.WarehouseDetailQuery;
import com.imile.attendance.migration.MigrationService;
import com.imile.attendance.permission.WpmPermissionService;
import com.imile.attendance.rule.PunchClassConfigManage;
import com.imile.attendance.rule.dto.DayPunchTimeDTO;
import com.imile.attendance.rule.service.PunchClassConfigQueryService;
import com.imile.attendance.shift.UserShiftService;
import com.imile.attendance.shift.command.DaysConfigCommand;
import com.imile.attendance.shift.command.UserShiftConfigAddCommand;
import com.imile.attendance.util.BaseDOUtil;
import com.imile.attendance.util.BusinessFieldUtils;
import com.imile.attendance.util.CommonUtil;
import com.imile.attendance.util.DateHelper;
import com.imile.attendance.util.DistanceCalculatorUtils;
import com.imile.attendance.warehouse.WarehouseAttendanceCalculateService;
import com.imile.attendance.warehouse.WarehouseAttendanceDurationCalculateService;
import com.imile.attendance.warehouse.WarehouseAttendanceHandlerService;
import com.imile.attendance.warehouse.WarehouseAttendancePushFinService;
import com.imile.attendance.warehouse.WarehouseBaseService;
import com.imile.attendance.warehouse.WarehouseClassesService;
import com.imile.attendance.warehouse.WarehouseManage;
import com.imile.attendance.warehouse.WarehouseOcService;
import com.imile.attendance.warehouse.WarehouseService;
import com.imile.attendance.warehouse.WarehouseSupplierService;
import com.imile.attendance.warehouse.param.BingShiftParam;
import com.imile.attendance.warehouse.param.CheckVendorConfirmStatusParam;
import com.imile.attendance.warehouse.param.ClassesParam;
import com.imile.attendance.warehouse.param.InOrOutParam;
import com.imile.attendance.warehouse.param.InV2Param;
import com.imile.attendance.warehouse.param.OutParam;
import com.imile.attendance.warehouse.param.UpdateWarehouseWorkClassesParam;
import com.imile.attendance.warehouse.param.UpdateWarehouseWorkOcParam;
import com.imile.attendance.warehouse.param.UpdateWarehouseWorkVendorParam;
import com.imile.attendance.warehouse.param.UserCardParam;
import com.imile.attendance.warehouse.param.VendorConfirmClassesParam;
import com.imile.attendance.warehouse.param.WarehouseUserCardParam;
import com.imile.attendance.warehouse.vo.CheckVendorConfirmStatusResultVO;
import com.imile.attendance.warehouse.vo.OutVO;
import com.imile.common.enums.IsDeleteEnum;
import com.imile.common.enums.StatusEnum;
import com.imile.common.exception.BusinessException;
import com.imile.genesis.api.model.param.user.UserCertificateSaveParam;
import com.imile.genesis.api.model.result.user.UserCertificateDTO;
import com.imile.hermes.enterprise.dto.EntOcApiDTO;
import com.imile.hrms.api.blacklist.api.BlacklistApi;
import com.imile.hrms.api.blacklist.dto.BlacklistInfoDTO;
import com.imile.hrms.api.primary.enums.CommonStatusEnum;
import com.imile.hrms.api.primary.enums.OperationSceneEnum;
import com.imile.hrms.api.primary.model.param.user.UserStatusSwitchParam;
import com.imile.rpc.common.RpcResult;
import com.imile.util.BeanUtils;
import com.imile.util.lang.I18nUtils;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.rpc.RpcException;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.Executor;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @since 2025/7/22
 */
@Slf4j
@Service
public class WarehouseServiceImpl extends WarehouseBaseService implements WarehouseService {

    @Resource
    private DefaultIdWorker defaultIdWorker;

    @Resource
    private WarehouseClassesService warehouseClassesService;

    @Resource
    private UserInfoDao userInfoDao;

    @Resource
    private RpcUserCertificateSupport userCertificateSupport;

    @Resource
    private PunchClassItemConfigDao punchClassItemConfigDao;

    @Resource
    private WarehouseAttendanceCalculateService attendanceCalculateService;

    @Resource
    private WarehouseOcService warehouseOcService;

    @Resource
    private WpmPermissionService wpmPermissionService;

    @Resource
    private WarehouseSupplierService warehouseSupplierService;

    @Resource
    private PunchClassConfigQueryService punchClassConfigQueryService;

    @Resource
    private WarehouseAttendancePushFinService attendancePushFinService;

    @Resource
    private EmployeeAbnormalAttendanceService employeeAbnormalAttendanceService;

    @Resource
    private WarehouseAttendanceHandlerService warehouseAttendanceHandlerService;

    @Resource
    private WarehouseAttendanceDurationCalculateService attendanceDurationCalculateService;

    @Resource
    private WarehouseDetailDao warehouseDetailDao;

    @Resource
    private WarehouseRecordDao warehouseRecordDao;

    @Resource
    private WarehouseDetailAbnormalDao warehouseDetailAbnormalDao;

    @Resource
    private WarehouseFaceRecordDao faceRecordDao;

    @Resource
    private WarehouseAttendanceConfigDao warehouseAttendanceConfigDao;

    @Resource
    private WarehouseVendorClassesConfirmDao warehouseVendorClassesConfirmDao;

    @Resource
    private AttendanceEmployeeDetailDao attendanceEmployeeDetailDao;

    @Resource
    private EmployeeAbnormalAttendanceDao abnormalAttendanceDao;

    @Resource
    private EmployeeAbnormalOperationRecordDao abnormalOperationRecordDao;

    @Resource
    private EmployeePunchRecordDao employeePunchRecordDao;

    @Resource
    private WarehouseManage warehouseManage;

    @Resource
    private Executor bizTaskThreadPool;

    @Resource
    private AttendanceProperties attendanceProperties;

    @Resource
    private BlacklistApi blacklistApi;

    @Resource
    private WarehouseBlackListDao warehouseBlackListDao;

    @Resource
    private CalendarConfigService calendarConfigService;

    @Resource
    private UserShiftService userShiftService;

    @Resource
    private RpcUserBaseClient userBaseClient;

    @Resource
    private UserReissueCardConfigService userReissueCardConfigService;

    @Resource
    private MigrationService migrationService;

    @Resource
    private PunchClassConfigManage punchClassConfigManage;

    @Resource
    private PunchClassConfigRangeDao punchClassConfigRangeDao;

    @Override
    public void in(InOrOutParam param) {
        BusinessLogicException.checkTrue(!BusinessConstant.WAREHOUSE_EMPLOYEE_TYPE.contains(param.getEmployeeType()), ErrorCodeEnum.PARAM_VALID_ERROR);
        BusinessLogicException.checkTrue(BusinessConstant.LABOR_DISPATCH.equals(param.getEmployeeType()) && Objects.isNull(param.getVendorId()), ErrorCodeEnum.PARAM_VALID_ERROR);

        //检查网点权限
        checkAuthOc(param.getOcId());

        //查询网点
        EntOcApiDTO oc = warehouseOcService.getOc(param.getOcId());

        //检查打卡时间,打卡时间由前端从手机采集，可能存在用户调整手机系统时间
        checkPunchTime(oc.getCountry(), param.getAttendanceTime());

        //巴西劳务派遣供应商校验
        braInVendorCheck(param);

        //是否允许上多班次
        boolean isMultipleShifts = false;
        //查询仓内考勤管理规则
        WarehouseAttendanceConfigDO warehouseAttendanceConfigDO = warehouseAttendanceConfigDao.selectByDeptId(param.getOcId());
        if (Objects.nonNull(warehouseAttendanceConfigDO) && Objects.equals(BusinessConstant.Y, warehouseAttendanceConfigDO.getIsMultipleShifts())) {
            isMultipleShifts = true;
        }

        for (Long userId : param.getUserIdList()) {
            UserInfoDO user = getUserForInOrOut(userId);
            if ((BusinessConstant.LABOR_DISPATCH.equals(param.getEmployeeType())
                    && !EmploymentTypeEnum.OS_FIXED_SALARY.getCode().equals(user.getEmployeeType()))
                    || (BusinessConstant.FORMAL.equals(param.getEmployeeType())
                    && !EmploymentTypeEnum.TYPE_OF_EMPLOYEE_WAREHOUSE.contains(user.getEmployeeType()))) {
                throw BusinessException.get(OcrErrorCodeEnum.EMPLOYEE_TYPE_NOT_MATCH.getCode(), I18nUtils.getMessage(OcrErrorCodeEnum.EMPLOYEE_TYPE_NOT_MATCH.getDesc()));
            }

            Long vendorId;
            String vendorCode;
            if (!BusinessConstant.LABOR_DISPATCH.equals(param.getEmployeeType())) {
                vendorId = user.getVendorId();
                vendorCode = user.getVendorCode();
            } else {
                vendorId = param.getVendorId();
                vendorCode = param.getVendorCode();
            }

            //检查刷脸记录
            WarehouseFaceRecordDO faceRecord = Optional.ofNullable(faceRecordDao.selectLastOne(userId, param.getAttendanceTime(), FaceRecordStatusEnum.EFFECTIVE.getCode()))
                    .orElseThrow(() -> BusinessException.get(ErrorCodeEnum.NOT_RECOGNITION_OR_UPLOAD_CANNOT_IN.getCode(),
                            I18nUtils.getMessage(ErrorCodeEnum.NOT_RECOGNITION_OR_UPLOAD_CANNOT_IN.getDesc(), BusinessFieldUtils.getUnifiedUserName(user.getUserName(), user.getUserNameEn()))));

            //考勤日校验
            checkWarehouseDate(param.getWarehouseDate(), param.getAttendanceTime(), Objects.nonNull(param.getClasses()) ? param.getClasses().getClassId() : null, WarehouseTypeEnum.INIT.getCode());

            Date warehouseDate = DateUtil.parseDate(DateUtil.formatDate(param.getWarehouseDate()));
            Long attendanceDayId = DateHelper.getDayId(warehouseDate);
            List<WarehouseDetailDO> warehouseDetailDOS = warehouseDetailDao.selectByWarehouseDateAndUserId(warehouseDate, userId);

            //入仓校验
            checkIn(warehouseDetailDOS, param, user, isMultipleShifts);
            //获取当天日历出勤类型
            String dayType = getCurrentDayType(warehouseDate, user);

            WarehouseDetailDO warehouseDetailDO = convertWarehouseDetail(param, warehouseDetailDOS, vendorId, vendorCode, user, oc, dayType, warehouseDate, WarehouseTypeEnum.IN.getCode(), warehouseAttendanceConfigDO);
            if (Objects.equals(WarehouseStatusEnum.INIT.getCode(), warehouseDetailDO.getWarehouseStatus())) {
                warehouseDetailDO.setWarehouseStatus(WarehouseTypeEnum.IN.getCode());
            }
            //生成排班计划
            if (Objects.nonNull(param.getClasses())) {
                generateSchedulingPlan(param.getClasses(), param.getAttendanceTime(), attendanceDayId, user.getId());
            }

            WarehouseRecordDO warehouseRecordDO = convertWarehouseRecord(param, vendorId, vendorCode, user, oc, faceRecord.getId(), warehouseDate, warehouseDetailDO.getId());
            Long classId = Objects.nonNull(param.getClasses()) ? param.getClasses().getClassId() : null;
            EmployeePunchRecordDO punchRecordDO = convertPunchRecord(param.getOcId(), param.getAttendanceTime(), classId, user, oc, attendanceDayId.toString());
            warehouseManage.warehouseAttendanceAdd(warehouseDetailDO, warehouseRecordDO, punchRecordDO);
        }

    }

    @Override
    public void inV2(InV2Param param) {
        //入仓
        in(param);
        //停用新的重复账号
        UserInfoDO userInfo = userInfoDao.getById(param.getOriginUserId());
        Boolean disabledUser = userBaseClient.switchUserStatus(UserStatusSwitchParam.builder()
                .userCode(userInfo.getUserCode())
                .userStatus(CommonStatusEnum.DISABLED.getCode())
                .operationScene(OperationSceneEnum.WPM)
                .reason(BusinessConstant.EMPLOYEE_DISABLED_REASON)
                .build());
        if (!disabledUser) {
            throw BusinessException.get(ErrorCodeEnum.DISABLED_USER_ERROR.getCode(), I18nUtils.getMessage(ErrorCodeEnum.DISABLED_USER_ERROR.getDesc()));
        }
        String certificateTypeCode = getCertificateType(userInfo.getLocationCountry());
        UserInfoDO user = userInfoDao.getByUserIds(param.getUserIdList()).get(0);
        //证件号更新
        List<UserCertificateDTO> userCertificateBOS = userCertificateSupport.listUserCertificateList(Collections.singletonList(user.getUserCode()), certificateTypeCode);
        if (CollectionUtils.isNotEmpty(userCertificateBOS) && !Objects.equals(param.getCertificatesCode(), userCertificateBOS.get(0).getCertificateCode())) {
            UserCertificateSaveParam userCertificateSaveParam = BeanUtils.convert(userCertificateBOS.get(0), UserCertificateSaveParam.class);
            userCertificateSaveParam.setCertificateCode(param.getCertificatesCode());
            userCertificateSupport.saveUserCertificate(user.getUserCode(), Collections.singletonList(userCertificateSaveParam));
        }
    }

    @Override
    public OutVO out(OutParam param) {
        BusinessLogicException.checkTrue(!BusinessConstant.WAREHOUSE_EMPLOYEE_TYPE.contains(param.getEmployeeType()), ErrorCodeEnum.PARAM_VALID_ERROR);
        BusinessLogicException.checkTrue(BusinessConstant.LABOR_DISPATCH.equals(param.getEmployeeType()) && Objects.isNull(param.getVendorId()), ErrorCodeEnum.PARAM_VALID_ERROR);

        checkAuthOc(param.getOcId());

        EntOcApiDTO oc = warehouseOcService.getOc(param.getOcId());

        checkPunchTime(oc.getCountry(), param.getAttendanceTime());

        OutVO vo = new OutVO();
        for (Long userId : param.getUserIdList()) {
            UserInfoDO user = getUserForInOrOut(userId);
            if ((BusinessConstant.LABOR_DISPATCH.equals(param.getEmployeeType())
                    && !EmploymentTypeEnum.OS_FIXED_SALARY.getCode().equals(user.getEmployeeType()))
                    || (BusinessConstant.FORMAL.equals(param.getEmployeeType())
                    && !EmploymentTypeEnum.TYPE_OF_EMPLOYEE_WAREHOUSE.contains(user.getEmployeeType()))) {
                throw BusinessException.get(OcrErrorCodeEnum.EMPLOYEE_TYPE_NOT_MATCH.getCode(), I18nUtils.getMessage(OcrErrorCodeEnum.EMPLOYEE_TYPE_NOT_MATCH.getDesc()));
            }

            Long vendorId;
            String vendorCode;
            if (!BusinessConstant.LABOR_DISPATCH.equals(param.getEmployeeType())) {
                vendorId = user.getVendorId();
                vendorCode = user.getVendorCode();
            } else {
                vendorId = param.getVendorId();
                vendorCode = param.getVendorCode();
            }

            //检查刷脸记录
            WarehouseFaceRecordDO faceRecord = faceRecordDao.selectLastOne(userId, param.getAttendanceTime(), FaceRecordStatusEnum.EFFECTIVE.getCode());
            if (Objects.isNull(faceRecord)) {
                throw BusinessException.get(ErrorCodeEnum.NOT_RECOGNITION_CANNOT_OUT.getCode(),
                        I18nUtils.getMessage(ErrorCodeEnum.NOT_RECOGNITION_CANNOT_OUT.getDesc(), BusinessFieldUtils.getUnifiedUserName(user.getUserName(), user.getUserNameEn())));
            }

            //考勤日校验
            checkWarehouseDate(param.getWarehouseDate(), param.getAttendanceTime(), Objects.nonNull(param.getClasses()) ? param.getClasses().getClassId() : null, WarehouseTypeEnum.OUT.getCode());

            Date warehouseDate = DateUtil.parseDate(DateUtil.formatDate(param.getWarehouseDate()));
            Long attendanceDayId = DateHelper.getDayId(warehouseDate);

            //查询当日考勤结果是否初始化
            List<WarehouseDetailDO> warehouseDetailDOList = warehouseDetailDao.selectByWarehouseDateAndUserId(warehouseDate, userId);

            //出仓检测
            vo = checkOut(warehouseDetailDOList, param, user, vendorId, vendorCode);
            if (!vo.getCheckOutResult()) {
                return vo;
            }

            if (CollectionUtils.isEmpty(warehouseDetailDOList)) {
                //直接出仓打卡
                //生成排班计划
                if (Objects.nonNull(param.getClasses())) {
                    generateSchedulingPlan(param.getClasses(), param.getAttendanceTime(), attendanceDayId, user.getId());
                }
                //获取当天日历出勤类型
                String dayType = getCurrentDayType(warehouseDate, user);
                //是否允许上多班次
                //查询仓内考勤管理规则
                WarehouseAttendanceConfigDO warehouseAttendanceConfigDO = warehouseAttendanceConfigDao.selectByDeptId(param.getOcId());
                //保存办公室打卡记录
                Long classId = Objects.nonNull(param.getClasses()) ? param.getClasses().getClassId() : null;
                EmployeePunchRecordDO punchRecordDO = convertPunchRecord(param.getOcId(), param.getAttendanceTime(), classId, user, oc, attendanceDayId.toString());
                WarehouseDetailDO warehouseDetailDO = convertWarehouseDetail(param, null, vendorId, vendorCode, user, oc, dayType, warehouseDate, WarehouseTypeEnum.OUT.getCode(), warehouseAttendanceConfigDO);
                WarehouseRecordDO warehouseRecordDO = convertWarehouseRecord(param, oc, vendorId, vendorCode, user, faceRecord, warehouseDate, null, warehouseDetailDO.getId());
                warehouseRecordDO.setWarehouseDetailId(warehouseDetailDO.getId());
                warehouseManage.warehouseAttendanceAdd(warehouseDetailDO, warehouseRecordDO, punchRecordDO);
                //计算考勤异常并结果处理
                warehouseAttendanceHandlerService.warehouseAttendanceResultHandler(warehouseDetailDO, attendanceDayId, BusinessConstant.OFF_WORK_PUNCH_IN);
            } else {
                //入仓-》出仓
                WarehouseDetailDO warehouseDetailDO = warehouseDetailDOList.get(0);
                if (param.getUpdateInVendor()) {
                    warehouseDetailDO.setVendorId(vendorId);
                    warehouseDetailDO.setVendorCode(vendorCode);
                }
                warehouseDetailDO.setWarehouseStatus(WarehouseStatusEnum.OUT.getCode());
                EmployeePunchRecordDO punchRecordDO = convertPunchRecord(param.getOcId(), param.getAttendanceTime(), warehouseDetailDO.getClassesId(), user, oc, attendanceDayId.toString());
                WarehouseRecordDO warehouseRecordDO = convertWarehouseRecord(param, oc, vendorId, vendorCode, user, faceRecord, warehouseDate, warehouseDetailDO, warehouseDetailDO.getId());
                warehouseManage.warehouseAttendanceAdd(warehouseDetailDO, warehouseRecordDO, punchRecordDO);

                if (Objects.nonNull(param.getClasses())) {
                    warehouseAttendanceHandlerService.warehouseAttendanceResultHandler(warehouseDetailDO, attendanceDayId, BusinessConstant.OFF_WORK_PUNCH_IN);
                } else {
                    setPcsStatus(warehouseDetailDO);
                    warehouseManage.warehouseAttendanceUpdate(warehouseDetailDO, true);
                }
            }
        }
        return vo;
    }

  /*  @SneakyThrows
    @Override
    public void quickOut(QuickOutParam param) {
        BusinessLogicException.checkTrue(StringUtils.isEmpty(param.getFile().getOriginalFilename()), ErrorCodeEnum.PARAM_VALID_ERROR);
        WarehouseDetailDO warehouseDetailDO = warehouseDetailDao.selectById(param.getId());
        if (Objects.isNull(warehouseDetailDO)) {
            return;
        }
        if (DateUtil.between(warehouseDetailDO.getWarehouseDate(), param.getAttendanceTime(), DateUnit.HOUR) > 23) {
            throw BusinessException.get(ErrorCodeEnum.QUICK_OUT_MUST_IN_24_HOURS.getCode(), I18nUtils.getMessage(ErrorCodeEnum.QUICK_OUT_MUST_IN_24_HOURS.getDesc()));
        }
        //文件上传
        String faceKey = ipepIntegration.upload(BusinessConstant.FACE_UPLOAD_FILE_PATH_PREFIX, param.getFile().getOriginalFilename(), param.getFile().getBytes(), BusinessConstant.OSS_PRIVATE_BUCKET_TYPE);
        //查询网点
        EntOcApiDTO oc = warehouseOcService.getOc(warehouseDetailDO.getOcId());

        checkPunchTime(oc.getCountry(), param.getAttendanceTime());

        //查询人脸录入照
        FaceFeatureDO faceFeatureDO = faceFeatureDao.getByUserCode(warehouseDetailDO.getUserCode());

        saveFaceRecord(warehouseDetailDO, faceKey, faceFeatureDO.getUrl(), param.getAttendanceTime());

        WarehouseFaceRecordDO faceRecord = faceRecordDao.selectLastOne(warehouseDetailDO.getUserId(), param.getAttendanceTime(), FaceRecordStatusEnum.EFFECTIVE.getCode());

        EmployeePunchRecordDO punchRecordDO = convertPunchRecord(warehouseDetailDO, oc, param.getAttendanceTime());
        WarehouseRecordDO warehouseRecordDO = convertWarehouseRecord(warehouseDetailDO, oc, faceRecord.getId(), param);
        warehouseDetailDO.setWarehouseStatus(WarehouseStatusEnum.OUT.getCode());
        warehouseManage.warehouseAttendanceAdd(warehouseDetailDO, warehouseRecordDO, punchRecordDO);

        if (Objects.nonNull(warehouseDetailDO.getClassesId())) {
            warehouseAttendanceHandlerService.warehouseAttendanceResultHandler(warehouseDetailDO, DateHelper.getDayId(warehouseDetailDO.getWarehouseDate()), BusinessConstant.OFF_WORK_PUNCH_IN);
        } else {
            setPcsStatus(warehouseDetailDO);
            warehouseManage.warehouseAttendanceUpdate(warehouseDetailDO, true);
        }
    }*/

    @Override
    public void updateWorkVendor(UpdateWarehouseWorkVendorParam param) {
        List<WarehouseDetailDO> warehouseDetailList = warehouseDetailDao.selectByIds(param.getIdList())
                .stream()
                .filter(warehouse -> Objects.equals(EmploymentTypeEnum.OS_FIXED_SALARY.getCode(), warehouse.getEmployeeType())
                        && !Objects.equals(CountryCodeEnum.BRA.getCode(), warehouse.getCountry()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(warehouseDetailList)) {
            log.error("仓内日报ID列表查询无效");
            return;
        }

        Map<String, String> vendorMap = warehouseSupplierService.getSupplierByCodes(Collections.singletonList(param.getVendorCode()));
        if (MapUtils.isEmpty(vendorMap)) {
            log.error("更新工作供应商：供应商查询异常");
            return;
        }

        List<EmployeeAbnormalAttendanceDO> updateAbnormalAttendanceList = new ArrayList<>();
        List<EmployeeAbnormalOperationRecordDO> deleteAbnormalOperationRecordList = new ArrayList<>();
        List<WarehouseVendorClassesConfirmDO> updateVendorClassesConfirmDOList = new ArrayList<>();
        List<Long> confirmAbnormalIds = new ArrayList<>();
        //已确认的需要回退日报状态 异常状态 清理异常操作记录 更新供应商班次确认记录
        supplierClassConfirmRollbackHandler(warehouseDetailList, updateAbnormalAttendanceList, deleteAbnormalOperationRecordList, updateVendorClassesConfirmDOList, confirmAbnormalIds);

        warehouseDetailList.forEach(warehouseDetailDO -> {
            warehouseDetailDO.setVendorId(param.getVendorId());
            warehouseDetailDO.setVendorCode(param.getVendorCode());
            if (Objects.equals(WarehouseClassConfirmStatusEnum.CONFIRMED.getCode(), warehouseDetailDO.getConfirmStatus())) {
                warehouseDetailDO.setConfirmStatus(WarehouseClassConfirmStatusEnum.UNCONFIRMED.getCode());
            }
            BaseDOUtil.fillDOUpdate(warehouseDetailDO);
        });

        List<Long> warehouseDetailIds = warehouseDetailList.stream().map(WarehouseDetailDO::getId).distinct().collect(Collectors.toList());
        List<WarehouseRecordDO> warehouseRecordDOList = warehouseRecordDao.selectByWarehouseDetailIds(warehouseDetailIds);
        if (CollectionUtils.isNotEmpty(warehouseRecordDOList)) {
            warehouseRecordDOList.forEach(warehouseRecordDO -> {
                warehouseRecordDO.setVendorId(param.getVendorId());
                warehouseRecordDO.setVendorCode(param.getVendorCode());
                BaseDOUtil.fillDOUpdate(warehouseRecordDO);
            });
        }

        List<WarehouseDetailAbnormalDO> warehouseDetailAbnormalDOS = warehouseDetailAbnormalDao.selectByWarehouseDetailIds(warehouseDetailIds);
        if (CollectionUtils.isNotEmpty(warehouseDetailAbnormalDOS)) {
            warehouseDetailAbnormalDOS.forEach(abnormal -> {
                abnormal.setVendorId(param.getVendorId());
                if (confirmAbnormalIds.contains(abnormal.getAbnormalId())) {
                    abnormal.setProcessed(WarehouseAbnormalStatusEnum.PENDING_PROCESSING.getCode());
                }
                BaseDOUtil.fillDOUpdate(abnormal);
            });
        }

        //持久化操作
        warehouseManage.warehouseAttendanceUpdate(warehouseDetailList, warehouseRecordDOList, warehouseDetailAbnormalDOS, null, updateAbnormalAttendanceList, deleteAbnormalOperationRecordList, updateVendorClassesConfirmDOList, null);

        List<Long> normalWarehouseIds = warehouseDetailList.stream().filter(warehouseDetailDO -> StringUtils.isEmpty(warehouseDetailDO.getWarehouseAttendanceCode())).map(WarehouseDetailDO::getId).distinct().collect(Collectors.toList());
        List<Long> retryWarehouseIds = warehouseDetailList.stream().filter(warehouseDetailDO -> StringUtils.isNotEmpty(warehouseDetailDO.getWarehouseAttendanceCode())).map(WarehouseDetailDO::getId).distinct().collect(Collectors.toList());

        //推送财务
        if (CollectionUtils.isNotEmpty(normalWarehouseIds)) {
            attendancePushFinService.asyncPushFin(normalWarehouseIds, BusinessConstant.OFF_WORK_PUNCH_IN);
        }
        if (CollectionUtils.isNotEmpty(retryWarehouseIds)) {
            attendancePushFinService.asyncPushFin(retryWarehouseIds, BusinessConstant.OFF_WORK_PUNCH_REISSUE);
        }
    }

    @Override
    public void updateWorkOc(UpdateWarehouseWorkOcParam param) {
        List<WarehouseDetailDO> warehouseDetailList = warehouseDetailDao.selectByIds(param.getIdList());
        if (CollectionUtils.isEmpty(warehouseDetailList)) {
            log.error("仓内日报ID列表查询无效");
            return;
        }

        AttendanceDept attendanceDept = deptService.getByDeptId(param.getOcId());
        if (Objects.isNull(attendanceDept)) {
            log.error("更新工作网点: 网点信息查询异常");
            return;
        }

        List<EmployeeAbnormalAttendanceDO> updateAbnormalAttendanceList = new ArrayList<>();
        List<EmployeeAbnormalOperationRecordDO> deleteAbnormalOperationRecordList = new ArrayList<>();
        List<WarehouseVendorClassesConfirmDO> updateVendorClassesConfirmDOList = new ArrayList<>();
        List<Long> confirmAbnormalIds = new ArrayList<>();
        //已确认的需要回退日报状态 异常状态 清理异常操作记录 更新供应商班次确认记录
        List<WarehouseDetailDO> laborWarehouseList = warehouseDetailList.stream().filter(warehouse -> Objects.equals(EmploymentTypeEnum.OS_FIXED_SALARY.getCode(), warehouse.getEmployeeType())).collect(Collectors.toList());
        supplierClassConfirmRollbackHandler(laborWarehouseList, updateAbnormalAttendanceList, deleteAbnormalOperationRecordList, updateVendorClassesConfirmDOList, confirmAbnormalIds);

        List<EmployeePunchRecordDO> employeePunchRecordDOList = new ArrayList<>();
        warehouseDetailList.forEach(warehouseDetailDO -> {
            warehouseDetailDO.setOcId(param.getOcId());
            if (Objects.equals(WarehouseClassConfirmStatusEnum.CONFIRMED.getCode(), warehouseDetailDO.getConfirmStatus())) {
                warehouseDetailDO.setConfirmStatus(WarehouseClassConfirmStatusEnum.UNCONFIRMED.getCode());
            }
            BaseDOUtil.fillDOUpdate(warehouseDetailDO);

            String dayId = DateUtil.format(warehouseDetailDO.getWarehouseDate(), DatePattern.PURE_DATE_PATTERN);
            List<EmployeePunchRecordDO> employeePunchRecordDOS = employeePunchRecordDao.listRecords(EmployeePunchCardRecordQuery.builder().userCode(warehouseDetailDO.getUserCode()).dayId(dayId).build());
            employeePunchRecordDOS.forEach(punchRecordDO -> punchRecordDO.setDeptId(param.getOcId()));
            employeePunchRecordDOList.addAll(employeePunchRecordDOS);
        });

        List<Long> warehouseDetailIds = warehouseDetailList.stream().map(WarehouseDetailDO::getId).distinct().collect(Collectors.toList());
        List<WarehouseRecordDO> warehouseRecordDOList = warehouseRecordDao.selectByWarehouseDetailIds(warehouseDetailIds);
        if (CollectionUtils.isNotEmpty(warehouseRecordDOList)) {
            warehouseRecordDOList.forEach(warehouseRecordDO -> {
                warehouseRecordDO.setOcId(param.getOcId());
                BaseDOUtil.fillDOUpdate(warehouseRecordDO);
            });
        }

        List<WarehouseDetailAbnormalDO> warehouseDetailAbnormalDOS = warehouseDetailAbnormalDao.selectByWarehouseDetailIds(warehouseDetailIds);
        if (CollectionUtils.isNotEmpty(warehouseDetailAbnormalDOS)) {
            warehouseDetailAbnormalDOS.forEach(abnormal -> {
                abnormal.setOcId(param.getOcId());
                if (confirmAbnormalIds.contains(abnormal.getAbnormalId())) {
                    abnormal.setProcessed(WarehouseAbnormalStatusEnum.PENDING_PROCESSING.getCode());
                }
                BaseDOUtil.fillDOUpdate(abnormal);
            });
        }

        warehouseManage.warehouseAttendanceUpdate(warehouseDetailList, warehouseRecordDOList, warehouseDetailAbnormalDOS, null, updateAbnormalAttendanceList, deleteAbnormalOperationRecordList, updateVendorClassesConfirmDOList, employeePunchRecordDOList);

        List<Long> normalWarehouseIds = laborWarehouseList.stream().filter(warehouseDetailDO -> StringUtils.isEmpty(warehouseDetailDO.getWarehouseAttendanceCode())).map(WarehouseDetailDO::getId).distinct().collect(Collectors.toList());
        List<Long> retryWarehouseIds = laborWarehouseList.stream().filter(warehouseDetailDO -> StringUtils.isNotEmpty(warehouseDetailDO.getWarehouseAttendanceCode())).map(WarehouseDetailDO::getId).distinct().collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(normalWarehouseIds)) {
            attendancePushFinService.asyncPushFin(normalWarehouseIds, BusinessConstant.OFF_WORK_PUNCH_IN);
        }
        if (CollectionUtils.isNotEmpty(retryWarehouseIds)) {
            attendancePushFinService.asyncPushFin(retryWarehouseIds, BusinessConstant.OFF_WORK_PUNCH_REISSUE);
        }
    }

    @Override
    public void updateWorkClasses(UpdateWarehouseWorkClassesParam param) {
        List<WarehouseDetailDO> warehouseDetailList = warehouseDetailDao.selectByIds(param.getIdList());
        if (CollectionUtils.isEmpty(warehouseDetailList)) {
            log.error("仓内日报ID列表查询无效");
            return;
        }

        //查询班次信息
        ClassesParam classes = warehouseClassesService.getClassesInfo(param.getClassId());

        List<AttendanceEmployeeDetailDO> attendanceEmployeeDetailList = new ArrayList<>();
        List<EmployeeAbnormalAttendanceDO> updateAbnormalAttendanceList = new ArrayList<>();
        List<EmployeeAbnormalOperationRecordDO> deleteAbnormalOperationRecordList = new ArrayList<>();
        List<WarehouseVendorClassesConfirmDO> updateVendorClassesConfirmDOList = new ArrayList<>();
        List<Long> confirmAbnormalIds = new ArrayList<>();
        //已确认的需要回退日报状态 异常状态 清理异常操作记录 更新供应商班次确认记录
        List<WarehouseDetailDO> laborWarehouseList = warehouseDetailList.stream().filter(warehouse -> Objects.equals(EmploymentTypeEnum.OS_FIXED_SALARY.getCode(), warehouse.getEmployeeType())).collect(Collectors.toList());
        supplierClassConfirmRollbackHandler(laborWarehouseList, updateAbnormalAttendanceList, deleteAbnormalOperationRecordList, updateVendorClassesConfirmDOList, confirmAbnormalIds);

        clearOriginalClassAttendanceData(warehouseDetailList, attendanceEmployeeDetailList, updateAbnormalAttendanceList, deleteAbnormalOperationRecordList);

        List<EmployeePunchRecordDO> employeePunchRecordDOList = new ArrayList<>();
        Map<String, List<WarehouseDetailDO>> warehouseGroupMap = warehouseDetailList
                .stream()
                .collect(Collectors.groupingBy(warehouse -> DateUtil.formatDate(warehouse.getWarehouseDate()) + "_" + warehouse.getUserCode()));

        for (String warehouseKey : warehouseGroupMap.keySet()) {
            List<WarehouseDetailDO> warehouseDetailDOS = warehouseGroupMap.get(warehouseKey);
            String[] keySplit = warehouseKey.split("_");
            Date warehouseDate = DateUtil.parseDate(keySplit[0]);
            String userCode = keySplit[1];
            Long dayId = DateHelper.getDayId(warehouseDate);
            List<EmployeePunchRecordDO> employeePunchRecordDOS = employeePunchRecordDao.listRecords(EmployeePunchCardRecordQuery.builder().userCode(userCode).dayId(dayId.toString()).build());
            employeePunchRecordDOS.forEach(punchRecordDO -> punchRecordDO.setClassId(classes.getClassId()));
            employeePunchRecordDOList.addAll(employeePunchRecordDOS);
            warehouseDetailDOS.forEach(warehouse -> {
                warehouse.setClassesId(classes.getClassId());
                warehouse.setClassesName(classes.getClassName());
                warehouse.setClassesType(classes.getClassType());
                warehouse.setRequiredAttendanceTime(classes.getAttendanceHours());
                warehouse.setLegalWorkingHours(classes.getLegalWorkingHours());

                if (Objects.equals(WarehouseClassConfirmStatusEnum.CONFIRMED.getCode(), warehouse.getConfirmStatus())) {
                    warehouse.setConfirmStatus(WarehouseClassConfirmStatusEnum.UNCONFIRMED.getCode());
                }
                BaseDOUtil.fillDOUpdate(warehouse);

                //重新排班
                generateSchedulingPlan(classes, warehouse.getWarehouseDate(), dayId, warehouse.getUserId());
            });
        }

        warehouseManage.warehouseAttendanceUpdate(warehouseDetailList, null, null, attendanceEmployeeDetailList, updateAbnormalAttendanceList, deleteAbnormalOperationRecordList, updateVendorClassesConfirmDOList, employeePunchRecordDOList);

        //计算异常
        warehouseDetailList.forEach(warehouse -> {
            if (Objects.equals(WarehouseAttendanceStatusEnum.INIT.getCode(), warehouse.getAttendanceStatus())) {
                warehouseAttendanceHandlerService.warehouseAttendanceResultHandler(warehouse, Long.valueOf(DateUtil.format(warehouse.getWarehouseDate(), DatePattern.PURE_DATE_PATTERN)), BusinessConstant.OFF_WORK_PUNCH_IN);
            } else {
                warehouseAttendanceHandlerService.warehouseAttendanceResultHandler(warehouse, Long.valueOf(DateUtil.format(warehouse.getWarehouseDate(), DatePattern.PURE_DATE_PATTERN)), BusinessConstant.OFF_WORK_PUNCH_REISSUE);
            }
        });
    }

    @Override
    public Boolean bingShift(BingShiftParam param) {
        WarehouseDetailQuery warehouseDetailParam = new WarehouseDetailQuery();
        warehouseDetailParam.setIds(param.getIdList());
        List<WarehouseDetailDO> warehouseDetailDOS = warehouseDetailDao.selectNoBindShiftByCondition(warehouseDetailParam);
        if (CollectionUtils.isEmpty(warehouseDetailDOS)) {
            return Boolean.FALSE;
        }

        List<Long> userIds = warehouseDetailDOS.stream().map(WarehouseDetailDO::getUserId).distinct().collect(Collectors.toList());
        List<String> userCodes = warehouseDetailDOS.stream().map(WarehouseDetailDO::getUserCode).distinct().collect(Collectors.toList());
        List<UserInfoDO> userInfoDOList = userInfoDao.getByUserIds(userIds);
        warehouseDetailDOS.forEach(warehouseDetail -> bizTaskThreadPool.execute(() -> {
            try {
                warehouseDetail.setClassesId(param.getClasses().getClassId());
                warehouseDetail.setClassesType(param.getClasses().getClassType());
                warehouseDetail.setClassesName(param.getClasses().getClassName());
                warehouseDetail.setRequiredAttendanceTime(param.getClasses().getAttendanceHours());
                warehouseDetail.setLegalWorkingHours(param.getClasses().getLegalWorkingHours());

                //先排班
                ClassesParam classes = BeanUtils.convert(param.getClasses(), ClassesParam.class);
                generateSchedulingPlan(classes, warehouseDetail.getWarehouseDate(), null, warehouseDetail.getUserId());

                if (StringUtils.isEmpty(warehouseDetail.getAttendanceType())) {
                    UserInfoDO userInfoDO = userInfoDOList.stream().filter(user -> Objects.equals(warehouseDetail.getUserId(), user.getId())).findFirst().orElse(new UserInfoDO());
                    String dayType = getCurrentDayType(warehouseDetail.getWarehouseDate(), userInfoDO);
                    warehouseDetail.setAttendanceType(dayType);
                }

                //考勤异常计算结果处理
                Long dayId = DateHelper.getDayId(warehouseDetail.getWarehouseDate());
                warehouseAttendanceHandlerService.warehouseAttendanceResultHandler(warehouseDetail, dayId, BusinessConstant.OFF_WORK_PUNCH_IN);
            } catch (Exception e) {
                log.info("仓内考勤：{} ,绑定班次考勤计算异常: {}", warehouseDetail.getId().toString(), e.getMessage());
            }
        }));
        //发放打卡次数
        Map<Date, List<WarehouseDetailDO>> warehouseDetailMap = warehouseDetailDOS.stream().collect(Collectors.groupingBy(WarehouseDetailDO::getWarehouseDate));
        List<UserCardParam> userCardParamList = new ArrayList<>();
        for (Date warehouseDate : warehouseDetailMap.keySet()) {
            List<WarehouseDetailDO> warehouseDetailDOList = warehouseDetailMap.get(warehouseDate);
            UserCardParam userCardParam = new UserCardParam();
            userCardParam.setInitDayId(DateHelper.getDayId(warehouseDate));
            userCardParam.setUserCodeList(warehouseDetailDOList.stream().map(WarehouseDetailDO::getUserCode).distinct().collect(Collectors.toList()));
            userCardParamList.add(userCardParam);
        }
        WarehouseUserCardParam userCardParam = new WarehouseUserCardParam();
        userCardParam.setAllUserCodeList(userCodes);
        userCardParam.setUserCardParamList(userCardParamList);
        userReissueCardConfigService.bindUserCardConfig(userCardParam);
        return Boolean.TRUE;
    }

    @Override
    public void retryPushFin(WarehouseDetailQuery param) {
        param.setAttendanceStatusList(Lists.newArrayList(WarehouseAttendanceStatusEnum.NORMAL.getCode(), WarehouseAttendanceStatusEnum.ABNORMAL.getCode()));
        List<WarehouseDetailDO> warehouseDetailDOS = warehouseDetailDao.selectByCondition(param);
        if (CollectionUtils.isEmpty(warehouseDetailDOS)) {
            return;
        }
        List<Long> warehouseDetailIdList = warehouseDetailDOS.stream().map(WarehouseDetailDO::getId).collect(Collectors.toList());
        log.info("补推财务仓内考勤日报ids: {}", warehouseDetailIdList);
        attendancePushFinService.asyncPushFin(warehouseDetailIdList, BusinessConstant.OFF_WORK_PUNCH_REISSUE);
    }

    @SneakyThrows
    @Override
    public Long vendorClassedConfirm(VendorConfirmClassesParam param) {
        // 判断是否到下班打卡时间
        Date endTime = getTheLastClockTime(param.getWarehouseDate(), param.getClassesId());
        if (endTime == null || endTime.after(param.getConfirmDate())) {
            throw BusinessException.get(ErrorCodeEnum.NOT_YET_THE_END_OF_SHIFT.getCode(), I18nUtils.getMessage(ErrorCodeEnum.NOT_YET_THE_END_OF_SHIFT.getDesc()));
        }

        if (!migrationService.verifyWarehouseUserIsEnableNewAttendance(param.getOcId(), null)) {
            log.info("供应商班次确认,非灰度人员网点: {}", param.getOcId());
            throw BusinessException.get(ErrorCodeEnum.WAREHOUSE_GRAYSCALE_DEPT_ERROR.getCode(), I18nUtils.getMessage(ErrorCodeEnum.WAREHOUSE_GRAYSCALE_DEPT_ERROR.getDesc()));
        }

        //判断班次结果是否重复确认
        WarehouseVendorClassesConfirmParam warehouseVendorClassesConfirmParam = new WarehouseVendorClassesConfirmParam();
        warehouseVendorClassesConfirmParam.setWarehouseDate(param.getWarehouseDate());
        warehouseVendorClassesConfirmParam.setOcId(param.getOcId());
        warehouseVendorClassesConfirmParam.setVendorCode(param.getVendorCode());
        warehouseVendorClassesConfirmParam.setClassesId(param.getClassesId());
        List<WarehouseVendorClassesConfirmDO> warehouseVendorClassesConfirmDOList = warehouseVendorClassesConfirmDao.selectByContidition(warehouseVendorClassesConfirmParam);
        if (CollectionUtils.isNotEmpty(warehouseVendorClassesConfirmDOList)) {
            throw BusinessException.get(ErrorCodeEnum.VENDOR_CLASSES_CONFIRM_REPEAT.getCode(), I18nUtils.getMessage(ErrorCodeEnum.VENDOR_CLASSES_CONFIRM_REPEAT.getDesc()));
        }

        //上传文件
        String signedUrl = ipepIntegration.upload(BusinessConstant.WAREHOUSE_UPLOAD_FILE_PATH_PREFIX, param.getSignedFile().getOriginalFilename(), param.getSignedFile().getBytes(), BusinessConstant.OSS_PRIVATE_BUCKET_TYPE);
        String faceUrl = ipepIntegration.upload(BusinessConstant.WAREHOUSE_UPLOAD_FILE_PATH_PREFIX, param.getFaceFile().getOriginalFilename(), param.getFaceFile().getBytes(), BusinessConstant.OSS_PRIVATE_BUCKET_TYPE);

        //人脸信息检测、人脸活性检测
        String imageBase64 = BaseEncoding.base64().encode(param.getFaceFile().getBytes());
        try {
            recognitionService.detectFace(faceUrl, imageBase64);
        } catch (RpcException e) {
            log.error("班次确认,人脸检测异常：{}", Throwables.getStackTraceAsString(e));
            throw BusinessException.get(String.valueOf(e.getCode()), e.getMessage());
        }

        //查询日报
        WarehouseDetailQuery warehouseDetailParam = new WarehouseDetailQuery();
        warehouseDetailParam.setWarehouseDate(param.getWarehouseDate());
        warehouseDetailParam.setOcId(param.getOcId());
        warehouseDetailParam.setVendorCode(param.getVendorCode());
        warehouseDetailParam.setClassId(param.getClassesId());
        List<WarehouseDetailDO> warehouseDetailDOS = warehouseDetailDao.selectByCondition(warehouseDetailParam);
        if (CollectionUtils.isEmpty(warehouseDetailDOS)) {
            throw BusinessException.get(ErrorCodeEnum.WAREHOUSE_NO_ATTENDANCE_RECORD.getCode(), I18nUtils.getMessage(ErrorCodeEnum.WAREHOUSE_NO_ATTENDANCE_RECORD.getDesc()));
        }

        //待计算考勤日报
        List<WarehouseDetailDO> attendanceCalculatedList = warehouseDetailDOS.stream().filter(warehouse -> Objects.equals(WarehouseAttendanceStatusEnum.INIT.getCode(), warehouse.getAttendanceStatus())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(attendanceCalculatedList)) {
            attendanceCalculatedList.forEach(warehouseDetailDO -> {
                //计算考勤时长
                List<WarehousePunchPeriodDO> warehousePunchPeriodDOList = attendanceDurationCalculateService.calculateAttendanceHours(warehouseDetailDO, false);

                Long dayId = DateHelper.getDayId(warehouseDetailDO.getWarehouseDate());
                AttendanceCalculateHandlerDTO attendanceHandlerDTO = new AttendanceCalculateHandlerDTO();
                attendanceHandlerDTO.setUserCodes(warehouseDetailDO.getUserCode());
                attendanceHandlerDTO.setAttendanceDayId(dayId);
                attendanceHandlerDTO.setOcId(warehouseDetailDO.getOcId());
                attendanceHandlerDTO.setClassId(warehouseDetailDO.getClassesId());
                attendanceHandlerDTO.setWarehouseAttendanceConfigId(warehouseDetailDO.getAttendanceConfigId());
                attendanceHandlerDTO.setActualAttendanceTime(warehouseDetailDO.getActualAttendanceTime());
                attendanceHandlerDTO.setActualWorkingHours(warehouseDetailDO.getActualWorkingHours());
                attendanceCalculateService.warehouseLaborWorkerAttendanceHandler(attendanceHandlerDTO);

                //考勤结果处理
                warehouseAttendanceHandlerService.attendanceAbnormalHandler(warehouseDetailDO, dayId, true, warehousePunchPeriodDOList);
            });
        }

        WarehouseDetailQuery detailParam = new WarehouseDetailQuery();
        detailParam.setWarehouseDate(param.getWarehouseDate());
        detailParam.setOcId(param.getOcId());
        detailParam.setVendorCode(param.getVendorCode());
        detailParam.setClassId(param.getClassesId());
        detailParam.setAttendanceStatusList(Collections.singletonList(WarehouseAttendanceStatusEnum.ABNORMAL.getCode()));
        List<WarehouseDetailDO> warehouseDetailDOList = warehouseDetailDao.selectByCondition(detailParam);

        WarehouseVendorClassesConfirmDO warehouseVendorClassesConfirmDO = BeanUtils.convert(param, WarehouseVendorClassesConfirmDO.class);
        warehouseVendorClassesConfirmDO.setId(defaultIdWorker.nextId());
        warehouseVendorClassesConfirmDO.setSignedPhoto(signedUrl);
        warehouseVendorClassesConfirmDO.setFacePhoto(faceUrl);

        if (CollectionUtils.isNotEmpty(warehouseDetailDOList)) {
            List<Long> warehouseIds = warehouseDetailDOList.stream().map(WarehouseDetailDO::getId).collect(Collectors.toList());
            List<WarehouseDetailAbnormalDO> warehouseDetailAbnormalDOS = warehouseDetailAbnormalDao.selectByWarehouseDetailIds(warehouseIds);
            List<Long> abnormalIdList = new ArrayList<>();
            List<Long> processingAbnormalIds = warehouseDetailAbnormalDOS
                    .stream()
                    .filter(abnormal -> Objects.equals(BusinessConstant.N, abnormal.getProcessed()))
                    .map(WarehouseDetailAbnormalDO::getAbnormalId)
                    .collect(Collectors.toList());

            if (CollectionUtils.isNotEmpty(processingAbnormalIds)) {
                abnormalIdList.addAll(processingAbnormalIds);
            }

            List<Long> processedAbnormalIds = warehouseDetailAbnormalDOS
                    .stream()
                    .filter(abnormal -> Objects.equals(BusinessConstant.Y, abnormal.getProcessed())
                            && Objects.equals(AttendanceAbnormalTypeEnum.ABNORMAL_DURATION.getCode(), abnormal.getAbnormalType()))
                    .map(WarehouseDetailAbnormalDO::getAbnormalId)
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(processedAbnormalIds)) {
                List<EmployeeAbnormalOperationRecordDO> employeeAbnormalOperationRecordDOS = abnormalOperationRecordDao.selectByAbnormalList(processedAbnormalIds);
                List<Long> confirmAbnormalIds = new ArrayList<>();
                List<EmployeeAbnormalOperationRecordDO> abnormalOperationRecordList = employeeAbnormalOperationRecordDOS
                        .stream()
                        .filter(abnormalOperation -> AbnormalOperationTypeEnum.ABNORMAL_CONFIRM.getCode().equals(abnormalOperation.getOperationType())).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(abnormalOperationRecordList)) {
                    confirmAbnormalIds.addAll(abnormalOperationRecordList.stream().map(EmployeeAbnormalOperationRecordDO::getAbnormalId).collect(Collectors.toList()));
                }
                processedAbnormalIds.removeAll(confirmAbnormalIds);
                if (CollectionUtils.isNotEmpty(processedAbnormalIds)) {
                    abnormalIdList.addAll(processedAbnormalIds);
                }
            }

            if (CollectionUtils.isNotEmpty(abnormalIdList)) {
                AbnormalAttendanceBatchUpdateParam abnormalAttendanceBatchUpdateParam = new AbnormalAttendanceBatchUpdateParam();
                abnormalAttendanceBatchUpdateParam.setAbnormalIdList(abnormalIdList);
                abnormalAttendanceBatchUpdateParam.setUpdateType(AbnormalOperationTypeEnum.ABNORMAL_CONFIRM.getCode());
                abnormalAttendanceBatchUpdateParam.setConfirmTime(param.getConfirmDate());
                employeeAbnormalAttendanceService.vendorBatchAbnormalConfirmHandler(abnormalAttendanceBatchUpdateParam);
            }

            List<WarehouseDetailAbnormalDO> abnormalList = warehouseDetailAbnormalDao.selectByWarehouseDetailIds(warehouseIds);
            Integer inWithoutPunchCount = abnormalCount(abnormalList, AttendanceAbnormalTypeEnum.BEFORE_OFFICE_LACK.getCode());
            Integer outWithoutPunchCount = abnormalCount(abnormalList, AttendanceAbnormalTypeEnum.AFTER_OFFICE_LACK.getCode());
            Integer latePunchCount = abnormalCount(abnormalList, AttendanceAbnormalTypeEnum.LATE.getCode());
            Integer leaveEarlyPunchCount = abnormalCount(abnormalList, AttendanceAbnormalTypeEnum.LEAVE_EARLY.getCode());
            Integer abnormalDurationPunchCount = abnormalCount(abnormalList, AttendanceAbnormalTypeEnum.ABNORMAL_DURATION.getCode());

            warehouseVendorClassesConfirmDO.setBeforeOfficeLackNum(inWithoutPunchCount);
            warehouseVendorClassesConfirmDO.setAfterOfficeLackNum(outWithoutPunchCount);
            warehouseVendorClassesConfirmDO.setLateNum(latePunchCount);
            warehouseVendorClassesConfirmDO.setLeaveEarlyNum(leaveEarlyPunchCount);
            warehouseVendorClassesConfirmDO.setAbnormalDurationNum(abnormalDurationPunchCount);
            //这里能这样写是因为劳务员工不存在上下班同时缺卡
            warehouseVendorClassesConfirmDO.setActualAttendanceNum(warehouseDetailDOS.size() - warehouseVendorClassesConfirmDO.getBeforeOfficeLackNum() - warehouseVendorClassesConfirmDO.getAfterOfficeLackNum());

            warehouseDetailDOS.forEach(warehouseDetailDO -> warehouseDetailDO.setConfirmStatus(WarehouseClassConfirmStatusEnum.CONFIRMED.getCode()));
            warehouseManage.warehouseAttendanceConfirm(warehouseDetailDOS, warehouseVendorClassesConfirmDO);

            if (CollectionUtils.isNotEmpty(attendanceCalculatedList)) {
                attendancePushFinService.asyncPushFin(attendanceCalculatedList.stream().map(WarehouseDetailDO::getId).collect(Collectors.toList()), BusinessConstant.OFF_WORK_PUNCH_IN);
            }
            return warehouseVendorClassesConfirmDO.getId();
        }

        //都是正常考勤
        warehouseVendorClassesConfirmDO.setActualAttendanceNum(warehouseDetailDOS.size());
        warehouseVendorClassesConfirmDO.setBeforeOfficeLackNum(0);
        warehouseVendorClassesConfirmDO.setAfterOfficeLackNum(0);
        warehouseVendorClassesConfirmDO.setLateNum(0);
        warehouseVendorClassesConfirmDO.setLeaveEarlyNum(0);
        warehouseVendorClassesConfirmDO.setAbnormalDurationNum(0);
        warehouseDetailDOS.forEach(warehouseDetailDO -> warehouseDetailDO.setConfirmStatus(WarehouseClassConfirmStatusEnum.CONFIRMED.getCode()));
        warehouseManage.warehouseAttendanceConfirm(warehouseDetailDOS, warehouseVendorClassesConfirmDO);

        if (CollectionUtils.isNotEmpty(attendanceCalculatedList)) {
            attendancePushFinService.asyncPushFin(attendanceCalculatedList.stream().map(WarehouseDetailDO::getId).collect(Collectors.toList()), BusinessConstant.OFF_WORK_PUNCH_IN);
        }
        return warehouseVendorClassesConfirmDO.getId();
    }

    @Override
    public CheckVendorConfirmStatusResultVO checkVendorConfirmStatus(CheckVendorConfirmStatusParam param) {
        CheckVendorConfirmStatusResultVO result = new CheckVendorConfirmStatusResultVO();
        // 获取确认状态和确认id
        WarehouseVendorClassesConfirmDO confirmResult = warehouseVendorClassesConfirmDao.selectConfirmResultBy(param.getOcId(), param.getVendorCode(), param.getClassesId(), param.getWarehouseDate());
        if (confirmResult != null) {
            result.setConfirmStatus(ConfirmStatusEnum.CONFIRM.getValue());
            result.setClassesConfirmId(confirmResult.getId());
            return result;
        }
        // 计算最晚打卡时间
        Date endTime = getTheLastClockTime(param.getWarehouseDate(), param.getClassesId());
        if (endTime == null) {
            result.setConfirmStatus(ConfirmStatusEnum.NOT_CONFIGURE.getValue());
            return result;
        }
        result.setConfirmStatus(endTime.after(param.getCurrentTime()) ? ConfirmStatusEnum.NOT_YET_TIME.getValue() : ConfirmStatusEnum.WAIT_CONFIRM.getValue());
        return result;
    }

    private Date getTheLastClockTime(Date warehouseDate, Long classId) {
        List<PunchClassItemConfigDO> classItemList = punchClassItemConfigDao.selectLatestByClassIds(Collections.singletonList(classId));
        if (CollectionUtils.isEmpty(classItemList)) {
            return null;
        }
        PunchClassItemConfigDO item = classItemList.stream().filter(e -> e.getSortNo().equals(1)).findFirst().orElse(null);
        if (item == null) {
            return null;
        }
        Long dayId = DateHelper.getDayId(warehouseDate);
        // 计算最晚打卡时间
        DayPunchTimeDTO punchTime = punchClassConfigQueryService.getUserPunchClassItemDayTime(dayId, item.getId(), classItemList);

        //最晚下班打卡时间
        Date latestPunchOutTime = punchTime.getDayPunchEndTime();
        //下班时间
        Date punchOutTime;
        if (item.getPunchOutTime().after(item.getLatestPunchOutTime())) {
            punchOutTime = DateHelper.concatDateAndTime(DateHelper.formatYYYYMMDD(DateHelper.pushDate(latestPunchOutTime, -1)), DateHelper.formatHHMMSS(item.getPunchOutTime()));
        } else {
            punchOutTime = DateHelper.concatDateAndTime(DateHelper.formatYYYYMMDD(latestPunchOutTime), DateHelper.formatHHMMSS(item.getPunchOutTime()));
        }
        return punchOutTime;
    }


    private void checkAuthOc(Long ocId) {
        List<AttendanceDept> attendanceDeptList = wpmPermissionService.getRegionStationAuthDeptList(RequestInfoHolder.getUserCode());
        List<Long> authOcIdList = attendanceDeptList.stream().map(AttendanceDept::getId).collect(Collectors.toList());
        if (!authOcIdList.contains(ocId)) {
            throw BusinessException.get(ErrorCodeEnum.USER_NOT_HAVE_THE_OPERATION_PERMISSION_ON_THIS_STATION.getCode(),
                    I18nUtils.getMessage(ErrorCodeEnum.USER_NOT_HAVE_THE_OPERATION_PERMISSION_ON_THIS_STATION.getDesc()));
        }
    }

    /**
     * 入仓检测
     *
     * @param warehouseDetailDOS 仓内考勤记录
     * @param param              出入参参数
     * @param user               用户
     * @param isMultipleShifts   是否允许上多班次
     */
    private void checkIn(List<WarehouseDetailDO> warehouseDetailDOS, InOrOutParam param, UserInfoDO user, boolean isMultipleShifts) {
        //判断是否是黑名单用户
        RpcResult<BlacklistInfoDTO> result = blacklistApi.getBlacklistInfo(user.getUserCode());
        if (result.isSuccess() && Objects.nonNull(result.getResult()) && BlacklistBanStatusEnum.getBanStatusList().contains(result.getResult().getBanStatus())) {
            addBlackListRecord(param, user, result, WarehouseBlackTypeEnum.IN);
            throw BusinessLogicException.getException(ErrorCodeEnum.BLACKLIST_USER_CANNOT_ENTER, result.getResult().getReason());
        }

        if (CollectionUtils.isEmpty(warehouseDetailDOS)) {
            return;
        }
        //同一个网点
        List<WarehouseDetailDO> warehouseDetailDOList = warehouseDetailDOS
                .stream()
                .filter(warehouse -> Objects.equals(warehouse.getOcId(), param.getOcId()))
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(warehouseDetailDOList)) {
            WarehouseDetailDO warehouseDetailDO = warehouseDetailDOList.get(0);
            List<WarehouseRecordDO> recordList = warehouseRecordDao.selectByWarehouseDetailIds(Collections.singletonList(warehouseDetailDO.getId()));
            List<WarehouseRecordDO> inRecordList = recordList.stream().filter(record -> WarehouseTypeEnum.IN.getCode().equals(record.getRecordType())).collect(Collectors.toList());
            List<WarehouseRecordDO> outRecordList = recordList.stream().filter(record -> WarehouseTypeEnum.OUT.getCode().equals(record.getRecordType())).collect(Collectors.toList());

            //同一天同网点不同供应商限制作业
            if (Objects.nonNull(param.getVendorId()) && !Objects.equals(warehouseDetailDO.getVendorId(), param.getVendorId())) {
                Map<String, String> vendorMap = warehouseSupplierService.getSupplierByCodes(Collections.singletonList(warehouseDetailDO.getVendorCode()));
                throw BusinessLogicException.getException(ErrorCodeEnum.CANT_CHANGE_VENDOR, vendorMap.get(warehouseDetailDO.getVendorCode()));
            }

            if (Objects.nonNull(param.getClasses())) {
                //若不允许上多班次，在前一个班次离仓后限制作业 || 自有员工限制同网点一天上多班次
                if (Objects.nonNull(warehouseDetailDO.getClassesId()) && !Objects.equals(param.getClasses().getClassId(), warehouseDetailDO.getClassesId())) {
                    if ((!isMultipleShifts && Objects.equals(EmploymentTypeEnum.OS_FIXED_SALARY.getCode(), user.getEmployeeType()))
                            || Lists.newArrayList(EmploymentTypeEnum.EMPLOYEE.getCode(), EmploymentTypeEnum.SUB_EMPLOYEE.getCode()).contains(user.getEmployeeType())) {
                        throw BusinessException.get(ErrorCodeEnum.ALREADY_WORKING_IN_THE_WAREHOSUE.getCode(), I18nUtils.getMessage(ErrorCodeEnum.ALREADY_WORKING_IN_THE_WAREHOSUE.getDesc()));
                    }
                }

                //若入仓两次的班次信息不同，要求前一段先出仓
                if (Objects.nonNull(warehouseDetailDO.getClassesId())
                        && !param.getClasses().getClassId().equals(warehouseDetailDO.getClassesId())
                        && inRecordList.size() > outRecordList.size()) {
                    throw BusinessLogicException.getException(ErrorCodeEnum.EXIT_WAREHOUSE_THEN_IN, warehouseDetailDO.getClassesName());
                }
            }
        }

        //当前已存在考勤结果记录
        List<WarehouseDetailDO> otherOcOrVendorList = warehouseDetailDOS
                .stream()
                .filter(warehouse -> !Objects.equals(warehouse.getOcId(), param.getOcId()))
                .collect(Collectors.toList());
        //在其他网点当天考勤记录
        if (CollectionUtils.isNotEmpty(otherOcOrVendorList)) {
            Map<Long, WarehouseDetailDO> warehouseDetailDOMap = otherOcOrVendorList.stream().collect(Collectors.toMap(WarehouseDetailDO::getId, Function.identity()));
            Map<Long, List<WarehouseRecordDO>> recordGroupMap = warehouseRecordDao.selectByWarehouseDetailIds(otherOcOrVendorList.stream().map(WarehouseDetailDO::getId).distinct().collect(Collectors.toList()))
                    .stream()
                    .collect(Collectors.groupingBy(WarehouseRecordDO::getWarehouseDetailId));
            for (Long warehouseDetailId : recordGroupMap.keySet()) {
                WarehouseDetailDO warehouseDetailDO = warehouseDetailDOMap.get(warehouseDetailId);
                List<WarehouseRecordDO> recordList = recordGroupMap.get(warehouseDetailId);
                List<WarehouseRecordDO> inRecordList = recordList.stream().filter(record -> WarehouseTypeEnum.IN.getCode().equals(record.getRecordType())).collect(Collectors.toList());
                List<WarehouseRecordDO> outRecordList = recordList.stream().filter(record -> WarehouseTypeEnum.OUT.getCode().equals(record.getRecordType())).collect(Collectors.toList());

                //在其他网点已入仓还未出仓 不可重复入仓
                if (inRecordList.size() > outRecordList.size() || CollectionUtils.isEmpty(recordList)) {
                    String ocName = Optional.ofNullable(deptService.getByDeptId(warehouseDetailDO.getOcId()))
                            .orElse(new AttendanceDept()).getDeptNameEn();
                    throw BusinessLogicException.getException(ErrorCodeEnum.EXIT_OTHER_OC_WAREHOUSE_THEN_IN, ocName);
                }
            }
        }
    }

    @NotNull
    private WarehouseDetailDO convertWarehouseDetail(InOrOutParam param,
                                                     List<WarehouseDetailDO> warehouseDetailDOS,
                                                     Long vendorId,
                                                     String vendorCode,
                                                     UserInfoDO user,
                                                     EntOcApiDTO oc,
                                                     String dayType,
                                                     Date warehouseDate,
                                                     Integer warehouseType,
                                                     WarehouseAttendanceConfigDO warehouseAttendanceConfigDO) {
        if (CollectionUtils.isNotEmpty(warehouseDetailDOS)) {
            if (Objects.isNull(param.getClasses())) {
                return warehouseDetailDOS.stream().filter(warehouse -> Objects.equals(warehouse.getOcId(), param.getOcId())).findFirst().orElse(warehouseDetailDOS.get(0));
            }

            Optional<WarehouseDetailDO> warehouseDetailDOOptional = warehouseDetailDOS
                    .stream()
                    .filter(record -> Objects.equals(record.getOcId(), param.getOcId())
                            && Objects.equals(param.getClasses().getClassId(), record.getClassesId()))
                    .findFirst();
            if (warehouseDetailDOOptional.isPresent()) {
                return warehouseDetailDOOptional.get();
            }
            List<WarehouseDetailDO> noClassWarehouseDetailDOList = warehouseDetailDOS
                    .stream()
                    .filter(record -> Objects.equals(record.getOcId(), param.getOcId())
                            && Objects.isNull(record.getClassesId()))
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(noClassWarehouseDetailDOList)) {
                WarehouseDetailDO warehouseDetailDO = warehouseDetailDOS.get(0);
                warehouseDetailDO.setClassesId(param.getClasses().getClassId());
                warehouseDetailDO.setClassesType(param.getClasses().getClassType());
                warehouseDetailDO.setClassesName(param.getClasses().getClassName());
                warehouseDetailDO.setLegalWorkingHours(param.getClasses().getLegalWorkingHours());
                warehouseDetailDO.setRequiredAttendanceTime(param.getClasses().getAttendanceHours());
                return warehouseDetailDO;
            }
        }

        WarehouseDetailDO warehouseDetailDO = new WarehouseDetailDO();
        warehouseDetailDO.setId(defaultIdWorker.nextId());
        warehouseDetailDO.setCountry(oc.getCountry());
        warehouseDetailDO.setCity(oc.getCity());
        warehouseDetailDO.setOcId(param.getOcId());
        warehouseDetailDO.setOcLongitude(oc.getLongitude());
        warehouseDetailDO.setOcLatitude(oc.getLatitude());
        warehouseDetailDO.setVendorId(vendorId);
        warehouseDetailDO.setVendorCode(vendorCode);
        warehouseDetailDO.setUserOcId(user.getDeptId());
        warehouseDetailDO.setUserVendorId(user.getVendorId());
        warehouseDetailDO.setUserVendorCode(user.getVendorCode());
        warehouseDetailDO.setUserId(user.getId());
        warehouseDetailDO.setUserCode(user.getUserCode());
        warehouseDetailDO.setWarehouseDate(warehouseDate);
        warehouseDetailDO.setSalaryDate(warehouseDate);
        warehouseDetailDO.setAttendanceType(dayType);
        warehouseDetailDO.setEmployeeType(user.getEmployeeType());
        warehouseDetailDO.setWarehouseStatus(Objects.equals(WarehouseTypeEnum.IN.getCode(), warehouseType) ? WarehouseStatusEnum.WAIT_OUT.getCode() : WarehouseStatusEnum.OUT.getCode());
        warehouseDetailDO.setConfirmStatus(convertConfirmStatus(param.getOcId(), user));
        warehouseDetailDO.setEmploymentForm(user.getEmployeeForm());
        warehouseDetailDO.setPunchStatus(PunchStatusEnum.ABNORMAL_PUNCH.getCode());
        if (Objects.nonNull(warehouseAttendanceConfigDO)) {
            warehouseDetailDO.setAttendanceConfigId(warehouseAttendanceConfigDO.getId());
        }
        BaseDOUtil.fillDOInsert(warehouseDetailDO);

        if (Objects.isNull(param.getClasses())) {
            warehouseDetailDO.setAttendanceStatus(WarehouseAttendanceStatusEnum.PENDING_SHIFT_CONFIG.getCode());
        } else {
            warehouseDetailDO.setClassesId(param.getClasses().getClassId());
            warehouseDetailDO.setClassesType(param.getClasses().getClassType());
            warehouseDetailDO.setClassesName(param.getClasses().getClassName());
            warehouseDetailDO.setLegalWorkingHours(param.getClasses().getLegalWorkingHours());
            warehouseDetailDO.setRequiredAttendanceTime(param.getClasses().getAttendanceHours());
        }
        return warehouseDetailDO;
    }

    private Integer convertConfirmStatus(Long ocId, UserInfoDO userInfo) {
        if (!Objects.equals(EmploymentTypeEnum.OS_FIXED_SALARY.getCode(), userInfo.getEmployeeType())) {
            return WarehouseClassConfirmStatusEnum.INIT.getCode();
        }
        List<String> ocIdList = Arrays.stream(attendanceProperties.getVendor().getOcId().split(BusinessConstant.DEFAULT_DELIMITER)).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(ocIdList) && ocIdList.contains(ocId.toString())) {
            return WarehouseClassConfirmStatusEnum.UNCONFIRMED.getCode();
        }
        return WarehouseClassConfirmStatusEnum.NOT_CONFIG.getCode();
    }

    private WarehouseRecordDO convertWarehouseRecord(InOrOutParam param,
                                                     Long vendorId,
                                                     String vendorCode,
                                                     UserInfoDO user,
                                                     EntOcApiDTO oc,
                                                     Long faceRecordId,
                                                     Date warehouseDate,
                                                     Long warehouseDetailId) {
        WarehouseRecordDO inRecord = new WarehouseRecordDO();
        inRecord.setId(defaultIdWorker.nextId());
        inRecord.setWarehouseDetailId(warehouseDetailId);
        inRecord.setCountry(oc.getCountry());
        inRecord.setCity(oc.getCity());
        inRecord.setOcId(param.getOcId());
        inRecord.setUserOcId(user.getDeptId());
        inRecord.setVendorId(vendorId);
        inRecord.setVendorCode(vendorCode);
        inRecord.setUserVendorId(user.getVendorId());
        inRecord.setUserVendorCode(user.getVendorCode());
        inRecord.setUserId(user.getId());
        inRecord.setUserCode(user.getUserCode());
        inRecord.setRecordType(WarehouseTypeEnum.IN.getCode());
        inRecord.setWarehouseDate(warehouseDate);
        inRecord.setWarehouseTime(param.getAttendanceTime());
        inRecord.setFaceRecordId(faceRecordId);
        inRecord.setOcLongitude(param.getLongitude());
        inRecord.setOcLatitude(param.getLatitude());
        inRecord.setDistance(calculateDistance(oc.getLongitude(), oc.getLatitude(), param.getLongitude(), param.getLatitude()));
        BaseDOUtil.fillDOInsert(inRecord);
        return inRecord;
    }

    private EmployeePunchRecordDO convertPunchRecord(Long ocId,
                                                     Date attendanceTime,
                                                     Long classId,
                                                     UserInfoDO user,
                                                     EntOcApiDTO oc,
                                                     String dayId) {
        EmployeePunchRecordDO punchRecordDO = new EmployeePunchRecordDO();
        punchRecordDO.setCountry(oc.getCountry());
        punchRecordDO.setDeptId(ocId);
        punchRecordDO.setOcCode(oc.getOcCode());
        punchRecordDO.setOcName(oc.getOcName());
        punchRecordDO.setUserCode(user.getUserCode());
        punchRecordDO.setEmployeeType(user.getEmployeeType());
        punchRecordDO.setDayId(dayId);
        punchRecordDO.setPunchTime(attendanceTime);
        punchRecordDO.setSourceType(SourceTypeEnum.WPM.name());
        punchRecordDO.setPunchCardType(PunchCardTypeEnum.WPM_FACE.name());
        punchRecordDO.setLongitude(BigDecimal.ZERO);
        punchRecordDO.setLatitude(BigDecimal.ZERO);
        if (Objects.nonNull(classId)) {
            punchRecordDO.setClassId(classId);
        }
        return punchRecordDO;
    }

    /**
     * 生成排班计划
     *
     * @param classes        班次信息
     * @param attendanceTime 考勤时间
     * @param dayId          考勤日
     * @param userId         用户id
     */
    private void generateSchedulingPlan(ClassesParam classes, Date attendanceTime, Long dayId, Long userId) {
        //是否已绑定过当前班次
        List<PunchClassConfigDTO> deptClassConfigList = punchClassConfigManage.selectUserClassConfigList(userId, ClassNatureEnum.MULTIPLE_CLASS.name())
                .stream()
                .filter(config -> Objects.equals(config.getId(), classes.getClassId()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(deptClassConfigList)) {
            Date date = DateUtil.beginOfDay(attendanceTime);
            PunchClassConfigRangeDO classConfigRangeDO = new PunchClassConfigRangeDO();
            classConfigRangeDO.setId(defaultIdWorker.nextId());
            classConfigRangeDO.setBizId(userId);
            classConfigRangeDO.setRangeType(RuleRangeTypeEnum.DEPT.getCode());
            classConfigRangeDO.setRuleConfigId(classes.getClassId());
            classConfigRangeDO.setRuleConfigNo(classes.getClassNo());
            classConfigRangeDO.setIsLatest(BusinessConstant.Y);
            classConfigRangeDO.setStatus(StatusEnum.ACTIVE.getCode());
            classConfigRangeDO.setEffectTime(date);
            classConfigRangeDO.setExpireTime(BusinessConstant.DEFAULT_END_TIME);
            classConfigRangeDO.setEffectTimestamp(date.getTime());
            classConfigRangeDO.setExpireTimestamp(BusinessConstant.DEFAULT_END_TIMESTAMP);
            classConfigRangeDO.setRemark("WPM加入部门适用范围");
            BaseDOUtil.fillDOInsertByUsrOrSystem(classConfigRangeDO);
            punchClassConfigRangeDao.save(classConfigRangeDO);
        }

        UserShiftConfigAddCommand addCommand = new UserShiftConfigAddCommand();
        addCommand.setUserId(userId);
        DaysConfigCommand daysConfigCommand = new DaysConfigCommand();
        daysConfigCommand.setDayId(Objects.isNull(dayId) ? DateHelper.getDayId(attendanceTime) : dayId);
        daysConfigCommand.setDate(DateHelper.transferDayIdToDate(daysConfigCommand.getDayId()));
        daysConfigCommand.setClassId(classes.getClassId());
        daysConfigCommand.setClassName(classes.getClassName());
        daysConfigCommand.setDayShiftRule(classes.getDayPunchType());
        addCommand.setDaysConfigParamList(Collections.singletonList(daysConfigCommand));
        log.info("生成排班计划入参: {}", JSON.toJSONString(addCommand));
        userShiftService.addShift(addCommand);
    }

    /**
     * 查询指定日期日历出勤类型
     *
     * @param attendanceTime 出勤时间
     * @param user           用户
     * @return 出勤类型
     */
    private String getCurrentDayType(Date attendanceTime, UserInfoDO user) {
        WarehouseAttendanceConfigQuery query = new WarehouseAttendanceConfigQuery();
        query.setUserId(user.getId());
        query.setLocationCountry(user.getLocationCountry());
        query.setNow(attendanceTime);
        return calendarConfigService.selectAttendanceDayType(query);
    }

    private WarehouseRecordDO convertWarehouseRecord(OutParam param,
                                                     EntOcApiDTO oc,
                                                     Long vendorId,
                                                     String vendorCode,
                                                     UserInfoDO user,
                                                     WarehouseFaceRecordDO faceRecord,
                                                     Date warehouseDate,
                                                     WarehouseDetailDO warehouseDetailDO,
                                                     Long warehouseDetailId) {
        String country = oc.getCountry();
        String city = oc.getCity();
        Long ocId = param.getOcId();
        if (Objects.equals(EmploymentTypeEnum.OS_FIXED_SALARY.getCode(), user.getEmployeeType()) && Objects.nonNull(warehouseDetailDO)) {
            country = warehouseDetailDO.getCountry();
            city = warehouseDetailDO.getCity();
            ocId = warehouseDetailDO.getOcId();
            vendorId = warehouseDetailDO.getVendorId();
            vendorCode = warehouseDetailDO.getVendorCode();
        }
        WarehouseRecordDO outRecord = new WarehouseRecordDO();
        outRecord.setId(defaultIdWorker.nextId());
        outRecord.setWarehouseDetailId(warehouseDetailId);
        outRecord.setCountry(country);
        outRecord.setCity(city);
        outRecord.setOcId(ocId);
        outRecord.setUserOcId(user.getDeptId());
        outRecord.setVendorId(vendorId);
        outRecord.setVendorCode(vendorCode);
        outRecord.setUserVendorId(user.getVendorId());
        outRecord.setUserVendorCode(user.getVendorCode());
        outRecord.setUserId(user.getId());
        outRecord.setUserCode(user.getUserCode());
        outRecord.setRecordType(WarehouseTypeEnum.OUT.getCode());
        outRecord.setWarehouseDate(warehouseDate);
        outRecord.setWarehouseTime(param.getAttendanceTime());
        outRecord.setFaceRecordId(Objects.nonNull(faceRecord) ? faceRecord.getId() : null);
        outRecord.setOcLongitude(param.getLongitude());
        outRecord.setOcLatitude(param.getLatitude());
        outRecord.setDistance(calculateDistance(oc.getLongitude(), oc.getLatitude(), param.getLongitude(), param.getLatitude()));
        BaseDOUtil.fillDOInsert(outRecord);
        return outRecord;
    }

    /**
     * 出仓检测
     *
     * @param param 出仓入参
     */
    private OutVO checkOut(List<WarehouseDetailDO> warehouseDetailList,
                           OutParam param,
                           UserInfoDO user,
                           Long vendorId,
                           String vendorCode) {
        //判断是否是黑名单用户
        RpcResult<BlacklistInfoDTO> result = blacklistApi.getBlacklistInfo(user.getUserCode());
        if (result.isSuccess() && Objects.nonNull(result.getResult()) && BlacklistBanStatusEnum.getBanStatusList().contains(result.getResult().getBanStatus())) {
            addBlackListRecord(param, user, result, WarehouseBlackTypeEnum.OUT);
            throw BusinessLogicException.getException(ErrorCodeEnum.BLACKLIST_USER_CANNOT_ENTER, result.getResult().getReason());
        }

        OutVO outVO = new OutVO();
        //未入仓直接出仓
        if (CollectionUtils.isEmpty(warehouseDetailList)) {
            if (Objects.isNull(param.getClasses())) {
                log.info("无班次员工{},必须先入仓再出仓", user.getUserCode());
                throw BusinessException.get(ErrorCodeEnum.FACE_IN_FIRST.getCode(), I18nUtils.getMessage(ErrorCodeEnum.FACE_IN_FIRST.getDesc()));
            }
            return outVO;
        }

        if (param.getUpdateInVendor()) {
            return outVO;
        }

        //匹配网点或供应商
        Optional<WarehouseDetailDO> allSameOptional = warehouseDetailList
                .stream()
                .filter(warehouse -> Objects.equals(warehouse.getOcId(), param.getOcId()) && Objects.equals(warehouse.getVendorCode(), vendorCode))
                .findFirst();
        if (allSameOptional.isPresent()) {
            WarehouseDetailDO warehouseDetailDO = allSameOptional.get();
            //自有员工考勤日限制上多班次
            if (Lists.newArrayList(WarehouseAttendanceStatusEnum.NORMAL.getCode(), WarehouseAttendanceStatusEnum.ABNORMAL.getCode()).contains(warehouseDetailDO.getAttendanceStatus())
                    && !Objects.equals(EmploymentTypeEnum.OS_FIXED_SALARY.getCode(), user.getEmployeeType())
                    && !Objects.equals(warehouseDetailDO.getClassesId(), param.getClasses().getClassId())) {
                throw BusinessException.get(ErrorCodeEnum.ALREADY_WORKING_OUT_THE_WAREHOSUE.getCode(), I18nUtils.getMessage(ErrorCodeEnum.ALREADY_WORKING_OUT_THE_WAREHOSUE.getDesc()));
            }
            return outVO;
        }

        List<WarehouseDetailDO> anySameList = warehouseDetailList
                .stream()
                .filter(warehouse -> Objects.equals(warehouse.getOcId(), param.getOcId()) || Objects.equals(warehouse.getVendorCode(), vendorCode))
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(anySameList)) {
            WarehouseDetailDO warehouseDetailDO = warehouseDetailList.get(0);
            return convertOutVO(param, vendorId, vendorCode, Boolean.TRUE, Boolean.TRUE, warehouseDetailDO);
        }

        Optional<WarehouseDetailDO> vendorDifferentOptional = anySameList.stream().filter(warehouse -> Objects.equals(warehouse.getOcId(), param.getOcId()) && !Objects.equals(warehouse.getVendorCode(), vendorCode)).findFirst();
        if (vendorDifferentOptional.isPresent() && !Objects.equals(CountryCodeEnum.BRA.getCode(), vendorDifferentOptional.get().getCountry())) {
            return convertOutVO(param, vendorId, vendorCode, Boolean.FALSE, Boolean.TRUE, vendorDifferentOptional.get());
        }

        Optional<WarehouseDetailDO> ocDifferentOptional = anySameList.stream().filter(warehouse -> !Objects.equals(warehouse.getOcId(), param.getOcId()) && Objects.equals(warehouse.getVendorCode(), vendorCode)).findFirst();
        return ocDifferentOptional.map(warehouseDetailDO -> convertOutVO(param, vendorId, vendorCode, Boolean.TRUE, Boolean.FALSE, warehouseDetailDO)).orElse(outVO);
    }

    private void addBlackListRecord(InOrOutParam param, UserInfoDO user, RpcResult<BlacklistInfoDTO> result, WarehouseBlackTypeEnum type) {
        try {
            WarehouseBlackListDO blackListDO = new WarehouseBlackListDO();
            blackListDO.setId(defaultIdWorker.nextId());
            blackListDO.setUserId(user.getId());
            blackListDO.setUserCode(user.getUserCode());
            blackListDO.setEmployeeType(user.getEmployeeType());
            blackListDO.setOcId(param.getOcId());
            if (StringUtils.isNotBlank(param.getVendorCode())) {
                blackListDO.setVendorCode(param.getVendorCode());
            }
            if (param.getClasses() != null) {
                blackListDO.setClassesId(param.getClasses().getClassId());
            }
            blackListDO.setWarehouseDate(DateUtil.parseDate(DateUtil.formatDate(param.getWarehouseDate())));
            blackListDO.setType(type.getCode());
            blackListDO.setReason(result.getResult().getReason());
            warehouseBlackListDao.save(blackListDO);
        } catch (Exception ex) {
            log.error("checkIn 添加黑名单记录失败, param: {}", JSONObject.toJSONString(param), ex);
        }
    }

    @NotNull
    private OutVO convertOutVO(OutParam param,
                               Long vendorId,
                               String vendorCode,
                               Boolean ocChange,
                               Boolean vendorChange,
                               WarehouseDetailDO warehouseDetailDO) {
        OutVO outVO = new OutVO();
        Map<String, String> vendorMap = warehouseSupplierService.getSupplierByCodes(Lists.newArrayList(warehouseDetailDO.getVendorCode(), vendorCode));
        Map<Long, String> ocMap = getOcMap(Lists.newArrayList(warehouseDetailDO.getOcId(), param.getOcId()));
        outVO.setInOcId(warehouseDetailDO.getOcId());
        outVO.setInOcName(ocMap.get(warehouseDetailDO.getOcId()));
        outVO.setOutOcId(param.getOcId());
        outVO.setOutOcName(ocMap.get(param.getOcId()));
        outVO.setInVendorId(warehouseDetailDO.getVendorId());
        outVO.setInVendorCode(warehouseDetailDO.getVendorCode());
        outVO.setInVendorName(vendorMap.get(warehouseDetailDO.getVendorCode()));
        outVO.setOutVendorId(vendorId);
        outVO.setOutVendorCode(vendorCode);
        outVO.setOutVendorName(vendorMap.get(vendorCode));
        outVO.setCheckOutResult(Boolean.FALSE);
        outVO.setOcChange(ocChange);
        outVO.setVendorChange(vendorChange);
        return outVO;
    }

    private UserInfoDO getUserForInOrOut(Long userId) {
        UserInfoDO user = userInfoDao.getById(userId);
        if (Objects.isNull(user)) {
            throw BusinessException.get(ErrorCodeEnum.ACCOUNT_NOT_EXITS.getCode(), I18nUtils.getMessage(ErrorCodeEnum.ACCOUNT_NOT_EXITS.getDesc()));
        }
        return user;
    }

    private BigDecimal calculateDistance(BigDecimal longitude1, BigDecimal latitude1, BigDecimal longitude2, BigDecimal latitude2) {
        if (Objects.isNull(longitude1) || Objects.isNull(latitude1) || Objects.isNull(longitude2) || Objects.isNull(latitude2)) {
            return null;
        }
        double distance = DistanceCalculatorUtils.getDistance(longitude1.doubleValue(), latitude1.doubleValue(), longitude2.doubleValue(), latitude2.doubleValue());
        BigDecimal bigDecimal = new BigDecimal(String.valueOf(distance));
        return bigDecimal.setScale(2, RoundingMode.HALF_UP);
    }

    private static void setPcsStatus(WarehouseDetailDO warehouseDetail) {
        if (!Objects.equals(EmploymentTypeEnum.OS_FIXED_SALARY.getCode(), warehouseDetail.getEmployeeType())) {
            return;
        }
        if (Objects.isNull(warehouseDetail.getActualAttendanceTime())) {
            warehouseDetail.setPcsStatus(WarehousePcsStatusEnum.ABNORMAL.getCode());
        }
        if (warehouseDetail.getActualAttendanceTime().compareTo(BigDecimal.ZERO) > 0) {
            warehouseDetail.setPcsStatus(WarehousePcsStatusEnum.NORMAL.getCode());
        } else {
            warehouseDetail.setPcsStatus(WarehousePcsStatusEnum.ABNORMAL.getCode());
        }
    }

    private void checkWarehouseDate(Date warehouseDate, Date attendanceTime, Long classId, Integer warehouseType) {
        if (Objects.isNull(classId)) {
            return;
        }
        Long dayId = DateHelper.getDayId(warehouseDate);
        List<PunchClassItemConfigDO> classItemConfigDOList = punchClassItemConfigDao.selectLatestByClassIds(Collections.singletonList(classId));
        PunchClassItemConfigDO punchClassItemConfigDO = classItemConfigDOList.get(0);
        DayPunchTimeDTO dayPunchTime = punchClassConfigQueryService.getUserPunchClassItemDayTime(dayId, punchClassItemConfigDO.getId(), classItemConfigDOList);
        log.info("checkWarehouseDate classId : {} dayPunchTimeDTO : {}", classId, dayPunchTime);
        if (ObjectUtil.isNull(dayPunchTime) || dayPunchTime.getDayPunchStartTime().compareTo(dayPunchTime.getDayPunchEndTime()) > -1) {
            log.info("checkWarehouseDate dayPunchTimeDTO data error");
        }
        if (attendanceTime.compareTo(dayPunchTime.getDayPunchStartTime()) < 0) {
            throw BusinessException.get(ErrorCodeEnum.BEFORE_CLASS_EARLIEST_PUNCH_IN_TIME.getCode(), I18nUtils.getMessage(ErrorCodeEnum.BEFORE_CLASS_EARLIEST_PUNCH_IN_TIME.getDesc()));
        }

        if (Objects.equals(WarehouseTypeEnum.OUT.getCode(), warehouseType)) {
            Date earliestPunchInTime = dayPunchTime.getDayPunchStartTime();
            //上班时间早于最早打卡时间，跨天
            Date punchInTime;
            if (punchClassItemConfigDO.getPunchInTime().before(punchClassItemConfigDO.getEarliestPunchInTime())) {
                punchInTime = DateHelper.concatDateAndTime(DateHelper.formatYYYYMMDD(DateHelper.pushDate(earliestPunchInTime, 1)), DateHelper.formatHHMMSS(punchClassItemConfigDO.getPunchInTime()));
            } else {
                punchInTime = DateHelper.concatDateAndTime(DateHelper.formatYYYYMMDD(earliestPunchInTime), DateHelper.formatHHMMSS(punchClassItemConfigDO.getPunchInTime()));
            }

            //最晚上班打卡时间
            Date latestPunchInTime;
            if (punchClassItemConfigDO.getLatestPunchInTime().before(punchClassItemConfigDO.getPunchInTime())) {
                latestPunchInTime = DateHelper.concatDateAndTime(DateHelper.formatYYYYMMDD(DateHelper.pushDate(punchInTime, 1)), DateHelper.formatHHMMSS(punchClassItemConfigDO.getLatestPunchInTime()));
            } else {
                latestPunchInTime = DateHelper.concatDateAndTime(DateHelper.formatYYYYMMDD(punchInTime), DateHelper.formatHHMMSS(punchClassItemConfigDO.getLatestPunchInTime()));
            }
            if (attendanceTime.compareTo(earliestPunchInTime) > -1 && attendanceTime.compareTo(latestPunchInTime) < 1) {
                log.info("上班时间内未入仓直接打出仓卡");
                throw BusinessException.get(ErrorCodeEnum.FACE_IN_FIRST.getCode(), I18nUtils.getMessage(ErrorCodeEnum.FACE_IN_FIRST.getDesc()));
            }
        }
    }

    /**
     * 巴西劳务派遣供应商校验
     */
    private void braInVendorCheck(InOrOutParam param) {
        AttendanceDept entDeptDO = deptService.getByDeptId(param.getOcId());
        if (Objects.isNull(entDeptDO)) {
            throw BusinessException.get(ErrorCodeEnum.STATION_NOT_EXITS.getCode(), I18nUtils.getMessage(ErrorCodeEnum.STATION_NOT_EXITS.getDesc()));
        }
        if (!StringUtils.equalsAnyIgnoreCase(entDeptDO.getCountry(), CountryCodeEnum.BRA.getCode())) {
            return;
        }
        if (!BusinessConstant.LABOR_DISPATCH.equals(param.getEmployeeType())) {
            return;
        }
        List<UserInfoDO> userInfoDOList = userInfoDao.getByUserIds(param.getUserIdList());
        Map<Long, UserInfoDO> userMap = userInfoDOList.stream().collect(Collectors.toMap(UserInfoDO::getId, Function.identity()));

        List<String> vendorCodeList = userInfoDOList.stream()
                .map(UserInfoDO::getVendorCode)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        vendorCodeList.add(param.getVendorCode());
        Map<String, String> vendorNameMap = warehouseSupplierService.getSupplierByCodes(vendorCodeList);

        for (Long userId : param.getUserIdList()) {
            UserInfoDO existUserInfo = userMap.get(userId);
            if (Objects.isNull(existUserInfo)) {
                throw BusinessException.get(ErrorCodeEnum.ACCOUNT_NOT_EXITS.getCode(), I18nUtils.getMessage(ErrorCodeEnum.ACCOUNT_NOT_EXITS.getDesc()));
            }
            if (Objects.equals(param.getVendorCode(), existUserInfo.getVendorCode())) {
                continue;
            }
            String userVendorName = vendorNameMap.get(existUserInfo.getVendorCode());
            String warehouseVendorName = vendorNameMap.get(param.getVendorCode());
            throw BusinessLogicException.getException(ErrorCodeEnum.USER_VENDOR_NOT_SAME_AS_WAREHOUSE_VENDOR, userVendorName, warehouseVendorName);
        }
    }


    private Integer abnormalCount(List<WarehouseDetailAbnormalDO> abnormalList, String abnormalType) {
        Map<Integer, List<WarehouseDetailAbnormalDO>> warehouseMap = abnormalList.stream().filter(e -> abnormalType.equals(e.getAbnormalType())).collect(Collectors.groupingBy(WarehouseDetailAbnormalDO::getProcessed));
        int result = warehouseMap.getOrDefault(WarehouseAbnormalStatusEnum.PENDING_PROCESSING.getCode(), Collections.emptyList()).size();
        List<WarehouseDetailAbnormalDO> processedList = warehouseMap.get(WarehouseAbnormalStatusEnum.PROCESSED.getCode());
        if (CollectionUtils.isEmpty(processedList)) {
            return result;
        }
        List<Long> abnormalIds = processedList.stream().map(WarehouseDetailAbnormalDO::getAbnormalId).collect(Collectors.toList());
        List<EmployeeAbnormalOperationRecordDO> employeeAbnormalOperationRecordDOS = abnormalOperationRecordDao.selectByAbnormalList(abnormalIds);
        List<Long> confirmAbnormalIds = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(employeeAbnormalOperationRecordDOS)) {
            List<EmployeeAbnormalOperationRecordDO> abnormalOperationRecordList = employeeAbnormalOperationRecordDOS
                    .stream()
                    .filter(abnormalOperation -> AbnormalOperationTypeEnum.ABNORMAL_CONFIRM.getCode().equals(abnormalOperation.getOperationType())).collect(Collectors.toList());
            confirmAbnormalIds = abnormalOperationRecordList.stream().map(EmployeeAbnormalOperationRecordDO::getAbnormalId).collect(Collectors.toList());
            result += abnormalOperationRecordList.size();
        }

        if (Objects.equals(AttendanceAbnormalTypeEnum.ABNORMAL_DURATION.getCode(), abnormalType)) {
            List<Long> finalConfirmAbnormalIds = confirmAbnormalIds;
            long abnormalDurationCount = processedList.stream().filter(abnormal -> Objects.equals(AttendanceAbnormalTypeEnum.ABNORMAL_DURATION.getCode(), abnormal.getAbnormalType())
                    && !finalConfirmAbnormalIds.contains(abnormal.getAbnormalId())).count();
            result += abnormalDurationCount;
        }

        return result;
    }

    private Integer abnormalDurationCount(List<WarehouseDetailAbnormalDO> abnormalList, Set<Long> warehouseIds) {
        String abnormalType = AttendanceAbnormalTypeEnum.ABNORMAL_DURATION.getCode();
        Map<Integer, List<WarehouseDetailAbnormalDO>> warehouseMap = abnormalList.stream().filter(e -> abnormalType.equals(e.getAbnormalType())).collect(Collectors.groupingBy(WarehouseDetailAbnormalDO::getProcessed));
        List<WarehouseDetailAbnormalDO> processingAbnormalList = warehouseMap.getOrDefault(WarehouseAbnormalStatusEnum.PENDING_PROCESSING.getCode(), Collections.emptyList());
        int result = processingAbnormalList.size();
        Set<Long> warehouseIdList = processingAbnormalList.stream().map(WarehouseDetailAbnormalDO::getWarehouseDetailId).collect(Collectors.toSet());
        if (CollectionUtils.isNotEmpty(warehouseIdList)) {
            warehouseIds.addAll(warehouseIdList);
        }
        List<WarehouseDetailAbnormalDO> processedList = warehouseMap.get(WarehouseAbnormalStatusEnum.PROCESSED.getCode());
        if (CollectionUtils.isEmpty(processedList)) {
            return result;
        }
        Map<Long, List<WarehouseDetailAbnormalDO>> processedMap = processedList.stream().collect(Collectors.groupingBy(WarehouseDetailAbnormalDO::getWarehouseDetailId));
        for (Long warehouseId : processedMap.keySet()) {
            List<WarehouseDetailAbnormalDO> warehouseDetailAbnormalDOS = processedMap.get(warehouseId);

            List<Long> abnormalIds = warehouseDetailAbnormalDOS.stream().map(WarehouseDetailAbnormalDO::getAbnormalId).collect(Collectors.toList());
            List<EmployeeAbnormalOperationRecordDO> employeeAbnormalOperationRecordDOS = abnormalOperationRecordDao.selectByAbnormalList(abnormalIds);
            List<Long> confirmAbnormalIds = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(employeeAbnormalOperationRecordDOS)) {
                List<EmployeeAbnormalOperationRecordDO> abnormalOperationRecordList = employeeAbnormalOperationRecordDOS
                        .stream()
                        .filter(abnormalOperation -> AbnormalOperationTypeEnum.ABNORMAL_CONFIRM.getCode().equals(abnormalOperation.getOperationType())).collect(Collectors.toList());
                confirmAbnormalIds = abnormalOperationRecordList.stream().map(EmployeeAbnormalOperationRecordDO::getAbnormalId).collect(Collectors.toList());
                result += abnormalOperationRecordList.size();
                warehouseIds.add(warehouseId);
            }

            if (Objects.equals(AttendanceAbnormalTypeEnum.ABNORMAL_DURATION.getCode(), abnormalType)) {
                List<Long> finalConfirmAbnormalIds = confirmAbnormalIds;
                int abnormalDurationCount = Math.toIntExact(warehouseDetailAbnormalDOS.stream().filter(abnormal -> Objects.equals(AttendanceAbnormalTypeEnum.ABNORMAL_DURATION.getCode(), abnormal.getAbnormalType())
                        && !finalConfirmAbnormalIds.contains(abnormal.getAbnormalId())).count());
                result += abnormalDurationCount;
                if (abnormalDurationCount > 0) {
                    warehouseIds.add(warehouseId);
                }
            }
        }

        return result;
    }

    private void checkPunchTime(String country, Date attendanceTime) {
        if (attendanceProperties.getVendor().getIsCheckPunchTimeOn()) {
            //临时方案颗粒度粗暂时使用
            CountryDTO countryDTO = countryService.queryCountry(country);
            Date date = CommonUtil.convertDateByTimeZonePlus(countryDTO.getTimeZone(), new Date());
            long timeInterval = DateUtil.between(date, attendanceTime, DateUnit.MINUTE);
            if (Objects.equals(CountryCodeEnum.MEX.getCode(), country) && (timeInterval > 120)) {
                throw BusinessException.get(ErrorCodeEnum.SYSTEM_TIME_NOT_MATCH_CURRENT_TIME.getCode(), I18nUtils.getMessage(ErrorCodeEnum.SYSTEM_TIME_NOT_MATCH_CURRENT_TIME.getDesc()));
            } else {
                if (timeInterval > 60) {
                    throw BusinessException.get(ErrorCodeEnum.SYSTEM_TIME_NOT_MATCH_CURRENT_TIME.getCode(), I18nUtils.getMessage(ErrorCodeEnum.SYSTEM_TIME_NOT_MATCH_CURRENT_TIME.getDesc()));
                }
            }
        }
    }

    private void supplierClassConfirmRollbackHandler(List<WarehouseDetailDO> warehouseDetailList,
                                                     List<EmployeeAbnormalAttendanceDO> updateAbnormalAttendanceList,
                                                     List<EmployeeAbnormalOperationRecordDO> deleteAbnormalOperationRecordList,
                                                     List<WarehouseVendorClassesConfirmDO> udpateVendorClassesConfirmDOList,
                                                     List<Long> confirmAbnormalIds) {

        Map<String, List<WarehouseDetailDO>> warehouseGroupMap = warehouseDetailList
                .stream()
                .filter(warehouse -> Objects.equals(WarehouseClassConfirmStatusEnum.CONFIRMED.getCode(), warehouse.getConfirmStatus()))
                .collect(Collectors.groupingBy(warehouse -> DateUtil.formatDate(warehouse.getWarehouseDate()) + "_" + warehouse.getOcId() + "_" + warehouse.getVendorCode() + "_" + warehouse.getClassesId()));


        for (String warehouseKey : warehouseGroupMap.keySet()) {
            List<WarehouseDetailDO> warehouseDetailDOS = warehouseGroupMap.get(warehouseKey);
            String[] keySplit = warehouseKey.split("_");
            Date warehouseDate = DateUtil.parseDate(keySplit[0]);
            Long ocId = Long.valueOf(keySplit[1]);
            String vendorCode = keySplit[2];
            Long classId = Long.valueOf(keySplit[3]);
            WarehouseVendorClassesConfirmDO vendorClassesConfirmDO = warehouseVendorClassesConfirmDao.selectConfirmResultBy(ocId, vendorCode, classId, warehouseDate);
            if (Objects.isNull(vendorClassesConfirmDO)) {
                continue;
            }

            //异常表状态回退待处理 异常操作记录逻辑删除
            List<WarehouseDetailAbnormalDO> abnormalList = warehouseDetailAbnormalDao.selectByWarehouseDetailIds(warehouseDetailDOS.stream().map(WarehouseDetailDO::getId).collect(Collectors.toList()));
            List<Long> abnormalIds = abnormalList.stream().map(WarehouseDetailAbnormalDO::getAbnormalId).collect(Collectors.toList());
            List<EmployeeAbnormalAttendanceDO> employeeAbnormalAttendanceDOList = abnormalAttendanceDao.selectAbnormalByIdList(abnormalIds);

            //过滤得到供应商确认异常的操作记录
            List<EmployeeAbnormalOperationRecordDO> abnormalOperationRecordDOList = abnormalOperationRecordDao.selectByAbnormalList(abnormalIds)
                    .stream()
                    .filter(abnormalRecord -> Objects.equals(BusinessConstant.SUPPLIER, abnormalRecord.getCreateUserCode()))
                    .collect(Collectors.toList());
            abnormalOperationRecordDOList.forEach(abnormal -> {
                abnormal.setIsDelete(IsDeleteEnum.YES.getCode());
                BaseDOUtil.fillDOUpdateByUserOrSystem(abnormal);
            });
            deleteAbnormalOperationRecordList.addAll(abnormalOperationRecordDOList);

            List<Long> confirmAbnormalIdList = abnormalOperationRecordDOList.stream().map(EmployeeAbnormalOperationRecordDO::getAbnormalId).distinct().collect(Collectors.toList());
            List<EmployeeAbnormalAttendanceDO> confirmAbnormalAttendanceDOList = employeeAbnormalAttendanceDOList.stream().filter(abnormal -> confirmAbnormalIdList.contains(abnormal.getId())).collect(Collectors.toList());
            confirmAbnormalAttendanceDOList.forEach(abnormal -> {
                abnormal.setStatus(AbnormalAttendanceStatusEnum.UN_PROCESSED.getCode());
                BaseDOUtil.fillDOUpdateByUserOrSystem(abnormal);
            });
            updateAbnormalAttendanceList.addAll(confirmAbnormalAttendanceDOList);

            confirmAbnormalIds.addAll(confirmAbnormalIdList);

            Integer outWithoutPunchCount;
            Integer latePunchCount = 0;
            Integer leaveEarlyPunchCount = 0;
            Integer abnormalDurationCount = 0;
            Integer inWithoutPunchCount = abnormalCount(abnormalList, AttendanceAbnormalTypeEnum.BEFORE_OFFICE_LACK.getCode());
            List<WarehouseAttendanceConfigDO> warehouseAttendanceConfigDOList = warehouseAttendanceConfigDao.selectByDateRange(DateUtil.endOfDay(warehouseDate), ocId);
            if (CollectionUtils.isNotEmpty(warehouseAttendanceConfigDOList) && Objects.equals(BusinessConstant.Y, warehouseAttendanceConfigDOList.get(0).getIsSegmentedCalculation())) {
                Set<Long> warehouseIds = new HashSet<>();
                abnormalDurationCount = abnormalDurationCount(abnormalList, warehouseIds);
                outWithoutPunchCount = Math.toIntExact(warehouseDetailDOS.stream()
                        .filter(warehouse -> Objects.equals(WarehouseStatusEnum.WAIT_OUT.getCode(), warehouse.getWarehouseStatus())
                                && !warehouseIds.contains(warehouse.getId())).count());
            } else {
                outWithoutPunchCount = Math.toIntExact(warehouseDetailDOS.stream().filter(warehouse -> Objects.equals(WarehouseStatusEnum.WAIT_OUT.getCode(), warehouse.getWarehouseStatus())).count());
                latePunchCount = abnormalCount(abnormalList, AttendanceAbnormalTypeEnum.LATE.getCode());
                leaveEarlyPunchCount = abnormalCount(abnormalList, AttendanceAbnormalTypeEnum.LEAVE_EARLY.getCode());
            }

            // 实际出勤 = 总记录 - 出仓未打卡 - 入仓未打卡
            Integer punchCount = Math.max(warehouseDetailDOS.size() - outWithoutPunchCount - inWithoutPunchCount, 0);

            vendorClassesConfirmDO.setActualAttendanceNum(Math.max(vendorClassesConfirmDO.getActualAttendanceNum() - punchCount, 0));
            vendorClassesConfirmDO.setBeforeOfficeLackNum(Math.max(vendorClassesConfirmDO.getBeforeOfficeLackNum() - inWithoutPunchCount, 0));
            vendorClassesConfirmDO.setAfterOfficeLackNum(Math.max(vendorClassesConfirmDO.getAfterOfficeLackNum() - outWithoutPunchCount, 0));
            vendorClassesConfirmDO.setLateNum(Math.max(vendorClassesConfirmDO.getLateNum() - latePunchCount, 0));
            vendorClassesConfirmDO.setLeaveEarlyNum(Math.max(vendorClassesConfirmDO.getLeaveEarlyNum() - leaveEarlyPunchCount, 0));
            vendorClassesConfirmDO.setAbnormalDurationNum(Math.max(vendorClassesConfirmDO.getAbnormalDurationNum() - abnormalDurationCount, 0));
            if (vendorClassesConfirmDO.getActualAttendanceNum() == 0 && vendorClassesConfirmDO.getBeforeOfficeLackNum() == 0 && vendorClassesConfirmDO.getAfterOfficeLackNum() == 0) {
                vendorClassesConfirmDO.setIsDelete(IsDeleteEnum.YES.getCode());
            }
            BaseDOUtil.fillDOUpdateByUserOrSystem(vendorClassesConfirmDO);
            udpateVendorClassesConfirmDOList.add(vendorClassesConfirmDO);
        }
    }


    private void clearOriginalClassAttendanceData(List<WarehouseDetailDO> warehouseDetailList,
                                                  List<AttendanceEmployeeDetailDO> attendanceEmployeeDetailList,
                                                  List<EmployeeAbnormalAttendanceDO> updateAbnormalAttendanceList,
                                                  List<EmployeeAbnormalOperationRecordDO> deleteAbnormalOperationRecordList) {
        Map<Integer, List<WarehouseDetailDO>> warehouseAttendanceStatusMap = warehouseDetailList
                .stream()
                .filter(warehouse -> Lists.newArrayList(WarehouseAttendanceStatusEnum.ABNORMAL.getCode(), WarehouseAttendanceStatusEnum.NORMAL.getCode()).contains(warehouse.getAttendanceStatus()))
                .collect(Collectors.groupingBy(WarehouseDetailDO::getAttendanceStatus));

        List<WarehouseDetailDO> normalWarehouseList = warehouseAttendanceStatusMap.get(WarehouseAttendanceStatusEnum.NORMAL.getCode());
        if (CollectionUtils.isNotEmpty(normalWarehouseList)) {
            for (WarehouseDetailDO warehouseDetailDO : normalWarehouseList) {
                List<AttendanceEmployeeDetailDO> attendanceEmployeeDetailDOList = attendanceEmployeeDetailDao.getAttendanceEmployeeDetailDO(warehouseDetailDO.getUserId(), DateHelper.getDayId(warehouseDetailDO.getWarehouseDate()))
                        .stream().filter(item -> Objects.isNull(item.getFormId()))
                        .collect(Collectors.toList());
                if (CollectionUtils.isEmpty(attendanceEmployeeDetailDOList)) {
                    continue;
                }
                attendanceEmployeeDetailDOList.forEach(item -> {
                    item.setIsDelete(IsDeleteEnum.YES.getCode());
                    BaseDOUtil.fillDOUpdateByUserOrSystem(item);
                });
                attendanceEmployeeDetailList.addAll(attendanceEmployeeDetailDOList);
            }
        }

        List<WarehouseDetailDO> abnormalWarehouseList = warehouseAttendanceStatusMap.get(WarehouseAttendanceStatusEnum.ABNORMAL.getCode());
        if (CollectionUtils.isNotEmpty(abnormalWarehouseList)) {
            for (WarehouseDetailDO warehouseDetailDO : abnormalWarehouseList) {
                AbnormalAttendanceQuery query = new AbnormalAttendanceQuery();
                query.setUserIds(Collections.singletonList(warehouseDetailDO.getUserId()));
                query.setDayId(DateUtil.format(warehouseDetailDO.getWarehouseDate(), DatePattern.PURE_DATE_PATTERN));
                List<EmployeeAbnormalAttendanceDO> employeeAbnormalAttendanceList = abnormalAttendanceDao.listAbnormal(query);
                if (CollectionUtils.isEmpty(employeeAbnormalAttendanceList)) {
                    continue;
                }
                employeeAbnormalAttendanceList.forEach(employeeAbnormal -> {
                    employeeAbnormal.setIsDelete(IsDeleteEnum.YES.getCode());
                    BaseDOUtil.fillDOUpdateByUserOrSystem(employeeAbnormal);
                });
                updateAbnormalAttendanceList.addAll(employeeAbnormalAttendanceList);
                List<Long> abnormalIds = employeeAbnormalAttendanceList.stream().map(EmployeeAbnormalAttendanceDO::getId).collect(Collectors.toList());
                List<EmployeeAbnormalOperationRecordDO> abnormalOperationRecordDOList = abnormalOperationRecordDao.selectByAbnormalList(abnormalIds);
                if (CollectionUtils.isEmpty(abnormalOperationRecordDOList)) {
                    continue;
                }
                abnormalOperationRecordDOList.forEach(employeeAbnormalRecord -> {
                    employeeAbnormalRecord.setIsDelete(IsDeleteEnum.YES.getCode());
                    BaseDOUtil.fillDOUpdateByUserOrSystem(employeeAbnormalRecord);
                });
                deleteAbnormalOperationRecordList.addAll(abnormalOperationRecordDOList);
            }
        }
    }


}
