package com.imile.attendance.warehouse.vo;

import com.imile.attendance.abnormal.dto.AbnormalOperationRecordDTO;
import com.imile.attendance.abnormal.vo.EmployeeBaseInfoVO;
import com.imile.attendance.annon.WithDict;
import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.rule.vo.PunchClassItemConfigVO;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/1/8
 */
@Data
public class WarehouseAbnormalDetailVO {
    /**
     * 员工基本信息
     */
    private EmployeeBaseInfoVO employeeBaseInfo;

    /**
     * 异常记录操作记录
     */
    private List<AbnormalOperationRecordDTO> abnormalOperationRecordDTOList;

    /**
     * 打卡记录
     */
    private List<WarehouseRecordDTO> warehouseRecordList;

    /**
     * 请假信息
     */
    private List<UserDayFormDTO> userDayFormDTOList;

    /**
     * 考勤异常信息
     */
    private AttendanceAbnormalInfo attendanceAbnormalInfo;


    @Data
    public static class WarehouseRecordDTO {

        private Long id;

        /**
         * 记录类型（1:入仓 2离仓）
         */
        @WithDict(ref = "recordTypeDesc", typeCode = "faceRecordType")
        private Integer recordType;

        /**
         * 记录类型描述
         */
        private String recordTypeDesc;

        /**
         * 出入仓时间
         */
        private Date warehouseTime;
    }

    @Data
    public static class UserDayFormDTO {
        /**
         * 单据id
         */
        private Long formId;

        /**
         * 单据类型
         */
        private String formType;

        /**
         * 单据状态
         */
        private String formStatus;

        /**
         * 请假/外勤 开始时间
         */
        private Date startDate;

        /**
         * 请假/外勤 结束时间
         */
        private Date endDate;
    }

    @Data
    public static class AttendanceAbnormalInfo {
        /**
         * 异常ID
         */
        private Long abnormalId;

        /**
         * 考勤日期
         */
        private Date attendanceDate;

        /**
         * 异常类型
         */
        @WithDict(typeCode = BusinessConstant.SysDictDataTypeConstant.HRMS_ABNORMAL_TYPE, ref = "abnormalTypeDesc")
        private String abnormalType;

        private String abnormalTypeDesc;

        /**
         * 考勤状态
         */
        private String attendanceStatus;

        /**
         * 当前班次 要求出勤时长
         */
        private BigDecimal attendanceHours;

        /**
         * 当前班次 要求法定工作时长
         */
        private BigDecimal legalWorkingHours;

        /**
         * 实际出勤时长 单位:小时 包含休息时间
         */
        private BigDecimal actualAttendanceTime;

        /**
         * 实际工作总时长 单位:小时 不包含休息时间
         */
        private BigDecimal actualWorkingHours;

        /**
         * 操作类型(请假/补卡/外勤/确认异常)
         */
        private String operationType;

        /**
         * bpm审批单ID
         */
        private Long approvalId;

        /**
         * 上下班缺卡为空
         * 时长异常为空
         * 迟到早退展示最近的打卡时间
         * 无排班计划异常展示所有打卡时间
         */
        private List<Date> punchTimeList;

        /**
         * 班次时段信息
         */
        private PunchClassItemConfigVO itemConfigInfo;
    }
}
