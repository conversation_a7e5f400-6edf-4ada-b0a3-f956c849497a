package com.imile.attendance.warehouse;


import com.imile.attendance.infrastructure.repository.warehouse.model.WarehouseDetailDO;
import com.imile.attendance.infrastructure.repository.warehouse.model.WarehousePunchPeriodDO;

import java.util.List;

/**
 * 仓内出勤时长计算服务
 *
 * <AUTHOR>
 * @since 2025/1/23
 */
public interface WarehouseAttendanceDurationCalculateService {

    /**
     * 计算仓内考勤时长
     */
    List<WarehousePunchPeriodDO> calculateAttendanceHours(WarehouseDetailDO warehouseDetailDO, boolean onlyUpdateWarehouseHours);
}
