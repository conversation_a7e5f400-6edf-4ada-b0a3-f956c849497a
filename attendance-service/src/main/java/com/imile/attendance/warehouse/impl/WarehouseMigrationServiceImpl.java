package com.imile.attendance.warehouse.impl;

import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.google.common.collect.Lists;
import com.imile.attendance.infrastructure.repository.hrms.dao.HrmsFaceFeatureDao;
import com.imile.attendance.infrastructure.repository.hrms.dao.HrmsFaceFeatureEncDao;
import com.imile.attendance.infrastructure.repository.hrms.dao.HrmsUserFaceMarkRecordDao;
import com.imile.attendance.infrastructure.repository.hrms.dao.HrmsWarehouseDetailAbnormalDao;
import com.imile.attendance.infrastructure.repository.hrms.dao.HrmsWarehouseDetailDao;
import com.imile.attendance.infrastructure.repository.hrms.dao.HrmsWarehouseDetailOriginalDao;
import com.imile.attendance.infrastructure.repository.hrms.dao.HrmsWarehouseRecordDao;
import com.imile.attendance.infrastructure.repository.hrms.modle.HrmsFaceFeatureDO;
import com.imile.attendance.infrastructure.repository.hrms.modle.HrmsFaceFeatureEncDO;
import com.imile.attendance.infrastructure.repository.hrms.modle.HrmsUserFaceMarkRecordDO;
import com.imile.attendance.infrastructure.repository.hrms.modle.HrmsWarehouseDetailAbnormalDO;
import com.imile.attendance.infrastructure.repository.hrms.modle.HrmsWarehouseDetailDO;
import com.imile.attendance.infrastructure.repository.hrms.modle.HrmsWarehouseRecordDO;
import com.imile.attendance.infrastructure.repository.hrms.query.AbnormalMigrationQuery;
import com.imile.attendance.infrastructure.repository.rule.dto.PunchClassConfigDTO;
import com.imile.attendance.infrastructure.repository.shift.model.UserShiftConfigDO;
import com.imile.attendance.infrastructure.repository.warehouse.dao.FaceFeatureDao;
import com.imile.attendance.infrastructure.repository.warehouse.dao.FaceFeatureEncDao;
import com.imile.attendance.infrastructure.repository.warehouse.dao.UserFaceMarkRecordDao;
import com.imile.attendance.infrastructure.repository.warehouse.dao.WarehouseDetailAbnormalDao;
import com.imile.attendance.infrastructure.repository.warehouse.dao.WarehouseDetailDao;
import com.imile.attendance.infrastructure.repository.warehouse.dao.WarehouseDetailSnapshotDao;
import com.imile.attendance.infrastructure.repository.warehouse.dao.WarehouseRecordDao;
import com.imile.attendance.infrastructure.repository.warehouse.model.FaceFeatureDO;
import com.imile.attendance.infrastructure.repository.warehouse.model.FaceFeatureEncDO;
import com.imile.attendance.infrastructure.repository.warehouse.model.UserFaceMarkRecordDO;
import com.imile.attendance.infrastructure.repository.warehouse.model.WarehouseDetailAbnormalDO;
import com.imile.attendance.infrastructure.repository.warehouse.model.WarehouseDetailDO;
import com.imile.attendance.infrastructure.repository.warehouse.model.WarehouseDetailSnapshotDO;
import com.imile.attendance.infrastructure.repository.warehouse.model.WarehouseRecordDO;
import com.imile.attendance.infrastructure.repository.warehouse.query.FaceFeatureQuery;
import com.imile.attendance.infrastructure.repository.warehouse.query.WarehouseDetailQuery;
import com.imile.attendance.migration.dto.AbnormalSyncDTO;
import com.imile.attendance.rule.PunchClassConfigManage;
import com.imile.attendance.shift.UserShiftConfigManage;
import com.imile.attendance.util.DateHelper;
import com.imile.attendance.warehouse.WarehouseMigrationService;
import com.imile.util.BeanUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2025/6/16
 */
@Slf4j
@Service
public class WarehouseMigrationServiceImpl implements WarehouseMigrationService {

    @Resource
    private HrmsWarehouseDetailDao hrmsWarehouseDetailDao;
    @Resource
    private HrmsWarehouseRecordDao hrmsWarehouseRecordDao;
    @Resource
    private HrmsWarehouseDetailAbnormalDao hrmsWarehouseDetailAbnormalDao;
    @Resource
    private HrmsWarehouseDetailOriginalDao hrmsWarehouseDetailOriginalDao;
    @Resource
    private HrmsFaceFeatureDao hrmsFaceFeatureDao;
    @Resource
    private HrmsFaceFeatureEncDao hrmsFaceFeatureEncDao;
    @Resource
    private HrmsUserFaceMarkRecordDao hrmsUserFaceMarkRecordDao;

    @Resource
    private WarehouseDetailDao warehouseDetailDao;
    @Resource
    private WarehouseRecordDao warehouseRecordDao;
    @Resource
    private WarehouseDetailAbnormalDao warehouseDetailAbnormalDao;
    @Resource
    private WarehouseDetailSnapshotDao warehouseDetailSnapshotDao;
    @Resource
    private PunchClassConfigManage punchClassConfigManage;
    @Resource
    private UserShiftConfigManage userShiftConfigManage;
    @Resource
    private FaceFeatureDao faceFeatureDao;
    @Resource
    private FaceFeatureEncDao faceFeatureEncDao;
    @Resource
    private UserFaceMarkRecordDao userFaceMarkRecordDao;


    @DSTransactional
    @Override
    public void syncNewSystemAbnormalRecord(AbnormalSyncDTO abnormalSyncDTO) {
        //同步打卡记录
        syncEmployeePunchRecordToNewSystem(abnormalSyncDTO);
    }

    @DSTransactional
    @Override
    public void syncNewSystemFaceInfo() {
        log.info("同步人脸库->faceFeature start");
        Long lastId = 0L;
        int currentPage = 0;
        FaceFeatureQuery query = new FaceFeatureQuery();
        int size = 0;
        while (true) {
            query.setLastId(lastId);
            List<HrmsFaceFeatureDO> pageUserInfoList = hrmsFaceFeatureDao.selectByCondition(query);
            // 总记录数
            if (CollectionUtils.isEmpty(pageUserInfoList)) {
                break;
            }
            lastId = pageUserInfoList.get(pageUserInfoList.size() - 1).getId();

            List<FaceFeatureDO> faceFeatureDOList = pageUserInfoList.stream()
                    .map(faceFeature -> BeanUtils.convert(faceFeature, FaceFeatureDO.class))
                    .collect(Collectors.toList());


            size += faceFeatureDOList.size();
            currentPage++;

            faceFeatureDao.saveOrUpdateBatch(faceFeatureDOList);
            log.info("同步人脸库->faceFeature currentPage:{},faceFeature.size:{}", currentPage, size);
        }
        log.info("同步人脸库->faceFeature end");

        log.info("同步人脸库->faceFeatureEnc start");
        List<FaceFeatureEncDO> faceFeatureEncDOList = new ArrayList<>();
        List<HrmsFaceFeatureEncDO> hrmsFaceFeatureEncDOList = hrmsFaceFeatureEncDao.selectAll();
        if (CollectionUtils.isNotEmpty(hrmsFaceFeatureEncDOList)) {
            faceFeatureEncDOList = hrmsFaceFeatureEncDOList.stream()
                    .map(faceFeatureEnc -> BeanUtils.convert(faceFeatureEnc, FaceFeatureEncDO.class))
                    .collect(Collectors.toList());

            log.info("同步人脸库->faceFeatureEnc size:{}", faceFeatureEncDOList.size());
        }

        log.info("同步人脸库->userFaceMarkRecord start");
        List<UserFaceMarkRecordDO> userFaceMarkRecordDOList = new ArrayList<>();
        List<HrmsUserFaceMarkRecordDO> hrmsUserFaceMarkRecordDOList = hrmsUserFaceMarkRecordDao.selectAll();
        if (CollectionUtils.isNotEmpty(hrmsUserFaceMarkRecordDOList)) {
            userFaceMarkRecordDOList = hrmsUserFaceMarkRecordDOList.stream()
                    .map(userFaceMark -> BeanUtils.convert(userFaceMark, UserFaceMarkRecordDO.class))
                    .collect(Collectors.toList());

            log.info("同步人脸库->userFaceMarkRecord size:{}", userFaceMarkRecordDOList.size());
        }
        if (CollectionUtils.isNotEmpty(faceFeatureEncDOList)) {
            faceFeatureEncDao.saveOrUpdateBatch(faceFeatureEncDOList);
        }
        if (CollectionUtils.isNotEmpty(userFaceMarkRecordDOList)) {
            userFaceMarkRecordDao.saveOrUpdateBatch(userFaceMarkRecordDOList);
        }
        log.info("同步人脸库->faceFeatureEnc end");
        log.info("同步人脸库->userFaceMarkRecord end");
    }

    public void syncEmployeePunchRecordToNewSystem(AbnormalSyncDTO abnormalSyncDTO) {
        log.info("同步仓内表->warehouseDetail start");
        Long lastId = 0L;
        int currentPage = 0;
        AbnormalMigrationQuery abnormalMigrationQuery = BeanUtils.convert(abnormalSyncDTO, AbnormalMigrationQuery.class);
        WarehouseDetailQuery query = new WarehouseDetailQuery();
        query.setCountryList(abnormalMigrationQuery.getCountryList());
        if (Objects.nonNull(abnormalSyncDTO.getStartDayId())) {
            query.setStartTime(DateHelper.transferDayIdToDate(abnormalSyncDTO.getStartDayId()));
        }
        if (Objects.nonNull(abnormalSyncDTO.getEndDayId())) {
            query.setEndTime(DateHelper.transferDayIdToDate(abnormalSyncDTO.getEndDayId()));
        }
        if (CollectionUtils.isNotEmpty(abnormalSyncDTO.getUserCodeList())) {
            query.setUserCodeList(abnormalSyncDTO.getUserCodeList());
        }
        if (CollectionUtils.isNotEmpty(abnormalSyncDTO.getDeptIdList())) {
            query.setOcIdList(abnormalSyncDTO.getDeptIdList());
        }
        int size = 0;
        while (true) {
            query.setLastId(lastId);
            List<HrmsWarehouseDetailDO> pageUserInfoList = hrmsWarehouseDetailDao.selectPage(query);
            // 总记录数
            if (CollectionUtils.isEmpty(pageUserInfoList)) {
                break;
            }
            lastId = pageUserInfoList.get(pageUserInfoList.size() - 1).getId();

            List<WarehouseDetailDO> warehouseDetailDOList = pageUserInfoList.stream()
                    .map(warehouseDetail -> BeanUtils.convert(warehouseDetail, WarehouseDetailDO.class))
                    .collect(Collectors.toList());

            List<Long> warehouseDetailIdList = pageUserInfoList.stream().map(HrmsWarehouseDetailDO::getId).collect(Collectors.toList());

            //班次出勤明细班次信息转换
            convertWarehouseDetailList(query, warehouseDetailDOList);

            //班次出勤明细快照转换
            List<WarehouseDetailSnapshotDO> warehouseDetailSnapshotDOList = hrmsWarehouseDetailOriginalDao.selectByIds(warehouseDetailIdList).stream()
                    .map(snapshot -> BeanUtils.convert(snapshot, WarehouseDetailSnapshotDO.class))
                    .collect(Collectors.toList());
            convertWarehouseDetailSnapshotList(warehouseDetailSnapshotDOList, warehouseDetailDOList);

            List<HrmsWarehouseRecordDO> hrmsWarehouseRecordDOList = hrmsWarehouseRecordDao.selectByWarehouseDetailIds(warehouseDetailIdList);
            List<WarehouseRecordDO> warehouseRecordDOList = hrmsWarehouseRecordDOList.stream()
                    .map(warehouseRecord -> BeanUtils.convert(warehouseRecord, WarehouseRecordDO.class))
                    .collect(Collectors.toList());

            List<HrmsWarehouseDetailAbnormalDO> hrmsWarehouseDetailAbnormalDOList = hrmsWarehouseDetailAbnormalDao.selectByWarehouseDetailIds(warehouseDetailIdList);
            List<WarehouseDetailAbnormalDO> warehouseDetailAbnormalDOList = hrmsWarehouseDetailAbnormalDOList.stream()
                    .map(abnormal -> BeanUtils.convert(abnormal, WarehouseDetailAbnormalDO.class))
                    .collect(Collectors.toList());

            size += warehouseDetailDOList.size();
            currentPage++;

            warehouseDetailDao.saveOrUpdateBatch(warehouseDetailDOList);

            if (CollectionUtils.isNotEmpty(warehouseDetailSnapshotDOList)) {
                warehouseDetailSnapshotDao.saveOrUpdateBatch(warehouseDetailSnapshotDOList);
            }

            if (CollectionUtils.isNotEmpty(warehouseRecordDOList)) {
                List<List<WarehouseRecordDO>> partitionList = Lists.partition(warehouseRecordDOList, 1000);
                partitionList.forEach(partition -> warehouseRecordDao.saveOrUpdateBatch(partition));
            }

            if (CollectionUtils.isNotEmpty(warehouseDetailAbnormalDOList)) {
                List<List<WarehouseDetailAbnormalDO>> partitionList = Lists.partition(warehouseDetailAbnormalDOList, 1000);
                partitionList.forEach(partition -> warehouseDetailAbnormalDao.saveOrUpdateBatch(partition));
            }
            log.info("同步仓内表->warehouseDetail currentPage:{},warehouseDetail.size:{}", currentPage, size);
        }
        log.info("同步仓内表->warehouseDetail end");
    }

    private void convertWarehouseDetailList(WarehouseDetailQuery query, List<WarehouseDetailDO> warehouseDetailDOList) {
        Map<Date, List<WarehouseDetailDO>> warehouseDateGroup = warehouseDetailDOList.stream().collect(Collectors.groupingBy(WarehouseDetailDO::getWarehouseDate));
        List<Long> userIdList = warehouseDetailDOList.stream().map(WarehouseDetailDO::getUserId).distinct().collect(Collectors.toList());
        Long startDayId = null;
        Long endDayId = null;
        if (Objects.nonNull(query.getStartTime())) {
            startDayId = DateHelper.getDayId(query.getStartTime());
        }
        if (Objects.nonNull(query.getEndTime())) {
            endDayId = DateHelper.getDayId(query.getEndTime());
        }
        //员工排班
        List<UserShiftConfigDO> multipleClassUserShiftConfigList = userShiftConfigManage.selectRecordByUserIdList(userIdList, startDayId, endDayId);
        Map<Long, List<UserShiftConfigDO>> userShiftGroup = multipleClassUserShiftConfigList.stream().collect(Collectors.groupingBy(UserShiftConfigDO::getDayId));

        Map<Date, Map<Long, List<PunchClassConfigDTO>>> dayPunchClassConfigMap = new HashMap<>();
        Map<Date, Map<Long, UserShiftConfigDO>> dayUserShiftConfigMap = new HashMap<>();
        for (Date warehouseDate : warehouseDateGroup.keySet()) {
            List<Long> userIds = warehouseDateGroup.get(warehouseDate).stream().map(WarehouseDetailDO::getUserId).distinct().collect(Collectors.toList());
            Date endDate = DateHelper.endOfDay(warehouseDate);
            Map<Long, List<PunchClassConfigDTO>> userPunchClassConfigMap = punchClassConfigManage.mapByUserIds(userIds, endDate);
            if (MapUtils.isNotEmpty(userPunchClassConfigMap)) {
                dayPunchClassConfigMap.put(warehouseDate, userPunchClassConfigMap);
            }

            List<UserShiftConfigDO> userShiftConfigDOList = userShiftGroup.get(DateHelper.getDayId(warehouseDate));
            if (CollectionUtils.isEmpty(userShiftConfigDOList)) {
                continue;
            }
            Map<Long, UserShiftConfigDO> userShiftMap = userShiftConfigDOList.stream().collect(Collectors.toMap(UserShiftConfigDO::getUserId, Function.identity(), (v1, v2) -> v1));
            dayUserShiftConfigMap.put(warehouseDate, userShiftMap);
        }

        warehouseDetailDOList.forEach(item -> {
            //排班计划中获取班次id
            Map<Long, UserShiftConfigDO> userShiftMap = dayUserShiftConfigMap.get(item.getWarehouseDate());
            if (MapUtils.isEmpty(userShiftMap)) {
                return;
            }
            UserShiftConfigDO userShiftConfigDO = userShiftMap.get(item.getUserId());
            if (Objects.isNull(userShiftConfigDO) || Objects.isNull(userShiftConfigDO.getPunchClassConfigId())) {
                return;
            }
            Long punchClassConfigId = userShiftConfigDO.getPunchClassConfigId();

            //班次
            Map<Long, List<PunchClassConfigDTO>> userPunchClassConfigMap = dayPunchClassConfigMap.get(item.getWarehouseDate());

            if (MapUtils.isEmpty(userPunchClassConfigMap)) {
                return;
            }
            List<PunchClassConfigDTO> punchClassConfigDTOList = userPunchClassConfigMap.get(item.getUserId());
            if (CollectionUtils.isEmpty(punchClassConfigDTOList)) {
                return;
            }
            PunchClassConfigDTO punchClassConfigDTO = punchClassConfigDTOList.stream()
                    .filter(punchClass -> Objects.equals(punchClass.getId(), punchClassConfigId))
                    .findFirst()
                    .orElse(new PunchClassConfigDTO());
            if (Objects.isNull(punchClassConfigDTO.getId())) {
                return;
            }
            item.setClassesId(punchClassConfigDTO.getId());
            item.setClassesType(punchClassConfigDTO.getClassType());
            item.setClassesName(punchClassConfigDTO.getClassName());
            item.setRequiredAttendanceTime(punchClassConfigDTO.getAttendanceHours());
            item.setLegalWorkingHours(punchClassConfigDTO.getLegalWorkingHours());
        });
    }

    private void convertWarehouseDetailSnapshotList(List<WarehouseDetailSnapshotDO> warehouseDetailSnapshotList, List<WarehouseDetailDO> warehouseDetailDOList) {
        if (CollectionUtils.isEmpty(warehouseDetailSnapshotList) || CollectionUtils.isEmpty(warehouseDetailDOList)) {
            return;
        }
        for (WarehouseDetailSnapshotDO snapshotDO : warehouseDetailSnapshotList) {
            for (WarehouseDetailDO warehouseDetailDO : warehouseDetailDOList) {
                if (!snapshotDO.getId().equals(warehouseDetailDO.getId())) {
                    continue;
                }
                snapshotDO.setClassesId(warehouseDetailDO.getClassesId());
                snapshotDO.setClassesName(warehouseDetailDO.getClassesName());
                snapshotDO.setClassesType(warehouseDetailDO.getClassesType());
                snapshotDO.setRequiredAttendanceTime(warehouseDetailDO.getRequiredAttendanceTime());
                snapshotDO.setLegalWorkingHours(warehouseDetailDO.getLegalWorkingHours());
            }
        }
    }
}

