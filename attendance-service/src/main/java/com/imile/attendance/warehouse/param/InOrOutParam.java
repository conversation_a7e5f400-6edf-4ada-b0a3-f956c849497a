package com.imile.attendance.warehouse.param;

import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @project hrms
 * @description 入离仓参数
 * @date 2024/7/4 11:02:06
 */
@Data
public class InOrOutParam {

    /**
     * 网点id
     */
    @NotNull(message = "ocId cannot be empty")
    private Long ocId;

    /**
     * 员工idList
     */
    @NotEmpty(message = "userIdList cannot be empty")
    private List<Long> userIdList;


    /**
     * 用工类型 劳务派遣 正式/挂靠
     */
    @NotNull(message = "employeeType cannot be empty")
    private String employeeType;

    /**
     * 供应商id
     */
    private Long vendorId;

    /**
     * 供应商编码
     */
    private String vendorCode;

    /**
     * 班次信息
     */
    private ClassesParam classes;

    /**
     * 当地打卡时间
     */
    @NotNull(message = "attendanceTime cannot be empty")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date attendanceTime;

    /**
     * 考勤日期
     */
    @NotNull(message = "warehouseDate cannot be empty")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date warehouseDate;

    /**
     * 出入仓地理坐标精度
     */
    private BigDecimal longitude;

    /**
     * 出入仓地理坐标维度
     */
    private BigDecimal latitude;
}
