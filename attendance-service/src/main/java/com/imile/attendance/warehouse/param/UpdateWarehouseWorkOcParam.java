package com.imile.attendance.warehouse.param;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;


/**
 * <AUTHOR>
 * @since 2025/01/21
 */
@Data
public class UpdateWarehouseWorkOcParam {

    @NotNull(message = "ocId cannot be empty")
    private Long ocId;

    @NotEmpty(message = "idList cannot be empty")
    private List<Long> idList;
}
