package com.imile.attendance.warehouse;

import com.imile.attendance.warehouse.param.ClassesParam;
import com.imile.attendance.warehouse.param.GetClassListByConditionParam;
import com.imile.attendance.warehouse.param.GetClassesByConditionParam;
import com.imile.attendance.warehouse.vo.ClassesBaseVO;
import com.imile.attendance.warehouse.vo.ClassesDetailVO;
import com.imile.attendance.warehouse.vo.ClassesVO;
import com.imile.attendance.warehouse.vo.ClassesWebVO;
import com.imile.attendance.warehouse.vo.PunchConfigAndClassesVO;

import java.util.List;

/**
 * 仓内班次服务
 * <AUTHOR>
 * @since 2025/1/23
 */
public interface WarehouseClassesService {

    /**
     * Web查询班次列表
     */
    List<ClassesWebVO> getClassesList(GetClassListByConditionParam param);

    /**
     * H5查询班次列表
     */
    List<ClassesVO> getClassesList(Long ocId, String currentTime, String warehouseDate);

    /**
     * 指定条件查询仓内日报班次列表
     */
    List<ClassesBaseVO> getClassesByCondition(GetClassesByConditionParam param);

    /**
     * 查询班次列表
     * 入参是hermes网点 需要先转换到部门
     */
    List<ClassesWebVO> getOuterClassesList(GetClassListByConditionParam param);

    /**
     * 查询班次详情
     */
    List<ClassesDetailVO> getClassesDetail(Long classesId);

    /**
     * 根据网点查询考勤组和班次列表
     */
    PunchConfigAndClassesVO getPunchConfigAndClasses(Long ocId);

    /**
     * 根据班次ID查询班次详细信息
     */
    ClassesParam getClassesInfo(Long classesId);

    /**
     * 根据网点获取未设班次考勤日
     * @param ocId
     * @return
     */
    List<String> getNoBindShiftDay(Long ocId);
}
