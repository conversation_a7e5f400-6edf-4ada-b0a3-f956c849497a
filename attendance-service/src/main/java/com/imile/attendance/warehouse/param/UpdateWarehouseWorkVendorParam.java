package com.imile.attendance.warehouse.param;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;


/**
 * <AUTHOR>
 * @since 2024/11/26
 */
@Data
public class UpdateWarehouseWorkVendorParam {

    @NotNull(message = "vendorId cannot be empty")
    private Long vendorId;

    @NotNull(message = "vendorCode cannot be empty")
    private String vendorCode;

    @NotEmpty(message = "idList cannot be empty")
    private List<Long> idList;
}
