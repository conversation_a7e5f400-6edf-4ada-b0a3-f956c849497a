package com.imile.attendance.warehouse.param;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR> chen
 * @Date 2025/07/09
 * @Time 16:22
 * @Description
 */
@Data
public class FaceSearchParam implements Serializable {

    @NotNull(message = "file cannot be empty")
    @JSONField(serialize = false)
    private MultipartFile file;

    @NotNull(message = "employeeType cannot be empty")
    private String employeeType;

    @NotNull(message = "ocId cannot be empty")
    private Long ocId;

    private Long vendorId;

    private String vendorCode;

    @NotNull(message = "faceTime cannot be empty")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date faceTime;

    /**
     * 班次信息id
     */
    private Long classId;
}
