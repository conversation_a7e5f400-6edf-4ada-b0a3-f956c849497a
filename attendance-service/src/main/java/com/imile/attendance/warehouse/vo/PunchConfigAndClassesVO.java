package com.imile.attendance.warehouse.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;


/**
 * <AUTHOR>
 * @since 2024/8/23
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PunchConfigAndClassesVO {

    /**
     * 打卡规则ID
     */
//    private Long punchConfigId;

    /**
     * 打卡规则名称
     */
//    private String punchConfigName;

    private List<ClassesVO> classesVOList;
}
