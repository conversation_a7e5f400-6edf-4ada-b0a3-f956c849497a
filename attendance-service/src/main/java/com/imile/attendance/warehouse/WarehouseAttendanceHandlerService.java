package com.imile.attendance.warehouse;


import com.imile.attendance.abnormal.dto.AttendanceCalculateHandlerDTO;
import com.imile.attendance.form.bo.AttendanceFormDetailBO;
import com.imile.attendance.infrastructure.repository.common.dto.AttendanceUser;
import com.imile.attendance.infrastructure.repository.employee.modle.UserInfoDO;
import com.imile.attendance.infrastructure.repository.form.model.AttendanceFormDO;
import com.imile.attendance.infrastructure.repository.warehouse.model.WarehouseDetailDO;
import com.imile.attendance.infrastructure.repository.warehouse.model.WarehousePunchPeriodDO;
import com.imile.attendance.infrastructure.repository.warehouse.query.WarehouseDetailQuery;
import com.imile.attendance.warehouse.dto.AttendanceAbnormalCheckDTO;

import java.util.List;

/**
 * 仓内考勤结果处理服务
 *
 * <AUTHOR>
 * @since 2025/1/23
 */
public interface WarehouseAttendanceHandlerService {

    /**
     * 仓内考勤计算结果处理
     */
    void warehouseAttendanceResultHandler(WarehouseDetailDO warehouseDetail, Long dayId, String stateType);

    /**
     * 判断考勤异常计算结果
     */
    AttendanceAbnormalCheckDTO checkExistAttendanceException(Long dayId, Long userId, Long classId);

    /**
     * 考勤异常处理
     */
    void attendanceAbnormalHandler(WarehouseDetailDO warehouseDetail,
                                   Long dayId,
                                   Boolean updateFaceRecord,
                                   List<WarehousePunchPeriodDO> warehousePunchPeriodDOList);

    /**
     * 仓内考勤请假处理
     */
    void warehouseAttendanceLeaveAuditPassHandler(AttendanceFormDO formDO, AttendanceCalculateHandlerDTO calculateHandlerDTO);

    /**
     * 仓内考勤补卡处理
     */
    void warehouseAttendanceReissueAuditPassHandler(AttendanceUser userInfoDO, Long abnormalId, Long dayId);

    /**
     * 仓内未打卡处理
     */
    void warehouseAttendanceNoPunchHandler(List<UserInfoDO> noClassUserList,
                                           List<UserInfoDO> offClassUserList,
                                           List<UserInfoDO> userListPreDay,
                                           List<UserInfoDO> userList,
                                           Long preDayId,
                                           Long dayId);

    /**
     * 仓内确认异常处理
     */
    void warehouseConfirmExceptionHandler(Long abnormalId, Long userId, Long dayId);

    /**
     * 仓内补工时处理
     */
    void warehouseAddDurationPassHandler(AttendanceFormDetailBO attendanceFormDetailBO);

    /**
     * 重试仓内异常计算
     */
    void retryCalculateAbnormal(WarehouseDetailQuery param);

    /**
     * 仓内异常计算
     */
    void calculateAbnormal(String userCodes, Long dayId);

}
