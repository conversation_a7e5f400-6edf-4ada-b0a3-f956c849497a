package com.imile.attendance.warehouse.out;

import com.imile.attendance.warehouse.WarehouseClassesService;
import com.imile.attendance.warehouse.api.WarehouseFinApi;
import com.imile.attendance.warehouse.dto.ClassesDTO;
import com.imile.attendance.warehouse.param.GetClassByConditionParam;
import com.imile.attendance.warehouse.param.GetClassListByConditionParam;
import com.imile.attendance.warehouse.vo.ClassesWebVO;
import com.imile.rpc.common.RpcResult;
import com.imile.util.BeanUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.dubbo.config.annotation.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/4/7
 */
@Service(version = "1.0.0")
@Slf4j
public class WarehouseFinApiImpl implements WarehouseFinApi {
    @Resource
    private WarehouseClassesService warehouseClassesService;

    @Override
    public RpcResult<List<ClassesDTO>> getClassList(GetClassByConditionParam param) {
        log.info("WarehouseFinApi.getClassList params:{}", param);
        GetClassListByConditionParam getClassListByConditionParam = BeanUtils.convert(param, GetClassListByConditionParam.class);
        List<ClassesWebVO> classesWebList = warehouseClassesService.getOuterClassesList(getClassListByConditionParam);
        if (CollectionUtils.isEmpty(classesWebList)) {
            return RpcResult.ok(Collections.emptyList());
        }
        return RpcResult.ok(BeanUtils.convert(ClassesDTO.class, classesWebList));
    }
}
