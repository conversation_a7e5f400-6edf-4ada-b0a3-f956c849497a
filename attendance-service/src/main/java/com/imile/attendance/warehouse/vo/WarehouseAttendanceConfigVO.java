package com.imile.attendance.warehouse.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;
import java.util.List;


/**
 * <AUTHOR>
 * @since 2024/11/30
 */
@Data
public class WarehouseAttendanceConfigVO {

    private Long id;

    /**
     * 国家
     */
    private String country;

    /**
     * 考勤规则名称
     */
    private String attendanceConfigName;

    /**
     * 状态 ACTIVE、DISABLED
     */
    private String status;

    /**
     * 部门ID列表
     */
    private List<Long> deptIds;

    /**
     * 适用部门
     */
    private List<String> deptNameList;

    /**
     * 用工类型
     */
    private String employeeType;

    /**
     * 是否允许上多班次
     */
    private Integer isMultipleShifts;

    /**
     * 满勤配置,允许实际总工作时长小于班次总时长分钟内
     */
    private Integer minute;

    /**
     * 是否分段计算工时 0计算总工时 1分段计算工时
     */
    private Integer isSegmentedCalculation;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createDate;

    /**
     * 创建人
     */
    private String createUserName;

    /**
     * 修改时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date lastUpdDate;

    /**
     * 修改人
     */
    private String lastUpdUserName;

}
