package com.imile.attendance.warehouse;

import com.imile.attendance.warehouse.param.GetOcListByConditionParam;
import com.imile.attendance.warehouse.vo.OcVO;
import com.imile.attendance.warehouse.vo.WarehousePcsMonthReportCountVO;
import com.imile.attendance.warehouse.vo.WarehousePcsMonthReportPassRateVO;
import com.imile.attendance.warehouse.vo.WarehousePcsReportVO;
import com.imile.common.page.PaginationResult;
import com.imile.hrms.api.warehouse.param.GetPcsReportByConditionParam;
import com.imile.hrms.api.warehouse.param.GetPcsReportCountByConditionParam;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/9/27
 */
public interface WarehouseExternalService {

    List<OcVO> getOcListByCondition(GetOcListByConditionParam param);

    PaginationResult<WarehousePcsReportVO> pcsDateReport(GetPcsReportByConditionParam param);

    PaginationResult<WarehousePcsReportVO> pcsWeekOrMonthReport(GetPcsReportByConditionParam param);

    WarehousePcsMonthReportCountVO pcsDateReportCount(GetPcsReportCountByConditionParam param);

    WarehousePcsMonthReportPassRateVO pcsDateMonthReportPassRate(GetPcsReportCountByConditionParam param);
}
