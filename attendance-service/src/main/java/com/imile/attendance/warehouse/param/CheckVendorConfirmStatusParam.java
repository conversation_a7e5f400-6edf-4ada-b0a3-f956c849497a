package com.imile.attendance.warehouse.param;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * <AUTHOR>
 * @since 2024/12/6
 */
@Data
public class CheckVendorConfirmStatusParam {
    /**
     * 当前时间
     */
    @NotNull(message = "current time cannot be empty")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date currentTime;

    /**
     * 作业日期
     */
    @NotNull(message = "warehouseDate cannot be empty")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date warehouseDate;

    /**
     * 班次id
     */
    @NotNull(message = "classesId cannot be empty")
    private Long classesId;

    /**
     * 网点
     */
    @NotNull(message = "ocId cannot be empty")
    private Long ocId;

    /**
     * 供应商编码
     */
    @NotNull(message = "vendorCode cannot be empty")
    private String vendorCode;
}
