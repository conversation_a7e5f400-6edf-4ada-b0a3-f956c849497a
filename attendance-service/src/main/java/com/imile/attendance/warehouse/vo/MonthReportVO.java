package com.imile.attendance.warehouse.vo;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.function.BiConsumer;

/**
 * <AUTHOR>
 * @project hrms
 * @description 月报展示
 * @date 2024/6/29 20:39:09
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class MonthReportVO extends ReportVO {

    private String date1;

    private String date2;
    private String date3;
    private String date4;
    private String date5;
    private String date6;
    private String date7;
    private String date8;
    private String date9;
    private String date10;
    private String date11;
    private String date12;
    private String date13;
    private String date14;
    private String date15;
    private String date16;
    private String date17;
    private String date18;
    private String date19;
    private String date20;
    private String date21;
    private String date22;
    private String date23;
    private String date24;
    private String date25;
    private String date26;
    private String date27;
    private String date28;
    private String date29;
    private String date30;
    private String date31;


    // 使用反射来动态获取 setter 方法
    public  BiConsumer<MonthReportVO, String> getSetter(Integer i) {
        if (i == null || i < 1 || i > 31) {
            throw new IllegalArgumentException(" index must be between 1 and 31");
        }

        String fieldName = "date" + i + "";
        try {
            // 获取字段
            Field field = this.getClass().getDeclaredField(fieldName);
            // 确保字段可访问
            field.setAccessible(true);
            // 获取 setter 方法名，假设遵循 JavaBean 命名规范
            String setterName = "set" + field.getName().substring(0, 1).toUpperCase() + field.getName().substring(1);
            // 获取 setter 方法
            Method setterMethod = this.getClass().getMethod(setterName, String.class);
            // 使用 lambda 表达式和 Method.invoke 来创建 BiConsumer
            return (vo, value) -> {
                try {
                    setterMethod.invoke(vo, value);
                } catch (Exception e) {
                    throw new RuntimeException("Failed to set field " + fieldName, e);
                }
            };
        } catch (NoSuchFieldException | NoSuchMethodException e) {
            throw new IllegalArgumentException("No such field or setter method for  " + i, e);
        }
    }

}
