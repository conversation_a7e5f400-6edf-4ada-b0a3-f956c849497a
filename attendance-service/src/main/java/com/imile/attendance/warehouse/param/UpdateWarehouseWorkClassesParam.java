package com.imile.attendance.warehouse.param;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;


/**
 * <AUTHOR>
 * @since 2025/01/21
 */
@Data
public class UpdateWarehouseWorkClassesParam {

    @NotNull(message = "classId cannot be empty")
    private Long classId;

    @NotEmpty(message = "idList cannot be empty")
    private List<Long> idList;
}
