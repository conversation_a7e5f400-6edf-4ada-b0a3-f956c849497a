package com.imile.attendance.warehouse.impl;

import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.imile.attendance.apollo.AttendanceProperties;
import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.context.RequestInfoHolder;
import com.imile.attendance.enums.ErrorCodeEnum;
import com.imile.attendance.exception.BusinessLogicException;
import com.imile.attendance.hermes.RpcHermesVendorClient;
import com.imile.attendance.infrastructure.idwork.DefaultIdWorker;
import com.imile.attendance.infrastructure.repository.common.AttendanceDeptService;
import com.imile.attendance.infrastructure.repository.common.dto.AttendanceDept;
import com.imile.attendance.purchase.RpcPurchaseClient;
import com.imile.attendance.util.QRCodeGeneratorUtil;
import com.imile.attendance.warehouse.WarehouseOcService;
import com.imile.attendance.warehouse.WarehouseSupplierService;
import com.imile.attendance.warehouse.dto.QrCodeTokenDTO;
import com.imile.attendance.warehouse.param.GetVendorListByOcListParam;
import com.imile.attendance.warehouse.vo.OcVO;
import com.imile.attendance.warehouse.vo.QrCodeResultVO;
import com.imile.attendance.warehouse.vo.QrCodeVerifyResultVO;
import com.imile.attendance.warehouse.vo.VendorVO;
import com.imile.common.exception.BusinessException;
import com.imile.common.page.PaginationResult;
import com.imile.framework.redis.client.ImileRedisClient;
import com.imile.hermes.vendor.dto.VendorInfoApiDTO;
import com.imile.purchase.api.contract.req.HubServiceConfigRes;
import com.imile.purchase.api.contract.response.HubServiceConfigResponse;
import com.imile.purchase.api.supplier.dto.CommonSupplierBasicDTO;
import com.imile.purchase.api.supplier.dto.CommonSupplierCenterDTO;
import com.imile.purchase.api.supplier.vo.CommonSupplierQueryVO;
import com.imile.util.lang.I18nUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR> chen
 * @Date 2025/07/09
 * @Time 17:17
 * @Description
 */
@Slf4j
@Service
public class WarehouseSupplierServiceImpl implements WarehouseSupplierService {

    @Resource
    private RpcPurchaseClient rpcPurchaseClient;
    @Resource
    private AttendanceDeptService deptService;
    @Resource
    private DefaultIdWorker defaultIdWorker;
    @Resource
    private RpcHermesVendorClient rpcHermesVendorClient;
    @Resource
    private ImileRedisClient imileRedisClient;
    @Resource
    private AttendanceProperties attendanceProperties;
    @Resource
    private WarehouseOcService warehouseOcService;

    @Value("${wpm.mini.program.host}")
    private String wpmMiniProgramHost;

    /**
     * 仓内二维码
     */
    private static final String WAREHOUSE_QRCODE_TOKEN = "warehouse:qrcode:token:%s";

    public static final String STATISTIC_URL = "/#/pages/QRCode/index?";

    @Override
    public List<VendorVO> getVendorListByCondition(GetVendorListByOcListParam param) {
        List<Long> ocDeptIdList = param.getOcIdList();
        if (CollectionUtils.isEmpty(param.getOcIdList())) {
            List<OcVO> ocVOList;
            if (StringUtils.isNotEmpty(param.getCountry()) || CollectionUtils.isNotEmpty(param.getCityList())) {
                GetVendorListByOcListParam ocListParam = new GetVendorListByOcListParam();
                ocListParam.setCountry(param.getCountry());
                ocListParam.setCityList(param.getCityList());
                ocVOList = warehouseOcService.getOcListByCondition(ocListParam);
            } else {
                ocVOList = warehouseOcService.getDeptAuthVOList(RequestInfoHolder.getUserCode());
            }
            ocDeptIdList = ocVOList.stream().map(OcVO::getOcId).collect(Collectors.toList());
        }
        return getVendorList(ocDeptIdList);
    }

    @Override
    public List<VendorVO> getVendorList(List<Long> ocDeptIdList) {
        if (CollectionUtils.isEmpty(ocDeptIdList)) {
            return Collections.emptyList();
        }
        List<AttendanceDept> deptList = deptService.selectDeptByIds(ocDeptIdList);
        List<String> ocCenterCodeList = deptList.stream()
                .filter(Objects::nonNull)
                .map(AttendanceDept::getOcCenterCode)
                .distinct()
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(ocCenterCodeList)) {
            return Collections.emptyList();
        }
        List<VendorVO> result = new ArrayList<>();
        boolean hasNext = true;
        int currentPage = 1;
        do {
            CommonSupplierQueryVO query = getCommonSupplierQuery(currentPage, ocCenterCodeList);
            PaginationResult<CommonSupplierBasicDTO> paginationResult = rpcPurchaseClient.supplierPage(query);

            if (CollectionUtils.isEmpty(paginationResult.getResults()) || paginationResult.getPagination().getTotalPage() <= currentPage) {
                hasNext = false;
            }
            currentPage++;

            Map<String, Long> vendorMap = rpcHermesVendorClient.getByVendorCodes(paginationResult.getResults()
                            .stream()
                            .map(CommonSupplierBasicDTO::getSupplierCode)
                            .distinct().collect(Collectors.toList()))
                    .stream()
                    .collect(Collectors.toMap(VendorInfoApiDTO::getVendorCode, VendorInfoApiDTO::getVendorId));

            List<VendorVO> vendorList = new ArrayList<>();
            for (CommonSupplierBasicDTO supplierBasic : paginationResult.getResults()) {
                Long vendorId = vendorMap.get(supplierBasic.getSupplierCode());
                if (Objects.isNull(vendorId)) {
                    continue;
                }
                VendorVO vendorVO = new VendorVO();
                vendorVO.setVendorId(vendorId);
                vendorVO.setVendorCode(supplierBasic.getSupplierCode());
                vendorVO.setVendorName(supplierBasic.getSupplierNameEn());
                vendorList.add(vendorVO);
            }
            result.addAll(vendorList);
        } while (hasNext);

        return result;
    }

    @NotNull
    private static CommonSupplierQueryVO getCommonSupplierQuery(int currentPage, List<String> ocCenterCodeList) {
        CommonSupplierQueryVO query = new CommonSupplierQueryVO();
        query.setCurrentPage(currentPage);
        query.setPageCount(BusinessConstant.TWO_HUNDRED_NUM);
        query.setSupplierCenterList(ocCenterCodeList);
        //审核状态 (0-未提交，1-审核中，2-通过，3-驳回、终止)
        query.setAuditStatusList(Collections.singletonList(BusinessConstant.TWO));
        //启用：1  停用：0
        query.setStatus(BusinessConstant.ONE);
        query.setSupplierTypeList(Collections.singletonList(BusinessConstant.HUB_SERVICE_SUPPLIER_TYPE));
        return query;
    }

    @Override
    public List<VendorVO> getNoLoginVendorListByOcList(List<Long> ocDeptIdList) {
        if (CollectionUtils.isEmpty(ocDeptIdList)) {
            return Collections.emptyList();
        }
        // 这个需求只会传一个网点
        List<AttendanceDept> deptList = deptService.listByDeptIds(ocDeptIdList);
        if (CollectionUtils.isEmpty(deptList)) {
            return Collections.emptyList();
        }
        AttendanceDept oc = deptList.get(BusinessConstant.FIRST_ELEMENT_INDEX);
        // 关闭开关，或者不是制定的网点，就还是走旧逻辑
        List<String> ocCodeList = Arrays.stream(attendanceProperties.getVendor().getOc()
                        .split(","))
                .collect(Collectors.toList());
        if (attendanceProperties.getVendor().getIsOn() &&
                CollectionUtils.isNotEmpty(ocCodeList) &&
                ocCodeList.contains(oc.getOcCode())) {
            // 新逻辑
            return getVendorListFromPurchase(deptList);
        }
        //旧逻辑
        return getVendorList(ocDeptIdList);
    }

    @Override
    public List<String> getOcCodeList(List<String> supplierCodeList) {
        if (CollectionUtils.isEmpty(supplierCodeList)) {
            return Collections.emptyList();
        }

        PaginationResult<CommonSupplierBasicDTO> paginationResult = getSupplierByCode(supplierCodeList, true);
        if (CollectionUtils.isEmpty(paginationResult.getResults())) {
            return Collections.emptyList();
        }

        return paginationResult.getResults().stream()
                .flatMap(supplier -> Optional.ofNullable(supplier.getSupplierCenterList())
                        .map(List::stream)
                        .orElse(Stream.empty()))
                .map(CommonSupplierCenterDTO::getOcCode)
                .filter(Objects::nonNull).distinct().collect(Collectors.toList());
    }

    @Override
    public Map<String, String> getSupplierByCodes(List<String> supplierCodes) {
        PaginationResult<CommonSupplierBasicDTO> paginationResult = getSupplierByCode(supplierCodes, false);
        return paginationResult.getResults()
                .stream()
                .collect(Collectors.toMap(CommonSupplierBasicDTO::getSupplierCode,
                        CommonSupplierBasicDTO::getSupplierNameEn, (v1, v2) -> v1));
    }

    @Override
    public QrCodeResultVO generateQrCode(Long ocId, String vendorCode, Integer permission) {
        AttendanceDept oc = deptService.getByDeptId(ocId);
        if (oc == null) {
            throw BusinessException.get(ErrorCodeEnum.STATION_NOT_EXITS.getCode(), I18nUtils.getMessage(ErrorCodeEnum.STATION_NOT_EXITS.getDesc()));
        }
        QrCodeResultVO result = new QrCodeResultVO();
        String uuid = defaultIdWorker.uuid();
        // 放入缓存中, 有效期5年
        QrCodeTokenDTO token = new QrCodeTokenDTO();
        token.setOcId(ocId);
        token.setOcNameCn(oc.getDeptNameCn());
        token.setOcNameEn(oc.getDeptNameEn());
        if (StringUtils.isNotEmpty(vendorCode)) {
            Map<String, Long> vendorMap = rpcHermesVendorClient.getByVendorCodes(Collections.singletonList(vendorCode))
                    .stream()
                    .collect(Collectors.toMap(VendorInfoApiDTO::getVendorCode, VendorInfoApiDTO::getVendorId));
            Map<String, String> vendorNameMap = getSupplierByCodes(Collections.singletonList(vendorCode));
            token.setVendorId(vendorMap.get(vendorCode));
            token.setVendorCode(vendorCode);
            token.setVendorName(vendorNameMap.get(vendorCode));
        }
        if (Objects.nonNull(permission)) {
            token.setPermission(permission);
        }
        imileRedisClient.set(String.format(WAREHOUSE_QRCODE_TOKEN, uuid), token,
                DateUtil.offset(new Date(), DateField.YEAR, 5).getTime());
        result.setToken(uuid);
        result.setUrl(wpmMiniProgramHost + STATISTIC_URL + "token=" + uuid + "&from=qrcode");
        String qrCode = QRCodeGeneratorUtil.generateQRCode(result.getUrl());
        result.setQrCode(qrCode);
        return result;
    }

    @Override
    public QrCodeVerifyResultVO verifyToken(String token) {
        if (StringUtils.isBlank(token)) {
            throw BusinessLogicException.get(ErrorCodeEnum.TOKEN_EXCEPTION.getCode(), I18nUtils.getMessage(ErrorCodeEnum.TOKEN_EXCEPTION.getDesc()));
        }
        QrCodeTokenDTO tokenDTO = imileRedisClient.get(String.format(WAREHOUSE_QRCODE_TOKEN, token), QrCodeTokenDTO.class);
        if (tokenDTO == null) {
            throw BusinessLogicException.get(ErrorCodeEnum.HYPERLINK_EXPIRE.getCode(), I18nUtils.getMessage(ErrorCodeEnum.HYPERLINK_EXPIRE.getDesc()));
        }
        QrCodeVerifyResultVO result = new QrCodeVerifyResultVO();
        result.setOcId(tokenDTO.getOcId());
        result.setOcNameCn(tokenDTO.getOcNameCn());
        result.setOcNameEn(tokenDTO.getOcNameEn());
        result.setVendorId(tokenDTO.getVendorId());
        result.setVendorCode(tokenDTO.getVendorCode());
        result.setVendorName(tokenDTO.getVendorName());
        result.setPermission(tokenDTO.getPermission());
        return result;
    }


    /**
     * 根据供应商编码查询采购供应商信息
     *
     * @param supplierCodeList   供应商编码列表
     * @param supplierCenterFlag 是否返回供应商关联区域中心
     * @return 供应商信息
     */
    private PaginationResult<CommonSupplierBasicDTO> getSupplierByCode(List<String> supplierCodeList, boolean supplierCenterFlag) {
        CommonSupplierQueryVO query = new CommonSupplierQueryVO();
        query.setSourceType(BusinessConstant.SYSTEM_CODE);
        query.setSupplierCodeList(supplierCodeList);
        query.setAuditStatusList(Collections.singletonList(BusinessConstant.TWO));
        query.setStatus(BusinessConstant.ONE);
        query.setSupplierTypeList(Collections.singletonList(BusinessConstant.HUB_SERVICE_SUPPLIER_TYPE));
        query.setSupplierCenterFlag(supplierCenterFlag);
        return rpcPurchaseClient.supplierPage(query);
    }


    private List<VendorVO> getVendorListFromPurchase(List<AttendanceDept> deptList) {
        if (CollectionUtils.isEmpty(deptList)) {
            return Collections.emptyList();
        }
        List<String> ocCodeList = deptList.stream().map(AttendanceDept::getOcCode).collect(Collectors.toList());
        HubServiceConfigRes param = new HubServiceConfigRes();
        param.setOcCodeList(ocCodeList);
        List<HubServiceConfigResponse> responses = rpcPurchaseClient.getHubServiceConfig(param);
        if (CollectionUtils.isEmpty(responses)) {
            return Collections.emptyList();
        }
        Map<String, Long> vendorMap = rpcHermesVendorClient.getByVendorCodes(responses
                        .stream()
                        .map(HubServiceConfigResponse::getSupplierCode)
                        .distinct().collect(Collectors.toList()))
                .stream()
                .collect(Collectors.toMap(VendorInfoApiDTO::getVendorCode, VendorInfoApiDTO::getVendorId));
        return responses.stream().map(e -> {
            VendorVO vendorVO = new VendorVO();
            vendorVO.setVendorId(vendorMap.getOrDefault(e.getSupplierCode(), 0L));
            vendorVO.setVendorCode(e.getSupplierCode());
            vendorVO.setVendorName(e.getSupplierName());
            return vendorVO;
        }).collect(Collectors.toList());
    }
}
