package com.imile.attendance.warehouse.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @description:
 * @author: taokang
 * @createDate: 2023-7-24
 * @version: 1.0
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class DayAttendanceHandlerDTO {
    /**
     * 入参:公司(定时任务执行的时候用到)
     */
    private String countryList;

    /**
     * 入参:用户编码   考勤审批通过后会通过这个字段来 定时任务也可以，手动执行某个用户
     * 优先看userCodeList有没有值，有就无视公司
     */
    private String userCodes;

    /**
     * 入参:需要计算的考勤天
     */
    private Long attendanceDayId;

    /**
     * 界面无法输入，考勤时间，当天的结束时间  yyyy-MM-dd HH:mm:ss
     */
    private Date attendanceTime;

    /**
     * 界面无法输入，开始时间  yyyy-MM-dd HH:mm:ss  周期的开始时间(3天的开始时间)
     */
    private Date startTime;

    /**
     * 界面无法输入，结束时间  yyyy-MM-dd HH:mm:ss  周期的结束时间(3天的结束时间)
     */
    private Date endTime;

    /**
     * 界面无法输入，开始时间  yyyyMMdd  周期的开始时间的long
     */
    private Long startDayId;

    /**
     * 界面无法输入，结束时间  yyyyMMdd  周期的结束时间的long
     */
    private Long endDayId;

    /**
     * 界面无法输入，改天考勤真正的起始时间 yyyy-MM-dd HH:mm:ss(排班计算出来的)
     */
    private Date actualAttendanceStartTime;

    /**
     * 界面无法输入，改天考勤真正的结束时间  yyyy-MM-dd HH:mm:ss(排班计算出来的)
     */
    private Date actualAttendanceEndTime;

    /**
     * 请假时长
     */
    private BigDecimal leaveHours;

    /**
     * 请假类型
     */
    private String leaveType;

    /**
     * 工作网点ID
     */
    private Long ocId;

    /**
     * 班次ID
     */
    private Long classId;

    /**
     * 仓内考勤配置ID
     */
    private Long attendanceConfigId;

    /**
     * 实际出勤时长 单位:小时 包含休息时间
     */
    private BigDecimal actualAttendanceTime = BigDecimal.ZERO;

    /**
     * 实际工作总时长 单位:小时 不包含休息时间
     */
    private BigDecimal actualWorkingHours = BigDecimal.ZERO;

}
