package com.imile.attendance.permission;

import com.imile.attendance.infrastructure.repository.common.dto.AttendanceDept;
import com.imile.permission.api.dto.DataPermissionApiDTO;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2025/6/24
 */
public interface WpmPermissionService {
    DataPermissionApiDTO getCountryRegionStationPermission(String userCode);

    Map<String, List<String>> selectUserCodeByRoleId(List<Long> roleIdList);

    Boolean addUserRole(String userCode, Long roleId);

    Boolean removeUserRole(String userCode, Long roleId);

    /**
     * 查询WPM国家区域网点权限
     * 从权限中心国家区域网点取值
     * 过滤加盟网点和非组织架构树中部门网点
     */
    List<AttendanceDept> getRegionStationAuthDeptList(String userCode);

    /**
     * 查询考勤部门主数据权限
     * 过滤加盟网点和非组织架构树中部门网点
     */
    List<AttendanceDept> getAuthDeptList(String userCode, String deptName);
}
