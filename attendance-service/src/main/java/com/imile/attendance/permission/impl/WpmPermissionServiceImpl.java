package com.imile.attendance.permission.impl;

import com.aliyun.opensearch.sdk.dependencies.com.google.common.collect.Lists;
import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.context.RequestInfoHolder;
import com.imile.attendance.enums.PermissionTypeEnum;
import com.imile.attendance.enums.warehouse.WpmPermissionTypeEnum;
import com.imile.attendance.infrastructure.repository.common.AttendanceDeptService;
import com.imile.attendance.infrastructure.repository.common.dto.AttendanceDept;
import com.imile.attendance.permission.WpmPermissionService;
import com.imile.attendance.util.RpcResultProcessor;
import com.imile.attendance.warehouse.WarehouseBaseService;
import com.imile.hrms.api.organization.query.DeptConditionParam;
import com.imile.permission.api.UserDataPermissionApi;
import com.imile.permission.api.UserManagerApi2;
import com.imile.permission.api.dto.DataPermissionApiDTO;
import com.imile.permission.api.dto.UserDataPermissionApiDTO;
import com.imile.permission.api.dto.UserRoleApiDTO;
import com.imile.permission.api.dto.UserWithSystemApiDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2025/6/24
 */
@Slf4j
@Service
public class WpmPermissionServiceImpl extends WarehouseBaseService implements WpmPermissionService {

    @Reference(version = "1.0.0", timeout = 30000, check = false)
    private UserDataPermissionApi userDataPermissionApi;

    @Reference(version = "1.0.0", timeout = 30000, check = false)
    private UserManagerApi2 userManagerApi2;

    @Resource
    private AttendanceDeptService deptService;

    @Override
    public DataPermissionApiDTO getCountryRegionStationPermission(String userCode) {
        if (StringUtils.isEmpty(userCode)) {
            return null;
        }
        UserWithSystemApiDTO apiDTO = new UserWithSystemApiDTO();
        apiDTO.setUserCode(userCode);
        apiDTO.setSystemCode(BusinessConstant.WPM);
        UserDataPermissionApiDTO process = RpcResultProcessor.process(userDataPermissionApi.getUserDataPermission(apiDTO));
        if (CollectionUtils.isEmpty(process.getDataPermissionApiDTOList())) {
            return null;
        }
        return process.getDataPermissionApiDTOList().stream()
                .filter(it -> WpmPermissionTypeEnum.WPM_COUNTRY_REGION_STATION_PERMISSION.getTypeCode().equals(it.getTypeCode()))
                .findFirst()
                .orElse(null);
    }

    @Override
    public Map<String, List<String>> selectUserCodeByRoleId(List<Long> roleIdList) {
        if (CollectionUtils.isEmpty(roleIdList)) {
            return null;
        }
        log.info("selectUserCodeByRoleId roleIdList:{}", roleIdList);
        Map<String, List<String>> userCodeMap = RpcResultProcessor.process(userManagerApi2.selectUserCodeByRoleId(roleIdList));
        log.info("selectUserCodeByRoleId process:{}", userCodeMap);
        return userCodeMap;
    }

    @Override
    public Boolean addUserRole(String userCode, Long roleId) {
        if (StringUtils.isEmpty(userCode) || Objects.isNull(roleId)) {
            return false;
        }
        UserRoleApiDTO param = new UserRoleApiDTO();
        param.setUserCode(userCode);
        param.setAddIdList(Collections.singletonList(roleId));
        log.info("addUserRole,userCode:{},roleId:{}", userCode, roleId);
        Boolean result = RpcResultProcessor.process(userManagerApi2.editUserRole(param));
        log.info("addUserRole, process:{} ", result);
        return result;
    }

    @Override
    public Boolean removeUserRole(String userCode, Long roleId) {
        if (StringUtils.isEmpty(userCode) || Objects.isNull(roleId)) {
            return false;
        }
        UserRoleApiDTO param = new UserRoleApiDTO();
        param.setUserCode(userCode);
        param.setRemoveIdList(Collections.singletonList(roleId));
        log.info("removeUserRole,userCode:{},roleId:{}", userCode, roleId);
        Boolean result = RpcResultProcessor.process(userManagerApi2.editUserRole(param));
        log.info("removeUserRole, process:{} ", result);
        return result;
    }

    @Override
    public List<AttendanceDept> getRegionStationAuthDeptList(String userCode) {
        if (StringUtils.isEmpty(userCode)) {
            userCode = RequestInfoHolder.getUserCode();
        }
        DataPermissionApiDTO dataPermissionApiDTO = this.getCountryRegionStationPermission(userCode);
        if (Objects.isNull(dataPermissionApiDTO) || CollectionUtils.isEmpty(dataPermissionApiDTO.getDataCodeList())) {
            log.info("国家区域网点权限为空,userCode:{}", userCode);
            return Collections.emptyList();
        }

        List<String> ocCodeList = dataPermissionApiDTO.getDataCodeList();
        List<List<String>> ocPartition = Lists.partition(ocCodeList, BusinessConstant.FIVE_HUNDRED_NUM);
        List<AttendanceDept> deptOcList = new ArrayList<>();
        for (List<String> ocList : ocPartition) {
            List<AttendanceDept> attendanceDeptList = deptService.listByOcCode(ocList)
                    .stream()
                    .filter(this::filterDept)
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(attendanceDeptList)) {
                deptOcList.addAll(attendanceDeptList);
            }
        }
        return deptOcList;
    }

    @Override
    public List<AttendanceDept> getAuthDeptList(String userCode, String deptName) {
        if (StringUtils.isEmpty(userCode)) {
            return Collections.emptyList();
        }
        UserWithSystemApiDTO apiDTO = new UserWithSystemApiDTO();
        apiDTO.setUserCode(userCode);
        apiDTO.setSystemCode(BusinessConstant.SYSTEM_CODE);
        UserDataPermissionApiDTO process = RpcResultProcessor.process(userDataPermissionApi.getUserDataPermission(apiDTO));
        if (CollectionUtils.isEmpty(process.getDataPermissionApiDTOList())) {
            return Collections.emptyList();
        }
        DataPermissionApiDTO dataPermissionApiDTO = process.getDataPermissionApiDTOList().stream()
                .filter(it -> PermissionTypeEnum.DEPT.getTypeCode().equals(it.getTypeCode()))
                .findFirst()
                .orElse(null);

        if (Objects.isNull(dataPermissionApiDTO) || CollectionUtils.isEmpty(dataPermissionApiDTO.getDataCodeList())) {
            log.info("部门主数据权限为空,userCode:{}", userCode);
            return Collections.emptyList();
        }

        List<String> deptIdList = dataPermissionApiDTO.getDataCodeList();
        List<List<String>> deptIdPartition = Lists.partition(deptIdList, BusinessConstant.FIVE_HUNDRED_NUM);
        List<AttendanceDept> deptOcList = new ArrayList<>();

        DeptConditionParam deptConditionParam = new DeptConditionParam();
        if (StringUtils.isNotBlank(deptName)) {
            deptConditionParam.setDeptName(deptName);
        }
        for (List<String> deptIds : deptIdPartition) {
            deptConditionParam.setIdList(deptIds.stream().map(Long::valueOf).collect(Collectors.toList()));
            List<AttendanceDept> attendanceDeptList = deptService.listByCondition(deptConditionParam)
                    .stream()
                    .filter(this::filterDept)
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(attendanceDeptList)) {
                deptOcList.addAll(attendanceDeptList);
            }
        }
        return deptOcList;
    }
}
