package com.imile.attendance.abnormal.vo;

import com.imile.attendance.abnormal.dto.AbnormalOperationRecordDTO;
import com.imile.attendance.annon.WithDict;
import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.rule.vo.PunchClassItemConfigVO;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/06/03
 */
@Data
public class AbnormalDetailVO {
    /**
     * 员工基本信息
     */
    private EmployeeBaseInfoVO employeeBaseInfo;

    /**
     * 考勤配置信息
     */
    private AbnormalDetailVO.AttendanceRuleConfigVO attendanceRuleConfigInfo;

    /**
     * 异常记录操作记录
     */
    private List<AbnormalOperationRecordDTO> abnormalOperationRecordDTOList;

    /**
     * 考勤异常信息
     */
    private AttendanceAbnormalInfo attendanceAbnormalInfo;



    @Data
    public static class AttendanceRuleConfigVO {
        /**
         * 班次性质
         */
        @WithDict(typeCode = BusinessConstant.SysDictDataTypeConstant.CLASS_NATURE, ref = "classNatureDesc")
        private String classNature;

        /**
         * 班次性质描述
         */
        private String classNatureDesc;

        /**
         * 班次ID
         */
        private Long classId;

        /**
         * 班次名称
         */
        private String className;

        /**
         * 排班ID
         */
        private Long userShiftConfigId;

        /**
         * 当天的排班规则，有班次时是班次名称，无班次是OFF H 等
         */
        private String dayShiftRule;

        /**
         * 日历ID
         */
        private Long calendarId;

        /**
         * 日历名称
         */
        private String calendarName;

        /**
         * 打卡规则ID
         */
        private Long punchConfigId;

        /**
         * 打卡规则名称
         */
        private String punchConfigName;

        /**
         * 打卡规则类型
         */
        private String punchConfigType;

        /**
         * 补卡规则ID
         */
        private Long reissueCardConfigId;

        /**
         * 补卡规则名称
         */
        private String reissueCardConfigName;

        /**
         * 加班规则ID
         */
        private Long overTimeConfigId;

        /**
         * 加班规则名称
         */
        private String overTimeConfigName;
    }


    @Data
    public static class AttendanceAbnormalInfo {
        /**
         * 异常ID
         */
        private Long abnormalId;

        /**
         * 考勤日期
         */
        private Date attendanceDate;

        /**
         * 异常类型
         */
        @WithDict(typeCode = BusinessConstant.SysDictDataTypeConstant.HRMS_ABNORMAL_TYPE, ref = "abnormalTypeDesc")
        private String abnormalType;

        private String abnormalTypeDesc;

        /**
         * 考勤状态
         */
        private String attendanceStatus;

        /**
         * 操作类型(请假/补卡/外勤/确认异常)
         */
        private String operationType;

        /**
         * bpm审批单ID
         */
        private Long approvalId;

        /**
         * 班次时段信息
         */
        private PunchClassItemConfigVO itemConfigInfo;

        /**
         * 打卡时间
         */
        private List<Date> punchTimeList;
    }
}
