package com.imile.attendance.abnormal.service.impl;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.aliyun.openservices.log.http.client.ServiceException;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.imile.attendance.abnormal.dto.AbnormalRemindTemplateDTO;
import com.imile.attendance.abnormal.param.AbnormalAttendanceDayReminderParam;
import com.imile.attendance.abnormal.param.AbnormalRemindRecordAddParam;
import com.imile.attendance.abnormal.param.AppMessageParam;
import com.imile.attendance.abnormal.service.AttendanceAbnormalRemindRecordService;
import com.imile.attendance.abnormal.service.AttendanceAbnormalRemindService;
import com.imile.attendance.common.AttendanceCountryService;
import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.entity.DateDO;
import com.imile.attendance.enums.BizTypeEnum;
import com.imile.attendance.enums.EmploymentTypeEnum;
import com.imile.attendance.enums.EnvEnum;
import com.imile.attendance.enums.PlatFormTypeEnum;
import com.imile.attendance.enums.abnormal.AbnormalAttendanceStatusEnum;
import com.imile.attendance.enums.abnormal.AttendanceAbnormalTypeEnum;
import com.imile.attendance.enums.rule.PunchConfigTypeEnum;
import com.imile.attendance.hrms.support.RpcPlatformRelationClientSupport;
import com.imile.attendance.infrastructure.repository.abnormal.dao.EmployeeAbnormalAttendanceDao;
import com.imile.attendance.infrastructure.repository.abnormal.dto.EmployeeAbnormalAttendanceDTO;
import com.imile.attendance.infrastructure.repository.abnormal.model.AttendanceAbnormalRemindRecordDO;
import com.imile.attendance.infrastructure.repository.abnormal.query.AbnormalRemindRecordQuery;
import com.imile.attendance.infrastructure.repository.abnormal.query.EmployeeAbnormalAttendancePageQuery;
import com.imile.attendance.infrastructure.repository.employee.dao.UserInfoDao;
import com.imile.attendance.infrastructure.repository.employee.modle.UserInfoDO;
import com.imile.attendance.infrastructure.repository.punch.dao.EmployeePunchRecordDao;
import com.imile.attendance.infrastructure.repository.punch.model.EmployeePunchRecordDO;
import com.imile.attendance.infrastructure.repository.punch.query.EmployeePunchCardRecordQuery;
import com.imile.attendance.infrastructure.repository.rule.dao.PunchClassItemConfigDao;
import com.imile.attendance.infrastructure.repository.rule.dao.PunchConfigDao;
import com.imile.attendance.infrastructure.repository.rule.model.PunchClassItemConfigDO;
import com.imile.attendance.infrastructure.repository.rule.model.PunchConfigDO;
import com.imile.attendance.rule.dto.DayPunchTimeDTO;
import com.imile.attendance.rule.service.PunchClassConfigQueryService;
import com.imile.attendance.user.UserService;
import com.imile.attendance.util.DateHelper;
import com.imile.hrms.api.platform.dto.PlatformRelationApiDTO;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @description: 异常考勤提醒业务实现类
 * @author: han.wang
 * @createDate: 2024-12-02
 * @version: 1.0
 */
@Slf4j
@Service
public class AttendanceAbnormalRemindServiceImpl implements AttendanceAbnormalRemindService {
    @Resource
    private AttendanceCountryService attendanceCountryService;
    @Resource
    private UserInfoDao userInfoDao;
    @Resource
    private EmployeePunchRecordDao employeePunchRecordDao;
    @Resource
    private RpcPlatformRelationClientSupport platformRelationClientSupport;
    @Resource
    private AttendanceAbnormalRemindRecordService abnormalRemindRecordService;
    @Resource
    private EmployeeAbnormalAttendanceDao employeeAbnormalAttendanceDao;
    @Resource
    private PunchConfigDao punchConfigDao;
    @Resource
    private PunchClassItemConfigDao punchClassItemConfigDao;
    @Resource
    private PunchClassConfigQueryService punchClassConfigQueryService;
    @Resource
    private UserService userService;


    @Value(value = "${spring.profiles.active}")
    private String env;

    private static final String IMILE_CLOCK_APP_ID = "1000146";

    private static final String IMILE_CLOCK_AGENT_ID = "1000156";

    private static final String IMILE_CLOCK_SECRET = "9AC8FD3AACDC3FE919FD60133C6FA802494F06185FFBA5D789E5F63DA5B3104D";

    private static final String BUTTON_URL = "https://attendance-miniapp.imile.com/#/pages/index/index?tab=1&source=NOTICE&date=";

    private static final String BUTTON_URL_TEST = "https://test-attendance-miniapp.52imile.cn/#/pages/index/index?tab=1&source=NOTICE&date=";

    private static final String TEMPLATE_NAME = "AttendanceAbnormalRemindTemplate";

    private static final String TEMPLATE_NAME_EN = "AttendanceAbnormalRemindTemplateEn";

    private static final List<String> MISMATCH_ABNORMAL_TYPE = Lists.newArrayList(AttendanceAbnormalTypeEnum.NO_SCHEDULING_PLAN.getCode(), AttendanceAbnormalTypeEnum.ABNORMAL_DURATION.getCode());

    @Override
    public void sendRemind(AbnormalAttendanceDayReminderParam param) {
        String handler = BusinessConstant.JobHandler.ABNORMAL_ATTENDANCE_DAY_REMIND_HANDLER;
        // 常驻国校验
        if (StringUtils.isBlank(param.getCountryList())) {
            XxlJobLogger.log("XXL-JOB,  {} location country not empty",
                    handler);
            return;
        }
        // 获取对应国家的当前时间
        List<String> countryList = Arrays.asList(param.getCountryList().split(","));
        Map<String, Date> countryCurrentDate = attendanceCountryService.getCountryCurrentDate(countryList);
        if (MapUtils.isEmpty(countryCurrentDate)) {
            XxlJobLogger.log("XXL-JOB,  {} 获取对应国家时间失败:{}", handler, countryList);
            return;
        }
        for (String country : countryList) {
            Long attendanceDayId = param.getDayId();
            Date date = countryCurrentDate.get(country);
            if (Objects.isNull(date)) {
                XxlJobLogger.log("XXL-JOB,  {} 未获取到当前国家对应时间:{}", handler, country);
                continue;
            }
            // 传递当前时间，则以传递的当前时间为准(测试使用)
            if (Objects.nonNull(param.getCurrentDate())) {
                date = param.getCurrentDate();
                XxlJobLogger.log("XXL-JOB,  {} 当前国家:{} 传递的当前时间:{}", handler, country, date);
            }
            // 传递日期则以传递的日期为考勤日，不传递日期则以当前日期得前一天当作考勤日，通知的是前一天的异常
            if (Objects.isNull(attendanceDayId) || attendanceDayId <= 0) {
                attendanceDayId = Long.valueOf(DateUtil.format(DateUtil.offsetDay(date, -1), "yyyyMMdd"));
                XxlJobLogger.log("XXL-JOB,  {} 当前国家:{} 考勤日:{}", handler, country, attendanceDayId);
            }

            EmployeeAbnormalAttendancePageQuery abnormalQuery = EmployeeAbnormalAttendancePageQuery.builder()
                    .statusList(Collections.singletonList(AbnormalAttendanceStatusEnum.UN_PROCESSED.getCode()))
                    .locationCountry(country)
                    .dayId(attendanceDayId)
                    .employeeTypeList(EmploymentTypeEnum.TYPE_OF_ATTENDANCE_REMIND_EMPLOYEE_TYPE)
                    .build();

            if (ObjectUtil.isNotEmpty(param.getUserCodeList())) {
                List<String> userCodeList = Arrays.asList(param.getUserCodeList().split(BusinessConstant.DEFAULT_DELIMITER));
                List<Long> userIdList = userInfoDao.listByUserCodes(userCodeList)
                        .stream().filter(user -> Objects.equals(BusinessConstant.N, user.getIsDriver())
                                && EmploymentTypeEnum.TYPE_OF_ATTENDANCE_REMIND_EMPLOYEE_TYPE.contains(user.getEmployeeType())).map(UserInfoDO::getId).collect(Collectors.toList());
                abnormalQuery.setUserIds(userIdList);
            }
            int currentPage = 1;
            int pageSize = 1000;
            Page<EmployeeAbnormalAttendanceDTO> page = PageHelper.startPage(currentPage, pageSize, true);
            PageInfo<EmployeeAbnormalAttendanceDTO> pageInfo = page.doSelectPageInfo(() -> employeeAbnormalAttendanceDao.list(abnormalQuery));
            // 总记录数
            List<EmployeeAbnormalAttendanceDTO> pageUserInfoList = pageInfo.getList();
            if (CollectionUtils.isNotEmpty(pageUserInfoList)) {
                log.info("XXL-JOB: {},country: {},pageUserInfoList size:{}，pageUserInfoList：{}", handler, country, pageUserInfoList.size(), JSON.toJSONString(pageUserInfoList));
                sendAttendanceAbnormalRemind(attendanceDayId, date, country, param.getSendType(), handler, pageUserInfoList);
            }
            log.info("XXL-JOB: {},country：{},pageUserInfoHandle | currentPage:{},pageSize:{},total:{}", handler, country, currentPage, pageSize, pageInfo.getTotal());
            while (currentPage < pageInfo.getPages()) {
                log.info("XXL-JOB: {},country：{},进入while循环", handler, country);
                currentPage++;
                log.info("XXL-JOB: {},country：{},currentPage：{}，pages：{}", handler, country, currentPage, pageInfo.getPages());
                page = PageHelper.startPage(currentPage, pageSize, true);
                pageInfo = page.doSelectPageInfo(() -> employeeAbnormalAttendanceDao.list(abnormalQuery));
                pageUserInfoList = pageInfo.getList();
                if (CollectionUtils.isNotEmpty(pageUserInfoList)) {
                    log.info("XXL-JOB: {}, country：{},,while循环：pageUserInfoList size:{}，pageUserInfoList：{}", handler, country, pageUserInfoList.size(), JSON.toJSONString(pageUserInfoList));
                    sendAttendanceAbnormalRemind(attendanceDayId, date, country, param.getSendType(), handler, pageUserInfoList);
                }
                log.info("XXL-JOB: {},country：{},while循环：pageUserInfoHandle | currentPage:{},pageSize:{},total:{}", handler, country, currentPage, pageSize, pageInfo.getTotal());
                log.info("XXL-JOB: {},country：{},currentPage {}，while循环结束", handler, country, currentPage);
            }
        }
    }


    private void sendAttendanceAbnormalRemind(Long attendanceDayId,
                                              Date currentDate,
                                              String country,
                                              Integer sendType,
                                              String handler,
                                              List<EmployeeAbnormalAttendanceDTO> abnormalAttendanceList) {
        abnormalAttendanceList = abnormalAttendanceList.stream().filter(abnormal -> userService.checkGrayscaleRange(abnormal.getUserId())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(abnormalAttendanceList)) {
            return;
        }
        //过滤补时长和无排班计划无需推送异常提醒
        abnormalAttendanceList = abnormalAttendanceList.stream()
                .filter(abnormal -> !MISMATCH_ABNORMAL_TYPE.contains(abnormal.getAbnormalType()) && Objects.nonNull(abnormal.getPunchClassItemConfigId()))
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(abnormalAttendanceList)) {
            return;
        }

        List<Long> punchConfigIdList = abnormalAttendanceList.stream().map(EmployeeAbnormalAttendanceDTO::getPunchConfigId).distinct().collect(Collectors.toList());
        List<Long> classIdList = abnormalAttendanceList.stream().map(EmployeeAbnormalAttendanceDTO::getPunchClassConfigId).distinct().collect(Collectors.toList());

        //查询打卡规则、班次规则
        Map<Long, PunchConfigDO> punchConfigMap = punchConfigDao.listByConfigIds(punchConfigIdList).stream().collect(Collectors.toMap(PunchConfigDO::getId, Function.identity()));
        List<PunchClassItemConfigDO> punchClassItemConfigDOList = punchClassItemConfigDao.selectByClassIds(classIdList);
        Map<Long, PunchClassItemConfigDO> classItemConfigMap = punchClassItemConfigDOList.stream().collect(Collectors.toMap(PunchClassItemConfigDO::getId, Function.identity()));
        Map<Long, List<PunchClassItemConfigDO>> groupClassMap = punchClassItemConfigDOList.stream().collect(Collectors.groupingBy(PunchClassItemConfigDO::getPunchClassId));

        Map<String, List<EmployeeAbnormalAttendanceDTO>> userAbnormalMap = abnormalAttendanceList.stream().collect(Collectors.groupingBy(EmployeeAbnormalAttendanceDTO::getUserCode));

        //待发送异常提醒的用户集合
        Set<String> waitSendRemindUserCodeList = new HashSet<>();
        //防止重复计算过滤的Key
        Set<String> filterkey = new HashSet<>();
        for (Map.Entry<String, List<EmployeeAbnormalAttendanceDTO>> entry : userAbnormalMap.entrySet()) {
            List<EmployeeAbnormalAttendanceDTO> abnormalAttendanceDTOList = entry.getValue();
            for (EmployeeAbnormalAttendanceDTO abnormalAttendanceDTO : abnormalAttendanceDTOList) {
                PunchClassItemConfigDO punchClassItemConfigDO = classItemConfigMap.get(abnormalAttendanceDTO.getPunchClassItemConfigId());
                PunchConfigDO punchConfigDO = punchConfigMap.get(abnormalAttendanceDTO.getPunchConfigId());
                List<PunchClassItemConfigDO> classItemConfigDOList = groupClassMap.get(abnormalAttendanceDTO.getPunchClassConfigId());
                if (Objects.isNull(punchConfigDO) || Objects.isNull(punchClassItemConfigDO) || CollectionUtils.isEmpty(classItemConfigDOList)) {
                    continue;
                }
                String combinationKey = buildCombinationKey(punchConfigDO.getId(), punchClassItemConfigDO.getId());
                if (filterkey.contains(combinationKey)) {
                    waitSendRemindUserCodeList.add(entry.getKey());
                    continue;
                }
                if (PunchConfigTypeEnum.isFlexibleWork(punchConfigDO.getConfigType())) {
                    if (classItemConfigDOList.size() > 1) {
                        log.error("异常提醒,用户：{} 打卡规则跟班次时段不匹配", entry.getKey());
                        continue;
                    }
                }
                DayPunchTimeDTO dayPunchTimeDTO;
                if (PunchConfigTypeEnum.isFlexibleWorkTwice(punchConfigDO.getConfigType())) {
                    dayPunchTimeDTO = punchClassConfigQueryService.getUserFreeWorkPunchClassItemDayTime(attendanceDayId, punchClassItemConfigDO);
                } else {
                    dayPunchTimeDTO = punchClassConfigQueryService.getUserPunchClassItemDayTime(attendanceDayId, punchClassItemConfigDO.getId(), classItemConfigDOList);
                }

                if (Objects.isNull(dayPunchTimeDTO)) {
                    continue;
                }
                if (currentDate.compareTo(dayPunchTimeDTO.getDayPunchStartTime()) >= 0) {
                    // 班次最晚下班时间
                    Date dayPunchEndTime = dayPunchTimeDTO.getDayPunchEndTime();
                    BigDecimal addMinutes = BigDecimal.valueOf(3).multiply(new BigDecimal(60));
                    DateTime abnormalPunchOutTime = DateUtil.offsetMinute(dayPunchEndTime, addMinutes.intValue());
                    // 判断当前时间是否大于等于最晚下班时间+3小时
                    if (currentDate.compareTo(abnormalPunchOutTime) >= 0) {
                        //最后一个时段满足了，则当天所有时段都满足
                        waitSendRemindUserCodeList.add(entry.getKey());
                        filterkey.add(combinationKey);
                    }
                }
            }

        }

        if (CollectionUtils.isEmpty(waitSendRemindUserCodeList)) {
            XxlJobLogger.log("XXL-JOB,  {} 当前国家:{} 考勤日:{} 无异常考勤用户", handler, country, attendanceDayId);
            return;
        }

        List<String> userCodeList = new ArrayList<>(waitSendRemindUserCodeList);
        //查询用户国籍
        Map<String, String> userMap = userInfoDao.listByUserCodes(userCodeList).stream().collect(Collectors.toMap(UserInfoDO::getUserCode, UserInfoDO::getCountryCode));

        //查询打卡记录
        EmployeePunchCardRecordQuery punchQuery = EmployeePunchCardRecordQuery.builder()
                .dayId(String.valueOf(attendanceDayId))
                .userCodes(userCodeList)
                .build();
        List<EmployeePunchRecordDO> punchRecordList = employeePunchRecordDao.listRecords(punchQuery);
        Map<String, List<EmployeePunchRecordDO>> userRecordMap = punchRecordList.stream().collect(Collectors.groupingBy(EmployeePunchRecordDO::getUserCode));

        //遍历用户 计算打卡次数 发送异常提醒
        for (String userCode : waitSendRemindUserCodeList) {
            // 用户打卡记录
            List<EmployeePunchRecordDO> userPunchRecordList = userRecordMap.get(userCode);
            // 用户考勤异常数据
            List<EmployeeAbnormalAttendanceDTO> userAbnormalAttendanceList = userAbnormalMap.get(userCode);
            // 封装消息传参 message
            String userCountryCode = userMap.get(userCode);
            AbnormalRemindTemplateDTO message = buildMessage(attendanceDayId, userCode, userCountryCode, userPunchRecordList, userAbnormalAttendanceList,
                    punchConfigMap, classItemConfigMap, groupClassMap);
            if (Objects.isNull(message)) {
                XxlJobLogger.log("XXL-JOB,  {} 当前国家:{} 考勤日:{} userCode:{} abnormalUserMap:{} 封装Message异常"
                        , handler, country, attendanceDayId, userCode, userAbnormalAttendanceList);
                continue;
            }
            // 自动发送需要判断用户今天是否发送过异常提醒
            if (BusinessConstant.N.equals(sendType)) {
                AbnormalRemindRecordQuery recordQuery = AbnormalRemindRecordQuery.builder()
                        .userCode(userCode)
                        .dayId(attendanceDayId)
                        .sendStatus(ReturnT.SUCCESS_CODE)
                        .build();
                List<AttendanceAbnormalRemindRecordDO> abnormalRemindRecordList = abnormalRemindRecordService.listOnly(recordQuery);
                if (CollectionUtils.isNotEmpty(abnormalRemindRecordList)) {
                    XxlJobLogger.log("XXL-JOB,  {}, userCode:{}, 当前国家:{}, 考勤日:{} 今日已发送成功异常消息提醒"
                            , BusinessConstant.JobHandler.ABNORMAL_ATTENDANCE_DAY_REMIND_HANDLER, userCode, country, attendanceDayId);
                    continue;
                }
            }
            // 发送企业微信异常提醒
            String templateName = TEMPLATE_NAME_EN;
            if (StringUtils.isNotBlank(userCountryCode) && "CN".equals(userCountryCode)) {
                templateName = TEMPLATE_NAME;
            }
            ReturnT returnMsg = sendClockMsg(message, Lists.newArrayList(userCode), templateName);
            // 发送完毕 新增异常提醒消息发送记录
            try {
                List<Long> abnormalIds = userAbnormalAttendanceList.stream().map(EmployeeAbnormalAttendanceDTO::getId).collect(Collectors.toList());
                List<Long> punchClassConfigIds = userAbnormalAttendanceList.stream().map(EmployeeAbnormalAttendanceDTO::getPunchClassConfigId).collect(Collectors.toList());
                List<Long> punchClassItemConfigIds = userAbnormalAttendanceList.stream().map(EmployeeAbnormalAttendanceDTO::getPunchClassItemConfigId).collect(Collectors.toList());
                AbnormalRemindRecordAddParam addParam = AbnormalRemindRecordAddParam.builder()
                        .userCode(userCode)
                        .dayId(attendanceDayId)
                        .abnormalType(StringUtils.join(abnormalIds, ","))
                        .punchClassConfigId(punchClassConfigIds.get(0))
                        .punchClassItemConfigId(StringUtils.join(punchClassItemConfigIds, ","))
                        .sendStatus(returnMsg.getCode())
                        .sendType(Objects.isNull(sendType) ? BusinessConstant.N : sendType)
                        .sendMsg(returnMsg.getMsg()).build();
                abnormalRemindRecordService.add(addParam);
            } catch (Exception e) {
                log.info("addAbnormalRemindRecord Error, userCode:{}, abnormalDate", userCodeList.get(0), message.getAbnormalDate());
            }
        }
    }

    private AbnormalRemindTemplateDTO buildMessage(Long dayId,
                                                   String userCode,
                                                   String countryCode,
                                                   List<EmployeePunchRecordDO> userPunchRecordList,
                                                   List<EmployeeAbnormalAttendanceDTO> userAbnormalList,
                                                   Map<Long, PunchConfigDO> punchConfigMap,
                                                   Map<Long, PunchClassItemConfigDO> classItemMap,
                                                   Map<Long, List<PunchClassItemConfigDO>> groupClassMap) {
        if (CollectionUtils.isEmpty(userAbnormalList)
                || MapUtils.isEmpty(classItemMap)
                || Objects.isNull(dayId)
                || MapUtils.isEmpty(punchConfigMap)) {
            return null;
        }
        userAbnormalList.sort(Comparator.comparing(EmployeeAbnormalAttendanceDTO::getId));
        //异常类型
        StringBuilder abnormalType = new StringBuilder();
        //根据时段分组异常数据
        Map<Long, List<EmployeeAbnormalAttendanceDTO>> itemListMap = userAbnormalList.stream()
                .collect(Collectors.groupingBy(EmployeeAbnormalAttendanceDTO::getPunchClassItemConfigId));
        // 计算打卡次数
        int punchTimes = CollectionUtils.isEmpty(userPunchRecordList) ? BusinessConstant.ZERO : userPunchRecordList.size();
        // 计算打卡间隔时长
        Long itemConfigId = userAbnormalList.get(0).getPunchClassItemConfigId();
        PunchClassItemConfigDO itemConfigDO = classItemMap.get(itemConfigId);
        if (Objects.isNull(itemConfigDO)) {
            return null;
        }
        PunchConfigDO punchConfigDO = punchConfigMap.get(userAbnormalList.get(0).getPunchConfigId());
        if (Objects.isNull(punchConfigDO)) {
            return null;
        }
        // 找出当前用户对应班次得所有时段，计算打卡时间
        List<PunchClassItemConfigDO> userClassItemConfigList = groupClassMap.get(itemConfigDO.getPunchClassId());

        BigDecimal actualHours = calcPunchHours(dayId, punchConfigDO, userPunchRecordList, userClassItemConfigList);

        List<Long> itemClassId = new ArrayList<>();
        for (EmployeeAbnormalAttendanceDTO abnormalAttendanceDTO : userAbnormalList) {
            Long punchClassItemConfigId = abnormalAttendanceDTO.getPunchClassItemConfigId();
            if (Objects.isNull(punchClassItemConfigId)) {
                continue;
            }
            //判断是否同一时段
            if (itemClassId.contains(punchClassItemConfigId)) {
                continue;
            }
            PunchClassItemConfigDO classItemConfigDO = classItemMap.get(punchClassItemConfigId);
            if (Objects.isNull(classItemConfigDO)) {
                continue;
            }
            List<EmployeeAbnormalAttendanceDTO> abnormalAttendanceList = itemListMap.get(punchClassItemConfigId);
            if (CollectionUtils.isEmpty(abnormalAttendanceList)) {
                continue;
            }
            // 异常翻译
            for (EmployeeAbnormalAttendanceDTO attendanceDTO : abnormalAttendanceList) {
                AttendanceAbnormalTypeEnum abnormalTypeEnum = AttendanceAbnormalTypeEnum.getInstanceByCode(attendanceDTO.getAbnormalType());
                if (StringUtils.isNotBlank(countryCode) && "CN".equals(countryCode)) {
                    attendanceDTO.setAbnormalTypeDesc(abnormalTypeEnum.getDesc());
                } else {
                    attendanceDTO.setAbnormalTypeDesc(abnormalTypeEnum.getDescEn());
                }
            }

            //拼接异常类型字符串格式
            Date begainDate = Objects.isNull(itemConfigDO.getPunchInTime()) ? itemConfigDO.getEarliestPunchInTime() : itemConfigDO.getPunchInTime();
            Date latestDate = Objects.isNull(itemConfigDO.getPunchOutTime()) ? itemConfigDO.getLatestPunchOutTime() : itemConfigDO.getPunchOutTime();
            abnormalType.append(DateUtil.format(begainDate, "HH:mm"));
            abnormalType.append("-");
            abnormalType.append(DateUtil.format(latestDate, "HH:mm"));
            abnormalType.append(":");
            List<String> abnormalTypeList = abnormalAttendanceList.stream().map(EmployeeAbnormalAttendanceDTO::getAbnormalTypeDesc).collect(Collectors.toList());
            abnormalType.append(StringUtils.join(abnormalTypeList, ","));
            abnormalType.append(";");
            abnormalType.append("\n");
            itemClassId.add(punchClassItemConfigId);
        }
        //没有查到异常
        if (abnormalType.length() == 0) {
            return null;
        }
        DateDO dateByDayId = DateHelper.getDateByDayId(dayId);
        //异常日期
        String abnormalDate = dateByDayId.getYear() + "年" + dateByDayId.getMonth() + "月" + dateByDayId.getDay() + "日";
        //打卡统计
        String statics = "打卡" + punchTimes + " 次, 工作时长" + actualHours + " 小时";
        if (!"CN".equals(countryCode)) {
            SimpleDateFormat dateFormat = new SimpleDateFormat("MMMM dd, yyyy", Locale.ENGLISH);
            abnormalDate = dateFormat.format(dateByDayId.getDate());
            statics = "Punched in " + punchTimes + " times, work duration " + actualHours + " hours";
        }
        //任务id
        String taskId = System.currentTimeMillis() + "_" + userCode;
        AbnormalRemindTemplateDTO message = AbnormalRemindTemplateDTO.builder()
                .abnormalDate(abnormalDate)
                .abnormalType(abnormalType.toString())
                .statics(statics)
                .taskId(taskId)
                .buttonUrl(getAddress() + dayId).build();

        return message;
    }

    /**
     * 计算上下班打卡间隔时长
     *
     * @param dayId               考勤日
     * @param punchConfigDO       打卡规则
     * @param userPunchRecordList 打卡记录
     * @param itemConfigDOList    班次时段
     * @return 上下班打卡间隔时长
     */
    private BigDecimal calcPunchHours(Long dayId,
                                      PunchConfigDO punchConfigDO,
                                      List<EmployeePunchRecordDO> userPunchRecordList,
                                      List<PunchClassItemConfigDO> itemConfigDOList) {

        if (CollectionUtils.isEmpty(userPunchRecordList) || CollectionUtils.isEmpty(itemConfigDOList) || userPunchRecordList.size() == 1) {
            return BigDecimal.ZERO;
        }
        //遍历每个时段，计算打卡时间
        BigDecimal diffHour = BigDecimal.ZERO;
        //忽略秒
        for (EmployeePunchRecordDO punchRecord : userPunchRecordList) {
            punchRecord.setPunchTime(DateHelper.parseIgnoreSeconds(punchRecord.getPunchTime()));
        }
        // 转换班次时段时间为具体的年月日+时分秒
        punchClassConfigQueryService.transferItemConfigTimeFormat(itemConfigDOList, dayId);

        for (PunchClassItemConfigDO itemConfigDO : itemConfigDOList) {
            List<EmployeePunchRecordDO> punchRecordOnTime = userPunchRecordList
                    .stream()
                    .filter(e -> e.getPunchTime().compareTo(itemConfigDO.getEarliestPunchInTime()) >= 0
                            && e.getPunchTime().compareTo(itemConfigDO.getLatestPunchOutTime()) <= 0)
                    .sorted(Comparator.comparing(EmployeePunchRecordDO::getPunchTime))
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(punchRecordOnTime)) {
                continue;
            }
            //灵活打卡
            if (PunchConfigTypeEnum.isFlexibleWork(punchConfigDO.getConfigType())) {
                //计算间隔时间
                diffHour = calculateWorkingHours(itemConfigDO,
                        punchRecordOnTime.get(0).getPunchTime(),
                        punchRecordOnTime.get(punchRecordOnTime.size() - 1).getPunchTime(), true);
                break;
            }
            //固班班次
            //计算间隔时间
            if (punchRecordOnTime.size() > 1) {
                diffHour = diffHour.add(calculateWorkingHours(itemConfigDO,
                        punchRecordOnTime.get(0).getPunchTime(),
                        punchRecordOnTime.get(punchRecordOnTime.size() - 1).getPunchTime(), false));
            }
        }
        return diffHour;
    }

    public ReturnT sendClockMsg(AbnormalRemindTemplateDTO message,
                                List<String> userCodeList,
                                String templateName) {
        List<String> wxUserIds = platformRelationClientSupport.listPlatFormRelation(userCodeList, BizTypeEnum.USER.name(), null, PlatFormTypeEnum.WECHAT_WORK.name())
                .stream().map(PlatformRelationApiDTO::getRelationId).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(wxUserIds)) {
            log.info("sendClockMsg: WeChat relation not exist, userCodes:{}", userCodeList);
            ReturnT returnT = new ReturnT();
            returnT.setMsg("dont have wechat userId");
            returnT.setCode(ReturnT.FAIL_CODE);
            return returnT;
        }
        AppMessageParam param = new AppMessageParam();
        param.setAppId(IMILE_CLOCK_APP_ID);
        param.setSecret(IMILE_CLOCK_SECRET);
        param.setMsgtype("template_card");
        param.setAgentId(IMILE_CLOCK_AGENT_ID);
        param.setNeedTemplate(true);
        param.setToUser(wxUserIds);
        param.setTemplateName(templateName);
        param.setContent(JSON.toJSONString(message));
        try {
            ResponseEntity<Object> responseEntity = sendAppMessage(param);
            Map body = (Map) responseEntity.getBody();
            ReturnT returnT = new ReturnT();
            if (Objects.isNull(body.get("status"))) {
                // 发送错误消息
                if (Objects.isNull(body.get("msg"))) {
                    returnT.setMsg(ReturnT.FAIL.getMsg());
                    returnT.setCode(ReturnT.FAIL_CODE);
                    return returnT;
                }
                returnT.setMsg(String.valueOf(body.get("msg")));
                returnT.setCode(Integer.valueOf(String.valueOf(body.get("code"))));
                return returnT;
            }
            returnT.setMsg(String.valueOf(body.get("status")));
            returnT.setCode(ReturnT.SUCCESS_CODE);
            return returnT;
        } catch (Exception e) {
            log.info("sendClockMsg Exception, userCode:{}, abnormalDate", userCodeList.get(0), message.getAbnormalDate());
            throw new RuntimeException(e);
        }
    }

    /**
     * 通过应用给人发送消息
     *
     * @param param
     * @return 响应结果
     */
    public static ResponseEntity<Object> sendAppMessage(AppMessageParam param) throws ServiceException {
        try {
            RestTemplate restTemplate = new RestTemplate();

            // 发送应用消息url
            String url = "/outward/send-app-message";

            // 请求地址
            String requestUrl = "https://imc.imile-inc.com/prod-api" + url;

            // 设置请求头，固定为application/x-www-form-urlencoded
            HttpHeaders headers = new HttpHeaders();
            MediaType type = MediaType.parseMediaType("application/x-www-form-urlencoded; charset=UTF-8");
            headers.setContentType(type);

            MultiValueMap<String, String> parameterMap = objectToMap(param);

            // 发送请求
            HttpEntity<MultiValueMap<String, String>> httpEntity = new HttpEntity<>(parameterMap, headers);
            ResponseEntity<Object> response = restTemplate.postForEntity(requestUrl, httpEntity, Object.class);
            return response;
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }

    /**
     * 对象转化为Map
     *
     * @param obj
     * @return
     * @throws Exception
     */
    public static MultiValueMap<String, String> objectToMap(Object obj) throws Exception {
        if (obj == null) {
            return null;
        }
        MultiValueMap<String, String> map = new LinkedMultiValueMap<>();

        Field[] declaredFields = obj.getClass().getDeclaredFields();
        for (Field field : declaredFields) {
            field.setAccessible(true);
            if (null != field.get(obj)) {
                map.add(field.getName(), String.valueOf(field.get(obj)));
            }
        }

        return map;
    }

    private String getAddress() {
        String address = BUTTON_URL;
        if (EnvEnum.DEV.getCode().equals(env) || EnvEnum.TEST.getCode().equals(env)) {
            address = BUTTON_URL_TEST;
        }
        return address;
    }


    /**
     * 打卡规则凭借班次时段构成组合Key
     *
     * @param punchConfigId 打卡规则ID
     * @param classItemId   班次时段ID
     * @return 组合Key
     */
    private String buildCombinationKey(Long punchConfigId, Long classItemId) {
        return punchConfigId + "_" + classItemId;
    }


    /**
     * 计算工作时长
     * @param itemConfig
     * @param punchInRecord
     * @param punchOutRecord
     * @return
     */
    private BigDecimal calculateWorkingHours(PunchClassItemConfigDO itemConfig,
                                             Date punchInRecord,
                                             Date punchOutRecord,
                                             Boolean isFreeWork) {
        // 获取班次时段配置
        Date punchInTime = itemConfig.getPunchInTime();
        Date punchOutTime = itemConfig.getPunchOutTime();
        Date restStartTime = itemConfig.getRestStartTime();
        Date restEndTime = itemConfig.getRestEndTime();

        // 1. 计算打卡时间与班次时间的交集
        Date workStart;
        Date workEnd;

        // 自由打卡只参照最早最晚打卡时间
        if (isFreeWork) {
            workStart = punchInRecord;
            workEnd = punchOutRecord;
        } else {
            workStart = DateHelper.max(punchInRecord, punchInTime);
            workEnd = DateHelper.min(punchOutRecord, punchOutTime);
        }

        // 检查有效工作时间段
        if (workStart.after(workEnd)) {
            return BigDecimal.ZERO; // 无重叠
        }

        // 2. 计算实际工作毫秒数
        long workMillis = workEnd.getTime() - workStart.getTime();

        // 3. 扣除休息时间重叠部分
        long restOverlapMillis = calculateRestOverlap(workStart, workEnd, restStartTime, restEndTime);
        long netWorkMillis = workMillis - restOverlapMillis;

        // 确保非负值
        if (netWorkMillis < 0) {
            netWorkMillis = 0;
        }

        // 4. 转换为小时并格式化为BigDecimal
        return millisecondsToHoursDecimal(netWorkMillis);
    }

    // 计算休息时间与工作时间的重叠
    private long calculateRestOverlap(Date workStart, Date workEnd, Date restStart, Date restEnd) {
        // 处理休息时间为空的情况
        if (restStart == null || restEnd == null) {
            return 0;
        }

        Date overlapStart = DateHelper.max(workStart, restStart);
        Date overlapEnd = DateHelper.min(workEnd, restEnd);

        if (overlapStart.after(overlapEnd)) {
            return 0; // 无重叠
        }
        return overlapEnd.getTime() - overlapStart.getTime();
    }

    // 毫秒转小时（BigDecimal类型，保留2位小数）
    private BigDecimal millisecondsToHoursDecimal(long millis) {
        // 毫秒转小时（1小时 = 3,600,000毫秒）
        BigDecimal hours = new BigDecimal(millis)
                .divide(BigDecimal.valueOf(3600000), 4, RoundingMode.HALF_UP);

        // 保留两位小数
        return hours.setScale(2, RoundingMode.HALF_UP);
    }

}
