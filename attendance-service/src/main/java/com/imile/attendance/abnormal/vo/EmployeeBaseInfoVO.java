package com.imile.attendance.abnormal.vo;

import com.imile.attendance.annon.WithDict;
import com.imile.attendance.constants.BusinessConstant;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @since 2025/7/23
 */
@Data
public class EmployeeBaseInfoVO {
    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 用户名称
     */
    private String userName;

    /**
     * 用户编码
     */
    private String userCode;

    /**
     * 员工类型
     */
    @WithDict(typeCode = BusinessConstant.SysDictDataTypeConstant.EMPLOYMENT_TYPE, ref = "employeeTypeDesc")
    private String employeeType;

    /**
     * 用工类型描述
     */
    private String employeeTypeDesc;

    /**
     * 用工形式
     */
    @WithDict(typeCode = "EmploymentForm", ref = "employmentFormDesc")
    private String employmentForm;

    /**
     * 用工形式描述
     */
    private String employmentFormDesc;

    /**
     * 状态
     */
    private String status;

    /**
     * 工作状态
     */
    private String workStatus;

    /**
     * 部门ID
     */
    private Long deptId;

    /**
     * 部门名称
     */
    private String deptName;

    /**
     * 岗位名称
     */
    private String postName;

    /**
     * 是否全球派遣（0:否 1:是）
     *
     */
    private Integer isGlobalRelocation;

    /**
     * 常驻地国家
     */
    private String locationCountry;

    /**
     * 常驻地省份
     */
    private String locationProvince;

    /**
     * 常驻地城市
     */
    private String locationCity;

    /**
     * 员工头像地址
     */
    private String profilePhotoUrl;

    /**
     * 入职时间
     */
    private Date entryDate;

    /**
     * 离职时间
     */
    private Date dimissionDate;

    /**
     * 工作网点ID
     */
    private Long ocId;

    /**
     * 工作网点名称
     */
    private String ocName;

    /**
     * 工作供应商ID
     */
    private Long vendorId;

    /**
     * 工作供应商名称
     */
    private String vendorName;
}
