package com.imile.attendance.abnormal.service;

import com.imile.attendance.abnormal.AttendanceCalculateContext;
import com.imile.attendance.abnormal.dto.UserAttendancePunchConfigDTO;
import com.imile.attendance.enums.rule.PunchConfigTypeEnum;

import java.util.List;

/**
 * 考勤计算
 *
 * <AUTHOR>
 * @since 2025/5/20
 */
public interface AttendanceCalculateService {

    boolean isMatch(List<UserAttendancePunchConfigDTO> userAttendancePunchConfigList, String punchConfigType, boolean isWarehouse, String employeeType);

    void execute(AttendanceCalculateContext calculateContext);
}
