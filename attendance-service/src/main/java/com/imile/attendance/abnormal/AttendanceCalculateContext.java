package com.imile.attendance.abnormal;

import com.imile.attendance.abnormal.dto.AttendanceCalculateHandlerDTO;
import com.imile.attendance.abnormal.dto.UserAttendancePunchConfigDTO;
import com.imile.attendance.enums.rule.PunchConfigTypeEnum;
import com.imile.attendance.form.bo.AttendanceFormDetailBO;
import com.imile.attendance.infrastructure.repository.abnormal.model.AttendanceEmployeeDetailDO;
import com.imile.attendance.infrastructure.repository.abnormal.model.EmployeeAbnormalAttendanceDO;
import com.imile.attendance.infrastructure.repository.calendar.model.CalendarConfigDO;
import com.imile.attendance.infrastructure.repository.employee.modle.UserInfoDO;
import com.imile.attendance.infrastructure.repository.employee.modle.UserLeaveDetailDO;
import com.imile.attendance.infrastructure.repository.employee.modle.UserLeaveStageDetailDO;
import com.imile.attendance.infrastructure.repository.rule.dto.PunchClassConfigDTO;
import com.imile.attendance.infrastructure.repository.rule.model.PunchConfigDO;
import com.imile.attendance.infrastructure.repository.shift.model.UserShiftConfigDO;
import com.imile.attendance.infrastructure.repository.vacation.model.CompanyLeaveConfigDO;
import com.imile.attendance.infrastructure.repository.warehouse.model.WarehouseAttendanceConfigDO;
import com.imile.attendance.punch.bo.UserPunchRecordBO;
import lombok.Data;

import java.util.List;

/**
 * 考勤计算上下文
 *
 * <AUTHOR>
 * @since 2025/5/20
 */
@Data
public class AttendanceCalculateContext {

    /**
     * 方法入参
     */
    private AttendanceCalculateHandlerDTO calculateHandlerDTO;

    /**
     * 出勤类型
     * com.imile.attendance.enums.AttendanceDayTypeEnum
     */
    private String attendanceType;

    /**
     * 用户
     */
    private UserInfoDO user;

    /**
     * 日历
     */
    private CalendarConfigDO calendarConfigDO;

    /**
     * 打卡规则
     */
    private PunchConfigDO punchConfigDO;

    /**
     * 排班计划
     */
    private List<UserShiftConfigDO> userShiftConfigList;

    /**
     * 用户打卡配置信息
     */
    private List<UserAttendancePunchConfigDTO> userAttendancePunchConfigDTOList;

    /**
     * 正常出勤
     */
    private List<AttendanceEmployeeDetailDO> attendanceEmployeeDetailDOList;

    /**
     * 用户当天未处理的异常考勤数据(待处理/审核中)
     */
    private List<EmployeeAbnormalAttendanceDO> userAbnormalAttendanceDOList;

    /**
     * 打卡记录
     */
    private List<UserPunchRecordBO> punchRecordDOList;

    /**
     * 用户假期详情
     */
    private List<UserLeaveDetailDO> userLeaveDetailDOList;

    /**
     * 用户假期余额
     */
    private List<UserLeaveStageDetailDO> userLeaveStageDetailDOList;

    /**
     * 公司假期配置
     */
    private List<CompanyLeaveConfigDO> userCompanyLeaveConfigDOList;

    /**
     * 用户审批通过的单据
     */
    private List<AttendanceFormDetailBO> userPassFormBOList;

    /**
     * 仓内考勤规则配置
     */
    private WarehouseAttendanceConfigDO warehouseAttendanceConfigDO;
}
