package com.imile.attendance.abnormal.service.impl;

import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.imile.attendance.abnormal.AttendanceCalculateContext;
import com.imile.attendance.abnormal.dto.AbnormalExtendDTO;
import com.imile.attendance.abnormal.dto.AttendanceCalculateHandlerDTO;
import com.imile.attendance.abnormal.dto.DayAttendanceHandlerFormDTO;
import com.imile.attendance.abnormal.dto.UserAttendancePunchConfigDTO;
import com.imile.attendance.abnormal.service.AttendanceCalculateCommonService;
import com.imile.attendance.abnormal.service.AttendanceCalculateService;
import com.imile.attendance.annon.Strategy;
import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.enums.AttendanceConcreteTypeEnum;
import com.imile.attendance.enums.abnormal.AttendanceAbnormalTypeEnum;
import com.imile.attendance.enums.rule.PunchConfigTypeEnum;
import com.imile.attendance.infrastructure.repository.abnormal.model.AttendanceEmployeeDetailDO;
import com.imile.attendance.infrastructure.repository.abnormal.model.EmployeeAbnormalAttendanceDO;
import com.imile.attendance.infrastructure.repository.employee.modle.UserInfoDO;
import com.imile.attendance.infrastructure.repository.employee.modle.UserLeaveDetailDO;
import com.imile.attendance.infrastructure.repository.employee.modle.UserLeaveStageDetailDO;
import com.imile.attendance.infrastructure.repository.rule.dto.PunchClassItemConfigDTO;
import com.imile.attendance.infrastructure.repository.vacation.model.CompanyLeaveConfigDO;
import com.imile.attendance.punch.bo.UserPunchRecordBO;
import com.imile.attendance.util.DateHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 灵活打卡两次考勤计算
 *
 * <AUTHOR>
 * @since 2025/5/20
 */
@Slf4j
@Service
@Strategy(value = AttendanceCalculateService.class, implKey = "FlexibleWorkTwiceCalculateServiceImpl")
public class FlexibleWorkTwiceCalculateServiceImpl extends AttendanceCalculateCommonService implements AttendanceCalculateService {
    @Override
    public boolean isMatch(List<UserAttendancePunchConfigDTO> userAttendancePunchConfigList, String punchConfigType, boolean isWarehouse, String employeeType) {
        return CollectionUtils.isNotEmpty(userAttendancePunchConfigList)
                && Objects.equals(BusinessConstant.Y, userAttendancePunchConfigList.get(0).getIsActualPunch())
                && Objects.equals(PunchConfigTypeEnum.FLEXIBLE_WORK_TWICE.getCode(), punchConfigType);
    }

    @Override
    public void execute(AttendanceCalculateContext calculateContext) {
        UserInfoDO user = calculateContext.getUser();
        AttendanceCalculateHandlerDTO calculateHandlerDTO = calculateContext.getCalculateHandlerDTO();
        log.info("attendanceCalculate userCode:{}, date:{}, 灵活打卡两次计算", user.getUserCode(), calculateHandlerDTO.getAttendanceDayId());

        UserAttendancePunchConfigDTO userAttendancePunchConfig = calculateContext.getUserAttendancePunchConfigDTOList().get(0);
        List<PunchClassItemConfigDTO> classItemConfigList = userAttendancePunchConfig.getClassConfigDO().getClassItemConfigList();
        if (classItemConfigList.size() > 1) {
            log.info("attendanceCalculate userCode:{}, date:{} , classId:{}, 灵活打卡两次排班中的班次存在多时段", user.getUserCode(), calculateHandlerDTO.getAttendanceDayId(),
                    userAttendancePunchConfig.getClassConfigDO().getId());
            return;
        }

        //出勤时长取打开规则的上下班时间间隔时长
        BigDecimal attendanceHours = calculateContext.getPunchConfigDO().getPunchTimeInterval();
        BigDecimal attendanceMinutes = attendanceHours.multiply(BusinessConstant.MINUTES);

        //法定工作时长取班次的法定工作时长
        BigDecimal legalWorkingHours = userAttendancePunchConfig.getClassConfigDO().getLegalWorkingHours();
        BigDecimal legalWorkingMinutes = legalWorkingHours.multiply(BusinessConstant.MINUTES);

        if (attendanceHours.compareTo(userAttendancePunchConfig.getClassConfigDO().getLegalWorkingHours()) > 0) {
            log.info("attendanceCalculate userCode:{}, date:{} , classId:{}, 灵活打卡两次打卡规则中上下班间隔时长大于班次工作时长", user.getUserCode(), calculateHandlerDTO.getAttendanceDayId(),
                    userAttendancePunchConfig.getClassConfigDO().getId());
            return;
        }

        List<AttendanceEmployeeDetailDO> addEmployeeDetailDOList = new ArrayList<>();
        List<EmployeeAbnormalAttendanceDO> addAbnormalAttendanceDOList = new ArrayList<>();
        List<AttendanceEmployeeDetailDO> updateEmployeeDetailDOList = new ArrayList<>();
        List<EmployeeAbnormalAttendanceDO> updateAbnormalAttendanceDOList = new ArrayList<>();

        deletePendingAttendanceRecords(calculateContext.getAttendanceEmployeeDetailDOList(), calculateContext.getUserAbnormalAttendanceDOList(),
                updateEmployeeDetailDOList, updateAbnormalAttendanceDOList);

        Date earliestPunchInTime = classItemConfigList.get(0).getEarliestPunchInTime();
        String earliestPunchInTimeString = DateHelper.formatHHMMSS(earliestPunchInTime);
        String earliestPunchInTimeDayString = DateHelper.formatYYYYMMDD(calculateHandlerDTO.getAttendanceTime());

        calculateHandlerDTO.setActualAttendanceStartTime(DateHelper.concatDateAndTime(earliestPunchInTimeDayString, earliestPunchInTimeString));
        calculateHandlerDTO.setActualAttendanceEndTime(DateHelper.pushDate(calculateHandlerDTO.getActualAttendanceStartTime(), 1));

        List<Long> usedFormIdList = calculateContext.getAttendanceEmployeeDetailDOList().stream()
                .map(AttendanceEmployeeDetailDO::getFormId)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        //需要先将当天的所有审批通过的请假/外勤拼接起来，一个时刻可以有多个审批通过的假期
        List<DayAttendanceHandlerFormDTO> handlerFormDTOList = attendanceMinuteCalculateService.dayFormInfoBuild(calculateContext.getUserPassFormBOList(), usedFormIdList);

        List<DayAttendanceHandlerFormDTO> filterFormDTOList = new ArrayList<>();
        dayFormDateHandler(handlerFormDTOList, filterFormDTOList, calculateHandlerDTO);

        //查询当天生成的正常考勤的所有分钟(请假/外勤的 正常考勤会被删除，然后根据本次运行结果，看是否生成正常还是异常考勤)
        BigDecimal usedMinutes = calculateUsedMinutes(calculateContext.getAttendanceEmployeeDetailDOList());

        usedMinutes = punchDayLeaveHandler(usedMinutes, legalWorkingMinutes, filterFormDTOList, calculateContext, addEmployeeDetailDOList);

        //请假时间足够法定工作时长了
        if (usedMinutes.compareTo(legalWorkingMinutes) == 0) {
            attendanceEmployeeDetailManage.attendanceGenerateUpdate(addEmployeeDetailDOList, updateEmployeeDetailDOList, addAbnormalAttendanceDOList, updateAbnormalAttendanceDOList);
            return;
        }

        punchFreeDayInfoHandler(user, usedMinutes, attendanceMinutes, legalWorkingMinutes, calculateContext.getAttendanceType(),
                calculateContext.getPunchConfigDO().getId(), userAttendancePunchConfig.getClassConfigDO().getId(),
                userAttendancePunchConfig.getClassConfigDO().getClassItemConfigList().get(0).getId(), calculateContext.getPunchRecordDOList(), calculateHandlerDTO,
                addEmployeeDetailDOList, addAbnormalAttendanceDOList);

        //过滤审批中的异常
        addAbnormalAttendanceDOList = filterAbnormalAttendanceList(calculateContext.getUserAbnormalAttendanceDOList(), addAbnormalAttendanceDOList);

        attendanceEmployeeDetailManage.attendanceGenerateUpdate(addEmployeeDetailDOList, updateEmployeeDetailDOList, addAbnormalAttendanceDOList, updateAbnormalAttendanceDOList);

    }


    private void punchFreeDayInfoHandler(UserInfoDO user,
                                         BigDecimal usedMinutes,
                                         BigDecimal attendanceMinutes,
                                         BigDecimal legalWorkingMinutes,
                                         String attendanceType,
                                         Long punchConfigId,
                                         Long punchClassId,
                                         Long itemConfigId,
                                         List<UserPunchRecordBO> punchRecordDOList,
                                         AttendanceCalculateHandlerDTO calculateHandlerDTO,
                                         List<AttendanceEmployeeDetailDO> addEmployeeDetailDOList,
                                         List<EmployeeAbnormalAttendanceDO> addAbnormalAttendanceDOList) {
        //休假时间不够，需要看打卡数据
        List<UserPunchRecordBO> itemPunchRecordList = getEffectiveUserPunchRecord(calculateHandlerDTO.getActualAttendanceStartTime(),
                calculateHandlerDTO.getActualAttendanceEndTime(), punchRecordDOList);

        //没有打卡数据，直接上下班缺卡异常
        if (CollectionUtils.isEmpty(itemPunchRecordList)) {
            //2条异常，上下班都缺卡
            AbnormalExtendDTO abnormalExtendDTO = new AbnormalExtendDTO();
            abnormalExtendDTO.setCorrectPunchTime(calculateHandlerDTO.getActualAttendanceStartTime());
            EmployeeAbnormalAttendanceDO beforeLackAbnormalAttendanceDO = buildAbnormal(user, calculateHandlerDTO, AttendanceAbnormalTypeEnum.BEFORE_OFFICE_LACK.getCode(),
                    attendanceType, punchConfigId, punchClassId, itemConfigId, JSON.toJSONString(abnormalExtendDTO));
            addAbnormalAttendanceDOList.add(beforeLackAbnormalAttendanceDO);

            abnormalExtendDTO.setCorrectPunchTime(DateUtil.offsetMinute(calculateHandlerDTO.getActualAttendanceStartTime(), attendanceMinutes.intValue()));
            EmployeeAbnormalAttendanceDO afterLackAbnormalAttendanceDO = buildAbnormal(user, calculateHandlerDTO, AttendanceAbnormalTypeEnum.AFTER_OFFICE_LACK.getCode(),
                    attendanceType, punchConfigId, punchClassId, itemConfigId, JSON.toJSONString(abnormalExtendDTO));
            addAbnormalAttendanceDOList.add(afterLackAbnormalAttendanceDO);
            return;
        }

        //只有一个打卡
        if (itemPunchRecordList.size() == 1) {
            BigDecimal beforeMinutes = BigDecimal.valueOf(DateUtil.between(calculateHandlerDTO.getActualAttendanceStartTime(), itemPunchRecordList.get(0).getFormatPunchTime(), DateUnit.MINUTE));
            BigDecimal afterMinutes = BigDecimal.valueOf(DateUtil.between(calculateHandlerDTO.getActualAttendanceEndTime(), itemPunchRecordList.get(0).getFormatPunchTime(), DateUnit.MINUTE));
            if (beforeMinutes.compareTo(afterMinutes) < 1) {
                AbnormalExtendDTO abnormalExtendDTO = new AbnormalExtendDTO();
                abnormalExtendDTO.setCorrectPunchTime(DateUtil.offsetMinute(itemPunchRecordList.get(0).getFormatPunchTime(), attendanceMinutes.intValue()));
                EmployeeAbnormalAttendanceDO afterLackAbnormalAttendanceDO = buildAbnormal(user, calculateHandlerDTO, AttendanceAbnormalTypeEnum.AFTER_OFFICE_LACK.getCode(),
                        attendanceType, punchConfigId, punchClassId, itemConfigId, JSON.toJSONString(abnormalExtendDTO));
                addAbnormalAttendanceDOList.add(afterLackAbnormalAttendanceDO);
                return;
            }

            AbnormalExtendDTO abnormalExtendDTO = new AbnormalExtendDTO();
            abnormalExtendDTO.setCorrectPunchTime(DateUtil.offsetMinute(itemPunchRecordList.get(0).getFormatPunchTime(), attendanceMinutes.multiply(BigDecimal.valueOf(-1)).intValue()));
            EmployeeAbnormalAttendanceDO afterLackAbnormalAttendanceDO = buildAbnormal(user, calculateHandlerDTO, AttendanceAbnormalTypeEnum.BEFORE_OFFICE_LACK.getCode(),
                    attendanceType, punchConfigId, punchClassId, itemConfigId, JSON.toJSONString(abnormalExtendDTO));
            addAbnormalAttendanceDOList.add(afterLackAbnormalAttendanceDO);
            return;
        }

        //有多个打卡记录
        BigDecimal presentMinutes = BigDecimal.valueOf(DateUtil.between(itemPunchRecordList.get(0).getFormatPunchTime(), itemPunchRecordList.get(itemPunchRecordList.size() - 1).getFormatPunchTime(), DateUnit.MINUTE));
        if (presentMinutes.compareTo(attendanceMinutes) > -1) {
            AttendanceEmployeeDetailDO userAttendance = buildUserAttendance(user, calculateHandlerDTO, attendanceType,
                    AttendanceConcreteTypeEnum.P.getCode(), BusinessConstant.Y, null, null, null, BigDecimal.ZERO,
                    presentMinutes, BigDecimal.ZERO, null, punchConfigId);
            addEmployeeDetailDOList.add(userAttendance);
            return;
        }

        if (presentMinutes.add(usedMinutes).compareTo(legalWorkingMinutes) > -1) {
            AttendanceEmployeeDetailDO userAttendance = buildUserAttendance(user, calculateHandlerDTO, attendanceType,
                    AttendanceConcreteTypeEnum.P.getCode(), BusinessConstant.Y, null, null, null, BigDecimal.ZERO,
                    legalWorkingMinutes.subtract(usedMinutes), BigDecimal.ZERO, null, punchConfigId);
            addEmployeeDetailDOList.add(userAttendance);
            return;
        }

        BigDecimal needPresentMinutes = attendanceMinutes.subtract(presentMinutes);
        //特殊逻辑，看第一次打卡是在改天中间打卡时间的前面还是后面
        Date middleDate = DateUtil.offsetHour(calculateHandlerDTO.getActualAttendanceStartTime(), 12);
        if (itemPunchRecordList.get(0).getFormatPunchTime().compareTo(middleDate) < 1) {
            AbnormalExtendDTO abnormalExtendDTO = new AbnormalExtendDTO();
            abnormalExtendDTO.setActualPunchTime(itemPunchRecordList.get(itemPunchRecordList.size() - 1).getFormatPunchTime());
            abnormalExtendDTO.setCorrectPunchTime(DateUtil.offsetMinute(itemPunchRecordList.get(itemPunchRecordList.size() - 1).getFormatPunchTime(), needPresentMinutes.intValue()));
            EmployeeAbnormalAttendanceDO abnormalAttendanceDO = buildAbnormal(user, calculateHandlerDTO, AttendanceAbnormalTypeEnum.LEAVE_EARLY.getCode(),
                    attendanceType, punchConfigId, punchClassId, itemConfigId, JSON.toJSONString(abnormalExtendDTO));
            addAbnormalAttendanceDOList.add(abnormalAttendanceDO);
            return;
        }

        AbnormalExtendDTO abnormalExtendDTO = new AbnormalExtendDTO();
        abnormalExtendDTO.setActualPunchTime(itemPunchRecordList.get(0).getFormatPunchTime());
        abnormalExtendDTO.setCorrectPunchTime(DateUtil.offsetMinute(itemPunchRecordList.get(0).getFormatPunchTime(), needPresentMinutes.multiply(BigDecimal.valueOf(-1)).intValue()));
        EmployeeAbnormalAttendanceDO abnormalAttendanceDO = buildAbnormal(user, calculateHandlerDTO, AttendanceAbnormalTypeEnum.LATE.getCode(),
                attendanceType, punchConfigId, punchClassId, itemConfigId, JSON.toJSONString(abnormalExtendDTO));
        addAbnormalAttendanceDOList.add(abnormalAttendanceDO);
    }


    /**
     * 计算员工出勤总时长
     *
     * @param usedMinutes             出勤时长
     * @param legalWorkingMinutes     法定工作时长
     * @param filterFormDTOList       员工有效外勤或请假单据
     * @param calculateContext        计算上下文
     * @param addEmployeeDetailDOList 待新增的员工出勤名称列表
     * @return 员工总的已出勤时长
     */
    private BigDecimal punchDayLeaveHandler(BigDecimal usedMinutes,
                                            BigDecimal legalWorkingMinutes,
                                            List<DayAttendanceHandlerFormDTO> filterFormDTOList,
                                            AttendanceCalculateContext calculateContext,
                                            List<AttendanceEmployeeDetailDO> addEmployeeDetailDOList) {
        AttendanceCalculateHandlerDTO calculateHandlerDTO = calculateContext.getCalculateHandlerDTO();
        //先构建请假
        for (DayAttendanceHandlerFormDTO handlerFormDTO : filterFormDTOList) {
            BigDecimal leaveMinutes = BigDecimal.valueOf(DateUtil.between(handlerFormDTO.getStartTime(), handlerFormDTO.getEndTime(), DateUnit.MINUTE));

            if (leaveMinutes.add(usedMinutes).compareTo(legalWorkingMinutes) > -1) {
                leaveMinutes = legalWorkingMinutes.subtract(usedMinutes);
            }
            if (leaveMinutes.compareTo(BigDecimal.ZERO) < 1) {
                return usedMinutes;
            }

            usedMinutes = usedMinutes.add(leaveMinutes);

            //看是不是外勤
            if (StringUtils.isBlank(handlerFormDTO.getLeaveType())) {
                AttendanceEmployeeDetailDO userAttendance = buildUserAttendance(calculateContext.getUser(), calculateHandlerDTO, calculateContext.getAttendanceType(),
                        AttendanceConcreteTypeEnum.OOO.getCode(), BusinessConstant.Y, null, null, null, BigDecimal.ZERO, leaveMinutes,
                        BigDecimal.ZERO, handlerFormDTO.getFormId(), null);
                addEmployeeDetailDOList.add(userAttendance);
                continue;
            }
            //请假
            List<CompanyLeaveConfigDO> companyLeaveList;
            List<UserLeaveDetailDO> leaveDetailList;
            if (Objects.nonNull(handlerFormDTO.getConfigId())) {
                companyLeaveList = calculateContext.getUserCompanyLeaveConfigDOList().stream()
                        .filter(item -> item.getId().equals(handlerFormDTO.getConfigId()))
                        .collect(Collectors.toList());
                leaveDetailList = calculateContext.getUserLeaveDetailDOList().stream()
                        .filter(item -> item.getConfigId().equals(handlerFormDTO.getConfigId()))
                        .collect(Collectors.toList());
            } else {
                companyLeaveList = calculateContext.getUserCompanyLeaveConfigDOList().stream()
                        .filter(item -> StringUtils.equalsIgnoreCase(item.getLeaveName(), handlerFormDTO.getLeaveType()))
                        .collect(Collectors.toList());
                leaveDetailList = calculateContext.getUserLeaveDetailDOList().stream()
                        .filter(item -> StringUtils.equalsIgnoreCase(item.getLeaveName(), handlerFormDTO.getLeaveType()))
                        .collect(Collectors.toList());
            }
            if (CollectionUtils.isEmpty(companyLeaveList)) {
                return usedMinutes;
            }
            if (CollectionUtils.isEmpty(leaveDetailList)) {
                return usedMinutes;
            }
            List<Long> leaveIdList = leaveDetailList.stream().map(UserLeaveDetailDO::getId).collect(Collectors.toList());
            //根据百分比降序，优先使用百分比最高的
            List<UserLeaveStageDetailDO> leaveStageDetailDOList = calculateContext.getUserLeaveStageDetailDOList().stream()
                    .filter(item -> leaveIdList.contains(item.getLeaveId()))
                    .sorted(Comparator.comparing(UserLeaveStageDetailDO::getPercentSalary).reversed())
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(leaveStageDetailDOList)) {
                return usedMinutes;
            }
            leaveInfoBuild(calculateContext.getUser(), calculateHandlerDTO, handlerFormDTO.getFormId(), companyLeaveList.get(0), leaveStageDetailDOList,
                    leaveMinutes, calculateContext.getAttendanceType(), addEmployeeDetailDOList);
        }
        return usedMinutes;
    }
}
