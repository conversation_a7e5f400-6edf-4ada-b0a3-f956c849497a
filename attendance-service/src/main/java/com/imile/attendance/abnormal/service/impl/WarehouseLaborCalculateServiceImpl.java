package com.imile.attendance.abnormal.service.impl;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.imile.attendance.abnormal.AttendanceCalculateContext;
import com.imile.attendance.abnormal.AttendanceMinuteCalculateService;
import com.imile.attendance.abnormal.dto.AbnormalExtendDTO;
import com.imile.attendance.abnormal.dto.AttendanceCalculateHandlerDTO;
import com.imile.attendance.abnormal.dto.DayItemConfigDateDTO;
import com.imile.attendance.abnormal.dto.UserAttendancePunchConfigDTO;
import com.imile.attendance.abnormal.service.AttendanceCalculateCommonService;
import com.imile.attendance.abnormal.service.AttendanceCalculateService;
import com.imile.attendance.annon.Strategy;
import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.enums.AttendanceConcreteTypeEnum;
import com.imile.attendance.enums.EmploymentTypeEnum;
import com.imile.attendance.enums.abnormal.AttendanceAbnormalTypeEnum;
import com.imile.attendance.enums.rule.PunchConfigTypeEnum;
import com.imile.attendance.infrastructure.repository.abnormal.model.AttendanceEmployeeDetailDO;
import com.imile.attendance.infrastructure.repository.abnormal.model.EmployeeAbnormalAttendanceDO;
import com.imile.attendance.infrastructure.repository.employee.modle.UserInfoDO;
import com.imile.attendance.infrastructure.repository.rule.model.PunchClassItemConfigDO;
import com.imile.attendance.infrastructure.repository.warehouse.model.WarehouseAttendanceConfigDO;
import com.imile.attendance.punch.bo.UserPunchRecordBO;
import com.imile.attendance.rule.mapstruct.PunchClassItemConfigMapstruct;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * 仓内劳务员工多班次打卡考勤计算
 *
 * <AUTHOR>
 * @since 2025/7/21
 */
@Slf4j
@Service("warehouseLaborCalculateServiceImpl")
@Strategy(value = AttendanceCalculateService.class, implKey = "WarehouseLaborCalculateServiceImpl")
public class WarehouseLaborCalculateServiceImpl extends AttendanceCalculateCommonService implements AttendanceCalculateService {
    @Resource
    private AttendanceMinuteCalculateService attendanceMinuteCalculateService;


    @Override
    public boolean isMatch(List<UserAttendancePunchConfigDTO> userAttendancePunchConfigList, String punchConfigType, boolean isWarehouse, String employeeType) {
        return isWarehouse
                && EmploymentTypeEnum.OS_FIXED_SALARY.getCode().equals(employeeType)
                && CollectionUtils.isNotEmpty(userAttendancePunchConfigList)
                && Objects.equals(BusinessConstant.Y, userAttendancePunchConfigList.get(0).getIsActualPunch())
                && Objects.equals(PunchConfigTypeEnum.FIXED_WORK.getCode(), punchConfigType);
    }

    @Override
    public void execute(AttendanceCalculateContext calculateContext) {
        UserInfoDO user = calculateContext.getUser();
        AttendanceCalculateHandlerDTO calculateHandlerDTO = calculateContext.getCalculateHandlerDTO();
        log.info("attendanceCalculate userCode:{}, date:{}, 仓内多班次计算", user.getUserCode(), calculateHandlerDTO.getAttendanceDayId());

        List<AttendanceEmployeeDetailDO> updateEmployeeDetailDOList = new ArrayList<>();
        List<EmployeeAbnormalAttendanceDO> updateAbnormalAttendanceDOList = new ArrayList<>();
        deletePendingAttendanceRecords(calculateContext.getAttendanceEmployeeDetailDOList(), calculateContext.getUserAbnormalAttendanceDOList(),
                updateEmployeeDetailDOList, updateAbnormalAttendanceDOList);


        List<AttendanceEmployeeDetailDO> addEmployeeDetailDOList = new ArrayList<>();
        List<EmployeeAbnormalAttendanceDO> addAbnormalAttendanceDOList = new ArrayList<>();

        UserAttendancePunchConfigDTO attendancePunchConfigDTO = calculateContext.getUserAttendancePunchConfigDTOList().get(0);
        List<PunchClassItemConfigDO> itemConfigDOList = PunchClassItemConfigMapstruct.INSTANCE.toDOList(attendancePunchConfigDTO.getClassConfigDO().getClassItemConfigList());
        for (PunchClassItemConfigDO itemConfigDO : itemConfigDOList) {
            //获取当前时刻的正常时间
            DayItemConfigDateDTO itemConfigDate = attendanceMinuteCalculateService.buildDayItemConfigDateDTO(calculateHandlerDTO.getAttendanceDayId(), itemConfigDO, itemConfigDOList, calculateContext.getPunchRecordDOList());
            if (Objects.isNull(itemConfigDate)) {
                continue;
            }

            log.info("attendanceCalculate userCode:{}, date:{},sortNo:{},itemId:{},当天的班次的具体信息 itemConfigDate:{}",
                    user.getUserCode(), calculateHandlerDTO.getAttendanceDayId(), itemConfigDO.getSortNo(), itemConfigDO.getId(), JSON.toJSONString(itemConfigDate));

            //过滤得到最早最晚范围时间内的打卡记录
            //没有全部请假，需要看打卡时间（可能打卡时间够，正常考勤，也可能不够，异常考勤）
            List<UserPunchRecordBO> itemPunchRecordList = getEffectiveUserPunchRecord(itemConfigDate.getEarliestPunchInTime(), itemConfigDate.getLatestPunchOutTime(), calculateContext.getPunchRecordDOList());

            //情况1:当天没有有效打卡记录
            if (CollectionUtils.isEmpty(itemPunchRecordList)) {
                log.info("userCode:{}, date:{}, 当天多班次 没有打卡记录", user.getUserCode(), calculateHandlerDTO.getAttendanceDayId());
                normalPunchNoPunchTimeHandler(user, calculateHandlerDTO, itemConfigDate.getPunchInTime(), itemConfigDate.getPunchOutTime(), calculateContext.getAttendanceType(),
                        attendancePunchConfigDTO.getClassConfigDO().getId(), itemConfigDO.getId(), addAbnormalAttendanceDOList);
                continue;
            }

            //情况2:有打卡记录
            log.info("userCode:{}, date:{}, 当天多班次 看打卡记录", user.getUserCode(), calculateHandlerDTO.getAttendanceDayId());
            warehouseNormalPunchHandler(user, calculateHandlerDTO, itemPunchRecordList, addAbnormalAttendanceDOList, addEmployeeDetailDOList,
                    attendancePunchConfigDTO.getClassConfigDO().getId(), itemConfigDO.getId(), attendancePunchConfigDTO.getClassConfigDO().getAttendanceHours(),
                    attendancePunchConfigDTO.getClassConfigDO().getLegalWorkingHours(), calculateContext.getAttendanceType(), itemConfigDO.getElasticTime(),
                    itemConfigDate.getPunchInTime(), itemConfigDate.getLatestPunchInTime(), itemConfigDate.getPunchOutTime(), itemConfigDate.getItemTotalMinutes(),
                    calculateContext.getWarehouseAttendanceConfigDO());
        }

        addAbnormalAttendanceDOList = filterAbnormalAttendanceList(calculateContext.getUserAbnormalAttendanceDOList(), addAbnormalAttendanceDOList);
        attendanceEmployeeDetailManage.attendanceGenerateUpdate(addEmployeeDetailDOList, updateEmployeeDetailDOList, addAbnormalAttendanceDOList, updateAbnormalAttendanceDOList);
    }

    /**
     * 当天没有打卡记录
     */
    private void normalPunchNoPunchTimeHandler(UserInfoDO user,
                                               AttendanceCalculateHandlerDTO calculateHandlerDTO,
                                               Date punchInTime,
                                               Date punchOutTime,
                                               String attendanceType,
                                               Long classId,
                                               Long itemConfigId,
                                               List<EmployeeAbnormalAttendanceDO> addAbnormalAttendanceDOList) {
        //2条异常，上下班都缺卡
        AbnormalExtendDTO abnormalExtendDTO = new AbnormalExtendDTO();
        abnormalExtendDTO.setCorrectPunchTime(punchInTime);
        EmployeeAbnormalAttendanceDO beforeLackAbnormalAttendanceDO = buildAbnormal(user, calculateHandlerDTO, AttendanceAbnormalTypeEnum.BEFORE_OFFICE_LACK.getCode(), attendanceType,
                null, classId, itemConfigId, JSON.toJSONString(abnormalExtendDTO));
        addAbnormalAttendanceDOList.add(beforeLackAbnormalAttendanceDO);

        abnormalExtendDTO.setCorrectPunchTime(punchOutTime);
        EmployeeAbnormalAttendanceDO afterLackAbnormalAttendanceDO = buildAbnormal(user, calculateHandlerDTO, AttendanceAbnormalTypeEnum.AFTER_OFFICE_LACK.getCode(), attendanceType,
                null, classId, itemConfigId, JSON.toJSONString(abnormalExtendDTO));
        addAbnormalAttendanceDOList.add(afterLackAbnormalAttendanceDO);
    }

    /**
     * 仓内打卡
     */
    private void warehouseNormalPunchHandler(UserInfoDO user,
                                             AttendanceCalculateHandlerDTO calculateHandlerDTO,
                                             List<UserPunchRecordBO> itemPunchRecordList,
                                             List<EmployeeAbnormalAttendanceDO> addAbnormalAttendanceDOList,
                                             List<AttendanceEmployeeDetailDO> addEmployeeDetailDOList,
                                             Long classId,
                                             Long classItemId,
                                             BigDecimal attendanceHours,
                                             BigDecimal legalWorkingHours,
                                             String attendanceType,
                                             BigDecimal elasticTime,
                                             Date punchInTime,
                                             Date latestPunchInTime,
                                             Date punchOutTime,
                                             BigDecimal itemTotalMinutes,
                                             WarehouseAttendanceConfigDO warehouseAttendanceConfigDO) {
        //灵活满勤配置时间
        BigDecimal fullAttendanceControlMinute = Objects.nonNull(warehouseAttendanceConfigDO) ? Convert.toBigDecimal(warehouseAttendanceConfigDO.getMinute()) : BigDecimal.ZERO;
        //分时段计算工时
        boolean isSegmentedCalculation = Objects.nonNull(warehouseAttendanceConfigDO) && Objects.equals(BusinessConstant.Y, warehouseAttendanceConfigDO.getIsSegmentedCalculation());

        //总工时计算考勤 异常类型包含：迟到、早退、上下班缺卡
        if (!isSegmentedCalculation) {
            totalWorkingHoursCalculateAttendance(user, calculateHandlerDTO, itemPunchRecordList, addAbnormalAttendanceDOList, addEmployeeDetailDOList, classId, classItemId,
                    attendanceType, elasticTime, punchInTime, latestPunchInTime, punchOutTime, fullAttendanceControlMinute);
            return;
        }

        //分时段计算工时 异常类型包含：上下班缺卡、时长异常
        segmentedCalculateAttendance(user, calculateHandlerDTO, itemPunchRecordList, addAbnormalAttendanceDOList, addEmployeeDetailDOList, classId, classItemId,
                attendanceHours, legalWorkingHours, attendanceType, punchInTime, latestPunchInTime, punchOutTime, itemTotalMinutes, fullAttendanceControlMinute);
    }

    /**
     * 劳务员工总工时计算考勤结果
     */
    private void totalWorkingHoursCalculateAttendance(UserInfoDO user,
                                                      AttendanceCalculateHandlerDTO calculateHandlerDTO,
                                                      List<UserPunchRecordBO> itemPunchRecordList,
                                                      List<EmployeeAbnormalAttendanceDO> addAbnormalAttendanceDOList,
                                                      List<AttendanceEmployeeDetailDO> addEmployeeDetailDOList,
                                                      Long classId,
                                                      Long classItemId,
                                                      String attendanceType,
                                                      BigDecimal elasticTime,
                                                      Date punchInTime,
                                                      Date latestPunchInTime,
                                                      Date punchOutTime,
                                                      BigDecimal fullAttendanceControlMinute) {
        if (itemPunchRecordList.size() == 1) {
            //打了上班卡
            if (itemPunchRecordList.get(0).getFormatPunchTime().compareTo(latestPunchInTime) < 1) {
                AbnormalExtendDTO abnormalExtendDTO = new AbnormalExtendDTO();
                abnormalExtendDTO.setCorrectPunchTime(punchOutTime);
                if (itemPunchRecordList.get(0).getFormatPunchTime().compareTo(punchInTime) > -1) {
                    // 打卡时间大于等于上班时间，小于等于最晚上班时间也就是弹性的时间，
                    long betweenMinutes = DateUtil.between(itemPunchRecordList.get(0).getFormatPunchTime(), punchInTime, DateUnit.MINUTE);
                    // 弹性之后的下班时间：
                    Date actualLeaveTime = DateUtil.offsetMinute(punchOutTime, (int) betweenMinutes);
                    abnormalExtendDTO.setCorrectPunchTime(actualLeaveTime);
                }
                EmployeeAbnormalAttendanceDO afterLackAbnormalAttendanceDO = buildAbnormal(user, calculateHandlerDTO, AttendanceAbnormalTypeEnum.AFTER_OFFICE_LACK.getCode(), attendanceType, null, classId, classItemId, JSON.toJSONString(abnormalExtendDTO));
                addAbnormalAttendanceDOList.add(afterLackAbnormalAttendanceDO);
                return;
            }
            //打了下班卡
            if (itemPunchRecordList.get(0).getFormatPunchTime().compareTo(punchOutTime) > -1) {
                AbnormalExtendDTO abnormalExtendDTO = new AbnormalExtendDTO();
                abnormalExtendDTO.setCorrectPunchTime(punchInTime);
                EmployeeAbnormalAttendanceDO afterLackAbnormalAttendanceDO = buildAbnormal(user, calculateHandlerDTO, AttendanceAbnormalTypeEnum.BEFORE_OFFICE_LACK.getCode(), attendanceType, null, classId, classItemId, JSON.toJSONString(abnormalExtendDTO));
                addAbnormalAttendanceDOList.add(afterLackAbnormalAttendanceDO);
                return;
            }
            //2条异常，一个缺卡，一个早退/迟到
            long beforeMinutes = DateUtil.between(itemPunchRecordList.get(0).getFormatPunchTime(), punchInTime, DateUnit.MINUTE);
            long afterMinutes = DateUtil.between(itemPunchRecordList.get(0).getFormatPunchTime(), punchOutTime, DateUnit.MINUTE);
            if (beforeMinutes < afterMinutes) {
                AbnormalExtendDTO abnormalExtendDTO = new AbnormalExtendDTO();
                abnormalExtendDTO.setActualPunchTime(itemPunchRecordList.get(0).getFormatPunchTime());
                abnormalExtendDTO.setCorrectPunchTime(punchInTime);
                EmployeeAbnormalAttendanceDO beforeLackAbnormalAttendanceDO = buildAbnormal(user, calculateHandlerDTO, AttendanceAbnormalTypeEnum.LATE.getCode(), attendanceType, null, classId, classItemId, JSON.toJSONString(abnormalExtendDTO));
                addAbnormalAttendanceDOList.add(beforeLackAbnormalAttendanceDO);

                abnormalExtendDTO.setActualPunchTime(null);
                abnormalExtendDTO.setCorrectPunchTime(punchOutTime);
                EmployeeAbnormalAttendanceDO afterLackAbnormalAttendanceDO = buildAbnormal(user, calculateHandlerDTO, AttendanceAbnormalTypeEnum.AFTER_OFFICE_LACK.getCode(), attendanceType, null, classId, classItemId, JSON.toJSONString(abnormalExtendDTO));
                addAbnormalAttendanceDOList.add(afterLackAbnormalAttendanceDO);
                return;
            }
            AbnormalExtendDTO abnormalExtendDTO = new AbnormalExtendDTO();
            abnormalExtendDTO.setActualPunchTime(itemPunchRecordList.get(0).getFormatPunchTime());
            abnormalExtendDTO.setCorrectPunchTime(punchOutTime);
            EmployeeAbnormalAttendanceDO beforeLackAbnormalAttendanceDO = buildAbnormal(user, calculateHandlerDTO, AttendanceAbnormalTypeEnum.LEAVE_EARLY.getCode(), attendanceType, null, classId, classItemId, JSON.toJSONString(abnormalExtendDTO));
            addAbnormalAttendanceDOList.add(beforeLackAbnormalAttendanceDO);

            abnormalExtendDTO.setActualPunchTime(null);
            abnormalExtendDTO.setCorrectPunchTime(punchInTime);
            EmployeeAbnormalAttendanceDO afterLackAbnormalAttendanceDO = buildAbnormal(user, calculateHandlerDTO, AttendanceAbnormalTypeEnum.BEFORE_OFFICE_LACK.getCode(), attendanceType, null, classId, classItemId, JSON.toJSONString(abnormalExtendDTO));
            addAbnormalAttendanceDOList.add(afterLackAbnormalAttendanceDO);
            return;
        }

        //多条打卡记录
        //下班未打卡
        if (itemPunchRecordList.get(itemPunchRecordList.size() - 1).getFormatPunchTime().compareTo(punchInTime) < 1) {
            AbnormalExtendDTO abnormalExtendDTO = new AbnormalExtendDTO();
            abnormalExtendDTO.setCorrectPunchTime(punchOutTime);
            EmployeeAbnormalAttendanceDO abnormalAttendanceDO = buildAbnormal(user, calculateHandlerDTO, AttendanceAbnormalTypeEnum.AFTER_OFFICE_LACK.getCode(), attendanceType, null, classId, classItemId, JSON.toJSONString(abnormalExtendDTO));
            addAbnormalAttendanceDOList.add(abnormalAttendanceDO);
            return;
        }

        //上班未打卡
        if (itemPunchRecordList.get(0).getFormatPunchTime().compareTo(punchOutTime) > -1) {
            AbnormalExtendDTO abnormalExtendDTO = new AbnormalExtendDTO();
            abnormalExtendDTO.setCorrectPunchTime(punchInTime);
            EmployeeAbnormalAttendanceDO abnormalAttendanceDO = buildAbnormal(user, calculateHandlerDTO, AttendanceAbnormalTypeEnum.BEFORE_OFFICE_LACK.getCode(), attendanceType, null, classId, classItemId, JSON.toJSONString(abnormalExtendDTO));
            addAbnormalAttendanceDOList.add(abnormalAttendanceDO);
            return;
        }

        //早于等于上班时间打卡
        if (itemPunchRecordList.get(0).getFormatPunchTime().compareTo(punchInTime) < 1) {
            //早退
            if (itemPunchRecordList.get(itemPunchRecordList.size() - 1).getFormatPunchTime().compareTo(punchOutTime) < 0) {
                BigDecimal punchOutIntervalMinutes = BigDecimal.valueOf(DateUtil.between(itemPunchRecordList.get(itemPunchRecordList.size() - 1).getFormatPunchTime(), punchOutTime, DateUnit.MINUTE));
                if (punchOutIntervalMinutes.compareTo(fullAttendanceControlMinute) > 0) {
                    AbnormalExtendDTO abnormalExtendDTO = new AbnormalExtendDTO();
                    abnormalExtendDTO.setActualPunchTime(itemPunchRecordList.get(itemPunchRecordList.size() - 1).getFormatPunchTime());
                    abnormalExtendDTO.setCorrectPunchTime(punchOutTime);
                    EmployeeAbnormalAttendanceDO abnormalAttendanceDO = buildAbnormal(user, calculateHandlerDTO, AttendanceAbnormalTypeEnum.LEAVE_EARLY.getCode(), attendanceType, null, classId, classItemId, JSON.toJSONString(abnormalExtendDTO));
                    addAbnormalAttendanceDOList.add(abnormalAttendanceDO);
                    return;
                }
            }
            AttendanceEmployeeDetailDO userAttendance = buildUserAttendance(user, calculateHandlerDTO, attendanceType, AttendanceConcreteTypeEnum.P.getCode(), BusinessConstant.Y, null, null, null, BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO, null, null);
            addEmployeeDetailDOList.add(userAttendance);
            return;
        }

        //上班弹性时间内打卡
        if (itemPunchRecordList.get(0).getFormatPunchTime().compareTo(latestPunchInTime) < 1) {
            BigDecimal punchInIntervalMinutes = BigDecimal.valueOf(DateUtil.between(itemPunchRecordList.get(0).getFormatPunchTime(), punchInTime, DateUnit.MINUTE));
            //早退
            if (itemPunchRecordList.get(itemPunchRecordList.size() - 1).getFormatPunchTime().compareTo(punchOutTime) < 0) {
                BigDecimal punchOutIntervalMinutes = BigDecimal.valueOf(DateUtil.between(itemPunchRecordList.get(itemPunchRecordList.size() - 1).getFormatPunchTime(), punchOutTime, DateUnit.MINUTE));
                if (punchInIntervalMinutes.add(punchOutIntervalMinutes).compareTo(fullAttendanceControlMinute) > 0) {
                    AbnormalExtendDTO abnormalExtendDTO = new AbnormalExtendDTO();
                    abnormalExtendDTO.setActualPunchTime(itemPunchRecordList.get(itemPunchRecordList.size() - 1).getFormatPunchTime());
                    abnormalExtendDTO.setCorrectPunchTime(DateUtil.offsetMinute(itemPunchRecordList.get(0).getFormatPunchTime(), (int) DateUtil.between(punchInTime, punchOutTime, DateUnit.MINUTE)));
                    EmployeeAbnormalAttendanceDO abnormalAttendanceDO = buildAbnormal(user, calculateHandlerDTO, AttendanceAbnormalTypeEnum.LEAVE_EARLY.getCode(), attendanceType, null, classId, classItemId, JSON.toJSONString(abnormalExtendDTO));
                    addAbnormalAttendanceDOList.add(abnormalAttendanceDO);
                    return;
                }
                AttendanceEmployeeDetailDO userAttendance = buildUserAttendance(user, calculateHandlerDTO, attendanceType, AttendanceConcreteTypeEnum.P.getCode(), BusinessConstant.Y, null, null, null, BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO, null, null);
                addEmployeeDetailDOList.add(userAttendance);
                return;
            }
            //比较时间长短
            if (BigDecimal.valueOf(DateUtil.between(itemPunchRecordList.get(0).getFormatPunchTime(), itemPunchRecordList.get(itemPunchRecordList.size() - 1).getFormatPunchTime(), DateUnit.MINUTE))
                    .add(fullAttendanceControlMinute)
                    .compareTo(BigDecimal.valueOf(DateUtil.between(punchInTime, punchOutTime, DateUnit.MINUTE))) > -1) {
                AttendanceEmployeeDetailDO userAttendance = buildUserAttendance(user, calculateHandlerDTO, attendanceType, AttendanceConcreteTypeEnum.P.getCode(), BusinessConstant.Y, null, null, null, BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO, null, null);
                addEmployeeDetailDOList.add(userAttendance);
                return;
            }
            //还是早退，注意这里，下班补卡时间不再是班次的下班时间了，应该是弹性时间后移
            AbnormalExtendDTO abnormalExtendDTO = new AbnormalExtendDTO();
            abnormalExtendDTO.setActualPunchTime(itemPunchRecordList.get(itemPunchRecordList.size() - 1).getFormatPunchTime());
            abnormalExtendDTO.setCorrectPunchTime(DateUtil.offsetMinute(itemPunchRecordList.get(0).getFormatPunchTime(), (int) DateUtil.between(punchInTime, punchOutTime, DateUnit.MINUTE)));
            EmployeeAbnormalAttendanceDO abnormalAttendanceDO = buildAbnormal(user, calculateHandlerDTO, AttendanceAbnormalTypeEnum.LEAVE_EARLY.getCode(), attendanceType, null, classId, classItemId, JSON.toJSONString(abnormalExtendDTO));
            addAbnormalAttendanceDOList.add(abnormalAttendanceDO);
            return;
        }

        //上班弹性时间外打卡
        BigDecimal punchInIntervalMinutes = BigDecimal.valueOf(DateUtil.between(itemPunchRecordList.get(0).getFormatPunchTime(), punchInTime, DateUnit.MINUTE));

        //下班时间早于班次下班时间
        if (itemPunchRecordList.get(itemPunchRecordList.size() - 1).getFormatPunchTime().compareTo(punchOutTime) < 0) {
            BigDecimal punchOutIntervalMinutes = BigDecimal.valueOf(DateUtil.between(itemPunchRecordList.get(itemPunchRecordList.size() - 1).getFormatPunchTime(), punchOutTime, DateUnit.MINUTE));
            if (punchInIntervalMinutes.add(punchOutIntervalMinutes).compareTo(fullAttendanceControlMinute) > 0) {
                //上班迟到
                AbnormalExtendDTO abnormalExtendDTO = new AbnormalExtendDTO();
                abnormalExtendDTO.setActualPunchTime(itemPunchRecordList.get(0).getFormatPunchTime());
                abnormalExtendDTO.setCorrectPunchTime(punchInTime);
                EmployeeAbnormalAttendanceDO abnormalAttendanceDO = buildAbnormal(user, calculateHandlerDTO, AttendanceAbnormalTypeEnum.LATE.getCode(), attendanceType, null, classId, classItemId, JSON.toJSONString(abnormalExtendDTO));
                addAbnormalAttendanceDOList.add(abnormalAttendanceDO);

                //下班也早退
                AbnormalExtendDTO leaveEarlyAbnormalExtendDTO = new AbnormalExtendDTO();
                leaveEarlyAbnormalExtendDTO.setActualPunchTime(itemPunchRecordList.get(itemPunchRecordList.size() - 1).getFormatPunchTime());
                leaveEarlyAbnormalExtendDTO.setCorrectPunchTime(punchOutTime);
                EmployeeAbnormalAttendanceDO leaveEarlyAbnormalAttendanceDO = buildAbnormal(user, calculateHandlerDTO, AttendanceAbnormalTypeEnum.LEAVE_EARLY.getCode(), attendanceType, null, classId, classItemId, JSON.toJSONString(leaveEarlyAbnormalExtendDTO));
                addAbnormalAttendanceDOList.add(leaveEarlyAbnormalAttendanceDO);
                return;
            }
            AttendanceEmployeeDetailDO userAttendance = buildUserAttendance(user, calculateHandlerDTO, attendanceType, AttendanceConcreteTypeEnum.P.getCode(), BusinessConstant.Y, null, null, null, BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO, null, null);
            addEmployeeDetailDOList.add(userAttendance);
            return;
        }

        //下班时间晚于班次下班时间
        if (itemPunchRecordList.get(itemPunchRecordList.size() - 1).getFormatPunchTime().compareTo(punchOutTime) > -1) {
            BigDecimal elasticMinutes = Objects.nonNull(elasticTime) ? elasticTime.multiply(new BigDecimal("60")) : BigDecimal.ZERO;
            BigDecimal fullAttendanceConfigTime = fullAttendanceControlMinute;
            if (fullAttendanceControlMinute.compareTo(elasticMinutes) < 1) {
                fullAttendanceConfigTime = fullAttendanceControlMinute.add(elasticMinutes);
            }

            //比较时间长短
            if (BigDecimal.valueOf(DateUtil.between(itemPunchRecordList.get(0).getFormatPunchTime(), itemPunchRecordList.get(itemPunchRecordList.size() - 1).getFormatPunchTime(), DateUnit.MINUTE))
                    .add(fullAttendanceControlMinute)
                    .compareTo(BigDecimal.valueOf(DateUtil.between(punchInTime, punchOutTime, DateUnit.MINUTE))) < 0) {
                //上班迟到
                AbnormalExtendDTO abnormalExtend = new AbnormalExtendDTO();
                abnormalExtend.setActualPunchTime(itemPunchRecordList.get(0).getFormatPunchTime());
                abnormalExtend.setCorrectPunchTime(punchInTime);
                EmployeeAbnormalAttendanceDO employeeAbnormalAttendanceDO = buildAbnormal(user, calculateHandlerDTO, AttendanceAbnormalTypeEnum.LATE.getCode(), attendanceType, null, classId, classItemId, JSON.toJSONString(abnormalExtend));
                addAbnormalAttendanceDOList.add(employeeAbnormalAttendanceDO);
                return;
            } else {
                //上班时间超出仓内管理配置时间视为迟到
                if (fullAttendanceConfigTime.compareTo(punchInIntervalMinutes) < 0) {
                    //上班迟到
                    AbnormalExtendDTO abnormalExtendDTO = new AbnormalExtendDTO();
                    abnormalExtendDTO.setActualPunchTime(itemPunchRecordList.get(0).getFormatPunchTime());
                    abnormalExtendDTO.setCorrectPunchTime(punchInTime);
                    EmployeeAbnormalAttendanceDO abnormalAttendanceDO = buildAbnormal(user, calculateHandlerDTO, AttendanceAbnormalTypeEnum.LATE.getCode(), attendanceType, null, classId, classItemId, JSON.toJSONString(abnormalExtendDTO));
                    addAbnormalAttendanceDOList.add(abnormalAttendanceDO);
                }
            }

            AttendanceEmployeeDetailDO userAttendance = buildUserAttendance(user, calculateHandlerDTO, attendanceType, AttendanceConcreteTypeEnum.P.getCode(), BusinessConstant.Y, null, null, null, BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO, null, null);
            addEmployeeDetailDOList.add(userAttendance);
        }
    }

    /**
     * 仓内员工分时段计算考勤结果
     */
    private void segmentedCalculateAttendance(UserInfoDO user,
                                              AttendanceCalculateHandlerDTO attendanceHandlerDTO,
                                              List<UserPunchRecordBO> itemPunchRecordList,
                                              List<EmployeeAbnormalAttendanceDO> addAbnormalAttendanceDOList,
                                              List<AttendanceEmployeeDetailDO> addEmployeeDetailDOList,
                                              Long classId,
                                              Long classItemId,
                                              BigDecimal attendanceHours,
                                              BigDecimal legalWorkingHours,
                                              String attendanceType,
                                              Date punchInTime,
                                              Date latestPunchInTime,
                                              Date punchOutTime,
                                              BigDecimal itemTotalMinutes,
                                              BigDecimal fullAttendanceControlMinute) {
        BigDecimal fullAttendanceControlHours = fullAttendanceControlMinute.divide(BusinessConstant.MINUTES, 2, RoundingMode.HALF_UP);
        if (itemPunchRecordList.size() == 1) {
            //打了上班卡
            if (itemPunchRecordList.get(0).getFormatPunchTime().compareTo(latestPunchInTime) < 1) {
                AbnormalExtendDTO abnormalExtendDTO = new AbnormalExtendDTO();
                abnormalExtendDTO.setCorrectPunchTime(punchOutTime);
                if (itemPunchRecordList.get(0).getFormatPunchTime().compareTo(punchInTime) > -1) {
                    // 打卡时间大于等于上班时间，小于等于最晚上班时间也就是弹性的时间，
                    long betweenMinutes = DateUtil.between(itemPunchRecordList.get(0).getFormatPunchTime(), punchInTime, DateUnit.MINUTE);
                    // 弹性之后的下班时间：
                    Date actualLeaveTime = DateUtil.offsetMinute(punchOutTime, (int) betweenMinutes);
                    abnormalExtendDTO.setCorrectPunchTime(actualLeaveTime);
                }
                EmployeeAbnormalAttendanceDO afterLackAbnormalAttendanceDO = buildAbnormal(user, attendanceHandlerDTO, AttendanceAbnormalTypeEnum.AFTER_OFFICE_LACK.getCode(), attendanceType, null, classId, classItemId, JSON.toJSONString(abnormalExtendDTO));
                addAbnormalAttendanceDOList.add(afterLackAbnormalAttendanceDO);
                return;
            }
            //打了下班卡
            if (itemPunchRecordList.get(0).getFormatPunchTime().compareTo(punchOutTime) > -1) {
                AbnormalExtendDTO abnormalExtendDTO = new AbnormalExtendDTO();
                abnormalExtendDTO.setCorrectPunchTime(punchInTime);
                EmployeeAbnormalAttendanceDO afterLackAbnormalAttendanceDO = buildAbnormal(user, attendanceHandlerDTO, AttendanceAbnormalTypeEnum.BEFORE_OFFICE_LACK.getCode(), attendanceType, null, classId, classItemId, JSON.toJSONString(abnormalExtendDTO));
                addAbnormalAttendanceDOList.add(afterLackAbnormalAttendanceDO);
                return;
            }

            //时长异常
            AbnormalExtendDTO abnormalExtendDTO = new AbnormalExtendDTO();
            abnormalExtendDTO.setActualWorkingHours(attendanceHandlerDTO.getActualWorkingHours());
            abnormalExtendDTO.setLegalWorkingHours(itemTotalMinutes.divide(BusinessConstant.MINUTES, 2, RoundingMode.HALF_UP));
            EmployeeAbnormalAttendanceDO abnormalAttendanceDO = buildAbnormal(user, attendanceHandlerDTO, AttendanceAbnormalTypeEnum.ABNORMAL_DURATION.getCode(), attendanceType, null, classId, classItemId, JSON.toJSONString(abnormalExtendDTO));
            addAbnormalAttendanceDOList.add(abnormalAttendanceDO);
            return;
        }

        //多条打卡记录
        //下班未打卡
        if (itemPunchRecordList.get(itemPunchRecordList.size() - 1).getFormatPunchTime().compareTo(punchInTime) < 1) {
            AbnormalExtendDTO abnormalExtendDTO = new AbnormalExtendDTO();
            abnormalExtendDTO.setCorrectPunchTime(punchOutTime);
            EmployeeAbnormalAttendanceDO abnormalAttendanceDO = buildAbnormal(user, attendanceHandlerDTO, AttendanceAbnormalTypeEnum.AFTER_OFFICE_LACK.getCode(), attendanceType, null, classId, classItemId, JSON.toJSONString(abnormalExtendDTO));
            addAbnormalAttendanceDOList.add(abnormalAttendanceDO);
            return;
        }

        //上班未打卡
        if (itemPunchRecordList.get(0).getFormatPunchTime().compareTo(punchOutTime) > -1) {
            AbnormalExtendDTO abnormalExtendDTO = new AbnormalExtendDTO();
            abnormalExtendDTO.setCorrectPunchTime(punchInTime);
            EmployeeAbnormalAttendanceDO abnormalAttendanceDO = buildAbnormal(user, attendanceHandlerDTO, AttendanceAbnormalTypeEnum.BEFORE_OFFICE_LACK.getCode(), attendanceType, null, classId, classItemId, JSON.toJSONString(abnormalExtendDTO));
            addAbnormalAttendanceDOList.add(abnormalAttendanceDO);
            return;
        }

        //时长异常
        if (attendanceHandlerDTO.getActualAttendanceTime().add(fullAttendanceControlHours).compareTo(attendanceHours) < 0
                || attendanceHandlerDTO.getActualWorkingHours().add(fullAttendanceControlHours).compareTo(legalWorkingHours) < 0) {
            AbnormalExtendDTO abnormalExtendDTO = new AbnormalExtendDTO();
            abnormalExtendDTO.setActualWorkingHours(attendanceHandlerDTO.getActualWorkingHours());
            abnormalExtendDTO.setLegalWorkingHours(itemTotalMinutes.divide(BusinessConstant.MINUTES, 2, RoundingMode.HALF_UP));
            EmployeeAbnormalAttendanceDO abnormalAttendanceDO = buildAbnormal(user, attendanceHandlerDTO, AttendanceAbnormalTypeEnum.ABNORMAL_DURATION.getCode(), attendanceType, null, classId, classItemId, JSON.toJSONString(abnormalExtendDTO));
            addAbnormalAttendanceDOList.add(abnormalAttendanceDO);
            return;
        }

        AttendanceEmployeeDetailDO userAttendance = buildUserAttendance(user, attendanceHandlerDTO, attendanceType, AttendanceConcreteTypeEnum.P.getCode(), BusinessConstant.Y, null, null, null, BigDecimal.ZERO, itemTotalMinutes, BigDecimal.ZERO, null, null);
        addEmployeeDetailDOList.add(userAttendance);
    }
}
