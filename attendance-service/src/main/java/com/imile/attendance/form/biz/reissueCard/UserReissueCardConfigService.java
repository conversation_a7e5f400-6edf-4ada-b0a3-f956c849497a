package com.imile.attendance.form.biz.reissueCard;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.cycleConfig.AttendanceCycleConfigService;
import com.imile.attendance.cycleConfig.dto.AttendanceDayCycleDTO;
import com.imile.attendance.enums.WorkStatusEnum;
import com.imile.attendance.form.UserCycleReissueCardCountManage;
import com.imile.attendance.infrastructure.idwork.DefaultIdWorker;
import com.imile.attendance.infrastructure.repository.cycleConfig.model.AttendanceCycleConfigDO;
import com.imile.attendance.infrastructure.repository.employee.dao.UserInfoDao;
import com.imile.attendance.infrastructure.repository.employee.modle.UserInfoDO;
import com.imile.attendance.infrastructure.repository.form.model.UserCycleReissueCardCountDO;
import com.imile.attendance.util.BaseDOUtil;
import com.imile.attendance.util.DateHelper;
import com.imile.attendance.warehouse.param.UserCardParam;
import com.imile.attendance.warehouse.param.WarehouseUserCardParam;
import com.imile.common.enums.StatusEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;


@Service
@Slf4j
public class UserReissueCardConfigService{
    @Resource
    private UserCycleReissueCardCountManage userCycleReissueCardCountManage;
    @Resource
    private AttendanceCycleConfigService attendanceCycleConfigService;
    @Resource
    private DefaultIdWorker defaultIdWorker;
    @Resource
    private UserInfoDao userInfoDao;

    /**
     * 绑定用户补卡次数
     *
     * @param param 入参
     */
    public void bindUserCardConfig(WarehouseUserCardParam param) {

        if (ObjectUtil.isNull(param) || CollUtil.isEmpty(param.getAllUserCodeList())) {
            log.info("bindUserCardConfig param is null or allUserCodeList is empty");
            return;
        }

        log.info("bindUserCardConfig param:{}", param);
        List<String> allUserCodeList = param.getAllUserCodeList();


        // 获取用户
        List<UserInfoDO> allUserInfoList = userInfoDao.listByUserCodes(allUserCodeList);

        allUserInfoList = allUserInfoList.stream()
                .filter(item -> ObjectUtil.isNotEmpty(item.getUserCode())
                        && ObjectUtil.equal(item.getStatus(), StatusEnum.ACTIVE.getCode())
                        && ObjectUtil.equal(item.getWorkStatus(), WorkStatusEnum.ON_JOB.getCode())).collect(Collectors.toList());

        List<Long> userIdList = allUserInfoList.stream().map(UserInfoDO::getId).collect(Collectors.toList());
        //查询员工所有的记录
        List<UserCycleReissueCardCountDO> userCardConfigList = userCycleReissueCardCountManage.selectByUserIdList(userIdList).stream()
                .sorted(Comparator.comparing(UserCycleReissueCardCountDO::getCycleStartDate)).collect(Collectors.toList());
        Map<Long, List<UserCycleReissueCardCountDO>> userCardConfigMap = userCardConfigList.stream().collect(Collectors.groupingBy(UserCycleReissueCardCountDO::getUserId));

        List<UserCycleReissueCardCountDO> addUserCardConfigList = new ArrayList<>();

        List<UserCardParam> userCardParamList = param.getUserCardParamList();
        if (CollUtil.isEmpty(userCardParamList)) {
            log.info("bindUserCardConfig userCardParamList is empty");
            return;
        }
        for (UserCardParam userCardParam : userCardParamList) {
            // 用户列表
            List<String> userCodeList = userCardParam.getUserCodeList();
            // 这批用户异常所属日期
            Long initDayId = userCardParam.getInitDayId();

            List<UserInfoDO> userInfoList = allUserInfoList.stream().filter(item -> userCodeList.contains(item.getUserCode())).collect(Collectors.toList());

            for (UserInfoDO userInfo : userInfoList) {
                Long userId = userInfo.getId();

                AttendanceCycleConfigDO userAttendanceCycleConfig = attendanceCycleConfigService.getUserAttendanceCycleConfigUserCard(userId);
                AttendanceDayCycleDTO attendanceDayCycle = attendanceCycleConfigService.getUserAttendanceCycleConfigDay(initDayId, userAttendanceCycleConfig);

                log.info("userCode:{} userAttendanceCycleConfig:{} attendanceDayCycle:{}", userInfo.getUserCode(), userAttendanceCycleConfig, attendanceDayCycle);

                if (ObjectUtil.isNull(attendanceDayCycle)) {
                    log.info("userCode:{} has no cycle config", userInfo.getUserCode());
                    continue;
                }

                List<UserCycleReissueCardCountDO> userCardList = userCardConfigMap.get(userId);
                if (CollUtil.isEmpty(userCardList)) {
                    addCardConfigBuild(userId, attendanceDayCycle, addUserCardConfigList);
                    continue;
                }
                List<UserCycleReissueCardCountDO> existList = userCardList.stream().filter(item -> Objects.equals(DateHelper.getDayId(item.getCycleStartDate()), DateHelper.getDayId(attendanceDayCycle.getAttendanceStartDate()))).collect(Collectors.toList());
                if (CollUtil.isNotEmpty(existList)) {
                    log.info("userCode:{} has exist user card config", userInfo.getUserCode());
                    continue;
                }
                addCardConfigBuild(userId, attendanceDayCycle, addUserCardConfigList);
            }
        }
        userCycleReissueCardCountManage.batchSave(addUserCardConfigList);

    }

    /**
     * 构建补卡记录
     *
     * @param userId                  用户id
     * @param attendanceDayCycleDTO   考勤周期
     * @param addUserCardConfigDOList 目标集合
     */
    private void addCardConfigBuild(Long userId, AttendanceDayCycleDTO attendanceDayCycleDTO, List<UserCycleReissueCardCountDO> addUserCardConfigDOList) {
        UserCycleReissueCardCountDO userCardConfig = new UserCycleReissueCardCountDO();
        userCardConfig.setId(defaultIdWorker.nextId());
        userCardConfig.setUserId(userId);
        userCardConfig.setUsedReissueCardCount(BusinessConstant.ZERO);
        userCardConfig.setCycleStartDate(attendanceDayCycleDTO.getAttendanceStartDate());
        userCardConfig.setCycleEndDate(DateUtil.offset(attendanceDayCycleDTO.getAttendanceEndDate(), DateField.MILLISECOND, -999));
        BaseDOUtil.fillDOInsert(userCardConfig);
        addUserCardConfigDOList.add(userCardConfig);
    }
}
