package com.imile.attendance.form.biz.reissueCard;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.imile.attendance.abnormal.EmployeeAbnormalAttendanceService;
import com.imile.attendance.abnormal.dto.AbnormalExtendDTO;
import com.imile.attendance.bpm.RpcBpmApprovalClient;
import com.imile.attendance.common.AttendanceBaseService;
import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.context.RequestInfoHolder;
import com.imile.attendance.cycleConfig.AttendanceCycleConfigService;
import com.imile.attendance.cycleConfig.dto.AttendanceDayCycleDTO;
import com.imile.attendance.enums.ApprovalNoPrefixEnum;
import com.imile.attendance.enums.CountryCodeEnum;
import com.imile.attendance.enums.EmploymentTypeEnum;
import com.imile.attendance.enums.ErrorCodeEnum;
import com.imile.attendance.enums.abnormal.AbnormalAttendanceStatusEnum;
import com.imile.attendance.enums.abnormal.AbnormalOperationTypeEnum;
import com.imile.attendance.enums.abnormal.AttendanceAbnormalTypeEnum;
import com.imile.attendance.enums.approval.AddDurationCustomFieldEnum;
import com.imile.attendance.enums.approval.ReissueCardCustomFieldEnum;
import com.imile.attendance.enums.form.ApplicationFormAttrKeyEnum;
import com.imile.attendance.enums.form.ApplicationRelationTypeEnum;
import com.imile.attendance.enums.form.FormStatusEnum;
import com.imile.attendance.enums.form.FormTypeEnum;
import com.imile.attendance.enums.punch.SourceTypeEnum;
import com.imile.attendance.enums.rule.PunchConfigTypeEnum;
import com.imile.attendance.form.AttendanceApprovalManage;
import com.imile.attendance.form.AttendanceFormManage;
import com.imile.attendance.form.CommonFormOperationService;
import com.imile.attendance.form.biz.reissueCard.param.AddDurationParam;
import com.imile.attendance.form.biz.reissueCard.param.ReissueCardAddParam;
import com.imile.attendance.form.biz.reissueCard.param.UserDayReissueInfoParam;
import com.imile.attendance.form.biz.reissueCard.service.UserDayReissueAbnormalDTO;
import com.imile.attendance.form.biz.reissueCard.vo.UserDayReissueInfoVO;
import com.imile.attendance.form.bo.AttendanceFormDetailBO;
import com.imile.attendance.form.dto.ApprovalDetailStepRecordDTO;
import com.imile.attendance.form.param.RevokeAddParam;
import com.imile.attendance.form.vo.ApprovalResultVO;
import com.imile.attendance.infrastructure.idwork.IdWorkUtils;
import com.imile.attendance.infrastructure.repository.abnormal.model.EmployeeAbnormalAttendanceDO;
import com.imile.attendance.infrastructure.repository.abnormal.model.EmployeeAbnormalOperationRecordDO;
import com.imile.attendance.infrastructure.repository.common.AttendanceDeptService;
import com.imile.attendance.infrastructure.repository.common.AttendancePostService;
import com.imile.attendance.infrastructure.repository.common.AttendanceUserService;
import com.imile.attendance.infrastructure.repository.common.dto.AttendanceDept;
import com.imile.attendance.infrastructure.repository.common.dto.AttendancePost;
import com.imile.attendance.infrastructure.repository.common.dto.AttendanceUser;
import com.imile.attendance.infrastructure.repository.cycleConfig.model.AttendanceCycleConfigDO;
import com.imile.attendance.infrastructure.repository.employee.modle.UserLeaveStageDetailDO;
import com.imile.attendance.infrastructure.repository.form.model.AttendanceFormAttrDO;
import com.imile.attendance.infrastructure.repository.form.model.AttendanceFormDO;
import com.imile.attendance.infrastructure.repository.form.model.AttendanceFormRelationDO;
import com.imile.attendance.infrastructure.repository.form.model.UserCycleReissueCardCountDO;
import com.imile.attendance.infrastructure.repository.punch.model.EmployeePunchRecordDO;
import com.imile.attendance.infrastructure.repository.rule.model.PunchClassConfigDO;
import com.imile.attendance.infrastructure.repository.rule.model.PunchClassItemConfigDO;
import com.imile.attendance.infrastructure.repository.rule.model.PunchConfigDO;
import com.imile.attendance.infrastructure.repository.shift.model.UserShiftConfigDO;
import com.imile.attendance.migration.MigrationService;
import com.imile.attendance.punch.EmployeePunchRecordService;
import com.imile.attendance.rule.PunchClassConfigManage;
import com.imile.attendance.rule.PunchConfigManage;
import com.imile.attendance.rule.ReissueCardConfigManage;
import com.imile.attendance.rule.service.PunchClassConfigQueryService;
import com.imile.attendance.shift.UserShiftConfigManage;
import com.imile.attendance.user.dto.AttachmentDTO;
import com.imile.attendance.util.BaseDOUtil;
import com.imile.attendance.util.DateFormatterUtil;
import com.imile.bpm.enums.LanguageTypeEnum;
import com.imile.bpm.mq.dto.ApprovalEmptyRecordApiDTO;
import com.imile.bpm.mq.dto.ApprovalEmptyRecordApiQuery;
import com.imile.bpm.mq.dto.ApprovalInfoCreateResultDTO;
import com.imile.bpm.mq.dto.ApprovalInitInfoApiDTO;
import com.imile.bpm.mq.dto.ApprovalTypeFieldApiDTO;
import com.imile.bpm.mq.dto.FileTemplateApiDTO;
import com.imile.common.exception.BusinessException;
import com.imile.idwork.IdWorkerUtil;
import com.imile.util.BeanUtils;
import com.imile.util.date.DateUtils;
import com.imile.util.lang.I18nUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2025/5/19
 * @Description 补卡及补时长审批服务
 */
@Slf4j
@Service
public class ReissueCardApprovalService {
    @Resource
    private CommonFormOperationService commonFormOperationService;
    @Resource
    private AttendanceApprovalManage attendanceApprovalManage;
    @Resource
    private RpcBpmApprovalClient bpmApprovalClient;
    @Resource
    private AttendanceDeptService deptService;
    @Resource
    private AttendancePostService postService;
    @Resource
    private AttendanceUserService userService;
    @Resource
    private UserShiftConfigManage userShiftConfigManage;
    @Resource
    private AttendanceBaseService attendanceBaseService;
    @Resource
    private AttendanceCycleConfigService attendanceCycleConfigService;
    @Resource
    private EmployeePunchRecordService punchRecordService;
    @Resource
    private UserReissueCardCountService userReissueCardCountService;
    @Resource
    private EmployeeAbnormalAttendanceService employeeAbnormalAttendanceService;
    @Resource
    private PunchClassConfigQueryService punchClassConfigQueryService;
    @Resource
    private AttendanceFormManage formManage;
    @Resource
    private PunchConfigManage punchConfigManage;
    @Resource
    private PunchClassConfigManage punchClassConfigManage;
    @Resource
    private IdWorkUtils idWorkUtils;
    @Resource
    private MigrationService migrationService;

    /**
     * 获取用户指定日期的可补卡异常及当天排班信息
     *
     * @param param
     * @return
     */
    public UserDayReissueInfoVO getUserDayReissueInfo(UserDayReissueInfoParam param) {
        AttendanceUser userInfo = userService.getByUserId(param.getUserId());
        if (userInfo == null) {
            throw BusinessException.get(ErrorCodeEnum.ACCOUNT_NOT_EXITS.getCode(),
                    I18nUtils.getMessage(ErrorCodeEnum.ACCOUNT_NOT_EXITS.getDesc()));
        }
        //1. 查询用户当天打卡规则
        Date date = DateUtil.parse(param.getDayId().toString(), "yyyyMMdd");
        PunchConfigDO punchConfig = punchConfigManage.getByUserIdAndDate(userInfo.getId(), DateUtil.endOfDay(date));
        if (Objects.isNull(punchConfig)) {
            throw BusinessException.get(ErrorCodeEnum.THIS_DAY_NOT_HAVE_PUNCH_CONFIG.getCode(),
                    I18nUtils.getMessage(ErrorCodeEnum.THIS_DAY_NOT_HAVE_PUNCH_CONFIG.getDesc()));
        }
        //2. 考勤周期校验 查询是否超过补卡时间
        Date nowDate = Objects.isNull(param.getDateTime()) ? new Date() : param.getDateTime();
        AttendanceCycleConfigDO userAttendanceCycleConfig = attendanceCycleConfigService.getUserAttendanceCycleConfig(param.getUserId());
        attendanceCycleConfigService.checkDateInAttendanceCycle(nowDate, userAttendanceCycleConfig, date);

        UserDayReissueInfoVO userDayReissueInfoVO = new UserDayReissueInfoVO();
        List<UserDayReissueAbnormalDTO> userDayReissueAbnormalDTOList = new ArrayList<>();
        userDayReissueInfoVO.setUserDayReissueAbnormalDTOList(userDayReissueAbnormalDTOList);
        //3. 查询当天没有被申请关联的未处理异常（可以进行补卡操作的）
        List<EmployeeAbnormalAttendanceDO> abnormalAttendanceDOList = employeeAbnormalAttendanceService.selectAbnormalByUserIdList(Arrays.asList(param.getUserId()),
                        Arrays.asList(param.getDayId())).stream()
                .filter(item -> StringUtils.equalsIgnoreCase(item.getStatus(), AbnormalAttendanceStatusEnum.UN_PROCESSED.getCode())
                        || StringUtils.equalsIgnoreCase(item.getStatus(), AbnormalAttendanceStatusEnum.REJECT.getCode()))
                .filter(item -> AttendanceAbnormalTypeEnum.getRessiueCodeList().contains(item.getAbnormalType())).collect(Collectors.toList());
        List<Long> classId = abnormalAttendanceDOList
                .stream()
                .map(EmployeeAbnormalAttendanceDO::getPunchClassConfigId)
                .collect(Collectors.toList());
        List<PunchClassItemConfigDO> attendancePunchClassItemConfig = punchClassConfigManage.selectClassItemByClassIds(classId);
        punchClassConfigQueryService.transferItemConfigTimeFormat(attendancePunchClassItemConfig, param.getDayId());
        employeeAbnormalAttendanceService.filterAbnormalAttendance(abnormalAttendanceDOList, attendancePunchClassItemConfig, nowDate);
        List<Long> abnormalIdList = abnormalAttendanceDOList.stream().map(item -> item.getId()).collect(Collectors.toList());
        List<AttendanceFormRelationDO> abnormalRelationDOList = formManage.selectRelationByRelationIdList(abnormalIdList)
                .stream()
                .filter(item -> StringUtils.equalsIgnoreCase(item.getRelationType()
                        , ApplicationRelationTypeEnum.ABNORMAL.getCode()))
                .collect(Collectors.toList());
        List<Long> abnormalFormIdList = abnormalRelationDOList
                .stream()
                .map(item -> item.getFormId())
                .collect(Collectors.toList());
        List<AttendanceFormDO> formDOList = formManage.selectByIdList(abnormalFormIdList);

        Long punchClassId = null;
        if (CollectionUtils.isEmpty(abnormalAttendanceDOList)) {
        // 4.自由补卡逻辑(无异常补加班卡)
            List<UserShiftConfigDO> employeeConfigDOS = userShiftConfigManage.selectUserShiftByDayIds(param.getUserId(), Collections.singletonList(param.getDayId()));
            if (CollectionUtils.isEmpty(employeeConfigDOS)) {
                return userDayReissueInfoVO;
            }
            punchClassId = employeeConfigDOS.get(0).getPunchClassConfigId();
            if (Objects.isNull(punchClassId)) {
                userDayReissueInfoVO.setPunchConfigClassItemInfo(employeeConfigDOS.get(0).getDayShiftRule());
            }
            userDayReissueInfoVO.setPunchClassId(punchClassId);
            userDayReissueInfoVO.setPunchConfigId(punchConfig.getId());
            attendancePunchClassItemConfig = punchClassConfigManage.selectClassItemByClassIds(Arrays.asList(punchClassId));
            punchClassConfigQueryService.transferItemConfigTimeFormat(attendancePunchClassItemConfig, param.getDayId());
            employeeAbnormalAttendanceService.filterAbnormalAttendance(abnormalAttendanceDOList, attendancePunchClassItemConfig, nowDate);
            // 拼接时段
            String configClassItemInfo = this.getConfigClassItemInfo(attendancePunchClassItemConfig);
            if (StringUtils.isNotBlank(configClassItemInfo)) {
                userDayReissueInfoVO.setPunchConfigClassItemInfo(configClassItemInfo);
            }
            // 5.查询用户当日最早最晚打卡记录
            List<EmployeePunchRecordDO> userPunchRecordList = punchRecordService.getUserPunchRecords(userInfo.getUserCode(),
                            Arrays.asList(param.getDayId()))
                    .stream()
                    .filter(item -> !SourceTypeEnum.REISSUE_CARD.name().equals(item.getSourceType()))
                    .sorted(Comparator.comparing(EmployeePunchRecordDO::getPunchTime)).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(userPunchRecordList)) {
                userDayReissueInfoVO.setActualEarliestPunchTime(userPunchRecordList.get(0).getPunchTime());
                userDayReissueInfoVO.setActualLatestPunchTime(userPunchRecordList.get(userPunchRecordList.size() - 1).getPunchTime());
            }
        }
        for (EmployeeAbnormalAttendanceDO abnormalAttendanceDO : abnormalAttendanceDOList) {
            List<PunchClassItemConfigDO> itemConfigDOList = punchClassConfigManage.selectClassItemByClassIds(Arrays.asList(abnormalAttendanceDO.getPunchClassConfigId()));
            List<AttendanceFormRelationDO> userRelationList = abnormalRelationDOList.stream().filter(item -> item.getRelationId().equals(abnormalAttendanceDO.getId())).collect(Collectors.toList());
            //没有被任何人申请过
            if (CollectionUtils.isEmpty(userRelationList)) {
                punchClassId = abnormalAttendanceDO.getPunchClassConfigId();
                userDayReissueAbnormalDTOBuild(abnormalAttendanceDO, itemConfigDOList, userDayReissueInfoVO, userDayReissueAbnormalDTOList);
                continue;
            }
            //被关联了，看关联的申请单的状态
            List<AttendanceFormDO> userFormList = formDOList.stream()
                    .filter(item -> item.getId().equals(userRelationList.get(0).getFormId()))
                    .filter(item -> !StringUtils.equalsIgnoreCase(item.getFormStatus(), FormStatusEnum.IN_REVIEW.getCode())
                            && !StringUtils.equalsIgnoreCase(item.getFormStatus(), FormStatusEnum.PASS.getCode())).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(userFormList)) {
                continue;
            }
            punchClassId = abnormalAttendanceDO.getPunchClassConfigId();
            userDayReissueAbnormalDTOBuild(abnormalAttendanceDO, itemConfigDOList, userDayReissueInfoVO, userDayReissueAbnormalDTOList);
        }
        //6. 设置最早最晚打卡时间
        setEarAndLatePunchTime(punchClassId, param.getDayId(), param.getUserId(), attendancePunchClassItemConfig, userDayReissueInfoVO);

        //获取用户指定日期所属的考勤周期
        AttendanceDayCycleDTO attendanceDayCycleDTO = attendanceBaseService.getUserAttendanceCycleConfigDay(param.getDayId(), userAttendanceCycleConfig);
        if (attendanceDayCycleDTO != null) {
            userDayReissueInfoVO.setAttendanceStartDate(attendanceDayCycleDTO.getAttendanceStartDate());
            userDayReissueInfoVO.setAttendanceEndDate(attendanceDayCycleDTO.getAttendanceEndDate());
        }
        ReissueCardAddParam reissueCardAddParam = new ReissueCardAddParam();
        reissueCardAddParam.setUserId(param.getUserId());
        int residueReissueCardCount = userReissueCardCountService.getUserResidueReissueCardCount(reissueCardAddParam, date);
        userDayReissueInfoVO.setResidueReissueCardCount(residueReissueCardCount);
        // 7. 设置打卡规则类型、名称
        if (Objects.isNull(userDayReissueInfoVO.getPunchConfigId())) {
            return userDayReissueInfoVO;
        }
        // 获取打卡规则
        PunchConfigDO punchConfigDO = punchConfigManage.getPunchConfigById(userDayReissueInfoVO.getPunchConfigId());
        if (Objects.nonNull(punchConfigDO)) {
            userDayReissueInfoVO.setPunchConfigType(punchConfigDO.getConfigType());
            userDayReissueInfoVO.setPunchConfigName(punchConfigDO.getConfigName());
        }
        return userDayReissueInfoVO;
    }

    /**
     * 补卡异常信息展示
     * @param abnormalAttendanceDO
     * @param itemConfigDOList
     * @param userDayReissueInfoVO
     * @param userDayReissueAbnormalDTOList
     */
    private void userDayReissueAbnormalDTOBuild(EmployeeAbnormalAttendanceDO abnormalAttendanceDO,
                                                List<PunchClassItemConfigDO> itemConfigDOList,
                                                UserDayReissueInfoVO userDayReissueInfoVO,
                                                List<UserDayReissueAbnormalDTO> userDayReissueAbnormalDTOList) {
        UserDayReissueAbnormalDTO abnormalDTO = new UserDayReissueAbnormalDTO();
        userDayReissueAbnormalDTOList.add(abnormalDTO);
        abnormalDTO.setAbnormalId(abnormalAttendanceDO.getId());
        abnormalDTO.setReissueCardType(abnormalAttendanceDO.getAbnormalType());
        abnormalDTO.setReissueCardTypeDesc(abnormalAttendanceDO.getAbnormalType());
        AttendanceAbnormalTypeEnum abnormalTypeEnum = AttendanceAbnormalTypeEnum.getInstanceByCode(abnormalAttendanceDO.getAbnormalType());
        if (abnormalTypeEnum != null) {
            abnormalDTO.setReissueCardTypeDesc(RequestInfoHolder.isChinese() ? abnormalTypeEnum.getDesc() : abnormalTypeEnum.getDescEn());
            abnormalDTO.setReissueCardTypeDetail(RequestInfoHolder.isChinese() ? abnormalTypeEnum.getDetail() : abnormalTypeEnum.getDetailEn());
        }
        String configClassItemInfo = getConfigClassItemInfo(itemConfigDOList);
        if (StringUtils.isNotBlank(configClassItemInfo)) {
            userDayReissueInfoVO.setPunchConfigClassItemInfo(configClassItemInfo);
        }
        if (StringUtils.isNotBlank(abnormalAttendanceDO.getExtend())) {
            AbnormalExtendDTO abnormalExtendDTO = JSON.parseObject(abnormalAttendanceDO.getExtend(), AbnormalExtendDTO.class);
            abnormalDTO.setCorrectPunchTime(abnormalExtendDTO.getCorrectPunchTime());
            abnormalDTO.setActualPunchTime(abnormalExtendDTO.getActualPunchTime());
        }
        userDayReissueInfoVO.setPunchConfigId(abnormalAttendanceDO.getPunchConfigId());
        userDayReissueInfoVO.setPunchClassId(abnormalAttendanceDO.getPunchClassConfigId());
    }

    /**
     * 设置最早最晚打卡时间
     * @param punchClassId
     * @param dayId
     * @param userId
     * @param attendancePunchClassItemConfig
     * @param userDayReissueInfoVO
     */
    private void setEarAndLatePunchTime(Long punchClassId, Long dayId, Long userId
            , List<PunchClassItemConfigDO> attendancePunchClassItemConfig
            , UserDayReissueInfoVO userDayReissueInfoVO) {
        //设置最早最晚打卡时间
        List<Date> earliestPunchInTimeList = attendancePunchClassItemConfig.stream()
                .filter(item -> Objects.nonNull(item.getEarliestPunchInTime()))
                .map(PunchClassItemConfigDO::getEarliestPunchInTime)
                .sorted()
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(earliestPunchInTimeList)) {
            userDayReissueInfoVO.setEarliestPunchTime(earliestPunchInTimeList.get(0));
        }
        List<Date> latestPunchOutTimeList = attendancePunchClassItemConfig.stream()
                .filter(item -> Objects.nonNull(item.getLatestPunchOutTime()))
                .map(PunchClassItemConfigDO::getLatestPunchOutTime)
                .sorted()
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(latestPunchOutTimeList)) {
            userDayReissueInfoVO.setLatestPunchTime(latestPunchOutTimeList.get(latestPunchOutTimeList.size() - 1));
        }
        //休息日，则需要查询前后两天的最早最晚下班时间是否跨天
        if (Objects.isNull(punchClassId)) {
            //获取当前时间-1day,+1day的班次配置
            Long preDayId = Long.valueOf(DateUtil.format(DateUtil.offsetDay(DateUtil.parse(dayId.toString()), -1), "yyyyMMdd"));
            Long nextDayId = Long.valueOf(DateUtil.format(DateUtil.offsetDay(DateUtil.parse(dayId.toString()), 1), "yyyyMMdd"));
            List<UserShiftConfigDO> employeeConfigDOS = userShiftConfigManage.selectUserShiftByDayIds(userId, Arrays.asList(preDayId, nextDayId));
            Map<Long, List<UserShiftConfigDO>> employeeConfigMap = employeeConfigDOS.stream().collect(Collectors.groupingBy(UserShiftConfigDO::getDayId));
            List<UserShiftConfigDO> preDayEmployeeConfig = employeeConfigMap.get(preDayId);
            List<UserShiftConfigDO> nextDayEmployeeConfig = employeeConfigMap.get(nextDayId);
            //设置休息日最早上班时间
            Date earliestPunchTime = DateUtils.str2Date(dayId + " 00:00:00", DateFormatterUtil.FORMAT_YYYYMMDD_HH_MM_SS);
            userDayReissueInfoVO.setEarliestPunchTime(earliestPunchTime);
            //设置休息日最晚下班时间
            Date latestPunchTime = DateUtils.str2Date(dayId + " 23:59:59", DateFormatterUtil.FORMAT_YYYYMMDD_HH_MM_SS);
            userDayReissueInfoVO.setLatestPunchTime(latestPunchTime);
            //设置前一天最晚打卡时间为休息日最早打卡时间
            if (CollectionUtils.isNotEmpty(preDayEmployeeConfig) && Objects.nonNull(preDayEmployeeConfig.get(0).getPunchClassConfigId())) {
                List<PunchClassItemConfigDO> preItemConfigDOS = punchClassConfigManage.selectClassItemByClassIds(Arrays.asList(preDayEmployeeConfig.get(0).getPunchClassConfigId()));
                punchClassConfigQueryService.transferItemConfigTimeFormat(preItemConfigDOS, preDayId);
                List<Date> prelatestPunchOutTimeList = preItemConfigDOS.stream()
                        .filter(item -> Objects.nonNull(item.getLatestPunchOutTime()))
                        .map(PunchClassItemConfigDO::getLatestPunchOutTime)
                        .sorted()
                        .collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(prelatestPunchOutTimeList)) {
                    Date date = prelatestPunchOutTimeList.get(prelatestPunchOutTimeList.size() - 1);
                    //如果上一天的最晚打卡时间和当前时间dayId一致，则取前一天最晚打卡时间为休息日最早打卡时间
                    if (dayId.equals(Long.valueOf(DateUtil.format(date, DateFormatterUtil.FORMAT_YYYYMMDD)))) {
                        userDayReissueInfoVO.setEarliestPunchTime(date);
                    }
                }
            }
            //设置后一天最早打卡时间为休息日最晚打卡时间
            if (CollectionUtils.isNotEmpty(nextDayEmployeeConfig) && Objects.nonNull(nextDayEmployeeConfig.get(0).getPunchClassConfigId())) {
                List<PunchClassItemConfigDO> nextItemConfigDOS = punchClassConfigManage.selectClassItemByClassIds(Arrays.asList(nextDayEmployeeConfig.get(0).getPunchClassConfigId()));
                punchClassConfigQueryService.transferItemConfigTimeFormat(nextItemConfigDOS, nextDayId);
                List<Date> nextEarlistPunchOutTimeList = nextItemConfigDOS.stream()
                        .filter(item -> Objects.nonNull(item.getEarliestPunchInTime()))
                        .map(PunchClassItemConfigDO::getEarliestPunchInTime)
                        .sorted()
                        .collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(nextEarlistPunchOutTimeList)) {
                    Date date = nextEarlistPunchOutTimeList.get(0);
                    //如果上一天的最晚打卡时间和当前时间dayId一致，则取前一天最晚打卡时间为休息日最早打卡时间
                    if (dayId.equals(Long.valueOf(DateUtil.format(date, DateFormatterUtil.FORMAT_YYYYMMDD)))) {
                        userDayReissueInfoVO.setLatestPunchTime(date);
                    }
                }
            }
        }
    }


    /**
     * 补卡申请(点击新增按钮调用 新增/暂存)
     *
     * @param param
     * @return
     */
    public ApprovalResultVO reissueCardAdd(ReissueCardAddParam param) {
        log.info("reissueCardAdd | ReissueCardAddParam :{}", JSON.toJSONString(param));
        ApprovalResultVO resultVO = new ApprovalResultVO();
        // 1. 封装单据参数
        commonFormOperationService.userBaseInfoBuild(null, null, param);
        // 不在灰度人员里走旧系统，新系统不处理
        if (!migrationService.verifyUserIsEnableNewAttendance(param.getUserId())) {
            log.info("reissueCardAdd | userInfo is on old attendance, userCode:{}" + param.getUserCode());
            throw BusinessException.get(ErrorCodeEnum.THE_GRAY_SCALE_PERSONNEL_NEED_TO_OPERATE_IN_THE_NEW_SYSTEM.getCode()
                    , I18nUtils.getMessage(ErrorCodeEnum.THE_GRAY_SCALE_PERSONNEL_NEED_TO_OPERATE_IN_THE_NEW_SYSTEM.getDesc()));
        }
        //暂存不校验任何信息，直接落库成功，提交时校验
        AttendanceFormDO attendanceFormDO = new AttendanceFormDO();
        List<AttendanceFormAttrDO> formAttrDOList = new ArrayList<>();
        List<AttendanceFormRelationDO> formAttrRelationDOList = new ArrayList<>();
        EmployeeAbnormalOperationRecordDO employeeAbnormalOperationRecord = new EmployeeAbnormalOperationRecordDO();
        //特殊操作，暂存，也需要异常的天数
        // 2. 暂存逻辑及业务校验
        EmployeeAbnormalAttendanceDO abnormalAttendanceDO = reissueSaveCheck(param, attendanceFormDO);
        // 3. 构建表单信息
        //注意，审批发起后，需要先扣除打卡次数1次
        this.reissueCardDataAddBuild(param, attendanceFormDO, formAttrRelationDOList, formAttrDOList, employeeAbnormalOperationRecord, abnormalAttendanceDO);
        //暂存不需要调用bpm
        if (param.getOperationType() == 0) {
            attendanceApprovalManage.reissueFormAdd(attendanceFormDO, formAttrRelationDOList, formAttrDOList, null, null, null);
            return resultVO;
        }
        // 4. bpm审批流构建
        ApprovalInitInfoApiDTO initInfoApiDTO = new ApprovalInitInfoApiDTO();
        this.reissueCardAddApprovalDataBuild(initInfoApiDTO, attendanceFormDO, formAttrDOList);
        ApprovalInfoCreateResultDTO approvalInfoCreateResultDTO = bpmApprovalClient.addApprovalInfo(initInfoApiDTO);
        attendanceFormDO.setApprovalId(approvalInfoCreateResultDTO.getApprovalId());
        // 5. 落库
        attendanceApprovalManage.reissueFormAdd(attendanceFormDO, formAttrRelationDOList, formAttrDOList, employeeAbnormalOperationRecord, param.getUserCardConfigDO(), abnormalAttendanceDO);
        resultVO.setApprovalCode(approvalInfoCreateResultDTO.getApprovalCode());
        resultVO.setApprovalId(approvalInfoCreateResultDTO.getApprovalId());
        return resultVO;
    }

    private EmployeeAbnormalAttendanceDO reissueSaveCheck(ReissueCardAddParam param,
                                                          AttendanceFormDO attendanceFormDO) {
        EmployeeAbnormalAttendanceDO abnormalAttendanceDO = null;
        if (param.getOperationType() == 0 && param.getAbnormalId() != null) {
            List<EmployeeAbnormalAttendanceDO> abnormalAttendanceDOList = employeeAbnormalAttendanceService.selectByIdList(Arrays.asList(param.getAbnormalId()));
            if (CollectionUtils.isEmpty(abnormalAttendanceDOList)) {
                throw BusinessException.get(ErrorCodeEnum.USER_NOT_HAVE_ABNORMAL_ATTENDANCE_RECORD_ON_THE_CURRENT_DAY.getCode(), I18nUtils.getMessage(ErrorCodeEnum.USER_NOT_HAVE_ABNORMAL_ATTENDANCE_RECORD_ON_THE_CURRENT_DAY.getDesc()));
            }
        }

        if (Objects.isNull(param.getAbnormalId())) {
            param.setReissueCardType(AttendanceAbnormalTypeEnum.REISSUE_CARD.getCode());
        }

        if (param.getOperationType() == 1) {
            abnormalAttendanceDO = reissueAddDataCheck(param);
            attendanceFormDO.setFormStatus(FormStatusEnum.IN_REVIEW.getCode());
        }
        return abnormalAttendanceDO;
    }

    private EmployeeAbnormalAttendanceDO reissueAddDataCheck(ReissueCardAddParam param) {
        if (Objects.isNull(param.getReissueCardDayId())) {
            throw BusinessException.get(ErrorCodeEnum.PARAM_NOT_NULL.getCode(), I18nUtils.getMessage(ErrorCodeEnum.PARAM_NOT_NULL.getDesc()));
        }
        List<PunchClassItemConfigDO> itemConfigDOList;
        List<PunchConfigDO> punchConfigDOList;
        Date startDate;
        String abnormalType = null;
        String abnormalExtend = null;
        EmployeeAbnormalAttendanceDO abnormalAttendanceDO = null;
        if (param.getAbnormalId() != null) {
            abnormalAttendanceDO = commonFormOperationService.userAbnormalRecordCheck(param.getAbnormalId());
            if (!AttendanceAbnormalTypeEnum.getRessiueCodeList().contains(abnormalAttendanceDO.getAbnormalType())) {
                throw BusinessException.get(ErrorCodeEnum.REISSUE_CARD_NOT_HANDLER_THIS_ABNORMAL_TYPE.getCode(), I18nUtils.getMessage(ErrorCodeEnum.REISSUE_CARD_NOT_HANDLER_THIS_ABNORMAL_TYPE.getDesc()));
            }
            itemConfigDOList = punchClassConfigManage.selectClassItemByClassIds(Arrays.asList(abnormalAttendanceDO.getPunchClassConfigId()));
            punchConfigDOList = punchConfigManage.getPunchConfigByIds(
                    Collections.singletonList(abnormalAttendanceDO.getPunchConfigId()));
            startDate = abnormalAttendanceDO.getDate();
            abnormalType = abnormalAttendanceDO.getAbnormalType();
            abnormalExtend = abnormalAttendanceDO.getExtend();
        } else {
            startDate = DateUtils.str2Date(param.getReissueCardDayId() + " 00:00:00",
                    DateFormatterUtil.FORMAT_YYYYMMDD_HH_MM_SS);
            itemConfigDOList = punchClassConfigManage.selectClassItemByClassIds(Arrays.asList(param.getReissueCardClassId()));
            punchConfigDOList = punchConfigManage.getPunchConfigByIds(
                    Collections.singletonList(param.getReissueCardConfigId()));
        }
        // 考勤周期校验
        AttendanceCycleConfigDO userAttendanceCycleConfig = attendanceCycleConfigService.getUserAttendanceCycleConfig(param.getUserId());
        attendanceCycleConfigService.checkDateInAttendanceCycle(new Date(), userAttendanceCycleConfig, startDate);

        //查询用户补卡次数,因为有7天的可补卡周期，所已补卡的日期，可能存在上个周期和本次周期
        int residueReissueCardCount = userReissueCardCountService.getUserResidueReissueCardCount(param, startDate);
        if (residueReissueCardCount < 1) {
            throw BusinessException.get(ErrorCodeEnum.USER_ATTENDANCE_REISSUE_CARD_COUNT_IS_OVER.getCode(), I18nUtils.getMessage(ErrorCodeEnum.USER_ATTENDANCE_REISSUE_CARD_COUNT_IS_OVER.getDesc()));
        }
        //小程序版本问题、校验补卡时间是否为空
        if (Objects.isNull(param.getCorrectPunchTime())) {
            throw BusinessException.get(ErrorCodeEnum.CORRECT_PUNCH_TIME_NOT_EMPTY.getCode(),
                    I18nUtils.getMessage(ErrorCodeEnum.CORRECT_PUNCH_TIME_NOT_EMPTY.getDesc()));
        }
        //构建参数
        if (StringUtils.isNotBlank(abnormalType)) {
            param.setReissueCardType(abnormalType);
        }
        param.setResidueReissueCardCount(residueReissueCardCount);
        AttendanceDayCycleDTO attendanceDayCycleDTO = attendanceBaseService.getUserAttendanceCycleConfigDay(param.getReissueCardDayId(),
                userAttendanceCycleConfig);
        if (attendanceDayCycleDTO != null) {
            param.setAttendanceStartDate(attendanceDayCycleDTO.getAttendanceStartDate());
            param.setAttendanceEndDate(attendanceDayCycleDTO.getAttendanceEndDate());
        }
        StringBuilder stringBuilder = new StringBuilder();
        if (CollectionUtils.isNotEmpty(itemConfigDOList)) {
            for (PunchClassItemConfigDO itemConfigDO : itemConfigDOList) {
                //自由打卡规则
                if (CollectionUtils.isNotEmpty(punchConfigDOList)
                        && StringUtils.equalsIgnoreCase(punchConfigDOList.get(0).getConfigType(),
                        PunchConfigTypeEnum.FLEXIBLE_WORK_TWICE.name())) {
                    stringBuilder.append(DateUtil.format(itemConfigDO.getEarliestPunchInTime(), "HH:mm")).append("-")
                            .append(DateUtil.format(itemConfigDO.getLatestPunchOutTime(), "HH:mm")).append("&");
                    continue;
                }
                stringBuilder.append(DateUtil.format(itemConfigDO.getPunchInTime(), "HH:mm")).append("-")
                        .append(DateUtil.format(itemConfigDO.getPunchOutTime(), "HH:mm")).append("&");
            }
        }
        if (stringBuilder.toString().length() > 0) {
            param.setPunchConfigClassItemInfo(stringBuilder.toString().substring(0, stringBuilder.toString().length() - 1));
        }
        //需要根据不同的异常类型在根据打卡时刻来自动填充
        if (StringUtils.isNotBlank(abnormalExtend)) {
            AbnormalExtendDTO abnormalExtendDTO = JSON.parseObject(abnormalExtend, AbnormalExtendDTO.class);
            param.setActualPunchTime(abnormalExtendDTO.getActualPunchTime());
        }
        //查询用户当日最早最晚打卡记录(过滤补卡得)
        List<EmployeePunchRecordDO> userPunchRecordList = punchRecordService.getUserPunchRecords(param.getUserCode(), Arrays.asList(param.getReissueCardDayId()))
                .stream()
                .filter(item -> !SourceTypeEnum.REISSUE_CARD.name().equals(item.getSourceType()))
                .sorted(Comparator.comparing(EmployeePunchRecordDO::getPunchTime)).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(userPunchRecordList)) {
            if (Objects.isNull(abnormalAttendanceDO) || userPunchRecordList.size() > 1) {
                param.setEarlyPunchTime(userPunchRecordList.get(0).getPunchTime());
                param.setLatePunchTime(userPunchRecordList.get(userPunchRecordList.size() - 1).getPunchTime());
            } else {
                if (!AttendanceAbnormalTypeEnum.BEFORE_OFFICE_LACK.getCode().equals(abnormalAttendanceDO.getAbnormalType())) {
                    param.setEarlyPunchTime(userPunchRecordList.get(0).getPunchTime());
                }
                if (!AttendanceAbnormalTypeEnum.AFTER_OFFICE_LACK.getCode().equals(abnormalAttendanceDO.getAbnormalType())) {
                    param.setLatePunchTime(userPunchRecordList.get(userPunchRecordList.size() - 1).getPunchTime());
                }
            }
        }
        return abnormalAttendanceDO;
    }

    private String getConfigClassItemInfo(List<PunchClassItemConfigDO> attendancePunchClassItemConfig) {
        StringBuilder stringBuilder = new StringBuilder();
        if (CollectionUtils.isNotEmpty(attendancePunchClassItemConfig)) {
            for (PunchClassItemConfigDO itemConfigDO : attendancePunchClassItemConfig) {
                if (itemConfigDO.getPunchInTime() != null) {
                    stringBuilder.append(DateUtil.format(itemConfigDO.getPunchInTime(), "HH:mm")).append("-")
                            .append(DateUtil.format(itemConfigDO.getPunchOutTime(), "HH:mm")).append("&");
                    continue;
                }
                stringBuilder.append(DateUtil.format(itemConfigDO.getEarliestPunchInTime(), "HH:mm")).append("-")
                        .append(DateUtil.format(itemConfigDO.getLatestPunchOutTime(), "HH:mm")).append("&");
            }
            if (stringBuilder.toString().length() > 0) {
                return stringBuilder.toString().substring(0, stringBuilder.toString().length() - 1);
            }
        }
        return null;
    }

    /**
     * 补时长申请(点击新增按钮调用)
     *
     * @param param
     * @return
     */
    public ApprovalResultVO addDuration(AddDurationParam param) {
        log.info("addDuration | AddDurationParam :{}", JSON.toJSONString(param));
        ApprovalResultVO resultVO = new ApprovalResultVO();
        // 封装实体
        this.buildDurationParam(param);
        // 不在灰度人员里走旧系统，新系统不处理
        if (!migrationService.verifyUserIsEnableNewAttendance(param.getUserId())) {
            log.info("addDuration | userInfo is on old attendance, userCode:{}" + param.getUserCode());
            throw BusinessException.get(ErrorCodeEnum.THE_GRAY_SCALE_PERSONNEL_NEED_TO_OPERATE_IN_THE_NEW_SYSTEM.getCode()
                    , I18nUtils.getMessage(ErrorCodeEnum.THE_GRAY_SCALE_PERSONNEL_NEED_TO_OPERATE_IN_THE_NEW_SYSTEM.getDesc()));
        }
        //暂存不校验任何信息，直接落库成功，提交时校验
        AttendanceFormDO attendanceFormDO = new AttendanceFormDO();
        List<AttendanceFormAttrDO> formAttrDOList = new ArrayList<>();
        List<AttendanceFormRelationDO> formAttrRelationDOList = new ArrayList<>();
        EmployeeAbnormalOperationRecordDO employeeAbnormalOperationRecord = new EmployeeAbnormalOperationRecordDO();
        List<EmployeeAbnormalAttendanceDO> abnormalAttendanceDOList = employeeAbnormalAttendanceService.selectByIdList(Collections.singletonList(param.getAbnormalId()));
        if (CollectionUtils.isEmpty(abnormalAttendanceDOList)) {
            throw BusinessException.get(ErrorCodeEnum.USER_NOT_HAVE_ABNORMAL_ATTENDANCE_RECORD_ON_THE_CURRENT_DAY.getCode(), I18nUtils.getMessage(ErrorCodeEnum.USER_NOT_HAVE_ABNORMAL_ATTENDANCE_RECORD_ON_THE_CURRENT_DAY.getDesc()));
        }
        EmployeeAbnormalAttendanceDO abnormalAttendanceDO = abnormalAttendanceDOList.get(0);

        if (param.getOperationType() == 1) {
            attendanceFormDO.setFormStatus(FormStatusEnum.IN_REVIEW.getCode());
        }

        this.addDurationDateBuild(param, attendanceFormDO, formAttrRelationDOList, formAttrDOList, employeeAbnormalOperationRecord, abnormalAttendanceDO);
        //暂存不需要调用bpm
        if (param.getOperationType() == 0) {
            attendanceApprovalManage.addDurationAdd(attendanceFormDO, formAttrRelationDOList, formAttrDOList, null, null);
            return resultVO;
        }
        ApprovalInitInfoApiDTO initInfoApiDTO = new ApprovalInitInfoApiDTO();
        this.addDurationApprovalDataBuild(initInfoApiDTO, attendanceFormDO, formAttrDOList, abnormalAttendanceDO);
        ApprovalInfoCreateResultDTO approvalInfoCreateResultDTO = bpmApprovalClient.addApprovalInfo(initInfoApiDTO);
        attendanceFormDO.setApprovalId(approvalInfoCreateResultDTO.getApprovalId());
        attendanceApprovalManage.addDurationAdd(attendanceFormDO, formAttrRelationDOList, formAttrDOList, employeeAbnormalOperationRecord, abnormalAttendanceDO);
        resultVO.setApprovalCode(approvalInfoCreateResultDTO.getApprovalCode());
        resultVO.setApprovalId(approvalInfoCreateResultDTO.getApprovalId());
        return resultVO;
    }

    /**
     * 补卡申请(暂存后更新/驳回后更新)
     *
     * @param param
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public ApprovalResultVO reissueCardUpdate(ReissueCardAddParam param) {
        log.info("reissueCardUpdate | ReissueCardAddParam :{}", JSON.toJSONString(param));
        ApprovalResultVO resultVO = new ApprovalResultVO();
        if (param.getOperationType() == 2) {
            return resultVO;
        }
        // 1. 实体构建
        commonFormOperationService.userBaseInfoBuild(null, null, param);
        // 不在灰度人员里走旧系统，新系统不处理
        if (!migrationService.verifyUserIsEnableNewAttendance(param.getUserId())) {
            log.info("reissueCardUpdate | userInfo is on old attendance, userCode:{}" + param.getUserCode());
            throw BusinessException.get(ErrorCodeEnum.THE_GRAY_SCALE_PERSONNEL_NEED_TO_OPERATE_IN_THE_NEW_SYSTEM.getCode()
                    , I18nUtils.getMessage(ErrorCodeEnum.THE_GRAY_SCALE_PERSONNEL_NEED_TO_OPERATE_IN_THE_NEW_SYSTEM.getDesc()));
        }
        AttendanceFormDetailBO formDetailBO = formManage.getFormDetailById(param.getApplicationFormId());
        if (formDetailBO == null || formDetailBO.getFormDO() == null) {
            throw BusinessException.get(ErrorCodeEnum.APPLICATION_FORM_IS_EMPTY.getCode(), I18nUtils.getMessage(ErrorCodeEnum.APPLICATION_FORM_IS_EMPTY.getDesc()));
        }
        //暂存不校验必填项，提交时校验，暂存后进入单据管理-状态为暂存待办
        AttendanceFormDO formDO = formDetailBO.getFormDO();
        List<AttendanceFormAttrDO> formAttrDOList = new ArrayList<>();
        List<AttendanceFormRelationDO> formAttrRelationDOList = new ArrayList<>();
        EmployeeAbnormalOperationRecordDO employeeAbnormalOperationRecord = new EmployeeAbnormalOperationRecordDO();
        //特殊操作，暂存，也需要异常的天数
        EmployeeAbnormalAttendanceDO abnormalAttendanceDO = reissueSaveCheck(param, formDO);
        this.reissueCardDataUpdateBuild(param, formDO, formAttrRelationDOList, formAttrDOList, employeeAbnormalOperationRecord, abnormalAttendanceDO);
        //暂存不需要调用bpm
        if (param.getOperationType() == 0) {
            //落库,需要删除所有旧的属性表/关联表数据   主表不能删除在新增，要保持单号的一致，主表更新
            commonFormOperationService.formUpdate(formDO, formDetailBO, attendanceApprovalManage);
            attendanceApprovalManage.reissueFormAdd(null, formAttrRelationDOList, formAttrDOList, null, null, null);
            return resultVO;
        }
        ApprovalInitInfoApiDTO initInfoApiDTO = new ApprovalInitInfoApiDTO();
        this.reissueCardAddApprovalDataBuild(initInfoApiDTO, formDO, formAttrDOList);
        //本次保存是否是驳回后重提
        commonFormOperationService.updateFormBuild(formDO, formDetailBO, initInfoApiDTO, resultVO);
        attendanceApprovalManage.reissueFormAdd(null, formAttrRelationDOList, formAttrDOList, employeeAbnormalOperationRecord, param.getUserCardConfigDO(), abnormalAttendanceDO);
        return resultVO;
    }

    /**
     * 补卡-撤销申请
     *
     * @param param
     * @return
     */
    public ApprovalResultVO reissueCardRevokeAdd(RevokeAddParam param) {
        ApprovalResultVO resultVO = new ApprovalResultVO();
        if (param.getOperationType() == 2) {
            return resultVO;
        }
        AttendanceFormDetailBO formDetailBO = null;
        if (param.getApplicationFormId() != null) {
            formDetailBO = formManage.getFormDetailById(param.getApplicationFormId());
        } else {
            formDetailBO = formManage.getFormDetailByCode(param.getApplicationFormCode());
        }
        if (formDetailBO == null || formDetailBO.getFormDO() == null) {
            throw BusinessException.get(ErrorCodeEnum.APPLICATION_FORM_IS_EMPTY.getCode(), I18nUtils.getMessage(ErrorCodeEnum.APPLICATION_FORM_IS_EMPTY.getDesc()));
        }
        AttendanceFormDO formDO = formDetailBO.getFormDO();
        // 不在灰度人员里走旧系统，新系统不处理
        if (!migrationService.verifyUserIsEnableNewAttendance(formDO.getUserId())) {
            log.info("reissueCardRevokeAdd | userInfo is on old attendance, userCode:{}" + formDO.getUserCode());
            throw BusinessException.get(ErrorCodeEnum.THE_GRAY_SCALE_PERSONNEL_NEED_TO_OPERATE_IN_THE_NEW_SYSTEM.getCode()
                    , I18nUtils.getMessage(ErrorCodeEnum.THE_GRAY_SCALE_PERSONNEL_NEED_TO_OPERATE_IN_THE_NEW_SYSTEM.getDesc()));
        }
        List<AttendanceFormAttrDO> leaveAttrDOS = formDetailBO.getAttrDOList();
        Map<String, AttendanceFormAttrDO> attrMap = leaveAttrDOS.stream().collect(Collectors.toMap(o -> o.getAttrKey(), o -> o, (v1, v2) -> v1));
        // mex特殊校验
        commonFormOperationService.checkMexUser(formDO.getUserId(),
                ErrorCodeEnum.WAREHOUSE_NOT_ALLOW_CARD_REVOKE,
                EmploymentTypeEnum.TYPE_OF_DEFAULT_WAREHOUSE);
        //防止被重复撤销
        commonFormOperationService.repeatRevokeCheck(formDO.getId());
        AttendanceFormAttrDO reissueCardDayId = attrMap.get(ApplicationFormAttrKeyEnum.reissueCardDayId.getLowerCode());
        if (reissueCardDayId == null) {
            throw BusinessException.get(ErrorCodeEnum.ABNORMAL_RECORD_DATE_NOT_EMPTY.getCode(),
                    I18nUtils.getMessage(ErrorCodeEnum.ABNORMAL_RECORD_DATE_NOT_EMPTY.getDesc()));
        }
        Date startDate = DateUtil.parse(reissueCardDayId.getAttrValue(), "yyyyMMdd");
        // 考勤周期校验
        attendanceCycleConfigService.checkDateInUserAttendanceCycle(formDO.getUserId(), startDate);
        AttendanceFormDO attendanceFormDO = new AttendanceFormDO();
        List<AttendanceFormAttrDO> formAttrDOList = new ArrayList<>();
        List<AttendanceFormRelationDO> formRelationDOS = new ArrayList<>();
        // 【补卡申请通过】撤销补卡申请：重新生成一条补卡取消单，创建补卡取消单与补卡申请单的关联。所以不需要操作假期详情数据列表
        List<UserLeaveStageDetailDO> userLeaveStageDetailList = Lists.newArrayList();
        commonFormOperationService.commonRevokeDataAddBuild(param, formDetailBO, attendanceFormDO, formRelationDOS, formAttrDOList,
                idWorkUtils.nextNo(ApprovalNoPrefixEnum.REISSUE_CARD_REVOKE), FormTypeEnum.REISSUE_CARD_REVOKE.getCode(), FormStatusEnum.IN_REVIEW.getCode());

        ApprovalInitInfoApiDTO initInfoApiDTO = new ApprovalInitInfoApiDTO();
        this.reissueCardRevokeAddApprovalDataBuild(initInfoApiDTO, formDO.getApprovalId(), attendanceFormDO, formAttrDOList);
        ApprovalInfoCreateResultDTO approvalInfoCreateResultDTO = bpmApprovalClient.addApprovalInfo(initInfoApiDTO);
        attendanceFormDO.setApprovalId(approvalInfoCreateResultDTO.getApprovalId());
        attendanceApprovalManage.formAdd(attendanceFormDO, formRelationDOS, formAttrDOList, null, null, userLeaveStageDetailList, null);
        resultVO.setApprovalCode(approvalInfoCreateResultDTO.getApprovalCode());
        resultVO.setApprovalId(approvalInfoCreateResultDTO.getApprovalId());
        return resultVO;
    }

    /**
     * 补卡申请预览
     *
     * @param param
     * @return
     */
    public List<ApprovalDetailStepRecordDTO> reissueCardPreview(ReissueCardAddParam param) {
        List<ApprovalDetailStepRecordDTO> resultDTOList = new ArrayList<>();
        if (param.getOperationType() != 2) {
            return resultDTOList;
        }
        commonFormOperationService.userBaseInfoBuild(null, null, param);
        //暂存就是保存前一步，必填数据都要填写完毕才可以
        EmployeeAbnormalAttendanceDO abnormalAttendanceDO = reissueAddDataCheck(param);
        //暂存不校验任何信息，直接落库成功，提交时校验
        AttendanceFormDO attendanceFormDO = new AttendanceFormDO();
        List<AttendanceFormAttrDO> formAttrDOList = new ArrayList<>();
        List<AttendanceFormRelationDO> formAttrRelationDOList = new ArrayList<>();
        EmployeeAbnormalOperationRecordDO employeeAbnormalOperationRecord = new EmployeeAbnormalOperationRecordDO();
        //参数构建，不落库
        this.reissueCardDataAddBuild(param, attendanceFormDO, formAttrRelationDOList, formAttrDOList, employeeAbnormalOperationRecord, abnormalAttendanceDO);

        ApprovalInitInfoApiDTO initInfoApiDTO = new ApprovalInitInfoApiDTO();
        this.reissueCardAddApprovalDataBuild(initInfoApiDTO, attendanceFormDO, formAttrDOList);

        ApprovalEmptyRecordApiQuery query = BeanUtils.convert(initInfoApiDTO, ApprovalEmptyRecordApiQuery.class);
        List<ApprovalEmptyRecordApiDTO> recordApiDTOList = bpmApprovalClient.getEmptyApprovalRecords(query);
        if (CollectionUtils.isEmpty(recordApiDTOList)) {
            return resultDTOList;
        }
        //previewDTOBuild(recordApiDTOList, resultDTOList);
        commonFormOperationService.previewDTOBuildContainsErrors(recordApiDTOList, resultDTOList, param.getUserCode());
        return resultDTOList;
    }

    /**
     * 补时长申请预览
     *
     * @param param
     * @return
     */
    public List<ApprovalDetailStepRecordDTO> addDurationPreview(AddDurationParam param) {
        List<ApprovalDetailStepRecordDTO> resultDTOList = new ArrayList<>();
        if (param.getOperationType() != 2) {
            return resultDTOList;
        }
        // 封装实体
        this.buildDurationParam(param);

        List<EmployeeAbnormalAttendanceDO> abnormalAttendanceDOList = employeeAbnormalAttendanceService.selectByIdList(Collections.singletonList(param.getAbnormalId()));
        if (CollectionUtils.isEmpty(abnormalAttendanceDOList)) {
            throw BusinessException.get(ErrorCodeEnum.USER_NOT_HAVE_ABNORMAL_ATTENDANCE_RECORD_ON_THE_CURRENT_DAY.getCode(), I18nUtils.getMessage(ErrorCodeEnum.USER_NOT_HAVE_ABNORMAL_ATTENDANCE_RECORD_ON_THE_CURRENT_DAY.getDesc()));
        }
        EmployeeAbnormalAttendanceDO abnormalAttendanceDO = abnormalAttendanceDOList.get(0);

        AttendanceFormDO attendanceFormDO = new AttendanceFormDO();
        List<AttendanceFormAttrDO> formAttrDOList = new ArrayList<>();
        List<AttendanceFormRelationDO> formAttrRelationDOList = new ArrayList<>();
        EmployeeAbnormalOperationRecordDO employeeAbnormalOperationRecord = new EmployeeAbnormalOperationRecordDO();
        //参数构建，不落库
        addDurationDateBuild(param, attendanceFormDO, formAttrRelationDOList, formAttrDOList, employeeAbnormalOperationRecord, abnormalAttendanceDO);

        ApprovalInitInfoApiDTO initInfoApiDTO = new ApprovalInitInfoApiDTO();
        addDurationApprovalDataBuild(initInfoApiDTO, attendanceFormDO, formAttrDOList, abnormalAttendanceDO);

        ApprovalEmptyRecordApiQuery query = BeanUtils.convert(initInfoApiDTO, ApprovalEmptyRecordApiQuery.class);
        List<ApprovalEmptyRecordApiDTO> recordApiDTOList = bpmApprovalClient.getEmptyApprovalRecords(query);
        if (CollectionUtils.isEmpty(recordApiDTOList)) {
            return resultDTOList;
        }
        commonFormOperationService.previewDTOBuildContainsErrors(recordApiDTOList, resultDTOList, param.getUserCode());
        return resultDTOList;
    }

    /**
     * 补卡-撤销申请预览
     *
     * @param param
     * @return
     */
    public List<ApprovalDetailStepRecordDTO> reissueCardRevokePreview(RevokeAddParam param) {
        List<ApprovalDetailStepRecordDTO> resultDTOList = new ArrayList<>();
        if (param.getOperationType() != 2) {
            return resultDTOList;
        }
        AttendanceFormDetailBO formDetailBO = formManage.getFormDetailById(param.getApplicationFormId());
        if (formDetailBO == null || formDetailBO.getFormDO() == null) {
            throw BusinessException.get(ErrorCodeEnum.APPLICATION_FORM_IS_EMPTY.getCode(), I18nUtils.getMessage(ErrorCodeEnum.APPLICATION_FORM_IS_EMPTY.getDesc()));
        }
        AttendanceFormDO formDO = formDetailBO.getFormDO();
        List<AttendanceFormAttrDO> leaveAttrDOS = formDetailBO.getAttrDOList();
        Map<String, AttendanceFormAttrDO> attrMap = leaveAttrDOS.stream().collect(Collectors.toMap(o -> o.getAttrKey(), o -> o, (v1, v2) -> v1));
        //防止被重复撤销
        commonFormOperationService.repeatRevokeCheck(formDO.getId());
        AttendanceFormAttrDO reissueCardDayId = attrMap.get(ApplicationFormAttrKeyEnum.reissueCardDayId.getLowerCode());
        if (reissueCardDayId == null) {
            throw BusinessException.get(ErrorCodeEnum.ABNORMAL_RECORD_DATE_NOT_EMPTY.getCode(),
                    I18nUtils.getMessage(ErrorCodeEnum.ABNORMAL_RECORD_DATE_NOT_EMPTY.getDesc()));
        }
        Date startDate = DateUtil.parse(reissueCardDayId.getAttrValue(), "yyyyMMdd");
        // 考勤周期校验
        attendanceCycleConfigService.checkDateInUserAttendanceCycle(formDO.getUserId(), startDate);

        AttendanceFormDO attendanceFormDO = new AttendanceFormDO();
        List<AttendanceFormAttrDO> formAttrDOList = new ArrayList<>();
        List<AttendanceFormRelationDO> formRelationDOS = new ArrayList<>();
        commonFormOperationService.commonRevokeDataAddBuild(param, formDetailBO, attendanceFormDO, formRelationDOS, formAttrDOList,
                idWorkUtils.nextNo(ApprovalNoPrefixEnum.REISSUE_CARD_REVOKE),
                FormTypeEnum.REISSUE_CARD_REVOKE.getCode(), FormStatusEnum.IN_REVIEW.getCode());

        ApprovalInitInfoApiDTO initInfoApiDTO = new ApprovalInitInfoApiDTO();
        this.reissueCardAddApprovalDataBuild(initInfoApiDTO, attendanceFormDO, formAttrDOList);

        ApprovalEmptyRecordApiQuery query = BeanUtils.convert(initInfoApiDTO, ApprovalEmptyRecordApiQuery.class);
        List<ApprovalEmptyRecordApiDTO> recordApiDTOList = bpmApprovalClient.getEmptyApprovalRecords(query);
        if (CollectionUtils.isEmpty(recordApiDTOList)) {
            return resultDTOList;
        }
        //previewDTOBuild(recordApiDTOList, resultDTOList);
        commonFormOperationService.previewDTOBuildContainsErrors(recordApiDTOList, resultDTOList, formDO.getUserCode());
        return resultDTOList;
    }

    private void reissueCardDataAddBuild(ReissueCardAddParam param,
                                         AttendanceFormDO formDO,
                                         List<AttendanceFormRelationDO> formRelationDOList,
                                         List<AttendanceFormAttrDO> formAttrDOList,
                                         EmployeeAbnormalOperationRecordDO employeeAbnormalOperationRecord,
                                         EmployeeAbnormalAttendanceDO abnormalAttendanceDO) {
        // 参数构建
        commonFormOperationService.commonFormAdd(param, formDO, FormTypeEnum.REISSUE_CARD.getCode());
        formDO.setId(IdWorkerUtil.getId());
        formDO.setApplicationCode(idWorkUtils.nextNo(ApprovalNoPrefixEnum.REISSUE_CARD));

        if (Lists.newArrayList(CountryCodeEnum.MEX.getCode(), CountryCodeEnum.BRA.getCode()).contains(param.getCountry())
                && ObjectUtil.equal(param.getIsWarehouseStaff(), BusinessConstant.Y)) {
            // 如果是仓内
            formDO.setFormType(FormTypeEnum.WAREHOUSE_REISSUE_CARD.getCode());
        }
        if (StringUtils.isBlank(formDO.getFormStatus())) {
            //为暂存
            formDO.setFormStatus(FormStatusEnum.STAGING.getCode());
        }
        BaseDOUtil.fillDOInsert(formDO);
        // 审批字段设置
        this.reissueCardFieldBuild(param, formDO, formAttrDOList, formRelationDOList,
                employeeAbnormalOperationRecord, abnormalAttendanceDO);
    }

    private void reissueCardDataUpdateBuild(ReissueCardAddParam param,
                                            AttendanceFormDO formDO,
                                            List<AttendanceFormRelationDO> formRelationDOList,
                                            List<AttendanceFormAttrDO> formAttrDOList,
                                            EmployeeAbnormalOperationRecordDO employeeAbnormalOperationRecord,
                                            EmployeeAbnormalAttendanceDO abnormalAttendanceDO) {
        // 参数构建
        commonFormOperationService.commonFormAdd(param, formDO, FormTypeEnum.REISSUE_CARD.getCode());
        if (Lists.newArrayList(CountryCodeEnum.MEX.getCode(), CountryCodeEnum.BRA.getCode()).contains(param.getCountry())
                && ObjectUtil.equal(param.getIsWarehouseStaff(), BusinessConstant.Y)) {
            // 如果是仓内
            formDO.setFormType(FormTypeEnum.WAREHOUSE_REISSUE_CARD.getCode());
        }
        if (StringUtils.isBlank(formDO.getFormStatus())) {
            //为暂存
            formDO.setFormStatus(FormStatusEnum.STAGING.getCode());
        }
        BaseDOUtil.fillDOUpdate(formDO);

        // 审批字段设置
        this.reissueCardFieldBuild(param, formDO, formAttrDOList, formRelationDOList,
                employeeAbnormalOperationRecord, abnormalAttendanceDO);
    }

    private void reissueCardFieldBuild(ReissueCardAddParam param,
                                       AttendanceFormDO formDO,
                                       List<AttendanceFormAttrDO> formAttrDOList,
                                       List<AttendanceFormRelationDO> formRelationDOList,
                                       EmployeeAbnormalOperationRecordDO employeeAbnormalOperationRecord,
                                       EmployeeAbnormalAttendanceDO abnormalAttendanceDO) {
        if (param.getReissueCardDayId() != null) {
            formAttrDOList.add(commonFormOperationService.insertAttrDOBuild(formDO.getId(), ApplicationFormAttrKeyEnum.reissueCardDayId.getLowerCode(), param.getReissueCardDayId().toString()));
        }
        if (StringUtils.isNotBlank(param.getReissueCardType())) {
            formAttrDOList.add(commonFormOperationService.insertAttrDOBuild(formDO.getId(), ApplicationFormAttrKeyEnum.reissueCardType.getLowerCode(), param.getReissueCardType()));
        }
        if (param.getResidueReissueCardCount() != null) {
            formAttrDOList.add(commonFormOperationService.insertAttrDOBuild(formDO.getId(), ApplicationFormAttrKeyEnum.residueReissueCardCount.getLowerCode(), param.getResidueReissueCardCount().toString()));
        }
        if (param.getAttendanceStartDate() != null) {
            formAttrDOList.add(commonFormOperationService.insertAttrDOBuild(formDO.getId(), ApplicationFormAttrKeyEnum.attendanceStartDate.getLowerCode(), DateUtil.format(param.getAttendanceStartDate(), "yyyy-MM-dd HH:mm:ss")));
        }
        if (param.getAttendanceEndDate() != null) {
            formAttrDOList.add(commonFormOperationService.insertAttrDOBuild(formDO.getId(), ApplicationFormAttrKeyEnum.attendanceEndDate.getLowerCode(), DateUtil.format(param.getAttendanceEndDate(), "yyyy-MM-dd HH:mm:ss")));
        }
        if (param.getActualPunchTime() != null) {
            formAttrDOList.add(commonFormOperationService.insertAttrDOBuild(formDO.getId(), ApplicationFormAttrKeyEnum.actualPunchTime.getLowerCode(), DateUtil.format(param.getActualPunchTime(), "yyyy-MM-dd HH:mm:ss")));
        }
        if (param.getCorrectPunchTime() != null) {
            formAttrDOList.add(commonFormOperationService.insertAttrDOBuild(formDO.getId(), ApplicationFormAttrKeyEnum.correctPunchTime.getLowerCode(), DateUtil.format(param.getCorrectPunchTime(), "yyyy-MM-dd HH:mm:ss")));
        }
        if (StringUtils.isNotBlank(param.getPunchConfigClassItemInfo())) {
            formAttrDOList.add(commonFormOperationService.insertAttrDOBuild(formDO.getId(), ApplicationFormAttrKeyEnum.punchConfigClassItemInfo.getLowerCode(), param.getPunchConfigClassItemInfo()));
        }
        if (StringUtils.isNotBlank(param.getRemark())) {
            formAttrDOList.add(commonFormOperationService.insertAttrDOBuild(formDO.getId(), ApplicationFormAttrKeyEnum.remark.getLowerCode(), param.getRemark()));
        }
        if (CollectionUtils.isNotEmpty(param.getAttachmentList())) {
            formAttrDOList.add(commonFormOperationService.insertAttrDOBuild(formDO.getId(), ApplicationFormAttrKeyEnum.attachmentList.getLowerCode(), JSON.toJSONString(param.getAttachmentList())));
        }
        if (Objects.nonNull(param.getEarlyPunchTime())) {
            formAttrDOList.add(commonFormOperationService.insertAttrDOBuild(formDO.getId(), ApplicationFormAttrKeyEnum.earlyPunchTime.getLowerCode(), DateUtil.format(param.getEarlyPunchTime(), "yyyy-MM-dd HH:mm:ss")));
        }
        if (Objects.nonNull(param.getLatePunchTime())) {
            formAttrDOList.add(commonFormOperationService.insertAttrDOBuild(formDO.getId(), ApplicationFormAttrKeyEnum.latePunchTime.getLowerCode(), DateUtil.format(param.getLatePunchTime(), "yyyy-MM-dd HH:mm:ss")));
        }

        //默认是没有被撤销
        formAttrDOList.add(commonFormOperationService.insertAttrDOBuild(formDO.getId(), ApplicationFormAttrKeyEnum.isRevoke.getLowerCode(), BusinessConstant.N.toString()));

        //把异常ID也关联下
        if (param.getAbnormalId() != null) {
            commonFormOperationService.associateAbnormal(formDO, formRelationDOList,
                    employeeAbnormalOperationRecord, param.getAbnormalId());
            employeeAbnormalOperationRecord.setOperationType(AbnormalOperationTypeEnum.REISSUE_CARD.getCode());
            BaseDOUtil.fillDOInsert(formDO);
        }

        if (abnormalAttendanceDO != null && param.getOperationType() == 1) {
            abnormalAttendanceDO.setStatus(AbnormalAttendanceStatusEnum.IN_REVIEW.getCode());
            BaseDOUtil.fillDOUpdate(abnormalAttendanceDO);
        }

        //暂存不需要扣次数
        if (param.getOperationType() == 0) {
            return;
        }
        UserCycleReissueCardCountDO userCardConfigDO = param.getUserCardConfigDO();
        userCardConfigDO.setUsedReissueCardCount(userCardConfigDO.getUsedReissueCardCount() + 1);
        BaseDOUtil.fillDOUpdate(userCardConfigDO);
    }


    private void addDurationDateBuild(AddDurationParam param,
                                      AttendanceFormDO formDO,
                                      List<AttendanceFormRelationDO> formRelationDOList,
                                      List<AttendanceFormAttrDO> formAttrDOList,
                                      EmployeeAbnormalOperationRecordDO employeeAbnormalOperationRecord,
                                      EmployeeAbnormalAttendanceDO abnormalAttendanceDO) {
        // 参数构建
        commonFormOperationService.commonFormAdd(param, formDO, FormTypeEnum.ADD_DURATION.getCode());
        formDO.setId(IdWorkerUtil.getId());
        formDO.setApplicationCode(idWorkUtils.nextNo(ApprovalNoPrefixEnum.ADD_DURATION));
        if (StringUtils.isBlank(formDO.getFormStatus())) {
            //为暂存
            formDO.setFormStatus(FormStatusEnum.STAGING.getCode());
        }
        BaseDOUtil.fillDOInsert(formDO);

        formAttrDOList.add(commonFormOperationService.insertAttrDOBuild(formDO.getId(), ApplicationFormAttrKeyEnum.abnormalType.getLowerCode(), AttendanceAbnormalTypeEnum.ABNORMAL_DURATION.getCode()));
        formAttrDOList.add(commonFormOperationService.insertAttrDOBuild(formDO.getId(), ApplicationFormAttrKeyEnum.abnormalDate.getLowerCode(), DateUtil.formatDate(abnormalAttendanceDO.getDate())));

        if (Objects.nonNull(param.getActualAttendanceTime())) {
            formAttrDOList.add(commonFormOperationService.insertAttrDOBuild(formDO.getId(), ApplicationFormAttrKeyEnum.actualAttendanceTime.getLowerCode(), param.getActualAttendanceTime().toString()));
        }
        if (Objects.nonNull(param.getActualWorkingHours())) {
            formAttrDOList.add(commonFormOperationService.insertAttrDOBuild(formDO.getId(), ApplicationFormAttrKeyEnum.actualWorkingHours.getLowerCode(), param.getActualWorkingHours().toString()));
        }
        if (Objects.nonNull(param.getNewActualAttendanceTime())) {
            formAttrDOList.add(commonFormOperationService.insertAttrDOBuild(formDO.getId(), ApplicationFormAttrKeyEnum.newActualAttendanceTime.getLowerCode(), param.getNewActualAttendanceTime().toString()));
        }
        if (Objects.nonNull(param.getNewActualWorkingHours())) {
            formAttrDOList.add(commonFormOperationService.insertAttrDOBuild(formDO.getId(), ApplicationFormAttrKeyEnum.newActualWorkingHours.getLowerCode(), param.getNewActualWorkingHours().toString()));
        }
        if (StringUtils.isNotBlank(param.getRemark())) {
            formAttrDOList.add(commonFormOperationService.insertAttrDOBuild(formDO.getId(), ApplicationFormAttrKeyEnum.remark.getLowerCode(), param.getRemark()));
        }
        if (CollectionUtils.isNotEmpty(param.getAttachmentList())) {
            formAttrDOList.add(commonFormOperationService.insertAttrDOBuild(formDO.getId(), ApplicationFormAttrKeyEnum.attachmentList.getLowerCode(), JSON.toJSONString(param.getAttachmentList())));
        }

        //默认是没有被撤销
        formAttrDOList.add(commonFormOperationService.insertAttrDOBuild(formDO.getId(), ApplicationFormAttrKeyEnum.isRevoke.getLowerCode(), BusinessConstant.N.toString()));

        //把异常ID也关联下
        commonFormOperationService.associateAbnormal(formDO, formRelationDOList,
                employeeAbnormalOperationRecord, param.getAbnormalId());
        employeeAbnormalOperationRecord.setOperationType(AbnormalOperationTypeEnum.ADD_DURATION.getCode());
        BaseDOUtil.fillDOInsert(formDO);

        if (param.getOperationType() == 1) {
            abnormalAttendanceDO.setStatus(AbnormalAttendanceStatusEnum.IN_REVIEW.getCode());
            BaseDOUtil.fillDOUpdate(abnormalAttendanceDO);
        }
    }


    private void reissueCardAddApprovalDataBuild(ApprovalInitInfoApiDTO initInfoApiDTO,
                                                 AttendanceFormDO formDO,
                                                 List<AttendanceFormAttrDO> formAttrDOList) {
        // 实体构建
        commonFormOperationService.commonApprovalAdd(initInfoApiDTO, formDO);

        List<ApprovalTypeFieldApiDTO> fieldApiDTOList = new ArrayList<>();
        // 审批单基础信息
        this.setBaseField(fieldApiDTOList, formDO);

        // 仓内外包人员不存在岗位,所以针对仓内外包人员不设置岗位：仓内补卡是新的流程，新流程不需要岗位字段。所以这边直接拦截了
        if (ObjectUtil.notEqual(formDO.getFormType(), FormTypeEnum.WAREHOUSE_REISSUE_CARD.getCode())) {
            //被申请人岗位
            AttendancePost postInfo = postService.getByPostId(formDO.getPostId());
            if (postInfo == null) {
                throw BusinessException.get(ErrorCodeEnum.POST_NOT_EXITS.getCode(), I18nUtils.getMessage(ErrorCodeEnum.POST_NOT_EXITS.getDesc()));
            }
            commonFormOperationService.customFieldBuild(fieldApiDTOList, ReissueCardCustomFieldEnum.POST_NAME.getCode(), postInfo.getPostNameEn(), null);
        }

        // mex仓内：用工类型获取
        AttendanceUser userInfo = userService.getByUserId(formDO.getUserId());
        if (ObjectUtil.isNotNull(userInfo)) {
            EmploymentTypeEnum employmentTypeEnum = EmploymentTypeEnum.getByCode(userInfo.getEmployeeType());
            Map<String, String> employmentTypeMap = new HashMap<>();
            employmentTypeMap.put(LanguageTypeEnum.zh_CN.getCode(), employmentTypeEnum.getDesc());
            employmentTypeMap.put(LanguageTypeEnum.en_US.getCode(), employmentTypeEnum.getDescEn());
            commonFormOperationService.customFieldBuild(fieldApiDTOList, ReissueCardCustomFieldEnum.EMPLOYMENT_TYPE.getCode(), employmentTypeEnum.getDescEn(), employmentTypeMap);
        }

        // mex仓内考勤特有字段：常驻国是mex，并且是仓内
        // TODO 仓内考勤补卡相关
//        if (Lists.newArrayList(CountryCodeEnum.MEX.getCode(), CountryCodeEnum.BRA.getCode()).contains(formDO.getCountry())
//                && ObjectUtil.equal(formDO.getIsWarehouseStaff(), BusinessConstant.Y)) {
//            WarehouseDetailParam warehouseDetailParam = new WarehouseDetailParam();
//            warehouseDetailParam.setUserCodeList(Collections.singletonList(formDO.getUserCode()));
//            String reissueCardDateStr = CollectionUtils.isNotEmpty(reissueCardDate) ? reissueCardDate.get(0).getAttrValue() : null;
//            if (StringUtils.isNotEmpty(reissueCardDateStr)) {
//                Date reissueDate = DateUtil.parse(reissueCardDateStr, DateFormatterUtil.FORMAT_YYYYMMDD);
//                warehouseDetailParam.setStartTime(reissueDate);
//                warehouseDetailParam.setEndTime(reissueDate);
//            }
//            List<HrmsWarehouseDetailDO> warehouseDetailDOS = warehouseDetailDao.selectByCondition(warehouseDetailParam);
//            if (CollectionUtils.isNotEmpty(warehouseDetailDOS)) {
//                HrmsWarehouseDetailDO warehouseDetailDO = warehouseDetailDOS.get(0);
//                deptInfo deptDO = hrmsDeptManage.selectById(warehouseDetailDO.getOcId());
//                Map<String, String> vendorNameMap = warehouseSupplierService.getSupplierByCodes(Collections.singletonList(warehouseDetailDO.getVendorCode()));
//                commonFormOperationService.customFieldBuild(fieldApiDTOList, ReissueCardCustomFieldEnum.WORK_OC_NAME.getCode(), deptDO.getDeptNameEn(), null);
//                commonFormOperationService.customFieldBuild(fieldApiDTOList, ReissueCardCustomFieldEnum.WORK_VENDOR_NAME.getCode(), vendorNameMap.get(warehouseDetailDO.getVendorCode()), null);
//            }
//        }
        // 补卡单据信息
        this.setReissueField(fieldApiDTOList, formAttrDOList);

        initInfoApiDTO.setFieldApiDTOList(fieldApiDTOList);

        log.info("reissueCardAddApprovalDataBuild||调用BPM出参值为:{}", JSON.toJSONString(initInfoApiDTO));

    }

    private void reissueCardRevokeAddApprovalDataBuild(ApprovalInitInfoApiDTO initInfoApiDTO,
                                                       Long outOfOfficeApprovalId,
                                                       AttendanceFormDO formDO,
                                                       List<AttendanceFormAttrDO> formAttrDOList) {
        // 实体构建
        commonFormOperationService.commonApprovalAdd(initInfoApiDTO, formDO);

        //关联请假单据的审批ID
        if (outOfOfficeApprovalId != null) {
            initInfoApiDTO.setRelationApprovalIdList(Arrays.asList(outOfOfficeApprovalId));
        }

        List<ApprovalTypeFieldApiDTO> fieldApiDTOList = new ArrayList<>();
        // 审批单基础信息
        this.setBaseField(fieldApiDTOList, formDO);

        //被申请人岗位
        AttendancePost postInfo = postService.getByPostId(formDO.getPostId());
        if (postInfo == null) {
            throw BusinessException.get(ErrorCodeEnum.POST_NOT_EXITS.getCode(), I18nUtils.getMessage(ErrorCodeEnum.POST_NOT_EXITS.getDesc()));
        }
        commonFormOperationService.customFieldBuild(fieldApiDTOList, ReissueCardCustomFieldEnum.POST_NAME.getCode(), postInfo.getPostNameEn(), null);

        //撤销原因
        List<AttendanceFormAttrDO> revokeReason = formAttrDOList.stream().filter(item -> StringUtils.equalsIgnoreCase(item.getAttrKey(), ApplicationFormAttrKeyEnum.revokeReason.getLowerCode())).collect(Collectors.toList());
        commonFormOperationService.customFieldBuild(fieldApiDTOList, ReissueCardCustomFieldEnum.REVOKE_REASON.getCode(), CollectionUtils.isNotEmpty(revokeReason) ? revokeReason.get(0).getAttrValue() : null, null);

        // 补卡单据信息
        this.setReissueField(fieldApiDTOList, formAttrDOList);

        initInfoApiDTO.setFieldApiDTOList(fieldApiDTOList);

        log.info("reissueCardRevokeAddApprovalDataBuild||调用BPM出参值为:{}", JSON.toJSONString(initInfoApiDTO));

    }


    private void addDurationApprovalDataBuild(ApprovalInitInfoApiDTO initInfoApiDTO,
                                              AttendanceFormDO formDO,
                                              List<AttendanceFormAttrDO> formAttrDOList,
                                              EmployeeAbnormalAttendanceDO abnormalAttendanceDO) {
        // 实体构建
        commonFormOperationService.commonApprovalAdd(initInfoApiDTO, formDO);

        List<ApprovalTypeFieldApiDTO> fieldApiDTOList = new ArrayList<>();
        // 审批单基础信息
        this.setBaseField(fieldApiDTOList, formDO);

        // 用工类型
        AttendanceUser userInfo = userService.getByUserId(formDO.getUserId());
        if (ObjectUtil.isNotNull(userInfo)) {
            EmploymentTypeEnum employmentTypeEnum = EmploymentTypeEnum.getByCode(userInfo.getEmployeeType());
            Map<String, String> employmentTypeMap = new HashMap<>();
            employmentTypeMap.put(LanguageTypeEnum.zh_CN.getCode(), employmentTypeEnum.getDesc());
            employmentTypeMap.put(LanguageTypeEnum.en_US.getCode(), employmentTypeEnum.getDescEn());
            commonFormOperationService.customFieldBuild(fieldApiDTOList, AddDurationCustomFieldEnum.EMPLOYMENT_TYPE.getCode(), employmentTypeEnum.getDescEn(), employmentTypeMap);
        }

        // 工作网点&工作供应商
        // TODO 仓内考勤
//        if (Lists.newArrayList(CountryCodeEnum.MEX.getCode(), CountryCodeEnum.BRA.getCode()).contains(formDO.getCountry())
//                && ObjectUtil.equal(formDO.getIsWarehouseStaff(), BusinessConstant.Y)) {
//            WarehouseDetailAbnormalDO warehouseDetailAbnormalDO = warehouseDetailAbnormalDao.selectByAbnormalId(abnormalAttendanceDO.getId());
//            if (Objects.nonNull(warehouseDetailAbnormalDO)) {
//                HrmsEntDeptDO deptDO = hrmsDeptManage.selectById(warehouseDetailAbnormalDO.getOcId());
//                Map<String, String> deptNameMap = new HashMap<>();
//                deptNameMap.put(LanguageTypeEnum.zh_CN.getCode(), deptDO.getDeptNameCn());
//                deptNameMap.put(LanguageTypeEnum.en_US.getCode(), deptDO.getDeptNameEn());
//
//                List<VendorInfoSimpleApiDTO> vendorInfoSimpleApiDTOS = vendorService.selectVendorList(Collections.singletonList(warehouseDetailAbnormalDO.getVendorId()));
//                Map<Long, VendorInfoSimpleApiDTO> vendorInfoMap = vendorInfoSimpleApiDTOS.stream().collect(Collectors.toMap(VendorInfoSimpleApiDTO::getVendorId, o -> o, (v1, v2) -> v1));
//                customFieldBuild(fieldApiDTOList, AddDurationCustomFieldEnum.WORK_OC_NAME.getCode(), deptDO.getDeptNameEn(), deptNameMap);
//                if (Objects.nonNull(vendorInfoMap.get(warehouseDetailAbnormalDO.getVendorId()))) {
//                    customFieldBuild(fieldApiDTOList, AddDurationCustomFieldEnum.WORK_VENDOR_NAME.getCode(), vendorInfoMap.get(warehouseDetailAbnormalDO.getVendorId()).getVendorName(), null);
//                }
//            }
//        }

        //异常日期
        List<AttendanceFormAttrDO> abnormalDate = formAttrDOList.stream().filter(item -> StringUtils.equalsIgnoreCase(item.getAttrKey(), ApplicationFormAttrKeyEnum.abnormalDate.getLowerCode())).collect(Collectors.toList());
        commonFormOperationService.customFieldBuild(fieldApiDTOList, AddDurationCustomFieldEnum.ABNORMAL_DATE.getCode(), CollectionUtils.isNotEmpty(abnormalDate) ? abnormalDate.get(0).getAttrValue() : null, null);

        //班次
        if (Objects.nonNull(abnormalAttendanceDO.getPunchClassConfigId())) {
            List<PunchClassConfigDO> punchClassConfigList = punchClassConfigManage.selectByClassIds(Collections.singletonList(abnormalAttendanceDO.getPunchClassConfigId()));
            commonFormOperationService.customFieldBuild(fieldApiDTOList, AddDurationCustomFieldEnum.CLASS_NAME.getCode(),
                    CollectionUtils.isNotEmpty(punchClassConfigList)
                            ? punchClassConfigList.get(0).getClassName() : null, null);
        }

        //补时长异常类型
        List<AttendanceFormAttrDO> addDurationType = formAttrDOList.stream().filter(item -> StringUtils.equalsIgnoreCase(item.getAttrKey(), ApplicationFormAttrKeyEnum.abnormalType.getLowerCode())).collect(Collectors.toList());
        String type = null;
        String typeDetail = null;
        Map<String, String> typeMap = new HashMap<>();
        Map<String, String> typeDetailMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(addDurationType)) {
            type = addDurationType.get(0).getAttrValue();
            AttendanceAbnormalTypeEnum abnormalTypeEnum = AttendanceAbnormalTypeEnum.getInstanceByCode(type);
            if (Objects.nonNull(abnormalTypeEnum)) {
                //设置异常类型中英文内容多语
                type = abnormalTypeEnum.getDescEn();
                typeMap.put(LanguageTypeEnum.zh_CN.getCode(), abnormalTypeEnum.getDesc());
                typeMap.put(LanguageTypeEnum.en_US.getCode(), abnormalTypeEnum.getDescEn());
                //设置异常描述中英文内容多语
                typeDetail = abnormalTypeEnum.getDetailEn();
                typeDetailMap.put(LanguageTypeEnum.zh_CN.getCode(), abnormalTypeEnum.getDetail());
                typeDetailMap.put(LanguageTypeEnum.en_US.getCode(), abnormalTypeEnum.getDetailEn());
            }
        }
        commonFormOperationService.customFieldBuild(fieldApiDTOList, AddDurationCustomFieldEnum.ABNORMAL_TYPE.getCode(), type, typeMap);
        //补时长异常类型描述
        commonFormOperationService.customFieldBuild(fieldApiDTOList, AddDurationCustomFieldEnum.ABNORMAL_TYPE_DESC.getCode(), typeDetail, typeDetailMap);

        //实出勤时长
        List<AttendanceFormAttrDO> actualAttendanceTime = formAttrDOList.stream().filter(item -> StringUtils.equalsIgnoreCase(item.getAttrKey(), ApplicationFormAttrKeyEnum.actualAttendanceTime.getLowerCode())).collect(Collectors.toList());
        commonFormOperationService.customFieldBuild(fieldApiDTOList, AddDurationCustomFieldEnum.ACTUAL_ATTENDANCE_TIME.getCode(), CollectionUtils.isNotEmpty(actualAttendanceTime) ? actualAttendanceTime.get(0).getAttrValue() : null, null);

        //更新后的实出勤时长
        List<AttendanceFormAttrDO> newActualAttendanceTime = formAttrDOList.stream().filter(item -> StringUtils.equalsIgnoreCase(item.getAttrKey(), ApplicationFormAttrKeyEnum.newActualAttendanceTime.getLowerCode())).collect(Collectors.toList());
        commonFormOperationService.customFieldBuild(fieldApiDTOList, AddDurationCustomFieldEnum.NEW_ACTUAL_ATTENDANCE_TIME.getCode(), CollectionUtils.isNotEmpty(newActualAttendanceTime) ? newActualAttendanceTime.get(0).getAttrValue() : null, null);

        //实工作时长
        List<AttendanceFormAttrDO> actualWorkingHours = formAttrDOList.stream().filter(item -> StringUtils.equalsIgnoreCase(item.getAttrKey(), ApplicationFormAttrKeyEnum.actualWorkingHours.getLowerCode())).collect(Collectors.toList());
        commonFormOperationService.customFieldBuild(fieldApiDTOList, AddDurationCustomFieldEnum.ACTUAL_WORKING_HOURS.getCode(), CollectionUtils.isNotEmpty(actualWorkingHours) ? actualWorkingHours.get(0).getAttrValue() : null, null);

        //更新后的实工作时长
        List<AttendanceFormAttrDO> newActualWorkingHours = formAttrDOList.stream().filter(item -> StringUtils.equalsIgnoreCase(item.getAttrKey(), ApplicationFormAttrKeyEnum.newActualWorkingHours.getLowerCode())).collect(Collectors.toList());
        commonFormOperationService.customFieldBuild(fieldApiDTOList, AddDurationCustomFieldEnum.NEW_ACTUAL_WORKING_HOURS.getCode(), CollectionUtils.isNotEmpty(newActualWorkingHours) ? newActualWorkingHours.get(0).getAttrValue() : null, null);

        //备注
        List<AttendanceFormAttrDO> remark = formAttrDOList.stream().filter(item -> StringUtils.equalsIgnoreCase(item.getAttrKey(), ApplicationFormAttrKeyEnum.remark.getLowerCode())).collect(Collectors.toList());
        commonFormOperationService.customFieldBuild(fieldApiDTOList, AddDurationCustomFieldEnum.REMARK.getCode(), CollectionUtils.isNotEmpty(remark) ? remark.get(0).getAttrValue() : null, null);

        //附件
        this.setFileField(fieldApiDTOList, formAttrDOList);

        initInfoApiDTO.setFieldApiDTOList(fieldApiDTOList);

        log.info("durationAddApprovalDataBuild||调用BPM出参值为:{}", JSON.toJSONString(initInfoApiDTO));

    }

    private void setBaseField(List<ApprovalTypeFieldApiDTO> fieldApiDTOList,
                              AttendanceFormDO formDO) {
        //被审批人ID
        commonFormOperationService.customFieldBuild(fieldApiDTOList, ReissueCardCustomFieldEnum.BE_APPROVERED_USER_ID.getCode(), formDO.getUserId() != null ? formDO.getUserId().toString() : null, null);

        //被申请人部门ID
        commonFormOperationService.customFieldBuild(fieldApiDTOList, ReissueCardCustomFieldEnum.DEPT_ID.getCode(), formDO.getDeptId() != null ? formDO.getDeptId().toString() : null, null);

        //被申请人所在国
        commonFormOperationService.customFieldBuild(fieldApiDTOList, ReissueCardCustomFieldEnum.USER_COUNTRY.getCode(), formDO.getCountry(), null);

        //被申请人结算国
        commonFormOperationService.customFieldBuild(fieldApiDTOList, ReissueCardCustomFieldEnum.USER_ORIGIN_COUNTRY.getCode(), formDO.getOriginCountry(), null);

        //被申请人姓名
        commonFormOperationService.customFieldBuild(fieldApiDTOList, ReissueCardCustomFieldEnum.USER_NAME.getCode(), formDO.getUserName(), null);

        //被申请人编码
        commonFormOperationService.customFieldBuild(fieldApiDTOList, ReissueCardCustomFieldEnum.USER_CODE.getCode(), formDO.getUserCode(), null);

        //被申请人部门
        AttendanceDept deptInfo = deptService.getByDeptId(formDO.getDeptId());
        if (deptInfo == null) {
            throw BusinessException.get(ErrorCodeEnum.DEPT_NOT_EXITS.getCode(), I18nUtils.getMessage(ErrorCodeEnum.DEPT_NOT_EXITS.getDesc()));
        }
        Map<String, String> deptMap = new HashMap<>();
        deptMap.put(LanguageTypeEnum.zh_CN.getCode(), deptInfo.getDeptNameCn());
        deptMap.put(LanguageTypeEnum.en_US.getCode(), deptInfo.getDeptNameEn());
        commonFormOperationService.customFieldBuild(fieldApiDTOList, ReissueCardCustomFieldEnum.DEPT_NAME.getCode(), deptInfo.getDeptNameEn(), deptMap);
    }

    private void setFileField(List<ApprovalTypeFieldApiDTO> fieldApiDTOList,
                              List<AttendanceFormAttrDO> formAttrDOList) {
        List<AttendanceFormAttrDO> attachment = formAttrDOList.stream()
                .filter(item -> StringUtils.equalsIgnoreCase(item.getAttrKey(),
                        ApplicationFormAttrKeyEnum.attachmentList.getLowerCode()))
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(attachment)) {
            List<AttachmentDTO> attachmentList = JSON.parseArray(attachment.get(0).getAttrValue(), AttachmentDTO.class);
            List<FileTemplateApiDTO> fileTemplateApiDTOList = new ArrayList<>();
            for (AttachmentDTO attachmentDTO : attachmentList) {
                FileTemplateApiDTO apiDTO = new FileTemplateApiDTO();
                apiDTO.setFileName(attachmentDTO.getAttachmentName());
                apiDTO.setFileType(attachmentDTO.getAttachmentType());
                apiDTO.setFileUrl(attachmentDTO.getUrlPath());
                fileTemplateApiDTOList.add(apiDTO);
            }
            commonFormOperationService.customFieldBuild(fieldApiDTOList, ReissueCardCustomFieldEnum.ATTACHMENT.getCode(), JSON.toJSONString(fileTemplateApiDTOList), null);
        }
    }

    private void buildDurationParam(AddDurationParam param) {
        Long userId = param.getUserId();
        AttendanceUser userInfo = userService.getByUserId(userId);
        if (userInfo == null) {
            throw BusinessException.get(ErrorCodeEnum.ACCOUNT_NOT_EXITS.getCode(), I18nUtils.getMessage(ErrorCodeEnum.ACCOUNT_NOT_EXITS.getDesc()));
        }
        param.setUserCode(userInfo.getUserCode());
        param.setUserName(userInfo.getUserName());
        param.setDeptId(userInfo.getDeptId());
        param.setPostId(userInfo.getPostId());
        param.setCountry(userInfo.getLocationCountry());
        param.setOriginCountry(userInfo.getOriginCountry());
        param.setIsWarehouseStaff(userInfo.getIsWarehouseStaff());
    }

    private void setReissueField(List<ApprovalTypeFieldApiDTO> fieldApiDTOList,
                                 List<AttendanceFormAttrDO> formAttrDOList) {
        //补卡日期
        List<AttendanceFormAttrDO> reissueCardDate = formAttrDOList.stream().filter(item -> StringUtils.equalsIgnoreCase(item.getAttrKey(), ApplicationFormAttrKeyEnum.reissueCardDayId.getLowerCode())).collect(Collectors.toList());
        commonFormOperationService.customFieldBuild(fieldApiDTOList, ReissueCardCustomFieldEnum.REISSUE_CARD_DATE.getCode(), CollectionUtils.isNotEmpty(reissueCardDate) ? reissueCardDate.get(0).getAttrValue() : null, null);

        //补卡异常类型
        List<AttendanceFormAttrDO> reissueCardType = formAttrDOList.stream().filter(item -> StringUtils.equalsIgnoreCase(item.getAttrKey(), ApplicationFormAttrKeyEnum.reissueCardType.getLowerCode())).collect(Collectors.toList());
        String type = null;
        String typeDetail = null;
        Map<String, String> typeMap = new HashMap<>();
        Map<String, String> typeDetailMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(reissueCardType)) {
            type = reissueCardType.get(0).getAttrValue();
            AttendanceAbnormalTypeEnum abnormalTypeEnum = AttendanceAbnormalTypeEnum.getInstanceByCode(type);
            if (Objects.nonNull(abnormalTypeEnum)) {
                //设置异常类型中英文内容多语
                type = abnormalTypeEnum.getDescEn();
                typeMap.put(LanguageTypeEnum.zh_CN.getCode(), abnormalTypeEnum.getDesc());
                typeMap.put(LanguageTypeEnum.en_US.getCode(), abnormalTypeEnum.getDescEn());
                //设置异常描述中英文内容多语
                typeDetail = abnormalTypeEnum.getDetailEn();
                typeDetailMap.put(LanguageTypeEnum.zh_CN.getCode(), abnormalTypeEnum.getDetail());
                typeDetailMap.put(LanguageTypeEnum.en_US.getCode(), abnormalTypeEnum.getDetailEn());
            }
        }
        commonFormOperationService.customFieldBuild(fieldApiDTOList, ReissueCardCustomFieldEnum.REISSUE_CARD_TYPE.getCode(), type, typeMap);
        //补卡异常类型描述
        commonFormOperationService.customFieldBuild(fieldApiDTOList, ReissueCardCustomFieldEnum.REISSUE_CARD_TYPE_DESC.getCode(), typeDetail, typeDetailMap);

        //剩余可用补卡次数
        List<AttendanceFormAttrDO> residueReissueCardCount = formAttrDOList.stream().filter(item -> StringUtils.equalsIgnoreCase(item.getAttrKey(), ApplicationFormAttrKeyEnum.residueReissueCardCount.getLowerCode())).collect(Collectors.toList());
        commonFormOperationService.customFieldBuild(fieldApiDTOList, ReissueCardCustomFieldEnum.RESIDUE_REISSUE_CARD_COUNT.getCode(), CollectionUtils.isNotEmpty(residueReissueCardCount) ? residueReissueCardCount.get(0).getAttrValue() : null, null);

        //当前补卡日期对应的考勤周期起始时间
        List<AttendanceFormAttrDO> attendanceStartDate = formAttrDOList.stream().filter(item -> StringUtils.equalsIgnoreCase(item.getAttrKey(), ApplicationFormAttrKeyEnum.attendanceStartDate.getLowerCode())).collect(Collectors.toList());
        commonFormOperationService.customFieldBuild(fieldApiDTOList, ReissueCardCustomFieldEnum.ATTENDANCE_START_DATE.getCode(), CollectionUtils.isNotEmpty(attendanceStartDate) ? attendanceStartDate.get(0).getAttrValue() : null, null);

        //当前补卡日期对应的考勤周期截止时间
        List<AttendanceFormAttrDO> attendanceEndDate = formAttrDOList.stream().filter(item -> StringUtils.equalsIgnoreCase(item.getAttrKey(), ApplicationFormAttrKeyEnum.attendanceEndDate.getLowerCode())).collect(Collectors.toList());
        commonFormOperationService.customFieldBuild(fieldApiDTOList, ReissueCardCustomFieldEnum.ATTENDANCE_END_DATE.getCode(), CollectionUtils.isNotEmpty(attendanceEndDate) ? attendanceEndDate.get(0).getAttrValue() : null, null);

        //打卡规则对应的班次的所有的时刻信息
        List<AttendanceFormAttrDO> punchConfigClassItemInfo = formAttrDOList.stream().filter(item -> StringUtils.equalsIgnoreCase(item.getAttrKey(), ApplicationFormAttrKeyEnum.punchConfigClassItemInfo.getLowerCode())).collect(Collectors.toList());
        commonFormOperationService.customFieldBuild(fieldApiDTOList, ReissueCardCustomFieldEnum.PUNCH_CONFIG_CLASS_ITEM_INFO.getCode(), CollectionUtils.isNotEmpty(punchConfigClassItemInfo) ? punchConfigClassItemInfo.get(0).getAttrValue() : null, null);

        //实际打卡时间(没有就为空)，有多个取离上下班最近的一个
        List<AttendanceFormAttrDO> actualPunchTime = formAttrDOList.stream().filter(item -> StringUtils.equalsIgnoreCase(item.getAttrKey(), ApplicationFormAttrKeyEnum.actualPunchTime.getLowerCode())).collect(Collectors.toList());
        commonFormOperationService.customFieldBuild(fieldApiDTOList, ReissueCardCustomFieldEnum.ACTUAL_PUNCH_TIME.getCode(), CollectionUtils.isNotEmpty(actualPunchTime) ? actualPunchTime.get(0).getAttrValue() : null, null);

        //补卡后的时间(根据时刻时间来补)
        List<AttendanceFormAttrDO> correctPunchTime = formAttrDOList.stream().filter(item -> StringUtils.equalsIgnoreCase(item.getAttrKey(), ApplicationFormAttrKeyEnum.correctPunchTime.getLowerCode())).collect(Collectors.toList());
        commonFormOperationService.customFieldBuild(fieldApiDTOList, ReissueCardCustomFieldEnum.CORRECT_PUNCH_TIME.getCode(), CollectionUtils.isNotEmpty(correctPunchTime) ? correctPunchTime.get(0).getAttrValue() : null, null);

        //备注
        List<AttendanceFormAttrDO> remark = formAttrDOList.stream().filter(item -> StringUtils.equalsIgnoreCase(item.getAttrKey(), ApplicationFormAttrKeyEnum.remark.getLowerCode())).collect(Collectors.toList());
        commonFormOperationService.customFieldBuild(fieldApiDTOList, ReissueCardCustomFieldEnum.REMARK.getCode(), CollectionUtils.isNotEmpty(remark) ? remark.get(0).getAttrValue() : null, null);

        //实际最早打卡时间
        List<AttendanceFormAttrDO> earlyPunchTime = formAttrDOList.stream().filter(item -> StringUtils.equalsIgnoreCase(item.getAttrKey(), ApplicationFormAttrKeyEnum.earlyPunchTime.getLowerCode())).collect(Collectors.toList());
        commonFormOperationService.customFieldBuild(fieldApiDTOList, ReissueCardCustomFieldEnum.EARLY_PUNCH_TIME.getCode(), CollectionUtils.isNotEmpty(earlyPunchTime) ? earlyPunchTime.get(0).getAttrValue() : null, null);

        //实际最晚打卡时间
        List<AttendanceFormAttrDO> latePunchTime = formAttrDOList.stream().filter(item -> StringUtils.equalsIgnoreCase(item.getAttrKey(), ApplicationFormAttrKeyEnum.latePunchTime.getLowerCode())).collect(Collectors.toList());
        commonFormOperationService.customFieldBuild(fieldApiDTOList, ReissueCardCustomFieldEnum.LATE_PUNCH_TIME.getCode(), CollectionUtils.isNotEmpty(latePunchTime) ? latePunchTime.get(0).getAttrValue() : null, null);

        //附件
        this.setFileField(fieldApiDTOList, formAttrDOList);
    }

}



