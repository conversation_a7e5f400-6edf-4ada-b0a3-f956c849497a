package com.imile.attendance.user.impl;

import cn.hutool.core.util.ObjectUtil;
import com.imile.attendance.apollo.AttendanceProperties;
import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.context.RequestInfoHolder;
import com.imile.attendance.context.UserContext;
import com.imile.attendance.enums.ClassNatureEnum;
import com.imile.attendance.enums.CountryCodeEnum;
import com.imile.attendance.enums.EmploymentTypeEnum;
import com.imile.attendance.enums.ErrorCodeEnum;
import com.imile.attendance.enums.WorkStatusEnum;
import com.imile.attendance.infrastructure.idwork.DefaultIdWorker;
import com.imile.attendance.infrastructure.repository.common.AttendanceDeptService;
import com.imile.attendance.infrastructure.repository.common.AttendancePostService;
import com.imile.attendance.infrastructure.repository.common.dto.AttendanceDept;
import com.imile.attendance.infrastructure.repository.common.dto.AttendancePost;
import com.imile.attendance.infrastructure.repository.common.dto.AttendanceUserEntryRecord;
import com.imile.attendance.infrastructure.repository.employee.dao.UserClassNatureModifyRecordDao;
import com.imile.attendance.infrastructure.repository.employee.dao.UserInfoDao;
import com.imile.attendance.infrastructure.repository.employee.dto.UserBaseInfoDTO;
import com.imile.attendance.infrastructure.repository.employee.dto.UserDTO;
import com.imile.attendance.infrastructure.repository.employee.modle.UserClassNatureModifyRecordDO;
import com.imile.attendance.infrastructure.repository.employee.modle.UserInfoDO;
import com.imile.attendance.infrastructure.repository.employee.query.UserDaoQuery;
import com.imile.attendance.migration.MigrationService;
import com.imile.attendance.user.UserService;
import com.imile.attendance.user.vo.UserInfoVO;
import com.imile.attendance.user.vo.UserOptionVO;
import com.imile.attendance.util.BaseDOUtil;
import com.imile.attendance.util.BusinessFieldUtils;
import com.imile.common.enums.StatusEnum;
import com.imile.common.exception.BusinessException;
import com.imile.util.lang.I18nUtils;
import com.imile.util.user.UserEvnHolder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2025/4/19
 */
@Slf4j
@Service
public class UserServiceImpl implements UserService {
    @Resource
    private UserInfoDao userInfoDao;
    @Resource
    private UserClassNatureModifyRecordDao userClassNatureModifyRecordDao;
    @Resource
    private DefaultIdWorker defaultIdWorker;
    @Resource
    private AttendanceDeptService attendanceDeptService;
    @Resource
    private AttendancePostService attendancePostService;
    @Resource
    private AttendanceProperties attendanceProperties;
    @Resource
    private MigrationService migrationService;

    @Override
    public List<UserOptionVO> getUserAssociateList(UserDaoQuery userDaoQuery) {
        if (!buildUserPermissionQuery(userDaoQuery)) {
            return Collections.emptyList();
        }
        List<UserInfoDO> userInfoDOList = userInfoDao.selectByAssociateCondition(userDaoQuery);
        return userInfoDOList.stream()
                .map(s -> UserOptionVO.builder()
                        .id(s.getId())
                        .userCode(s.getUserCode())
                        .userName(getUnifiedUserName(s.getUserName(), s.getUserNameEn()))
                        .deptId(s.getDeptId())
                        .postId(s.getPostId())
                        .build())
                .collect(Collectors.toList());
    }

    @Override
    public UserBaseInfoDTO getUserBaseInfo(String userCode) {
        if (StringUtils.isEmpty(userCode)) {
            throw BusinessException.get(ErrorCodeEnum.PARAM_NOT_NULL.getCode(), I18nUtils.getMessage(ErrorCodeEnum.PARAM_NOT_NULL.getDesc()));
        }
        UserDTO userDTO = Optional.ofNullable(userInfoDao.getUserByCode(userCode))
                .orElseThrow(() -> BusinessException.get(ErrorCodeEnum.ACCOUNT_NOT_EXITS.getCode()
                        , I18nUtils.getMessage(ErrorCodeEnum.ACCOUNT_NOT_EXITS.getDesc())));
        // 封装部门、岗位信息
        AttendanceDept deptInfo = null;
        if (Objects.nonNull(userDTO.getDeptId())) {
            deptInfo = attendanceDeptService.getByDeptIdAndStatus(userDTO.getDeptId(), BusinessConstant.ALL_STATUS_LIST);
        }
        AttendancePost postInfo = null;
        if (Objects.nonNull(userDTO.getPostId())) {
            postInfo = attendancePostService.getByPostId(userDTO.getPostId());
        }
        // 封装基本信息
        return buildUserBaseInfo(userDTO, deptInfo, postInfo);
    }

    @Override
    public void setUserClassNature(String userCode, AttendanceUserEntryRecord attendanceUserEntryRecord) {
        UserInfoDO userInfo = userInfoDao.getByUserCode(userCode);
        if (Objects.isNull(userInfo)) {
            log.error("员工入职设置班次性质:用户查询为空");
            return;
        }
        // 不在灰度国家范围的人员里走旧系统，新系统不处理
        if (!migrationService.verifyUserIsEnableAttendanceForCountry(userInfo.getId())) {
            log.info("setUserClassNature | userInfo is not in new attendance country, userCode:{}", userInfo.getUserCode());
            return;
        }

        List<UserInfoDO> deptUserInfoList = userInfoDao.userList(UserDaoQuery.builder().deptId(userInfo.getDeptId()).build());
        int fixedClassCount = 0;
        int multipleClassCount = 0;
        for (UserInfoDO userInfoDO : deptUserInfoList) {
            if (StringUtils.isEmpty(userInfoDO.getClassNature())) {
                continue;
            }
            if (Objects.equals(ClassNatureEnum.FIXED_CLASS.name(), userInfoDO.getClassNature())) {
                fixedClassCount++;
            } else {
                multipleClassCount++;
            }
        }
        String classNature = ClassNatureEnum.FIXED_CLASS.name();
        if (multipleClassCount > fixedClassCount) {
            classNature = ClassNatureEnum.MULTIPLE_CLASS.name();
        }
        userInfo.setClassNature(classNature);
        BaseDOUtil.fillDOUpdateByUserOrSystem(userInfo);
        userInfoDao.updateById(userInfo);

        UserClassNatureModifyRecordDO userClassNatureModifyRecordDO = new UserClassNatureModifyRecordDO();
        userClassNatureModifyRecordDO.setId(defaultIdWorker.nextId());
        userClassNatureModifyRecordDO.setUserId(userInfo.getId());
        userClassNatureModifyRecordDO.setClassNature(classNature);
        userClassNatureModifyRecordDO.setEffectTime(attendanceUserEntryRecord.getConfirmDate());
        userClassNatureModifyRecordDO.setExpireTime(BusinessConstant.DEFAULT_END_TIME);
        userClassNatureModifyRecordDO.setIsLatest(BusinessConstant.Y);
        userClassNatureModifyRecordDO.setRemark("员工入职班次性质初始化");
        BaseDOUtil.fillDOInsertByUsrOrSystem(userClassNatureModifyRecordDO);
        userClassNatureModifyRecordDao.save(userClassNatureModifyRecordDO);
    }

    @Override
    public boolean checkValidUserAttendanceRange(Long userId) {
        UserInfoDO userInfoDO = userInfoDao.getByUserId(userId);
        if (Objects.isNull(userInfoDO)) {
            return false;
        }
        if (!(Objects.equals(StatusEnum.ACTIVE.getCode(), userInfoDO.getStatus())
                && Objects.equals(WorkStatusEnum.ON_JOB.getCode(), userInfoDO.getWorkStatus())
                && Objects.equals(BusinessConstant.N, userInfoDO.getIsDriver()))) {
            return false;
        }

        String locationCountry = userInfoDO.getLocationCountry();
        String employeeType = userInfoDO.getEmployeeType();
        if (CountryCodeEnum.SPECIAL_COUNTRY_FOR_PAGE_SHIFT.contains(locationCountry)) {
            return EmploymentTypeEnum.SPECIAL_COUNTRY_EMPLOYEE_TYPE_FOR_PAGE_SHIFT.contains(employeeType);
        } else {
            return EmploymentTypeEnum.ALL_COUNTRY_EMPLOYEE_TYPE_FOR_PAGE_SHIFT.contains(employeeType);
        }
    }

    @Override
    public boolean checkValidDimissionUserAttendanceRange(Long userId) {
        UserInfoDO userInfoDO = userInfoDao.getByUserId(userId);
        if (Objects.isNull(userInfoDO)) {
            return false;
        }

        String locationCountry = userInfoDO.getLocationCountry();
        String employeeType = userInfoDO.getEmployeeType();
        if (CountryCodeEnum.SPECIAL_COUNTRY_FOR_PAGE_SHIFT.contains(locationCountry)) {
            return EmploymentTypeEnum.SPECIAL_COUNTRY_EMPLOYEE_TYPE_FOR_PAGE_SHIFT.contains(employeeType);
        } else {
            return EmploymentTypeEnum.ALL_COUNTRY_EMPLOYEE_TYPE_FOR_PAGE_SHIFT.contains(employeeType);
        }
    }

    @Override
    public boolean isWarehouseSupportUser(UserInfoDO userInfoDO) {
        return ObjectUtil.equal(userInfoDO.getIsWarehouseStaff(), BusinessConstant.Y)
                && EmploymentTypeEnum.TYPE_OF_DEFAULT_WAREHOUSE.contains(userInfoDO.getEmployeeType())
                && CountryCodeEnum.WAREHOUSE_COUNTRY.contains(userInfoDO.getLocationCountry())
                && ObjectUtil.notEqual(userInfoDO.getIsDriver(), BusinessConstant.Y);
    }

    @Override
    public boolean checkGrayscaleRange(Long userId) {
        if (Objects.isNull(userId)) {
            return false;
        }

        UserInfoDO userInfo = userInfoDao.getByUserId(userId);
        if (Objects.isNull(userInfo)) {
            return false;
        }

        AttendanceProperties.Attendance attendance = attendanceProperties.getAttendance();

        if (StringUtils.isNotBlank(attendance.getGrayscaleUserCode()) && StringUtils.isNotBlank(userInfo.getUserCode())) {
            List<String> userCodeList = Arrays.stream(attendance.getGrayscaleUserCode().split(BusinessConstant.DEFAULT_DELIMITER)).collect(Collectors.toList());
            if (userCodeList.contains(userInfo.getUserCode())) {
                return true;
            }
        }

        if (StringUtils.isNotBlank(attendance.getGrayscaleDept()) && Objects.nonNull(userInfo.getDeptId())) {
            List<String> deptList = Arrays.stream(attendance.getGrayscaleDept().split(BusinessConstant.DEFAULT_DELIMITER)).collect(Collectors.toList());
            if (deptList.contains(userInfo.getDeptId().toString())) {
                return true;
            }
        }

        if (StringUtils.isNotBlank(attendance.getGrayscaleCountry()) && StringUtils.isNotBlank(userInfo.getLocationCountry())) {
            List<String> countryList = Arrays.stream(attendance.getGrayscaleCountry().split(BusinessConstant.DEFAULT_DELIMITER)).collect(Collectors.toList());
            return countryList.contains(userInfo.getLocationCountry());
        }

        return false;
    }

    @Override
    public UserInfoVO getUserInfo(String userCode) {
        UserInfoDO userInfoDO = userInfoDao.getByUserCode(userCode);
        if (Objects.isNull(userInfoDO)) {
            return null;
        }
        UserInfoVO result = new UserInfoVO();
        result.setId(userInfoDO.getId());
        result.setUserCode(userInfoDO.getUserCode());
        result.setUserName(BusinessFieldUtils.getUnifiedUserName(userInfoDO.getUserName(), userInfoDO.getUserNameEn()));
        result.setLocationCountry(userInfoDO.getLocationCountry());
        result.setEmployeeType(userInfoDO.getEmployeeType());
        result.setIsDriver(userInfoDO.getIsDriver());
        result.setIsGlobalRelocation(userInfoDO.getIsGlobalRelocation());
        result.setIsWarehouseStaff(userInfoDO.getIsWarehouseStaff());
        result.setWorkStatus(userInfoDO.getWorkStatus());
        result.setStatus(userInfoDO.getStatus());

        if (Objects.nonNull(userInfoDO.getDeptId())) {
            AttendanceDept attendanceDept = attendanceDeptService.getByDeptId(userInfoDO.getDeptId());
            if (Objects.nonNull(attendanceDept)) {
                result.setDeptId(attendanceDept.getId());
                if (Locale.CHINA.equals(UserEvnHolder.getLocal())) {
                    result.setDeptName(attendanceDept.getDeptNameCn());
                } else {
                    result.setDeptName(attendanceDept.getDeptNameEn());
                }
            }
        }
        if (Objects.nonNull(userInfoDO.getPostId())) {
            AttendancePost attendancePost = attendancePostService.getByPostId(userInfoDO.getPostId());
            if (Objects.nonNull(attendancePost)) {
                result.setPostId(attendancePost.getId());
                if (Locale.CHINA.equals(UserEvnHolder.getLocal())) {
                    result.setPostName(attendancePost.getPostNameCn());
                } else {
                    result.setPostName(attendancePost.getPostNameEn());
                }
            }
        }
        return result;
    }

    @Override
    public void userClassNatureHandler(String userCode) {
        UserInfoDO userInfo = userInfoDao.getByUserCode(userCode);
        if (Objects.isNull(userInfo)) {
            log.error("员工调动班次性质处理:用户查询为空");
            return;
        }
        if (StringUtils.isNotBlank(userInfo.getClassNature())) {
            log.info("userClassNatureHandler 存在班次性质 userCode:{}", userInfo.getUserCode());
            return;
        }
        // 不在灰度国家范围的人员里走旧系统，新系统不处理
        if (!migrationService.verifyUserIsEnableAttendanceForCountry(userInfo.getId())) {
            log.info("userClassNatureHandler | userInfo is not in new attendance country, userCode:{}", userInfo.getUserCode());
            return;
        }

        String classNature = ClassNatureEnum.FIXED_CLASS.name();
        userInfo.setClassNature(classNature);
        BaseDOUtil.fillDOUpdateByUserOrSystem(userInfo);
        userInfoDao.updateById(userInfo);

        UserClassNatureModifyRecordDO userClassNatureModifyRecordDO = new UserClassNatureModifyRecordDO();
        userClassNatureModifyRecordDO.setId(defaultIdWorker.nextId());
        userClassNatureModifyRecordDO.setUserId(userInfo.getId());
        userClassNatureModifyRecordDO.setClassNature(classNature);
        userClassNatureModifyRecordDO.setEffectTime(new Date());
        userClassNatureModifyRecordDO.setExpireTime(BusinessConstant.DEFAULT_END_TIME);
        userClassNatureModifyRecordDO.setIsLatest(BusinessConstant.Y);
        userClassNatureModifyRecordDO.setRemark("员工国家或部门变动班次性质初始化");
        BaseDOUtil.fillDOInsertByUsrOrSystem(userClassNatureModifyRecordDO);
        userClassNatureModifyRecordDao.save(userClassNatureModifyRecordDO);
    }

    public String getUnifiedUserName(String userName, String userNameEn) {
        if (StringUtils.isBlank(userName)) {
            return "";
        }
        // 若userName为全中文且userNameEn不为空 则将俩字段的值拼接在一起再返回
        if (isAllInChinese(userName) && StringUtils.isNotBlank(userNameEn)) {
            return StringUtils.capitalize(userNameEn.trim()) + userName.trim();
        }
        // 否则直接返回userName的值
        return userName.trim();
    }

    /**
     * 判断关键字是否全中文
     *
     * @param keyword 关键字
     * @return Boolean
     */
    public Boolean isAllInChinese(String keyword) {
        if (StringUtils.isBlank(keyword)) {
            return Boolean.TRUE;
        }
        String keywordTrim = keyword.trim();
        Pattern pattern = Pattern.compile(BusinessConstant.CHINESE_PATTERN);
        Matcher mather = pattern.matcher(keywordTrim);
        int length = 0;
        while (mather.find()) {
            length++;
        }
        return length == keywordTrim.length() ? Boolean.TRUE : Boolean.FALSE;
    }

    /**
     * 当页面传入用工类型和国家时,国家与权限常驻国取交集,用工类型用页面传入
     * 当页面不传国家或用工类型时，按照考勤的有效范围进行过滤
     */
    private Boolean buildUserPermissionQuery(UserDaoQuery query) {
        UserContext userContext = RequestInfoHolder.getLoginInfo();

        List<String> countryList = null;
        if (CollectionUtils.isNotEmpty(userContext.getCountryList())) {
            countryList = userContext.getCountryList();
            if (StringUtils.isNotEmpty(query.getCountry())) {
                countryList = countryList.stream().filter(country -> Objects.equals(country, query.getCountry())).distinct().collect(Collectors.toList());
            }
        }

        if (CollectionUtils.isEmpty(countryList) && CollectionUtils.isEmpty(userContext.getOrganizationIds())) {
            return false;
        }

        //特殊逻辑: 用工类型筛选不为空,直接用页面输入
        if (CollectionUtils.isNotEmpty(query.getEmployeeTypeList())) {
            query.setNormalCountryList(countryList);
            query.setNormalEmployeeTypeList(query.getEmployeeTypeList());
            return true;
        }

        if (CollectionUtils.isNotEmpty(countryList)) {
            }
            if (CollectionUtils.isNotEmpty(countryList)){
                // 检查是否包含特殊国家
                boolean hasSpecialCountry = countryList.stream()
                        .anyMatch(CountryCodeEnum.SPECIAL_COUNTRY_FOR_PAGE_SHIFT::contains);

            if (hasSpecialCountry) {
                // 特殊国家和普通国家分组
                Map<Boolean, List<String>> countryMap = countryList.stream()
                        .collect(Collectors.groupingBy(CountryCodeEnum.SPECIAL_COUNTRY_FOR_PAGE_SHIFT::contains));

                query.setIsNeedQuerySpecialCountry(true);
                query.setSpecialCountryList(countryMap.get(true));
                query.setNormalCountryList(countryMap.get(false));
                // 处理用工类型
                query.setSpecialEmployeeTypeList(EmploymentTypeEnum.SPECIAL_COUNTRY_EMPLOYEE_TYPE_FOR_PAGE_SHIFT);
                query.setNormalEmployeeTypeList(EmploymentTypeEnum.ALL_COUNTRY_EMPLOYEE_TYPE_FOR_PAGE_SHIFT);
            } else {
                // 只有普通国家
                query.setNormalCountryList(countryList);
                query.setNormalEmployeeTypeList(EmploymentTypeEnum.ALL_COUNTRY_EMPLOYEE_TYPE_FOR_PAGE_SHIFT);
            }
        }

        if (CollectionUtils.isNotEmpty(userContext.getOrganizationIds())) {
            Map<String, List<AttendanceDept>> countryGroup = attendanceDeptService.listByDeptIds(userContext.getOrganizationIds())
                    .stream()
                    .filter(dept -> StringUtils.isNotBlank(dept.getCountry())
                            && (StringUtils.isBlank(query.getCountry()) || Objects.equals(query.getCountry(), dept.getCountry())))
                    .collect(Collectors.groupingBy(AttendanceDept::getCountry));
            Set<Long> normalDeptList = new HashSet<>();
            Set<Long> specialDeptList = new HashSet<>();
            for (String country : countryGroup.keySet()) {
                List<AttendanceDept> attendanceDeptList = countryGroup.get(country);
                if (CollectionUtils.isEmpty(attendanceDeptList)) {
                    continue;
                }
                List<Long> deptIds = attendanceDeptList.stream().map(AttendanceDept::getId).distinct().collect(Collectors.toList());
                if (CountryCodeEnum.SPECIAL_COUNTRY_FOR_PAGE_SHIFT.contains(country)) {
                    specialDeptList.addAll(deptIds);
                } else {
                    normalDeptList.addAll(deptIds);
                }
            }
            if (CollectionUtils.isNotEmpty(normalDeptList)) {
                query.setNormalDeptList(new ArrayList<>(normalDeptList));
            }
            if (CollectionUtils.isNotEmpty(specialDeptList)) {
                query.setIsNeedQuerySpecialCountry(true);
                query.setSpecialDeptList(new ArrayList<>(specialDeptList));
            }
        }

        if (CollectionUtils.isEmpty(query.getNormalEmployeeTypeList()) && CollectionUtils.isEmpty(query.getSpecialEmployeeTypeList())) {
            return false;
        }

        //如果正常国家或正常国家的用工类型为空，则将特殊国家和其用工类型调换（去除union的条件）
        if (CollectionUtils.isEmpty(query.getNormalEmployeeTypeList())) {
            query.setIsNeedQuerySpecialCountry(false);
            query.setNormalCountryList(query.getSpecialCountryList());
            query.setNormalDeptList(query.getSpecialDeptList());
            query.setNormalEmployeeTypeList(query.getSpecialEmployeeTypeList());
            return true;
        }

        if (CollectionUtils.isEmpty(query.getSpecialEmployeeTypeList())) {
            query.setIsNeedQuerySpecialCountry(false);
        }

        return true;
    }

    private static UserBaseInfoDTO buildUserBaseInfo(UserDTO userDTO,
                                                     AttendanceDept attendanceDept,
                                                     AttendancePost attendancePost) {
        return UserBaseInfoDTO.builder()
                .id(userDTO.getId())
                .userCode(userDTO.getUserCode())
                .userName(userDTO.getUserName())
                .employeeType(userDTO.getEmployeeType())
                .workStatus(userDTO.getWorkStatus())
                .status(userDTO.getStatus())
                .deptId(userDTO.getDeptId())
                .deptName(Locale.US.equals(UserEvnHolder.getLocal())
                        ? Objects.isNull(attendanceDept) ? null : attendanceDept.getDeptNameEn()
                        : Objects.isNull(attendanceDept) ? null : attendanceDept.getDeptNameCn())
                .postId(userDTO.getPostId())
                .postName(Locale.US.equals(UserEvnHolder.getLocal())
                        ? Objects.isNull(attendancePost) ? null : attendancePost.getPostNameEn()
                        : Objects.isNull(attendancePost) ? null : attendancePost.getPostNameCn())
                .isDriver(userDTO.getIsDriver())
                .isGlobalRelocation(userDTO.getIsGlobalRelocation())
                .locationCountry(userDTO.getLocationCountry())
                .locationProvince(userDTO.getLocationProvince())
                .locationCity(userDTO.getLocationCity())
                .entryDate(userDTO.getConfirmDate())
                .dimissionDate(userDTO.getActualDimissionDate())
                .classNature(userDTO.getClassNature())
                .build();
    }
}
