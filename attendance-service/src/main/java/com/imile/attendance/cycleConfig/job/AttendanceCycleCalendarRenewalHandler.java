package com.imile.attendance.cycleConfig.job;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.cycleConfig.service.AttendanceCycleCalendarRenewalService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * 考勤周期日历数据自动续期定时任务处理器
 * 
 * <p>功能说明：</p>
 * <ul>
 *   <li>定期检查各国家的考勤周期日历数据有效期</li>
 *   <li>当检测到日历数据即将到期时（剩余不足6个月），自动续期</li>
 *   <li>保持3年的数据窗口（删除过期数据，添加新的未来数据）</li>
 *   <li>只处理月度周期，周度周期暂不处理</li>
 * </ul>
 * 
 * <p>任务参数格式（JSON）：</p>
 * <pre>
 * {
 *   "renewalThresholdMonths": 6,     // 续期阈值（月），默认6个月
 *   "dataWindowYears": 3,            // 数据窗口年数，默认3年
 * }
 * </pre>
 *
 * <AUTHOR> chen
 * @since 2025-08-11
 */
@Slf4j
@Component
public class AttendanceCycleCalendarRenewalHandler {

    @Resource
    private AttendanceCycleCalendarRenewalService renewalService;

    /**
     * 考勤周期日历数据自动续期定时任务主方法
     *
     * @param content 任务参数JSON字符串
     * @return ReturnT<String> XXL-Job执行结果
     */
    @XxlJob(BusinessConstant.JobHandler.ATTENDANCE_CYCLE_CALENDAR_RENEWAL_HANDLER)
    public ReturnT<String> attendanceCycleCalendarRenewalHandler(String content) {
        XxlJobLogger.log("考勤周期日历数据自动续期定时任务开始，参数：{}", content);
        log.info("考勤周期日历数据自动续期定时任务开始，参数：{}", content);

        try {
            // 解析任务参数
            RenewalTaskParam param = parseTaskParam(content);
            XxlJobLogger.log("解析后的任务参数：{}", JSON.toJSONString(param));

            // 执行续期任务
            Map<String, Object> result = renewalService.executeRenewalTask(param);
            
            // 记录执行结果
            String resultMessage = String.format("续期任务执行完成，结果：%s", JSON.toJSONString(result));
            XxlJobLogger.log(resultMessage);
            log.info(resultMessage);

            // 检查是否有失败的情况
            Integer failedCount = (Integer) result.get("failedCount");
            if (failedCount != null && failedCount > 0) {
                String errorMessage = String.format("续期任务部分失败，失败数量：%d", failedCount);
                XxlJobLogger.log(errorMessage);
                log.warn(errorMessage);
                return new ReturnT<>(ReturnT.FAIL_CODE, errorMessage);
            }

            return ReturnT.SUCCESS;

        } catch (Exception e) {
            String errorMessage = String.format("考勤周期日历数据自动续期定时任务执行异常：%s", e.getMessage());
            XxlJobLogger.log(errorMessage);
            log.error(errorMessage, e);
            return new ReturnT<>(ReturnT.FAIL_CODE, errorMessage);
        }
    }

    /**
     * 解析任务参数
     *
     * @param content 参数JSON字符串
     * @return 解析后的参数对象
     */
    private RenewalTaskParam parseTaskParam(String content) {
        RenewalTaskParam param;
        
        if (StrUtil.isNotBlank(content)) {
            try {
                param = JSON.parseObject(content, RenewalTaskParam.class);
                if (param == null) {
                    param = new RenewalTaskParam();
                }
            } catch (Exception e) {
                log.warn("解析任务参数失败，使用默认参数，原始参数：{}，错误：{}", content, e.getMessage());
                XxlJobLogger.log("解析任务参数失败，使用默认参数，错误：{}", e.getMessage());
                param = new RenewalTaskParam();
            }
        } else {
            param = new RenewalTaskParam();
        }

        // 设置默认值
        if (param.getRenewalThresholdMonths() == null || param.getRenewalThresholdMonths() <= 0) {
            param.setRenewalThresholdMonths(6);
        }
        if (param.getDataWindowYears() == null || param.getDataWindowYears() <= 0) {
            param.setDataWindowYears(3);
        }
        if (param.getDryRun() == null) {
            param.setDryRun(false);
        }

        return param;
    }

    /**
     * 续期任务参数类
     */
    @Data
    public static class RenewalTaskParam {
        
        /**
         * 续期阈值（月），当剩余有效期少于此值时触发续期
         * 默认值：6个月
         */
        private Integer renewalThresholdMonths = 6;
        
        /**
         * 数据窗口年数，保持多少年的数据
         * 默认值：3年
         */
        private Integer dataWindowYears = 3;
        
        /**
         * 指定处理的国家列表，为空则处理所有启用的国家
         */
        private List<String> countryList;
        
        /**
         * 是否为试运行模式，试运行模式只检查不执行实际续期操作
         * 默认值：false
         */
        private Boolean dryRun = false;
        
        /**
         * 批处理大小，默认使用系统配置的批处理大小
         */
        private Integer batchSize;
    }
}
