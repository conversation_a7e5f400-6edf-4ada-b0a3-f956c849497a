package com.imile.attendance.cycleConfig.dto;

import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 考勤周期日历数据续期结果统计DTO
 *
 * <AUTHOR> chen
 * @since 2025-08-11
 */
@Data
public class AttendanceCycleCalendarRenewalResultDTO {

    /**
     * 任务执行时间
     */
    private Date executeTime;

    /**
     * 总检查的配置数量
     */
    private Integer totalCount;

    /**
     * 实际检查的配置数量
     */
    private Integer checkedCount;

    /**
     * 成功续期的国家数量
     */
    private Integer renewedCount;

    /**
     * 失败的国家数量
     */
    private Integer failedCount;

    /**
     * 成功续期的国家列表
     */
    private List<String> renewedCountries;

    /**
     * 失败的国家列表
     */
    private List<String> failedCountries;

    /**
     * 是否为试运行模式
     */
    private Boolean dryRun;

    /**
     * 执行结果消息
     */
    private String message;

    /**
     * 错误信息（如果有失败的情况）
     */
    private String errors;

    /**
     * 任务是否成功
     */
    private Boolean success;

    /**
     * 续期阈值（月）
     */
    private Integer renewalThresholdMonths;

    /**
     * 数据窗口年数
     */
    private Integer dataWindowYears;

    /**
     * 任务执行耗时（毫秒）
     */
    private Long executionTimeMs;

    /**
     * 构造方法
     */
    public AttendanceCycleCalendarRenewalResultDTO() {
        this.executeTime = new Date();
        this.success = true;
    }

    /**
     * 计算成功率
     *
     * @return 成功率百分比
     */
    public Double getSuccessRate() {
        if (checkedCount == null || checkedCount == 0) {
            return 0.0;
        }
        int successCount = checkedCount - (failedCount != null ? failedCount : 0);
        return (double) successCount / checkedCount * 100;
    }

    /**
     * 判断是否有失败的情况
     *
     * @return 是否有失败
     */
    public boolean hasFailures() {
        return failedCount != null && failedCount > 0;
    }

    /**
     * 获取简要统计信息
     *
     * @return 统计信息字符串
     */
    public String getSummary() {
        return String.format("检查：%d，续期：%d，失败：%d，成功率：%.1f%%",
                checkedCount != null ? checkedCount : 0,
                renewedCount != null ? renewedCount : 0,
                failedCount != null ? failedCount : 0,
                getSuccessRate());
    }
}
