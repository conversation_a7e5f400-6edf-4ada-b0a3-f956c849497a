package com.imile.attendance.cycleConfig.config;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 考勤周期日历数据自动续期配置类
 *
 * <AUTHOR> chen
 * @since 2025-08-11
 */
@Slf4j
@Data
@Component
@ConfigurationProperties(prefix = "attendance.cycle.calendar.renewal")
public class AttendanceCycleCalendarRenewalConfig {

    /**
     * 默认续期阈值（月）
     * 当剩余有效期少于此值时触发续期
     */
    private Integer defaultRenewalThresholdMonths = 6;

    /**
     * 默认数据窗口年数
     * 保持多少年的数据
     */
    private Integer defaultDataWindowYears = 3;

    /**
     * 默认批处理大小
     */
    private Integer defaultBatchSize = 1000;

    /**
     * 是否启用自动续期功能
     */
    private Boolean enabled = true;

    /**
     * 最大续期阈值（月）
     * 防止配置过大的阈值
     */
    private Integer maxRenewalThresholdMonths = 12;

    /**
     * 最小续期阈值（月）
     * 防止配置过小的阈值
     */
    private Integer minRenewalThresholdMonths = 1;

    /**
     * 最大数据窗口年数
     * 防止配置过大的数据窗口
     */
    private Integer maxDataWindowYears = 5;

    /**
     * 最小数据窗口年数
     * 防止配置过小的数据窗口
     */
    private Integer minDataWindowYears = 2;

    /**
     * 任务执行超时时间（分钟）
     */
    private Integer taskTimeoutMinutes = 60;

    /**
     * 是否允许并发执行
     * 防止多个续期任务同时执行
     */
    private Boolean allowConcurrentExecution = false;

    /**
     * 验证续期阈值是否合法
     *
     * @param thresholdMonths 续期阈值
     * @return 验证后的阈值
     */
    public Integer validateRenewalThreshold(Integer thresholdMonths) {
        if (thresholdMonths == null) {
            return defaultRenewalThresholdMonths;
        }
        
        if (thresholdMonths < minRenewalThresholdMonths) {
            log.warn("续期阈值 {} 小于最小值 {}，使用最小值", thresholdMonths, minRenewalThresholdMonths);
            return minRenewalThresholdMonths;
        }
        
        if (thresholdMonths > maxRenewalThresholdMonths) {
            log.warn("续期阈值 {} 大于最大值 {}，使用最大值", thresholdMonths, maxRenewalThresholdMonths);
            return maxRenewalThresholdMonths;
        }
        
        return thresholdMonths;
    }

    /**
     * 验证数据窗口年数是否合法
     *
     * @param windowYears 数据窗口年数
     * @return 验证后的年数
     */
    public Integer validateDataWindowYears(Integer windowYears) {
        if (windowYears == null) {
            return defaultDataWindowYears;
        }
        
        if (windowYears < minDataWindowYears) {
            log.warn("数据窗口年数 {} 小于最小值 {}，使用最小值", windowYears, minDataWindowYears);
            return minDataWindowYears;
        }
        
        if (windowYears > maxDataWindowYears) {
            log.warn("数据窗口年数 {} 大于最大值 {}，使用最大值", windowYears, maxDataWindowYears);
            return maxDataWindowYears;
        }
        
        return windowYears;
    }

    /**
     * 验证批处理大小是否合法
     *
     * @param batchSize 批处理大小
     * @return 验证后的批处理大小
     */
    public Integer validateBatchSize(Integer batchSize) {
        if (batchSize == null || batchSize <= 0) {
            return defaultBatchSize;
        }
        
        // 限制最大批处理大小，防止内存溢出
        int maxBatchSize = 5000;
        if (batchSize > maxBatchSize) {
            log.warn("批处理大小 {} 大于最大值 {}，使用最大值", batchSize, maxBatchSize);
            return maxBatchSize;
        }
        
        return batchSize;
    }

    /**
     * 获取任务执行超时时间（毫秒）
     *
     * @return 超时时间毫秒数
     */
    public Long getTaskTimeoutMs() {
        return taskTimeoutMinutes * 60 * 1000L;
    }

    /**
     * 检查功能是否启用
     *
     * @return 是否启用
     */
    public boolean isEnabled() {
        return enabled != null && enabled;
    }
}
