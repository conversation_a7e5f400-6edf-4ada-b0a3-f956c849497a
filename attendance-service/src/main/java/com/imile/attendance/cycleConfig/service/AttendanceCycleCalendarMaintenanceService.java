package com.imile.attendance.cycleConfig.service;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.cycleConfig.util.AttendanceCycleCalendarCalculator;
import com.imile.attendance.infrastructure.repository.cycleConfig.dao.AttendanceCycleCalendarDao;
import com.imile.attendance.infrastructure.repository.cycleConfig.model.AttendanceCycleCalendarDO;
import com.imile.attendance.infrastructure.repository.cycleConfig.model.AttendanceCycleConfigDO;
import com.imile.attendance.util.BaseDOUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 考勤周期日历数据维护服务
 * 
 * 负责维护 attendance_cycle_calendar 表的数据，
 * 在考勤周期配置变更时同步更新日历数据
 *
 * <AUTHOR> chen
 * @since 2025-08-11
 */
@Slf4j
@Service
public class AttendanceCycleCalendarMaintenanceService {

    @Resource
    private AttendanceCycleCalendarDao attendanceCycleCalendarDao;
    
    @Resource
    private AttendanceCycleCalendarCalculator calendarCalculator;

    /**
     * 为新增的考勤周期配置维护日历数据
     *
     * @param cycleConfig 考勤周期配置
     */
    public void maintainCalendarForNewConfig(AttendanceCycleConfigDO cycleConfig) {
        log.info("开始为新增配置维护日历数据，国家：{}", cycleConfig.getCountry());
        // 生成日历数据
        List<AttendanceCycleCalendarDO> calendarDataList = calendarCalculator.generateCalendarData(cycleConfig);
        if (CollectionUtil.isEmpty(calendarDataList)) {
            log.warn("未生成任何日历数据，国家：{}", cycleConfig.getCountry());
            return;
        }

        // 批量保存日历数据
        saveCalendarDataBatch(calendarDataList);

        log.info("成功为国家 {} 维护了 {} 条日历数据", cycleConfig.getCountry(), calendarDataList.size());
    }

    /**
     * 为更新的考勤周期配置维护日历数据
     *
     * @param oldConfig 旧配置
     * @param newConfig 新配置
     */
    public void maintainCalendarForUpdatedConfig(AttendanceCycleConfigDO oldConfig, AttendanceCycleConfigDO newConfig) {
        log.info("开始为更新配置维护日历数据，国家：{}", newConfig.getCountry());
        // 检查是否需要重新生成日历数据
        if (needsCalendarRegeneration(oldConfig, newConfig)) {
            log.info("配置变更需要重新生成日历数据，国家：{}", newConfig.getCountry());

            // 删除旧的日历数据
            removeCalendarDataByCountry(newConfig.getCountry());

            // 生成新的日历数据
            List<AttendanceCycleCalendarDO> calendarDataList = calendarCalculator.generateCalendarData(newConfig);

            if (CollectionUtil.isNotEmpty(calendarDataList)) {
                saveCalendarDataBatch(calendarDataList);
                log.info("成功重新生成国家 {} 的 {} 条日历数据", newConfig.getCountry(), calendarDataList.size());
            }
        } else {
            log.info("配置变更不影响日历数据，跳过重新生成，国家：{}", newConfig.getCountry());
        }
    }

    /**
     * 重建指定国家的日历数据
     *
     * @param cycleConfig 考勤周期配置
     */
    @Transactional(rollbackFor = Exception.class)
    public void rebuildCalendarData(AttendanceCycleConfigDO cycleConfig) {
        log.info("开始重建国家 {} 的日历数据", cycleConfig.getCountry());
        
        try {
            // 删除现有数据
            removeCalendarDataByCountry(cycleConfig.getCountry());
            
            // 生成新数据
            List<AttendanceCycleCalendarDO> calendarDataList = calendarCalculator.generateCalendarData(cycleConfig);
            
            if (CollectionUtil.isNotEmpty(calendarDataList)) {
                saveCalendarDataBatch(calendarDataList);
                log.info("成功重建国家 {} 的 {} 条日历数据", cycleConfig.getCountry(), calendarDataList.size());
            }
            
        } catch (Exception e) {
            log.error("重建日历数据失败，国家：{}，错误：{}", cycleConfig.getCountry(), e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 批量保存日历数据
     *
     * @param calendarDataList 日历数据列表
     */
    private void saveCalendarDataBatch(List<AttendanceCycleCalendarDO> calendarDataList) {
        if (CollectionUtil.isEmpty(calendarDataList)) {
            return;
        }
        
        // 设置基础字段
        calendarDataList.forEach(BaseDOUtil::fillDOInsert);
        
        // 过滤有效数据
        List<AttendanceCycleCalendarDO> validDataList = calendarDataList.stream()
                .filter(calendarCalculator::validateCalendarData)
                .collect(Collectors.toList());
        
        if (CollectionUtil.isEmpty(validDataList)) {
            log.warn("没有有效的日历数据需要保存");
            return;
        }
        
        // 分批保存，避免单次操作数据量过大
        int batchSize = 1000;
        for (int i = 0; i < validDataList.size(); i += batchSize) {
            int endIndex = Math.min(i + batchSize, validDataList.size());
            List<AttendanceCycleCalendarDO> batch = validDataList.subList(i, endIndex);
            
            try {
                attendanceCycleCalendarDao.saveBatch(batch);
                log.debug("成功保存第 {} 批日历数据，数量：{}", (i / batchSize + 1), batch.size());
            } catch (Exception e) {
                log.error("保存第 {} 批日历数据失败，错误：{}", (i / batchSize + 1), e.getMessage(), e);
                throw e;
            }
        }
        
        log.info("批量保存日历数据完成，总数量：{}", validDataList.size());
    }

    /**
     * 删除指定国家的日历数据
     *
     * @param country 国家代码
     */
    private void removeCalendarDataByCountry(String country) {
        LambdaQueryWrapper<AttendanceCycleCalendarDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AttendanceCycleCalendarDO::getCountry, country);
        queryWrapper.eq(AttendanceCycleCalendarDO::getIsDelete, BusinessConstant.N);

        List<AttendanceCycleCalendarDO> list = attendanceCycleCalendarDao.list(queryWrapper);
        if (CollectionUtil.isNotEmpty(list)) {
            list.forEach(item -> {
                item.setIsDelete(BusinessConstant.Y);
                BaseDOUtil.fillDOUpdate(item);
            });
            attendanceCycleCalendarDao.updateBatchById(list);
            log.info("成功删除国家 {} 的 {} 条日历数据", country, list.size());
        }
    }

    /**
     * 判断是否需要重新生成日历数据
     *
     * @param oldConfig 旧配置
     * @param newConfig 新配置
     * @return 是否需要重新生成
     */
    private boolean needsCalendarRegeneration(AttendanceCycleConfigDO oldConfig, AttendanceCycleConfigDO newConfig) {
        if (oldConfig == null || newConfig == null) {
            return true;
        }
        
        // 检查影响日历计算的关键字段是否发生变化
        return !oldConfig.getCycleType().equals(newConfig.getCycleType())
                || !oldConfig.getCycleStart().equals(newConfig.getCycleStart())
                || !oldConfig.getCycleEnd().equals(newConfig.getCycleEnd());
    }

    /**
     * 获取指定国家的日历数据统计信息
     *
     * @param country 国家代码
     * @return 数据条数
     */
    public long getCalendarDataCount(String country) {
        LambdaQueryWrapper<AttendanceCycleCalendarDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AttendanceCycleCalendarDO::getCountry, country);
        return attendanceCycleCalendarDao.count(queryWrapper);
    }

    /**
     * 验证指定国家的日历数据完整性
     *
     * @param country 国家代码
     * @return 验证结果描述
     */
    public String validateCalendarDataIntegrity(String country) {
        try {
            long dataCount = getCalendarDataCount(country);
            
            if (dataCount == 0) {
                return String.format("国家 %s 没有日历数据", country);
            }

            // todo 这里可以添加更多的完整性检查逻辑
            // 比如检查是否覆盖了完整的时间范围等
            
            return String.format("国家 %s 的日历数据完整，共 %d 条记录", country, dataCount);
            
        } catch (Exception e) {
            return String.format("验证国家 %s 的日历数据时发生错误：%s", country, e.getMessage());
        }
    }
}
