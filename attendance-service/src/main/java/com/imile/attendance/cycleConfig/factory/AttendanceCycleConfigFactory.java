package com.imile.attendance.cycleConfig.factory;

import cn.hutool.core.util.ObjectUtil;
import com.imile.attendance.apollo.AttendanceProperties;
import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.cycleConfig.command.AttendanceCycleConfigAddCommand;
import com.imile.attendance.cycleConfig.command.AttendanceCycleConfigStatusSwitchCommand;
import com.imile.attendance.cycleConfig.command.AttendanceCycleConfigUpdateCommand;
import com.imile.attendance.cycleConfig.enums.AttendanceCycleTypeEnum;
import com.imile.attendance.cycleConfig.enums.CycleTypeEnum;
import com.imile.attendance.cycleConfig.mapstruct.AttendanceCycleConfigMapstruct;
import com.imile.attendance.cycleConfig.service.AttendanceCycleCalendarMaintenanceService;
import com.imile.attendance.enums.CountryCodeEnum;
import com.imile.attendance.enums.ErrorCodeEnum;
import com.imile.attendance.exception.BusinessLogicException;
import com.imile.attendance.context.RequestInfoHolder;
import com.imile.attendance.infrastructure.idwork.DefaultIdWorker;
import com.imile.attendance.infrastructure.repository.cycleConfig.dao.AttendanceCycleConfigDao;
import com.imile.attendance.infrastructure.repository.cycleConfig.model.AttendanceCycleConfigDO;
import com.imile.attendance.infrastructure.repository.cycleConfig.query.AttendanceCycleConfigQuery;
import com.imile.attendance.util.BaseDOUtil;
import com.imile.common.enums.IsDeleteEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR> chen
 * @Date 2025/2/24
 * @Description
 */
@Slf4j
@Service
public class AttendanceCycleConfigFactory {

    @Resource
    private AttendanceCycleConfigDao attendanceCycleConfigDao;
    @Resource
    private DefaultIdWorker defaultIdWorker;
    @Resource
    private AttendanceCycleCalendarMaintenanceService calendarMaintenanceService;
    @Resource
    private AttendanceProperties attendanceProperties;

    @Transactional(rollbackFor =  Exception.class)
    public void add(AttendanceCycleConfigAddCommand addCommand) {
        log.info("新增考勤周期配置，参数:{}", addCommand);
        validateAndProcessCommand(addCommand, null);
        AttendanceCycleConfigDO configDO = createConfigDO(addCommand);
        attendanceCycleConfigDao.save(configDO);
        calendarMaintenanceService.maintainCalendarForNewConfig(configDO);
        log.info("已触发日历数据维护，国家：{}", configDO.getCountry());
    }

    public void update(AttendanceCycleConfigUpdateCommand updateCommand) {
        log.info("更新考勤周期配置，参数:{}", updateCommand);
        if (ObjectUtil.isNull(updateCommand) || ObjectUtil.isNull(updateCommand.getId())) {
            throw BusinessLogicException.getException(ErrorCodeEnum.PARAM_VALID_ERROR);
        }
        // 获取并处理旧配置
        AttendanceCycleConfigDO oldConfig = getAndMarkOldConfigAsDeleted(updateCommand.getId());
        // 创建新配置
        validateAndProcessCommand(updateCommand, updateCommand.getId());
        AttendanceCycleConfigDO newConfig = createConfigDO(updateCommand);
        // 保存更新
        attendanceCycleConfigDao.saveAndUpdateAttendanceCycleConfig(oldConfig, newConfig);
        calendarMaintenanceService.maintainCalendarForUpdatedConfig(oldConfig, newConfig);
        log.info("已触发日历数据更新维护，国家：{}", newConfig.getCountry());
    }


    public void statusSwitch(AttendanceCycleConfigStatusSwitchCommand statusSwitchCommand) {
        log.info("修改考勤周期配置:{}", statusSwitchCommand);
        if (ObjectUtil.isNull(statusSwitchCommand) || ObjectUtil.isNull(statusSwitchCommand.getId()) ||
                ObjectUtil.isEmpty(statusSwitchCommand.getStatus())) {
            throw BusinessLogicException.getException(ErrorCodeEnum.PARAM_VALID_ERROR);
        }
        // 查询考勤周期
        AttendanceCycleConfigDO oldAttendanceCycleConfig = attendanceCycleConfigDao.getById(statusSwitchCommand.getId());
        if (ObjectUtil.isNull(oldAttendanceCycleConfig) ||
                ObjectUtil.equal(oldAttendanceCycleConfig.getIsDelete(), IsDeleteEnum.YES.getCode())) {
            throw BusinessLogicException.getException(ErrorCodeEnum.ATTENDANCE_CYCLE_CONFIG_NO_EXISTS_ERROR);
        }

        // 状态未发生变化
        if (oldAttendanceCycleConfig.getStatus().equals(statusSwitchCommand.getStatus())) {
            throw BusinessLogicException.getException(ErrorCodeEnum.ATTENDANCE_CYCLE_CONFIG_STATUS_NO_CHANGE_ERROR);
        }

        oldAttendanceCycleConfig.setStatus(statusSwitchCommand.getStatus());
        BaseDOUtil.fillDOUpdate(oldAttendanceCycleConfig);
        attendanceCycleConfigDao.updateById(oldAttendanceCycleConfig);
    }


    /**
     * 构建默认考勤周期配置
     *
     * @return HrmsAttendanceCycleConfigDO
     */
    public AttendanceCycleConfigDO buildDefaultAttendanceCycleConfig() {
        AttendanceCycleConfigDO attendanceCycleConfigDO = new AttendanceCycleConfigDO();
        // 默认月维度
        attendanceCycleConfigDO.setCycleType(AttendanceCycleTypeEnum.MONTH.getType());
        attendanceCycleConfigDO.setCycleStart(BusinessConstant.ONE.toString());
        attendanceCycleConfigDO.setCycleEnd(CycleTypeEnum.END_OF_MONTH_CODE);
        attendanceCycleConfigDO.setAbnormalExpired(2);
        return attendanceCycleConfigDO;
    }


    /**
     * 验证并处理命令
     */
    private void validateAndProcessCommand(AttendanceCycleConfigAddCommand command, Long id) {
        BusinessLogicException.checkTrue(Objects.isNull(command), ErrorCodeEnum.PARAM_VALID_ERROR);
        // 新增校验：只有MEX和BRA支持配置月/周维度，其他国家只能配置月的维度
        validCountryIsMatchCycleType(command);
        // 验证重复配置
        validateDuplicateConfig(command, id);
        // 设置周期类型字符串
        setCycleTypeString(command);
    }

    private void validCountryIsMatchCycleType(AttendanceCycleConfigAddCommand command) {
        if (ObjectUtil.equal(command.getCycleType(), AttendanceCycleTypeEnum.WEEK.getType())) {
            // 使用周维度的国家判断
            List<String> attendanceCycleWeekDimensionCountry = Optional.ofNullable(attendanceProperties.getAttendance().getAttendanceCycleWeekDimensionCountry())
                    .map(str -> str.split(BusinessConstant.DEFAULT_DELIMITER))
                    .map(Arrays::stream)
                    .orElseGet(Stream::empty)
                    .collect(Collectors.toList());
            // 判断国家是否为MEX或BRA
            boolean isSupportWeekly = attendanceCycleWeekDimensionCountry.contains(command.getCountry());
            if (!isSupportWeekly) {
                String countryName = CountryCodeEnum.getCountryName(command.getCountry(), RequestInfoHolder.isChinese());
                throw BusinessLogicException.getException(
                        ErrorCodeEnum.ATTENDANCE_CYCLE_COUNTRY_NOT_SUPPORT_WEEKLY,
                        countryName);
            }
        }
    }

    /**
     * 验证重复配置
     */
    private void validateDuplicateConfig(AttendanceCycleConfigAddCommand command, Long id) {
        AttendanceCycleConfigQuery query = new AttendanceCycleConfigQuery();
        query.setCountry(command.getCountry());
        query.setCycleType(command.getCycleType());

        List<AttendanceCycleConfigDO> existingConfigs = attendanceCycleConfigDao.selectByCondition(query);
        if (id != null) {
            existingConfigs = existingConfigs.stream()
                    .filter(config -> !id.equals(config.getId()))
                    .collect(Collectors.toList());
        }

        if (!existingConfigs.isEmpty()) {
            String countryName = CountryCodeEnum.getCountryName(command.getCountry(), RequestInfoHolder.isChinese());
            String cycleTypeDesc = getLocalizedCycleTypeDesc(command.getCycleType());
            throw BusinessLogicException.getException(
                    ErrorCodeEnum.ATTENDANCE_CYCLE_CONFIG_HAVE_EXISTS_ERROR,
                    countryName,
                    cycleTypeDesc);
        }
    }

    /**
     * 获取本地化的周期类型描述
     */
    private String getLocalizedCycleTypeDesc(Integer cycleType) {
        AttendanceCycleTypeEnum cycleTypeEnum = AttendanceCycleTypeEnum.getByType(cycleType);
        if (cycleTypeEnum != null) {
            return RequestInfoHolder.isChinese() ? cycleTypeEnum.getDesc() : cycleTypeEnum.getDescEn();
        }
        return "";
    }

    /**
     * 设置周期类型字符串
     */
    private void setCycleTypeString(AttendanceCycleConfigAddCommand command) {
        command.setCycleTypeString(AttendanceCycleTypeEnum.getCycleTypeString(command.getCycleType()));
    }

    /**
     * 创建配置DO对象
     */
    private AttendanceCycleConfigDO createConfigDO(AttendanceCycleConfigAddCommand command) {
        return AttendanceCycleConfigMapstruct.INSTANCE.toModel(command, defaultIdWorker.nextId());
    }


    /**
     * 获取并标记旧配置为已删除
     */
    private AttendanceCycleConfigDO getAndMarkOldConfigAsDeleted(Long id) {
        AttendanceCycleConfigDO oldConfig = attendanceCycleConfigDao.getById(id);
        BusinessLogicException.checkTrue(Objects.isNull(oldConfig),
                ErrorCodeEnum.ATTENDANCE_CYCLE_CONFIG_NO_EXISTS_ERROR);

        oldConfig.setIsDelete(IsDeleteEnum.YES.getCode());
        BaseDOUtil.fillDOUpdate(oldConfig);
        return oldConfig;
    }


}
