package com.imile.attendance.cycleConfig.util;

import cn.hutool.core.date.DateUtil;
import com.imile.attendance.cycleConfig.dto.AttendanceCycleCalendarDataDTO;
import com.imile.attendance.cycleConfig.enums.AttendanceCycleTypeEnum;
import com.imile.attendance.cycleConfig.enums.CycleTypeEnum;
import com.imile.attendance.infrastructure.repository.cycleConfig.model.AttendanceCycleCalendarDO;
import com.imile.attendance.infrastructure.repository.cycleConfig.model.AttendanceCycleConfigDO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * 考勤周期日历计算工具类
 * <p>
 * 负责根据考勤周期配置计算具体的周期开始和结束日期，
 * 参考 AttendanceMonthReportService#buildAttendanceCycleDTO 的计算逻辑
 *
 * <AUTHOR> chen
 * @since 2025-08-11
 */
@Slf4j
@Component
public class AttendanceCycleCalendarCalculator {

    /**
     * 默认维护的年份跨度（去年、今年、后年）
     */
    private static final int YEAR_SPAN = 3;

    /**
     * 当前年份偏移量：-1表示去年，0表示今年，1表示后年
     */
    private static final int[] YEAR_OFFSETS = {-1, 0, 1};

    /**
     * 为指定的考勤周期配置生成日历数据
     *
     * @param cycleConfig 考勤周期配置
     * @return 生成的日历数据列表
     */
    public List<AttendanceCycleCalendarDO> generateCalendarData(AttendanceCycleConfigDO cycleConfig) {
        if (cycleConfig == null || cycleConfig.getCountry() == null) {
            log.warn("考勤周期配置为空或国家为空，跳过日历数据生成");
            return Collections.emptyList();
        }

        log.info("开始为国家 {} 生成考勤周期日历数据，周期类型：{}",
                cycleConfig.getCountry(), cycleConfig.getCycleType());

        List<AttendanceCycleCalendarDO> calendarDataList = new ArrayList<>();
        int currentYear = DateUtil.year(new Date());

        // 生成3年跨度的日历数据
        for (int yearOffset : YEAR_OFFSETS) {
            int targetYear = currentYear + yearOffset;
            List<AttendanceCycleCalendarDO> yearlyData = generateYearlyCalendarData(cycleConfig, targetYear);
            calendarDataList.addAll(yearlyData);
        }

        log.info("为国家 {} 生成了 {} 条日历数据", cycleConfig.getCountry(), calendarDataList.size());
        return calendarDataList;
    }

    /**
     * 生成指定年份的日历数据
     *
     * @param cycleConfig 考勤周期配置
     * @param year        目标年份
     * @return 该年份的日历数据列表
     */
    private List<AttendanceCycleCalendarDO> generateYearlyCalendarData(AttendanceCycleConfigDO cycleConfig, int year) {
        List<AttendanceCycleCalendarDO> yearlyData = new ArrayList<>();

        // 根据周期类型生成数据
        if (AttendanceCycleTypeEnum.MONTH.getType().equals(cycleConfig.getCycleType())) {
            // 月度周期：为每个月生成一条记录
            for (int month = 1; month <= 12; month++) {
                AttendanceCycleCalendarDO calendarDO = calculateMonthlyCycle(cycleConfig, year, month);
                if (calendarDO != null) {
                    yearlyData.add(calendarDO);
                }
            }
        } else if (AttendanceCycleTypeEnum.WEEK.getType().equals(cycleConfig.getCycleType())) {
            log.info("周度周期暂不处理");
        }

        return yearlyData;
    }

    /**
     * 计算月度周期的开始和结束日期
     *
     * @param cycleConfig 考勤周期配置
     * @param year        年份
     * @param month       月份
     * @return 日历数据对象
     */
    private AttendanceCycleCalendarDO calculateMonthlyCycle(AttendanceCycleConfigDO cycleConfig, int year, int month) {
        try {
            String cycleStart = cycleConfig.getCycleStart();
            String cycleEnd = cycleConfig.getCycleEnd();

            Date cycleStartDate;
            Date cycleEndDate;

            String cycleMonth = String.format("%04d%02d", year, month);

            if (CycleTypeEnum.END_OF_MONTH_CODE.equals(cycleEnd)) {
                // 整月周期：从当月第一天到当月最后一天
                cycleStartDate = DateUtil.beginOfMonth(DateUtil.parse(cycleMonth, "yyyyMM"));
                cycleEndDate = DateUtil.endOfMonth(DateUtil.parse(cycleMonth, "yyyyMM"));
            } else {
                // 自定义日期周期：从上月的cycleStart到当月的cycleEnd
                cycleStartDate = DateUtil.offsetMonth(
                        DateUtil.parse(cycleMonth + cycleStart, "yyyyMMdd"), -1);
                cycleEndDate = DateUtil.parse(
                        DateUtil.format(DateUtil.offsetMonth(cycleStartDate, 1), "yyyyMM") + cycleEnd, "yyyyMMdd");
            }

            // 创建日历数据对象
            AttendanceCycleCalendarDO calendarDO = new AttendanceCycleCalendarDO();
            calendarDO.setCountry(cycleConfig.getCountry());
            calendarDO.setAttendanceYear(year);
            calendarDO.setAttendanceMonth(month);
            calendarDO.setCycleStartDate(cycleStartDate);
            calendarDO.setCycleEndDate(cycleEndDate);

            return calendarDO;

        } catch (Exception e) {
            log.error("计算月度周期失败，国家：{}，年月：{}-{}，错误：{}",
                    cycleConfig.getCountry(), year, month, e.getMessage(), e);
            return null;
        }
    }


    /**
     * 检查是否需要处理跨年场景
     *
     * @param cycleStartDate 周期开始日期
     * @param cycleEndDate   周期结束日期
     * @return 是否跨年
     */
    public boolean isCrossYearScenario(Date cycleStartDate, Date cycleEndDate) {
        return DateUtil.year(cycleStartDate) != DateUtil.year(cycleEndDate);
    }

    /**
     * 处理跨年场景的特殊逻辑
     *
     * @param cycleConfig 考勤周期配置
     * @param year        基准年份
     * @param month       基准月份
     * @return 处理跨年后的日历数据列表
     */
    public List<AttendanceCycleCalendarDO> handleCrossYearScenario(AttendanceCycleConfigDO cycleConfig, int year, int month) {
        List<AttendanceCycleCalendarDO> crossYearData = new ArrayList<>();

        try {
            // 计算基础的月度周期
            AttendanceCycleCalendarDO baseCalendar = calculateMonthlyCycle(cycleConfig, year, month);

            if (baseCalendar != null && isCrossYearScenario(baseCalendar.getCycleStartDate(), baseCalendar.getCycleEndDate())) {
                log.info("检测到跨年场景，年月：{}-{}，开始日期：{}，结束日期：{}",
                        year, month, baseCalendar.getCycleStartDate(), baseCalendar.getCycleEndDate());

                // 对于跨年场景，可能需要生成额外的记录
                // 比如12月的周期可能延续到次年1月
                if (month == 12) {
                    // 12月跨年到次年1月的情况
                    AttendanceCycleCalendarDO nextYearCalendar = new AttendanceCycleCalendarDO();
                    nextYearCalendar.setCountry(cycleConfig.getCountry());
                    nextYearCalendar.setAttendanceYear(year + 1);
                    nextYearCalendar.setAttendanceMonth(1);
                    nextYearCalendar.setCycleStartDate(baseCalendar.getCycleStartDate());
                    nextYearCalendar.setCycleEndDate(baseCalendar.getCycleEndDate());

                    crossYearData.add(nextYearCalendar);
                }

                crossYearData.add(baseCalendar);
            } else if (baseCalendar != null) {
                crossYearData.add(baseCalendar);
            }

        } catch (Exception e) {
            log.error("处理跨年场景失败，年月：{}-{}，错误：{}", year, month, e.getMessage(), e);
        }

        return crossYearData;
    }

    /**
     * 获取指定配置的建议维护年份范围
     *
     * @param cycleConfig 考勤周期配置
     * @return 年份范围描述
     */
    public String getRecommendedYearRange(AttendanceCycleConfigDO cycleConfig) {
        int currentYear = DateUtil.year(new Date());
        int startYear = currentYear - 1;
        int endYear = currentYear + 1;

        return String.format("建议维护年份范围：%d - %d（共3年）", startYear, endYear);
    }

    /**
     * 计算指定配置预期生成的数据量
     *
     * @param cycleConfig 考勤周期配置
     * @return 预期数据量
     */
    public int calculateExpectedDataCount(AttendanceCycleConfigDO cycleConfig) {
        if (AttendanceCycleTypeEnum.MONTH.getType().equals(cycleConfig.getCycleType())) {
            // 月度周期：3年 × 12个月 = 36条记录
            return YEAR_SPAN * 12;
        } else if (AttendanceCycleTypeEnum.WEEK.getType().equals(cycleConfig.getCycleType())) {
            // 周度周期：3年 × 约52周 = 约156条记录
            return YEAR_SPAN * 52;
        }
        return 0;
    }

    /**
     * 验证日历数据的有效性
     *
     * @param calendarDO 日历数据对象
     * @return 是否有效
     */
    public boolean validateCalendarData(AttendanceCycleCalendarDO calendarDO) {
        if (calendarDO == null) {
            return false;
        }

        return calendarDO.getCountry() != null
                && calendarDO.getAttendanceYear() != null
                && calendarDO.getAttendanceMonth() != null
                && calendarDO.getCycleStartDate() != null
                && calendarDO.getCycleEndDate() != null
                && calendarDO.getCycleStartDate().before(calendarDO.getCycleEndDate());
    }
}
