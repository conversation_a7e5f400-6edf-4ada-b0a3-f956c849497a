package com.imile.attendance.cycleConfig.service;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.imile.attendance.cycleConfig.CycleConfigManage;
import com.imile.attendance.infrastructure.repository.cycleConfig.dao.AttendanceCycleCalendarDao;
import com.imile.attendance.infrastructure.repository.cycleConfig.model.AttendanceCycleCalendarDO;
import com.imile.attendance.infrastructure.repository.cycleConfig.model.AttendanceCycleConfigDO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 考勤周期日历数据管理服务
 * 
 * 提供日历数据的管理、监控和维护功能
 *
 * <AUTHOR> chen
 * @since 2025-08-11
 */
@Slf4j
@Service
public class AttendanceCycleCalendarManagementService {

    @Resource
    private AttendanceCycleCalendarDao attendanceCycleCalendarDao;
    
    @Resource
    private AttendanceCycleCalendarMaintenanceService maintenanceService;
    
    @Resource
    private CycleConfigManage cycleConfigManage;

    /**
     * 手动重建所有国家的日历数据
     *
     * @return 操作结果统计
     */
    @Transactional(rollbackFor = Exception.class)
    public Map<String, Object> rebuildAllCalendarData() {
        log.info("开始手动重建所有国家的日历数据");
        
        Map<String, Object> result = new HashMap<>();
        int successCount = 0;
        int failedCount = 0;
        StringBuilder errorMessages = new StringBuilder();
        
        try {
            // 获取所有启用的周期配置
            List<AttendanceCycleConfigDO> allConfigs = cycleConfigManage.getAllEnabled();
            
            if (CollectionUtil.isEmpty(allConfigs)) {
                result.put("message", "没有找到启用的考勤周期配置");
                result.put("totalCount", 0);
                return result;
            }
            
            log.info("找到 {} 个启用的考勤周期配置", allConfigs.size());
            
            // 为每个配置重建日历数据
            for (AttendanceCycleConfigDO config : allConfigs) {
                try {
                    maintenanceService.rebuildCalendarData(config);
                    successCount++;
                    log.info("成功重建国家 {} 的日历数据", config.getCountry());
                } catch (Exception e) {
                    failedCount++;
                    String errorMsg = String.format("重建国家 %s 的日历数据失败：%s", config.getCountry(), e.getMessage());
                    errorMessages.append(errorMsg).append("; ");
                    log.error(errorMsg, e);
                }
            }
            
            result.put("totalCount", allConfigs.size());
            result.put("successCount", successCount);
            result.put("failedCount", failedCount);
            result.put("message", String.format("重建完成，成功：%d，失败：%d", successCount, failedCount));
            
            if (failedCount > 0) {
                result.put("errors", errorMessages.toString());
            }
            
        } catch (Exception e) {
            log.error("重建所有日历数据时发生异常", e);
            result.put("message", "重建过程中发生异常：" + e.getMessage());
            result.put("success", false);
        }
        
        log.info("手动重建所有日历数据完成，结果：{}", result);
        return result;
    }

    /**
     * 重建指定国家的日历数据
     *
     * @param country 国家代码
     * @return 操作结果
     */
    @Transactional(rollbackFor = Exception.class)
    public Map<String, Object> rebuildCalendarDataByCountry(String country) {
        log.info("开始重建国家 {} 的日历数据", country);
        
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 获取该国家的配置
            List<AttendanceCycleConfigDO> configs = cycleConfigManage.getAllEnabled()
                    .stream()
                    .filter(config -> country.equals(config.getCountry()))
                    .collect(Collectors.toList());
            
            if (CollectionUtil.isEmpty(configs)) {
                result.put("success", false);
                result.put("message", String.format("未找到国家 %s 的启用配置", country));
                return result;
            }
            
            // 重建日历数据
            AttendanceCycleConfigDO config = configs.get(0); // 每个国家应该只有一个配置
            maintenanceService.rebuildCalendarData(config);
            
            // 统计重建后的数据
            long dataCount = maintenanceService.getCalendarDataCount(country);
            
            result.put("success", true);
            result.put("message", String.format("成功重建国家 %s 的日历数据", country));
            result.put("dataCount", dataCount);
            
        } catch (Exception e) {
            log.error("重建国家 {} 的日历数据失败", country, e);
            result.put("success", false);
            result.put("message", String.format("重建失败：%s", e.getMessage()));
        }
        
        return result;
    }

    /**
     * 获取所有国家的日历数据统计信息
     *
     * @return 统计信息
     */
    public Map<String, Object> getCalendarDataStatistics() {
        log.info("开始获取日历数据统计信息");
        
        Map<String, Object> statistics = new HashMap<>();
        
        try {
            // 获取总数据量
            long totalCount = attendanceCycleCalendarDao.count();
            statistics.put("totalCount", totalCount);
            
            // 按国家统计
            List<AttendanceCycleConfigDO> allConfigs = cycleConfigManage.getAllEnabled();
            Map<String, Long> countryStatistics = new HashMap<>();
            
            for (AttendanceCycleConfigDO config : allConfigs) {
                long countryCount = maintenanceService.getCalendarDataCount(config.getCountry());
                countryStatistics.put(config.getCountry(), countryCount);
            }
            
            statistics.put("countryStatistics", countryStatistics);
            statistics.put("configuredCountries", allConfigs.size());
            
            // 数据完整性检查
            Map<String, String> integrityResults = new HashMap<>();
            for (AttendanceCycleConfigDO config : allConfigs) {
                String integrityResult = maintenanceService.validateCalendarDataIntegrity(config.getCountry());
                integrityResults.put(config.getCountry(), integrityResult);
            }
            statistics.put("integrityCheck", integrityResults);
            
        } catch (Exception e) {
            log.error("获取日历数据统计信息失败", e);
            statistics.put("error", "获取统计信息失败：" + e.getMessage());
        }
        
        return statistics;
    }

    /**
     * 清理指定国家的日历数据
     *
     * @param country 国家代码
     * @return 操作结果
     */
    @Transactional(rollbackFor = Exception.class)
    public Map<String, Object> cleanupCalendarDataByCountry(String country) {
        log.info("开始清理国家 {} 的日历数据", country);
        
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 统计清理前的数据量
            long beforeCount = maintenanceService.getCalendarDataCount(country);
            
            // 执行清理
            LambdaQueryWrapper<AttendanceCycleCalendarDO> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(AttendanceCycleCalendarDO::getCountry, country);
            attendanceCycleCalendarDao.remove(queryWrapper);
            
            result.put("success", true);
            result.put("message", String.format("成功清理国家 %s 的日历数据", country));
            result.put("cleanedCount", beforeCount);
            
        } catch (Exception e) {
            log.error("清理国家 {} 的日历数据失败", country, e);
            result.put("success", false);
            result.put("message", String.format("清理失败：%s", e.getMessage()));
        }
        
        return result;
    }

    /**
     * 查询指定国家和年份的日历数据
     *
     * @param country 国家代码
     * @param year 年份
     * @return 日历数据列表
     */
    public List<AttendanceCycleCalendarDO> queryCalendarData(String country, Integer year) {
        LambdaQueryWrapper<AttendanceCycleCalendarDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AttendanceCycleCalendarDO::getCountry, country);
        
        if (year != null) {
            queryWrapper.eq(AttendanceCycleCalendarDO::getAttendanceYear, year);
        }
        
        queryWrapper.orderByAsc(AttendanceCycleCalendarDO::getAttendanceYear)
                   .orderByAsc(AttendanceCycleCalendarDO::getAttendanceMonth);
        
        return attendanceCycleCalendarDao.list(queryWrapper);
    }

    /**
     * 检查日历数据的一致性
     *
     * @return 检查结果
     */
    public Map<String, Object> checkDataConsistency() {
        log.info("开始检查日历数据一致性");
        
        Map<String, Object> result = new HashMap<>();
        
        try {
            List<AttendanceCycleConfigDO> allConfigs = cycleConfigManage.getAllEnabled();
            Map<String, Object> inconsistencies = new HashMap<>();
            
            for (AttendanceCycleConfigDO config : allConfigs) {
                // 检查该国家是否有对应的日历数据
                long calendarCount = maintenanceService.getCalendarDataCount(config.getCountry());
                
                if (calendarCount == 0) {
                    inconsistencies.put(config.getCountry(), "缺少日历数据");
                } else {
                    // 可以添加更多的一致性检查逻辑
                    // 比如检查日期范围是否合理等
                }
            }
            
            result.put("hasInconsistencies", !inconsistencies.isEmpty());
            result.put("inconsistencies", inconsistencies);
            result.put("checkedConfigs", allConfigs.size());
            
        } catch (Exception e) {
            log.error("检查数据一致性失败", e);
            result.put("error", "检查失败：" + e.getMessage());
        }
        
        return result;
    }
}
