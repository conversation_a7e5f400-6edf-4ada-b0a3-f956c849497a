package com.imile.attendance.cycleConfig.service;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.cycleConfig.CycleConfigManage;
import com.imile.attendance.cycleConfig.config.AttendanceCycleCalendarRenewalConfig;
import com.imile.attendance.cycleConfig.enums.AttendanceCycleTypeEnum;
import com.imile.attendance.cycleConfig.job.AttendanceCycleCalendarRenewalHandler.RenewalTaskParam;
import com.imile.attendance.cycleConfig.util.AttendanceCycleCalendarCalculator;
import com.imile.attendance.infrastructure.config.XxlJobConfig;
import com.imile.attendance.infrastructure.repository.cycleConfig.dao.AttendanceCycleCalendarDao;
import com.imile.attendance.infrastructure.repository.cycleConfig.model.AttendanceCycleCalendarDO;
import com.imile.attendance.infrastructure.repository.cycleConfig.model.AttendanceCycleConfigDO;
import com.imile.attendance.util.BaseDOUtil;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 考勤周期日历数据自动续期服务
 * 
 * <p>核心功能：</p>
 * <ul>
 *   <li>检查各国家日历数据的有效期</li>
 *   <li>识别需要续期的国家</li>
 *   <li>执行数据续期操作（删除过期数据，生成新数据）</li>
 *   <li>维护固定的时间窗口</li>
 * </ul>
 *
 * <AUTHOR> chen
 * @since 2025-08-11
 */
@Slf4j
@Service
public class AttendanceCycleCalendarRenewalService {

    @Resource
    private CycleConfigManage cycleConfigManage;

    @Resource
    private AttendanceCycleCalendarDao attendanceCycleCalendarDao;

    @Resource
    private AttendanceCycleCalendarCalculator calendarCalculator;

    @Resource
    private AttendanceCycleCalendarMaintenanceService maintenanceService;

    @Resource
    private AttendanceCycleCalendarRenewalConfig renewalConfig;

    /**
     * 执行续期任务
     *
     * @param param 任务参数
     * @return 执行结果统计
     */
    @Transactional(rollbackFor = Exception.class)
    public Map<String, Object> executeRenewalTask(RenewalTaskParam param) {
        log.info("开始执行考勤周期日历数据续期任务，参数：{}", param);

        // 检查功能是否启用
        if (!renewalConfig.isEnabled()) {
            log.warn("考勤周期日历数据自动续期功能已禁用");
            XxlJobLogger.log("考勤周期日历数据自动续期功能已禁用");
            Map<String, Object> result = new HashMap<>();
            result.put("message", "自动续期功能已禁用");
            result.put("success", false);
            return result;
        }

        // 验证和调整参数
        param.setRenewalThresholdMonths(renewalConfig.validateRenewalThreshold(param.getRenewalThresholdMonths()));
        param.setDataWindowYears(renewalConfig.validateDataWindowYears(param.getDataWindowYears()));
        param.setBatchSize(renewalConfig.validateBatchSize(param.getBatchSize()));

        Map<String, Object> result = new HashMap<>();
        int totalCount = 0;
        int checkedCount = 0;
        int renewedCount = 0;
        int failedCount = 0;
        List<String> renewedCountries = new ArrayList<>();
        List<String> failedCountries = new ArrayList<>();
        StringBuilder errorMessages = new StringBuilder();
        long startTime = System.currentTimeMillis();

        try {
            // 获取需要检查的国家配置
            List<AttendanceCycleConfigDO> configsToCheck = getConfigsToCheck(param);
            totalCount = configsToCheck.size();
            
            if (CollectionUtil.isEmpty(configsToCheck)) {
                result.put("message", "没有找到需要检查的考勤周期配置");
                result.put("totalCount", 0);
                return result;
            }

            log.info("找到 {} 个需要检查的考勤周期配置", totalCount);
            XxlJobLogger.log("找到 {} 个需要检查的考勤周期配置", totalCount);

            // 检查每个国家的日历数据
            for (AttendanceCycleConfigDO config : configsToCheck) {
                try {
                    checkedCount++;
                    
                    // 只处理月度周期
                    if (!AttendanceCycleTypeEnum.MONTH.getType().equals(config.getCycleType())) {
                        log.info("跳过非月度周期配置，国家：{}，周期类型：{}", 
                                config.getCountry(), config.getCycleType());
                        XxlJobLogger.log("跳过非月度周期配置，国家：{}，周期类型：{}",
                                config.getCountry(), config.getCycleType());
                        continue;
                    }

                    // 检查是否需要续期
                    RenewalCheckResult checkResult = checkRenewalNeeded(config, param);
                    
                    if (checkResult.isNeedRenewal()) {
                        log.info("国家 {} 需要续期，当前最大日期：{}，剩余月数：{}", 
                                config.getCountry(), 
                                DateUtil.formatDate(checkResult.getCurrentMaxDate()),
                                checkResult.getRemainingMonths());
                        XxlJobLogger.log("国家 {} 需要续期，当前最大日期：{}，剩余月数：{}",
                                config.getCountry(),
                                DateUtil.formatDate(checkResult.getCurrentMaxDate()),
                                checkResult.getRemainingMonths());

                        if (!param.getDryRun()) {
                            // 执行续期操作
                            executeRenewalForCountry(config, param, checkResult);
                            renewedCount++;
                            renewedCountries.add(config.getCountry());
                            log.info("成功为国家 {} 执行续期操作", config.getCountry());
                            XxlJobLogger.log("成功为国家 {} 执行续期操作", config.getCountry());
                        } else {
                            log.info("试运行模式，跳过实际续期操作，国家：{}", config.getCountry());
                            renewedCountries.add(config.getCountry() + "(试运行)");
                        }
                    } else {
                        log.info("国家 {} 暂不需要续期，当前最大日期：{}，剩余月数：{}",
                                config.getCountry(), 
                                DateUtil.formatDate(checkResult.getCurrentMaxDate()),
                                checkResult.getRemainingMonths());
                        XxlJobLogger.log("国家 {} 暂不需要续期，当前最大日期：{}，剩余月数：{}",
                                config.getCountry(),
                                DateUtil.formatDate(checkResult.getCurrentMaxDate()),
                                checkResult.getRemainingMonths());
                    }

                } catch (Exception e) {
                    failedCount++;
                    String errorMsg = String.format("处理国家 %s 的续期检查失败：%s", 
                            config.getCountry(), e.getMessage());
                    errorMessages.append(errorMsg).append("; ");
                    failedCountries.add(config.getCountry());
                    log.error(errorMsg, e);
                    XxlJobLogger.log(errorMsg);
                }
            }

            // 构建结果
            long executionTime = System.currentTimeMillis() - startTime;
            result.put("totalCount", totalCount);
            result.put("checkedCount", checkedCount);
            result.put("renewedCount", renewedCount);
            result.put("failedCount", failedCount);
            result.put("renewedCountries", renewedCountries);
            result.put("failedCountries", failedCountries);
            result.put("dryRun", param.getDryRun());
            result.put("executionTimeMs", executionTime);
            result.put("renewalThresholdMonths", param.getRenewalThresholdMonths());
            result.put("dataWindowYears", param.getDataWindowYears());

            String message = String.format("续期任务完成，检查：%d，续期：%d，失败：%d，耗时：%dms",
                    checkedCount, renewedCount, failedCount, executionTime);
            result.put("message", message);
            result.put("success", failedCount == 0);

            if (failedCount > 0) {
                result.put("errors", errorMessages.toString());
            }

        } catch (Exception e) {
            log.error("执行续期任务时发生异常", e);
            result.put("message", "续期任务执行异常：" + e.getMessage());
            result.put("success", false);
        }

        log.info("考勤周期日历数据续期任务执行完成，结果：{}", result);
        XxlJobLogger.log("考勤周期日历数据续期任务执行完成，结果：{}", result);
        return result;
    }

    /**
     * 获取需要检查的配置列表
     *
     * @param param 任务参数
     * @return 配置列表
     */
    private List<AttendanceCycleConfigDO> getConfigsToCheck(RenewalTaskParam param) {
        List<AttendanceCycleConfigDO> allConfigs = cycleConfigManage.getAllEnabled();
        
        if (CollectionUtil.isEmpty(param.getCountryList())) {
            return allConfigs;
        }
        
        // 过滤指定的国家
        return allConfigs.stream()
                .filter(config -> param.getCountryList().contains(config.getCountry()))
                .collect(Collectors.toList());
    }

    /**
     * 检查指定国家是否需要续期
     *
     * @param config 国家配置
     * @param param 任务参数
     * @return 检查结果
     */
    private RenewalCheckResult checkRenewalNeeded(AttendanceCycleConfigDO config, RenewalTaskParam param) {
        RenewalCheckResult result = new RenewalCheckResult();
        result.setCountry(config.getCountry());
        
        // 查询该国家的最大日期
        Date maxDate = getMaxCycleCalendarDate(config.getCountry());
        result.setCurrentMaxDate(maxDate);
        
        if (maxDate == null) {
            // 没有数据，需要续期
            result.setNeedRenewal(true);
            result.setRemainingMonths(0);
            result.setReason("没有找到日历数据");
            return result;
        }
        
        // 计算剩余月数
        Date now = new Date();
        long remainingMonths = DateUtil.betweenMonth(now, maxDate, false);
        result.setRemainingMonths((int) remainingMonths);
        
        // 判断是否需要续期
        boolean needRenewal = remainingMonths <= param.getRenewalThresholdMonths();
        result.setNeedRenewal(needRenewal);
        
        if (needRenewal) {
            result.setReason(String.format("剩余有效期 %d 个月，少于阈值 %d 个月", 
                    remainingMonths, param.getRenewalThresholdMonths()));
        }
        
        return result;
    }

    /**
     * 获取指定国家的日历数据最大日期
     *
     * @param country 国家代码
     * @return 最大日期，如果没有数据则返回null
     */
    private Date getMaxCycleCalendarDate(String country) {
        try {
            List<AttendanceCycleCalendarDO> calendarData = attendanceCycleCalendarDao.lambdaQuery()
                    .eq(AttendanceCycleCalendarDO::getCountry, country)
                    .eq(AttendanceCycleCalendarDO::getIsDelete, BusinessConstant.N)
                    .orderByDesc(AttendanceCycleCalendarDO::getCycleEndDate)
                    .last("LIMIT 1")
                    .list();

            if (CollectionUtil.isNotEmpty(calendarData)) {
                return calendarData.get(0).getCycleEndDate();
            }

            return null;
        } catch (Exception e) {
            log.error("查询国家 {} 的最大日历日期失败", country, e);
            return null;
        }
    }

    /**
     * 为指定国家执行续期操作
     *
     * @param config 国家配置
     * @param param 任务参数
     * @param checkResult 检查结果
     */
    private void executeRenewalForCountry(AttendanceCycleConfigDO config,
                                        RenewalTaskParam param,
                                        RenewalCheckResult checkResult) {
        log.info("开始为国家 {} 执行续期操作", config.getCountry());
        XxlJobLogger.log("开始为国家 {} 执行续期操作", config.getCountry());

        try {
            // 计算新的时间窗口
            Date now = new Date();
            int currentYear = DateUtil.year(now);

            // 计算需要保留的最早年份和最新年份
            int earliestYear = currentYear - (param.getDataWindowYears() / 2);
            int latestYear = currentYear + (param.getDataWindowYears() / 2);

            // 如果数据窗口年数为奇数，向未来多保留一年
            if (param.getDataWindowYears() % 2 == 1) {
                latestYear++;
            }

            log.info("国家 {} 的新时间窗口：{} 年到 {} 年", config.getCountry(), earliestYear, latestYear);
            XxlJobLogger.log("国家 {} 的新时间窗口：{} 年到 {} 年", config.getCountry(), earliestYear, latestYear);

            // 删除过期的数据（早于最早年份的数据）
            int deletedCount = deleteExpiredCalendarData(config.getCountry(), earliestYear);
            log.info("国家 {} 删除过期数据 {} 条", config.getCountry(), deletedCount);
            XxlJobLogger.log("国家 {} 删除过期数据 {} 条", config.getCountry(), deletedCount);

            // 生成新的未来数据
            int addedCount = generateFutureCalendarData(config, latestYear);
            log.info("国家 {} 生成新数据 {} 条", config.getCountry(), addedCount);
            XxlJobLogger.log("国家 {} 生成新数据 {} 条", config.getCountry(), addedCount);

        } catch (Exception e) {
            log.error("为国家 {} 执行续期操作失败", config.getCountry(), e);
            XxlJobLogger.log("为国家 {} 执行续期操作失败，错误：{}", config.getCountry(), e.getMessage());
            throw e;
        }
    }

    /**
     * 删除过期的日历数据
     *
     * @param country 国家代码
     * @param earliestYear 保留的最早年份
     * @return 删除的记录数
     */
    private int deleteExpiredCalendarData(String country, int earliestYear) {
        try {
            List<AttendanceCycleCalendarDO> expiredData = attendanceCycleCalendarDao.lambdaQuery()
                    .eq(AttendanceCycleCalendarDO::getCountry, country)
                    .eq(AttendanceCycleCalendarDO::getIsDelete, BusinessConstant.N)
                    .lt(AttendanceCycleCalendarDO::getAttendanceYear, earliestYear)
                    .list();

            if (CollectionUtil.isEmpty(expiredData)) {
                return 0;
            }

            // 标记为删除
            expiredData.forEach(data -> {
                data.setIsDelete(BusinessConstant.Y);
                BaseDOUtil.fillDOUpdate(data);
            });
            attendanceCycleCalendarDao.updateBatchById(expiredData);

            return expiredData.size();
        } catch (Exception e) {
            log.error("删除国家 {} 的过期日历数据失败", country, e);
            throw e;
        }
    }

    /**
     * 生成未来的日历数据
     *
     * @param config 国家配置
     * @param latestYear 需要生成到的最新年份
     * @return 生成的记录数
     */
    private int generateFutureCalendarData(AttendanceCycleConfigDO config, int latestYear) {
        try {
            // 查询当前已有的最大年份
            Integer currentMaxYear = attendanceCycleCalendarDao.lambdaQuery()
                    .eq(AttendanceCycleCalendarDO::getCountry, config.getCountry())
                    .eq(AttendanceCycleCalendarDO::getIsDelete, BusinessConstant.N)
                    .orderByDesc(AttendanceCycleCalendarDO::getAttendanceYear)
                    .last("LIMIT 1")
                    .list()
                    .stream()
                    .findFirst()
                    .map(AttendanceCycleCalendarDO::getAttendanceYear)
                    .orElse(DateUtil.year(new Date()) - 1);

            int addedCount = 0;

            // 为每个缺失的年份生成数据
            for (int year = currentMaxYear + 1; year <= latestYear; year++) {
                List<AttendanceCycleCalendarDO> yearlyData = generateYearlyCalendarData(config, year);
                if (CollectionUtil.isNotEmpty(yearlyData)) {
                    // 设置基础字段
                    yearlyData.forEach(BaseDOUtil::fillDOInsert);

                    // 批量保存
                    maintenanceService.saveCalendarDataBatch(yearlyData);
                    addedCount += yearlyData.size();
                    log.debug("为国家 {} 生成 {} 年的日历数据 {} 条", config.getCountry(), year, yearlyData.size());
                }
            }

            return addedCount;
        } catch (Exception e) {
            log.error("为国家 {} 生成未来日历数据失败", config.getCountry(), e);
            throw e;
        }
    }

    /**
     * 生成指定年份的日历数据
     *
     * @param config 国家配置
     * @param year 年份
     * @return 该年份的日历数据列表
     */
    private List<AttendanceCycleCalendarDO> generateYearlyCalendarData(AttendanceCycleConfigDO config, int year) {
        // 使用现有的日历计算器生成数据
        return calendarCalculator.generateYearlyCalendarData(config, year);
    }

    /**
     * 续期检查结果
     */
    @Data
    private static class RenewalCheckResult {
        private String country;
        private Date currentMaxDate;
        private int remainingMonths;
        private boolean needRenewal;
        private String reason;
    }
}
