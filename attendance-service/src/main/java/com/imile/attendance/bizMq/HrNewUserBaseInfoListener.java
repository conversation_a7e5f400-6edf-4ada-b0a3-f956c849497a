package com.imile.attendance.bizMq;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.imile.attendance.bizMq.mapstruct.UserInfoMapstruct;
import com.imile.attendance.bizMq.param.BaseDataSyncParam;
import com.imile.attendance.enums.ClassNatureEnum;
import com.imile.attendance.infrastructure.mq.BaseRocketMQListener;
import com.imile.attendance.infrastructure.mq.helper.OperatorHelper;
import com.imile.attendance.infrastructure.repository.common.mapstruct.CommonMapstruct;
import com.imile.attendance.infrastructure.repository.employee.dao.UserInfoDao;
import com.imile.attendance.infrastructure.repository.employee.modle.UserInfoDO;
import com.imile.attendance.infrastructure.repository.hrms.dao.HrmsUserInfoDao;
import com.imile.attendance.infrastructure.repository.hrms.modle.HrmsUserInfoDO;
import com.imile.attendance.warehouse.WarehouseUserService;
import com.imile.hrms.api.base.component.DifferField;
import com.imile.hrms.api.base.result.OperatorDTO;
import com.imile.hrms.api.primary.enums.UserBaseEventTagsEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.common.message.MessageExt;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * topic触发场景: 非hrms人事业务流程触发
 * 监听用户新增、编辑、状态启停用消息
 *
 * <AUTHOR>
 * @since 2025/07/17
 */
@Slf4j
@Component
@RocketMQMessageListener(
        nameServer = "${rocketmq.nameServer}",
        topic = "${rocket.mq.hr.user.base.topic}",
        consumerGroup = "${rocket.mq.hr.user.baseInfo.group}",
        selectorExpression = "${rocket.mq.hr.user.baseInfo.add.tag} || ${rocket.mq.hr.user.baseInfo.update.tag} || ${rocket.mq.hr.user.baseInfo.switchStatus.tag}",
        consumeThreadMax = 1)
public class HrNewUserBaseInfoListener extends BaseRocketMQListener {

    @Resource
    private UserInfoDao userInfoDao;
    @Resource
    private HrmsUserInfoDao hrmsUserInfoDao;
    @Resource
    private WarehouseUserService warehouseUserService;

    private static final ObjectMapper objectMapper = new ObjectMapper();

    @Override
    public void doOnMessage(MessageExt messageExt) {
        String operateTags = messageExt.getTags();
        BaseDataSyncParam param = JSON.parseObject(new String(messageExt.getBody()), BaseDataSyncParam.class);
        if (UserBaseEventTagsEnum.ADD.getCode().equals(operateTags)
                || UserBaseEventTagsEnum.UPDATE.getCode().equals(operateTags)) {
            log.info("收到人员新增编辑变动消息,msgId:{},topic:{},tags:{},param:{}",
                    messageExt.getMsgId(), messageExt.getTopic(), messageExt.getTags(), JSON.toJSONString(param));
            doBaseInfoAddOrUpdateEvent(param);
        } else if (UserBaseEventTagsEnum.STATUS_SWITCH.getCode().equals(operateTags)) {
            log.info("收到人员状态变动消息,msgId:{},topic:{},tags:{},param:{}",
                    messageExt.getMsgId(), messageExt.getTopic(), messageExt.getTags(), JSON.toJSONString(param));
            doBaseInfoSwitchStatusEvent(param);
        }
    }

    /**
     * 员工新增编辑
     */
    private void doBaseInfoAddOrUpdateEvent(BaseDataSyncParam param) {
        OperatorDTO operator = new OperatorDTO();
        if (Objects.nonNull(param.getOperator())) {
            operator.setUserCode(param.getOperator().getUserCode());
            operator.setUserCode(param.getOperator().getUserName());
        }
        OperatorHelper.putOperatorInfo(operator);
        String userCode = param.getDataCode();
        log.info("收到人员新增编辑变动消息,userCode:{}", userCode);
        //1.更新考勤用户表信息
        UserInfoDO userInfoDO = userInfoDao.getByUserCode(userCode);
        HrmsUserInfoDO hrmsUserInfoDO = hrmsUserInfoDao.getByCode(userCode);
        if (Objects.isNull(hrmsUserInfoDO)) {
            return;
        }
        if (userInfoDO == null) {
            log.info("考勤人员信息表中不存在该人员,userCode:{}", userCode);
            UserInfoDO attendanceUser = UserInfoMapstruct.INSTANCE.mapToAttendanceUserInfoDO(hrmsUserInfoDO);
            if (!warehouseUserService.isWarehouseLaborSupport(CommonMapstruct.INSTANCE.mapToUser(attendanceUser))) {
                log.info("非仓内劳务员工,userCode:{}", userCode);
                return;
            }
            attendanceUser.setClassNature(ClassNatureEnum.MULTIPLE_CLASS.name());
            if (Objects.isNull(param.getBizData())) {
                userInfoDao.save(attendanceUser);
                return;
            }

            Map<String, Object> bizMap = objectMapper.convertValue(param.getBizData(), Map.class);
            if (MapUtils.isEmpty(bizMap)) {
                return;
            }
            String employeeForm = (String) bizMap.get("employeeForm");
            if (StringUtils.isNotBlank(employeeForm)) {
                attendanceUser.setEmployeeForm(employeeForm);
            }
            userInfoDao.save(attendanceUser);
        } else {
            if (!warehouseUserService.isWarehouseLaborSupport(CommonMapstruct.INSTANCE.mapToUser(userInfoDO))) {
                log.info("非仓内劳务员工,userCode:{}", userCode);
                return;
            }

            UserInfoDO attendanceUser = UserInfoMapstruct.INSTANCE.mapToAttendanceUserInfoDO(hrmsUserInfoDO);
            attendanceUser.setId(userInfoDO.getId());
            attendanceUser.setClassNature(userInfoDO.getClassNature());
            if (Objects.isNull(param.getBizData())) {
                userInfoDao.updateById(attendanceUser);
                return;
            }

            Map<String, Object> bizMap = objectMapper.convertValue(param.getBizData(), Map.class);
            if (MapUtils.isEmpty(bizMap)) {
                return;
            }
            String employeeForm = (String) bizMap.get("employeeForm");
            if (StringUtils.isNotBlank(employeeForm) && !Objects.equals(employeeForm, userInfoDO.getEmployeeForm())) {
                attendanceUser.setEmployeeForm(employeeForm);
            }
            userInfoDao.updateById(attendanceUser);
        }
    }


    /**
     * 员工状态变更
     */
    private void doBaseInfoSwitchStatusEvent(BaseDataSyncParam param) {
        OperatorDTO operator = new OperatorDTO();
        if (Objects.nonNull(param.getOperator())) {
            operator.setUserCode(param.getOperator().getUserCode());
            operator.setUserCode(param.getOperator().getUserName());
        }
        OperatorHelper.putOperatorInfo(operator);
        String userCode = param.getDataCode();
        log.info("收到人员状态变动消息,userCode:{}", userCode);
        //1.更新考勤用户表信息
        UserInfoDO userInfoDO = userInfoDao.getByUserCode(userCode);
        if (userInfoDO == null) {
            log.info("考勤人员信息表中不存在该人员,userCode:{}", userCode);
            return;
        }

        List<DifferField> changeFieldList = param.getChangeFieldList();
        if (CollectionUtils.isEmpty(changeFieldList)) {
            return;
        }

        String afterStatus = null;
        for (DifferField differField : changeFieldList) {
            if (Objects.equals(differField.getFieldName(), "status")) {
                afterStatus = (String) differField.getAfterValue();
                break;
            }
        }

        if (StringUtils.isNotBlank(afterStatus)) {
            userInfoDO.setStatus(afterStatus);
            userInfoDao.updateById(userInfoDO);
        }
    }
}
