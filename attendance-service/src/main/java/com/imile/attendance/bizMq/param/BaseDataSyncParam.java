package com.imile.attendance.bizMq.param;

import com.imile.hrms.api.base.component.DifferField;
import com.imile.hrms.api.primary.enums.OperationSceneEnum;
import com.imile.ucenter.api.dto.user.UserInfoDTO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/07/17
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BaseDataSyncParam implements Serializable {
    private static final long serialVersionUID = -5480094806929988954L;

    /**
     * 数据编码
     */
    private String dataCode;

    /**
     * 变动字段列表（更新场景才需要赋值）
     */
    private List<DifferField> changeFieldList;

    /**
     * 操作人
     */
    private UserInfoDTO operator;

    /**
     * 人员操作场景
     */
    private OperationSceneEnum operationScene;

    /**
     * 业务数据
     * 主数据消息广播时传递给下游系统
     */
    private Object bizData;

    /**
     * 当前时间戳
     */
    private Long timestamp;
}
