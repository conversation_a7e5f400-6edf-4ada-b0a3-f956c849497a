package com.imile.attendance.rule;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.imile.attendance.calendar.CalendarConfigService;
import com.imile.attendance.calendar.dto.CalendarAndPunchHandlerDTO;
import com.imile.attendance.enums.WorkStatusEnum;
import com.imile.attendance.infrastructure.repository.common.AttendanceDeptService;
import com.imile.attendance.infrastructure.repository.common.dto.AttendanceDept;
import com.imile.attendance.infrastructure.repository.common.dto.AttendanceUser;
import com.imile.attendance.rule.application.PunchClassConfigApplicationService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR> chen
 * @Date 2025/07/09
 * @Time 20:10
 * @Description
 */
@Slf4j
@Service
public class WarehouseRuleHandler {

    @Resource
    private AttendanceDeptService deptService;
    @Resource
    private CalendarConfigService calendarConfigService;
    @Resource
    private RuleConfigRangeService ruleConfigRangeService;
    @Resource
    private PunchClassConfigApplicationService punchClassConfigApplicationService;



    public void warehouseCalendarAndPunchConfigHandler(AttendanceUser user, AttendanceUser oldRecord) {
        log.info("warehouseCalendarAndPunchConfigHandler | userInfoDO:{} | oldRecord:{}",
                JSON.toJSON(user), JSON.toJSON(oldRecord));
        if (StringUtils.equalsIgnoreCase(WorkStatusEnum.DIMISSION.getCode(), user.getWorkStatus())) {
            return;
        }

        // 查询新的部门信息
        AttendanceDept newDeptInfo = deptService.getByDeptId(user.getDeptId());
        if (ObjectUtil.isNull(newDeptInfo)) {
            log.info("warehouseCalendarAndPunchConfigHandler | newDeptInfo is null | newDeptInfo:{}", user.getDeptId());
            return;
        }

        // 如果常驻国或部门变动，需要重新匹配考勤日历、打卡规则、假期绑定范围
        if (ObjectUtil.notEqual(user.getLocationCountry(), oldRecord.getLocationCountry())
                || ObjectUtil.notEqual(user.getDeptId(), oldRecord.getDeptId())) {
            log.info("warehouseCalendarAndPunchConfigHandler | country or deptId changed");
            CalendarAndPunchHandlerDTO calendarAndPunchHandlerDTO = new CalendarAndPunchHandlerDTO();
            calendarAndPunchHandlerDTO.setUserId(user.getId());
            calendarAndPunchHandlerDTO.setUserCode(oldRecord.getUserCode());
            calendarAndPunchHandlerDTO.setNewCountry(user.getLocationCountry());
            calendarAndPunchHandlerDTO.setNewDeptId(user.getDeptId());
            calendarAndPunchHandlerDTO.setOldCountry(oldRecord.getLocationCountry());
            calendarAndPunchHandlerDTO.setOldDeptId(oldRecord.getDeptId());
            this.warehouseUserCalendarAndPunchHandler(calendarAndPunchHandlerDTO);
        }
    }

    /**
     * 仓内用户日历和打卡规则处理
     */
    public void warehouseUserCalendarAndPunchHandler(CalendarAndPunchHandlerDTO calendarAndPunchHandlerDTO){
        //日历处理
        calendarConfigService.userCalendarHandler(calendarAndPunchHandlerDTO);
        //考勤规则处理
        ruleConfigRangeService.userRuleConfigRangeHandler(calendarAndPunchHandlerDTO);
        //班次处理
        punchClassConfigApplicationService.classSchedulingHandler(calendarAndPunchHandlerDTO);
    }
}
