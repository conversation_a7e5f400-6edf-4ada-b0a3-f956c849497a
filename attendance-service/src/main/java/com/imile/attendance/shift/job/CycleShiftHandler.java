package com.imile.attendance.shift.job;


import com.alibaba.fastjson.JSON;
import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.shift.factory.CycleShiftConfigFactory;
import com.imile.attendance.util.DateHelper;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.Objects;

/**
 * 循环排班自动续期
 *
 * <AUTHOR>
 * @since 2025/4/23
 */
@Slf4j
@Component
public class CycleShiftHandler {

    @Resource
    private CycleShiftConfigFactory cycleShiftConfigFactory;


    @XxlJob(BusinessConstant.JobHandler.CYCLE_SHIFT_HANDLER)
    public ReturnT<String> cycleShiftHandler(String param) {
        XxlJobLogger.log("循环排班自动续期任务开始,参数为:{}", param);

        CycleShiftHandler.CycleShiftHandlerParam handlerParam = StringUtils.isNotBlank(param)
                ? JSON.parseObject(param, CycleShiftHandler.CycleShiftHandlerParam.class)
                : new CycleShiftHandler.CycleShiftHandlerParam();

        long currentDayId;
        Date nowDate;
        if (Objects.nonNull(handlerParam.getDayId())) {
            currentDayId = handlerParam.getDayId();
            nowDate = DateHelper.transferDayIdToDate(currentDayId);
        } else {
            nowDate = new Date();
            currentDayId = DateHelper.getDayId(nowDate);
        }
        cycleShiftConfigFactory.cycleShiftAutoRenewal(nowDate, currentDayId);
        XxlJobLogger.log("循环排班自动续期任务结束");
        return ReturnT.SUCCESS;
    }


    @Data
    private static class CycleShiftHandlerParam {

        /**
         * 日期 yyyyMMdd
         */
        private Long dayId;
    }
}
