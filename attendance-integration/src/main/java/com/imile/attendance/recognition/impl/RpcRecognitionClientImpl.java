package com.imile.attendance.recognition.impl;

import com.alibaba.fastjson.JSON;
import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.recognition.RpcRecognitionClient;
import com.imile.attendance.util.RpcResultProcessor;
import com.imile.recognition.api.face.model.dto.FaceDecryptDTO;
import com.imile.recognition.api.face.model.dto.OssDTO;
import com.imile.recognition.api.face.model.dto.UserFacePhotoDTO;
import com.imile.recognition.api.face.model.dto.UserFaceSearchDTO;
import com.imile.recognition.api.face.model.param.FaceDecryptApiParam;
import com.imile.recognition.api.face.model.param.FaceEncryptionApiParam;
import com.imile.recognition.api.face.model.param.FaceFeatureSaveApiParam;
import com.imile.recognition.api.face.model.param.FaceRecognitionApiParam;
import com.imile.recognition.api.face.model.param.FaceSearchApiParam;
import com.imile.recognition.api.face.model.param.FaceSingleCheckApiParam;
import com.imile.recognition.api.face.service.FaceApi;
import com.imile.recognition.api.ocr.model.dto.CertificatesDTO;
import com.imile.recognition.api.ocr.model.param.CertificatesRecognitionParam;
import com.imile.recognition.api.ocr.service.OcrApi;
import com.imile.recognition.api.watermark.model.dto.WatermarkDTO;
import com.imile.recognition.api.watermark.model.param.WatermarkApiParam;
import com.imile.recognition.api.watermark.service.WatermarkApi;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR> chen
 * @Date 2025/07/09
 * @Time 16:07
 * @Description
 */
@Slf4j
@Service
public class RpcRecognitionClientImpl implements RpcRecognitionClient {

    @Reference(version = "1.0.0", check = false, timeout = 15000)
    private OcrApi ocrApi;

    @Reference(version = "1.0.0", check = false, timeout = 10000)
    private FaceApi faceApi;

    @Reference(version = "1.0.0", check = false, timeout = 10000)
    private WatermarkApi watermarkApi;

    @Override
    public CertificatesDTO certificatesRecognition(String fileKey, String country, String certificateType) {
        CertificatesRecognitionParam param = new CertificatesRecognitionParam();
        param.setSource(BusinessConstant.WPM);
        param.setFileKey(fileKey);
        param.setCountry(country);
        param.setCertificateType(certificateType);
        return RpcResultProcessor.process(ocrApi.certificatesRecognitionWpm(param));
    }

    @Override
    public Boolean detectFace(String fileKey, String imageBase64) {
        FaceRecognitionApiParam param = new FaceRecognitionApiParam();
        param.setSource(BusinessConstant.WPM);
        param.setFileKey(fileKey);
        param.setImageBase64(imageBase64);
        return RpcResultProcessor.process(faceApi.detectFace(param));
    }

    @Override
    public Boolean faceFeatureSave(String userCode,
                                   String fileKey,
                                   String imageBase64,
                                   Boolean facialPrivacyControl) {
        if ((StringUtils.isBlank(fileKey) && StringUtils.isBlank(imageBase64))) {
            return false;
        }
        FaceFeatureSaveApiParam param = new FaceFeatureSaveApiParam();
        param.setSource(BusinessConstant.WPM);
        param.setUserCode(userCode);
        param.setFileKey(fileKey);
        param.setImageBase64(imageBase64);
        param.setFacialPrivacyControl(facialPrivacyControl);
        return RpcResultProcessor.process(faceApi.faceFeatureSave(param));
    }

    @Override
    public UserFaceSearchDTO faceRecognition(String fileKey,
                                             String imageBase64,
                                             String country,
                                             List<String> employeeTypeList,
                                             Boolean facialPrivacyControl) {
        if (StringUtils.isBlank(fileKey) && StringUtils.isBlank(imageBase64)) {
            return new UserFaceSearchDTO();
        }
        FaceSearchApiParam param = new FaceSearchApiParam();
        param.setSource(BusinessConstant.WPM);
        param.setFileKey(fileKey);
        param.setImageBase64(imageBase64);
        param.setCountry(country);
        param.setEmployeeTypeList(employeeTypeList);
        param.setFacialPrivacyControl(facialPrivacyControl);
        return RpcResultProcessor.process(faceApi.faceRecognition(param));
    }

    @Override
    public UserFaceSearchDTO faceSingleCheck(String userCode,
                                             String fileKey,
                                             String imageBase64,
                                             Boolean facialPrivacyControl) {
        FaceSingleCheckApiParam param = new FaceSingleCheckApiParam();
        param.setSource(BusinessConstant.WPM);
        param.setUserCode(userCode);
        param.setImageBase64(imageBase64);
        param.setFileKey(fileKey);
        param.setFacialPrivacyControl(facialPrivacyControl);
        return RpcResultProcessor.process(faceApi.faceSingleCheck(param));
    }

    @Override
    public List<UserFaceSearchDTO> faceRecognitionPlus(String fileKey,
                                                       String imageBase64,
                                                       String country,
                                                       List<String> employeeTypeList,
                                                       Boolean facialPrivacyControl) {
        FaceSearchApiParam param = new FaceSearchApiParam();
        param.setSource(BusinessConstant.WPM);
        param.setImageBase64(imageBase64);
        param.setFileKey(fileKey);
        param.setCountry(country);
        param.setEmployeeTypeList(employeeTypeList);
        param.setFacialPrivacyControl(facialPrivacyControl);
        return RpcResultProcessor.process(faceApi.faceRecognitionPlus(param));
    }

    @Override
    public OssDTO getFaceRecognitionPhoto(String userCode) {
        if (StringUtils.isBlank(userCode)) {
            return null;
        }
        return RpcResultProcessor.process(faceApi.getFaceRecognitionPhoto(userCode));
    }

    @Override
    public OssDTO getFaceRecognitionPhotoByUserId(Long userId) {
        if (userId == null) {
            return null;
        }
        return RpcResultProcessor.process(faceApi.getFaceRecognitionPhotoByUserId(userId));
    }

    @Override
    public Boolean clearUserFacePhoto(String userCode, Long userId) {
        return RpcResultProcessor.process(faceApi.clearUserFacePhoto(userCode, userId));
    }

    @Override
    public WatermarkDTO watermarking(String fileKey, String imageBase64, String watermarkContent) {
        if (StringUtils.isBlank(imageBase64) && StringUtils.isBlank(fileKey)) {
            return null;
        }
        WatermarkApiParam param = new WatermarkApiParam();
        param.setImageBase64(imageBase64);
        param.setFileKey(fileKey);
        param.setWatermarkContent(watermarkContent);
        WatermarkDTO result = RpcResultProcessor.process(watermarkApi.watermarking(param));
        log.info("watermarking process: {}", JSON.toJSONString(result));
        return result;
    }

    @Override
    public List<UserFacePhotoDTO> listFacePhotoByUserCodes(List<String> userCodeList) {
        if (CollectionUtils.isEmpty(userCodeList)) {
            return Collections.emptyList();
        }
        List<UserFacePhotoDTO> result = RpcResultProcessor.process(faceApi.listFacePhotoByUserCodes(userCodeList));
        log.info("listFacePhotoByUserCodes process: {}", JSON.toJSONString(result));
        return result;
    }

    @Override
    public Boolean clearFacePhotoByUserIds(List<Long> userIdList) {
        if (CollectionUtils.isEmpty(userIdList)) {
            return Boolean.FALSE;
        }
        Boolean result = RpcResultProcessor.process(faceApi.clearFacePhotoByUserIds(userIdList));
        log.info("clearFacePhotoByUserIds process: {}", result);
        return result;
    }

    @Override
    public String faceEncryption(String fileKey, boolean photoWatermarking) {
        if (StringUtils.isBlank(fileKey)) {
            return null;
        }
        FaceEncryptionApiParam apiParam = new FaceEncryptionApiParam();
        apiParam.setFileKey(fileKey);
        apiParam.setPhotoWatermarking(photoWatermarking);
        String encFileKey = RpcResultProcessor.process(faceApi.faceEncryption(apiParam));
        log.info("faceEncryption process: {}", encFileKey);
        return encFileKey;
    }

    @Override
    public List<FaceDecryptDTO> faceBatchDecrypt(List<FaceDecryptApiParam> apiParam) {
        if (CollectionUtils.isEmpty(apiParam)) {
            return Collections.emptyList();
        }
        List<FaceDecryptDTO> result = RpcResultProcessor.process(faceApi.faceBatchDecrypt(apiParam));
        log.info("faceBatchDecrypt process: {}", result);
        return result;
    }

}
