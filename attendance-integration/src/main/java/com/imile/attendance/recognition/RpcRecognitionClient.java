package com.imile.attendance.recognition;

import com.imile.recognition.api.face.model.dto.FaceDecryptDTO;
import com.imile.recognition.api.face.model.dto.OssDTO;
import com.imile.recognition.api.face.model.dto.UserFacePhotoDTO;
import com.imile.recognition.api.face.model.dto.UserFaceSearchDTO;
import com.imile.recognition.api.face.model.param.FaceDecryptApiParam;
import com.imile.recognition.api.ocr.model.dto.CertificatesDTO;
import com.imile.recognition.api.watermark.model.dto.WatermarkDTO;

import java.util.List;

/**
 * <AUTHOR> chen
 * @Date 2025/07/09
 * @Time 16:06
 * @Description
 */
public interface RpcRecognitionClient {

    /**
     * 证件识别
     */
    CertificatesDTO certificatesRecognition(String fileKey,
                                            String country,
                                            String certificateType);

    /**
     * 人脸特征检测
     */
    Boolean detectFace(String fileKey, String imageBase64);

    /**
     * 人脸录入
     */
    Boolean faceFeatureSave(String userCode,
                            String fileKey,
                            String imageBase64,
                            Boolean facialPrivacyControl);

    /**
     * 人脸识别搜索
     * 返回得分最高的匹配用户
     */
    UserFaceSearchDTO faceRecognition(String fileKey,
                                      String imageBase64,
                                      String country,
                                      List<String> employeeTypeList,
                                      Boolean facialPrivacyControl);

    /**
     * 人脸一对一识别检测
     */
    UserFaceSearchDTO faceSingleCheck(String userCode,
                                      String fileKey,
                                      String imageBase64,
                                      Boolean facialPrivacyControl);

    /**
     * 人脸识别搜索
     * 返回得分通过的所有用户
     */
    List<UserFaceSearchDTO> faceRecognitionPlus(String fileKey,
                                                String imageBase64,
                                                String country,
                                                List<String> employeeTypeList,
                                                Boolean facialPrivacyControl);

    /**
     * 根据账号查询人脸照片
     *
     * @param userCode
     * @return
     */
    OssDTO getFaceRecognitionPhoto(String userCode);

    /**
     * 根据ID查询人脸照片
     *
     * @param userId
     * @return
     */
    OssDTO getFaceRecognitionPhotoByUserId(Long userId);


    /**
     * 清除指定用户人脸照片
     *
     * @param userCode 用户编码
     * @param userId   用户ID
     * @return Boolean
     */
    Boolean clearUserFacePhoto(String userCode, Long userId);

    /**
     * 图片加水印
     *
     * @return 图片水印
     */
    WatermarkDTO watermarking(String fileKey,
                              String imageBase64,
                              String watermarkContent);

    /**
     * 批量查询用户人脸录入照
     *
     * @param userCodeList 用户编码列表
     * @return 人脸录入照集合
     */
    List<UserFacePhotoDTO> listFacePhotoByUserCodes(List<String> userCodeList);


    /**
     * 批量清理人脸照片
     *
     * @param userIdList 用户ID集合
     * @return Boolean
     */
    Boolean clearFacePhotoByUserIds(List<Long> userIdList);

    /**
     * 图片加密
     *
     * @param fileKey OSS短链
     * @return 加密的图片短链
     */
    String faceEncryption(String fileKey, boolean photoWatermarking);

    /**
     * 图片解密
     */
    List<FaceDecryptDTO> faceBatchDecrypt(List<FaceDecryptApiParam> apiParam);
}
