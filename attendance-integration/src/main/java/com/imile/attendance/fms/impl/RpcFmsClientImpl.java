package com.imile.attendance.fms.impl;

import com.alibaba.fastjson.JSON;
import com.imile.attendance.fms.RpcFmsClient;
import com.imile.attendance.util.RpcResultProcessor;
import com.imile.fms.api.bill.api.FeeBillingRecordApi;
import com.imile.fms.api.bill.dto.BillingRecordDTO;
import com.imile.fms.api.bill.dto.FeeBillingRecordQuery;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/7/21
 */
@Slf4j
@Service
public class RpcFmsClientImpl implements RpcFmsClient {
    @Reference(version = "1.0.0", check = false, timeout = 30000)
    private FeeBillingRecordApi feeBillingRecordApi;

    @Override
    public List<BillingRecordDTO> queryFeeBillingRecords(FeeBillingRecordQuery query) {
        log.info("queryFeeBillingRecords||query:{}", JSON.toJSONString(query));
        List<BillingRecordDTO> recordDTOList = RpcResultProcessor.process(feeBillingRecordApi.queryBillingRecords(query));
        log.info("queryFeeBillingRecords||result:{}", recordDTOList);
        return recordDTOList;
    }
}
