package com.imile.attendance.purchase.impl;

import com.imile.WareHouseEmployeeApi;
import com.imile.attendance.purchase.RpcPurchaseClient;
import com.imile.attendance.util.RpcResultProcessor;
import com.imile.common.page.PaginationResult;
import com.imile.purchase.api.contract.api.PurchaseContractQryApi;
import com.imile.purchase.api.contract.req.HubServiceConfigRes;
import com.imile.purchase.api.contract.response.HubServiceConfigResponse;
import com.imile.purchase.api.supplier.api.SupplierManagerApi;
import com.imile.purchase.api.supplier.dto.CommonSupplierBasicDTO;
import com.imile.purchase.api.supplier.vo.CommonSupplierQueryVO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR> chen
 * @Date 2025/07/09
 * @Time 17:22
 * @Description
 */
@Service
public class RpcPurchaseClientImpl implements RpcPurchaseClient {

    @Reference(version = "1.0.0", check = false, timeout = 3000)
    private SupplierManagerApi supplierManagerApi;

    @Reference(version = "1.0.0", check = false, timeout = 3000)
    private PurchaseContractQryApi purchaseContractQryApi;

    @Reference(version = "1.0.0", check = false, timeout = 30000)
    private WareHouseEmployeeApi wareHouseEmployeeApi;

    @Override
    public PaginationResult<CommonSupplierBasicDTO> supplierPage(CommonSupplierQueryVO query) {
        query.setSourceType("C&BMS");
        return RpcResultProcessor.process(supplierManagerApi.supplierPage(query));
    }

    @Override
    public List<HubServiceConfigResponse> getHubServiceConfig(HubServiceConfigRes hubServiceConfigRes) {
        if (CollectionUtils.isEmpty(hubServiceConfigRes.getOcCodeList())) {
            return Collections.emptyList();
        }
        return RpcResultProcessor.process(purchaseContractQryApi.getHubServiceConfig(hubServiceConfigRes));
    }

    @Override
    public void addEmployeeCertificateDiff(String userCode) {
        wareHouseEmployeeApi.addEmployeeCertificateDiff(userCode);
    }
}
