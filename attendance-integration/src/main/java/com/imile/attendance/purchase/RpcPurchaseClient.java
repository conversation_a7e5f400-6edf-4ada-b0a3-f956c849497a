package com.imile.attendance.purchase;

import com.imile.common.page.PaginationResult;
import com.imile.purchase.api.contract.req.HubServiceConfigRes;
import com.imile.purchase.api.contract.response.HubServiceConfigResponse;
import com.imile.purchase.api.supplier.dto.CommonSupplierBasicDTO;
import com.imile.purchase.api.supplier.vo.CommonSupplierQueryVO;

import java.util.List;

/**
 * <AUTHOR> chen
 * @Date 2025/07/09
 * @Time 17:22
 * @Description
 */
public interface RpcPurchaseClient {


    PaginationResult<CommonSupplierBasicDTO> supplierPage(CommonSupplierQueryVO query);


    List<HubServiceConfigResponse> getHubServiceConfig(HubServiceConfigRes hubServiceConfigRes);

    void addEmployeeCertificateDiff(String userCode);
}
