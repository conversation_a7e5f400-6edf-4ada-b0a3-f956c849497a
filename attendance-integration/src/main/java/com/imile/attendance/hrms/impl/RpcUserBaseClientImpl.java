package com.imile.attendance.hrms.impl;

import com.alibaba.fastjson.JSON;
import com.imile.attendance.hrms.RpcUserBaseClient;
import com.imile.attendance.util.RpcResultProcessor;
import com.imile.hrms.api.primary.model.param.user.UserAddParam;
import com.imile.hrms.api.primary.model.param.user.UserStatusSwitchParam;
import com.imile.hrms.api.primary.model.param.user.UserUpdateParam;
import com.imile.hrms.api.primary.service.UserBaseApi;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2025/7/15
 */
@Slf4j
@Component
public class RpcUserBaseClientImpl implements RpcUserBaseClient {

    @Reference(version = "1.0.0", retries = 0, check = false, timeout = 15000)
    private UserBaseApi userBaseApi;

    @Override
    public String addUser(UserAddParam param) {
        log.info("addUser params: {}", JSON.toJSONString(param));
        return RpcResultProcessor.process(userBaseApi.addUser(param));
    }

    @Override
    public Boolean updateUser(UserUpdateParam param) {
        log.info("updateUser params: {}", JSON.toJSONString(param));
        return RpcResultProcessor.process(userBaseApi.updateUser(param));
    }

    @Override
    public Boolean switchUserStatus(UserStatusSwitchParam param) {
        log.info("switchUserStatus params: {}", JSON.toJSONString(param));
        return RpcResultProcessor.process(userBaseApi.switchUserStatus(param));
    }
}
