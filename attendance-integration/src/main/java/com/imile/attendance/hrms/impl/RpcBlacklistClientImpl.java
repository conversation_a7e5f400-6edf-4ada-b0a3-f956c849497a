package com.imile.attendance.hrms.impl;

import com.imile.attendance.hrms.RpcBlacklistClient;
import com.imile.attendance.util.RpcResultProcessor;
import com.imile.hrms.api.blacklist.api.BlacklistApi;
import com.imile.hrms.api.blacklist.dto.BlacklistInfoDTO;
import com.imile.hrms.api.blacklist.param.UserCertificateInfoParam;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR> chen
 * @Date 2025/07/09
 * @Time 17:43
 * @Description
 */
@Service
public class RpcBlacklistClientImpl implements RpcBlacklistClient {

    @Reference(version = "1.0.0", retries = 0, check = false, timeout = 10000)
    private BlacklistApi blacklistApi;

    @Override
    public BlacklistInfoDTO getBlacklistInfo(String userCode) {
        return RpcResultProcessor.process(blacklistApi.getBlacklistInfo(userCode));
    }

    @Override
    public BlacklistInfoDTO checkExitBlacklist(List<UserCertificateInfoParam> certificateInfoDTOList) {
        return RpcResultProcessor.process(blacklistApi.checkExitBlacklist(certificateInfoDTOList));
    }
}
