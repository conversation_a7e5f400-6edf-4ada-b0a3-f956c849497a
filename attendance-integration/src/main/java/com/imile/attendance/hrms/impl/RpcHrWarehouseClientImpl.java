package com.imile.attendance.hrms.impl;

import com.imile.attendance.hrms.RpcHrWarehouseClient;
import com.imile.hrms.api.warehouse.api.WarehouseSyncApi;
import com.imile.hrms.api.warehouse.dto.HrmsWarehouseDetailAbnormalDTO;
import com.imile.hrms.api.warehouse.dto.HrmsWarehouseDetailDTO;
import com.imile.hrms.api.warehouse.dto.HrmsWarehouseDetailSnapshotDTO;
import com.imile.hrms.api.warehouse.dto.HrmsWarehouseRecordDTO;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/7/25
 */
@RequiredArgsConstructor
@Component
public class RpcHrWarehouseClientImpl implements RpcHrWarehouseClient {
    @Reference(version = "1.0.0", retries = 0, check = false, timeout = 60000)
    private WarehouseSyncApi warehouseSyncApi;

    @Override
    public void warehouseBatch(List<HrmsWarehouseDetailDTO> param) {
        warehouseSyncApi.warehouseBatch(param);
    }

    @Override
    public void warehouseAbnormalBatch(List<HrmsWarehouseDetailAbnormalDTO> param) {
        warehouseSyncApi.warehouseAbnormalBatch(param);
    }

    @Override
    public void warehouseRecordBatch(List<HrmsWarehouseRecordDTO> param) {
        warehouseSyncApi.warehouseRecordBatch(param);
    }

    @Override
    public void warehouseSnapshotBatch(List<HrmsWarehouseDetailSnapshotDTO> param) {
        warehouseSyncApi.warehouseSnapshotBatch(param);
    }
}
