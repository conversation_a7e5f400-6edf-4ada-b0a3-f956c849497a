package com.imile.attendance.hrms;


import com.imile.hrms.api.primary.model.param.user.UserAddParam;
import com.imile.hrms.api.primary.model.param.user.UserStatusSwitchParam;
import com.imile.hrms.api.primary.model.param.user.UserUpdateParam;


/**
 * 用户RPC写入
 *
 * <AUTHOR>
 * @since 2025/7/15
 */
public interface RpcUserBaseClient {

    /**
     * 新增用户
     */
    String addUser(UserAddParam param);

    /**
     * 更新用户
     */
    Boolean updateUser(UserUpdateParam param);

    /**
     * 用户状态变更
     */
    Boolean switchUserStatus(UserStatusSwitchParam param);
}
