package com.imile.attendance.hrms.impl;

import com.google.common.collect.Lists;
import com.imile.attendance.hrms.RpcUserClient;
import com.imile.attendance.util.RpcResultProcessor;
import com.imile.hrms.api.primary.model.param.user.UserAddParam;
import com.imile.hrms.api.primary.model.param.user.UserUpdateParam;
import com.imile.hrms.api.primary.service.UserBaseApi;
import com.imile.hrms.api.user.api.UserApi;
import com.imile.hrms.api.user.enums.UserDynamicFieldEnum;
import com.imile.hrms.api.user.result.UserDynamicInfoDTO;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR> chen
 * @Date 2025/3/24
 * @Description
 */
@Component
public class RpcUser<PERSON>lientImpl implements RpcUserClient {

    @Reference(version = "1.0.0", retries = 0, check = false, timeout = 15000)
    private UserApi userApi;

    private static final int SPLIT_SIZE = 500;

    @Reference(version = "1.0.0", retries = 0, check = false, timeout = 15000)
    private UserBaseApi userBaseApi;

    @Override
    public List<UserDynamicInfoDTO> listUserDynamicInfo(List<String> userCodeList,
                                                        List<UserDynamicFieldEnum> dynamicFieldList) {
        if (userCodeList == null || userCodeList.isEmpty()) {
            return Collections.emptyList();
        }
        if (userCodeList.size() >= 1000) {
            List<List<String>> partition = Lists.partition(userCodeList, SPLIT_SIZE);
            return partition.stream()
                    .flatMap(codes -> RpcResultProcessor.process(
                            userApi.listUserDynamicInfo(codes, dynamicFieldList)).stream())
                    .collect(Collectors.toList());
        }
        return RpcResultProcessor.process(userApi.listUserDynamicInfo(userCodeList, dynamicFieldList));
    }

    @Override
    public List<UserDynamicInfoDTO> listUserDynamicInfoById(List<Long> idList, List<UserDynamicFieldEnum> dynamicFieldList) {
        if (idList == null || idList.isEmpty()) {
            return Collections.emptyList();
        }
        if (idList.size() >= 1000) {
            List<List<Long>> partition = Lists.partition(idList, SPLIT_SIZE);
            return partition.stream()
                    .flatMap(ids -> RpcResultProcessor.process(
                            userApi.listUserDynamicInfoById(ids, dynamicFieldList)).stream())
                    .collect(Collectors.toList());
        }
        return RpcResultProcessor.process(userApi.listUserDynamicInfoById(idList, dynamicFieldList));
    }

    @Override
    public String addUser(UserAddParam param) {
        return RpcResultProcessor.process(userBaseApi.addUser(param));
    }

    @Override
    public Boolean updateUser(UserUpdateParam param) {
        return RpcResultProcessor.process(userBaseApi.updateUser(param));
    }
}
