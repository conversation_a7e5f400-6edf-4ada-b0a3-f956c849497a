package com.imile.attendance.hrms;

import com.imile.hrms.api.warehouse.dto.HrmsWarehouseDetailAbnormalDTO;
import com.imile.hrms.api.warehouse.dto.HrmsWarehouseDetailDTO;
import com.imile.hrms.api.warehouse.dto.HrmsWarehouseDetailSnapshotDTO;
import com.imile.hrms.api.warehouse.dto.HrmsWarehouseRecordDTO;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/7/17
 */
public interface RpcHrWarehouseClient {

    void warehouseBatch(List<HrmsWarehouseDetailDTO> param);

    void warehouseAbnormalBatch(List<HrmsWarehouseDetailAbnormalDTO> param);

    void warehouseRecordBatch(List<HrmsWarehouseRecordDTO> param);

    void warehouseSnapshotBatch(List<HrmsWarehouseDetailSnapshotDTO> param);
}
