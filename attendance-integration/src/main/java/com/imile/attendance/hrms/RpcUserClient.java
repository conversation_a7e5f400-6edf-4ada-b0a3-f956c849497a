package com.imile.attendance.hrms;


import com.imile.hrms.api.primary.model.param.user.UserAddParam;
import com.imile.hrms.api.primary.model.param.user.UserUpdateParam;
import com.imile.hrms.api.user.enums.UserDynamicFieldEnum;
import com.imile.hrms.api.user.result.UserDynamicInfoDTO;

import java.util.List;

/**
 * 用户RPC查询
 *
 * <AUTHOR>
 * @Date 2025/4/26
 */
public interface RpcUserClient {

    /**
     * 批量获取人员动态信息
     *
     * @param userCodeList     人员编码列表（一次性不能超过1000，自行分批）
     * @param dynamicFieldList 动态字段列表（详见UserDynamicFieldEnum）
     * @return List<UserDynamicInfoDTO>
     */
    List<UserDynamicInfoDTO> listUserDynamicInfo(List<String> userCodeList,
                                                 List<UserDynamicFieldEnum> dynamicFieldList);

    /**
     * 添加人员
     *
     * @param param UserAddParam
     * @return String
     */
    String addUser(UserAddParam param);

    /**
     * 更新人员
     *
     * @param param UserUpdateParam
     * @return Boolean
     */
    Boolean updateUser(UserUpdateParam param);


    /**
     * 根据人员ID批量获取人员动态业务信息
     *
     * @param idList           人员ID列表（一次性不能超过1000，自行分批）
     * @param dynamicFieldList 动态字段列表（详见UserDynamicFieldEnum）
     * @return List<UserDynamicInfoDTO>
     */
    List<UserDynamicInfoDTO> listUserDynamicInfoById(List<Long> idList, List<UserDynamicFieldEnum> dynamicFieldList);
}
