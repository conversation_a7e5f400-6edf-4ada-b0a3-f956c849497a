package com.imile.attendance.hrms;

import com.imile.hrms.api.blacklist.dto.BlacklistInfoDTO;
import com.imile.hrms.api.blacklist.param.UserCertificateInfoParam;
import com.imile.rpc.common.RpcResult;

import java.util.List;

/**
 * <AUTHOR> chen
 * @Date 2025/07/09
 * @Time 17:42
 * @Description
 */
public interface RpcBlacklistClient {

    /**
     * 获取黑名单信息
     * 内部走数据库，适用于常规场景
     *
     * @param userCode 人员编码
     * @return BlacklistInfoDTO
     */
    BlacklistInfoDTO getBlacklistInfo(String userCode);

    /**
     * 校验用户信息是否已被拉黑
     *
     * @param certificateInfoDTOList 用户证件信息
     * @return LockdownInfoDTO
     */
    BlacklistInfoDTO checkExitBlacklist(List<UserCertificateInfoParam> certificateInfoDTOList);
}
