package com.imile.attendance.gensis.impl;

import com.imile.attendance.gensis.RpcUserCertificateClient;
import com.imile.attendance.util.RpcResultProcessor;
import com.imile.genesis.api.enums.OperationSceneEnum;
import com.imile.genesis.api.model.param.user.UserCertificateDuplicateCheckParam;
import com.imile.genesis.api.model.param.user.UserCertificateSaveParam;
import com.imile.genesis.api.model.result.user.UserCertificateCheckResultDTO;
import com.imile.genesis.api.model.result.user.UserCertificateDTO;
import com.imile.genesis.api.service.UserCertificateApi;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR> chen
 * @Date 2025/07/09
 * @Time 20:48
 * @Description
 */
@Service
public class RpcUserCertificateClientImpl implements RpcUserCertificateClient {

    @Reference(version = "1.0.0", retries = 0, check = false, timeout = 10000)
    private UserCertificateApi userCertificateApi;

    @Override
    public List<UserCertificateDTO> getUserCertificateList(String userCode) {
        return RpcResultProcessor.process(userCertificateApi.getUserCertificateList(userCode));
    }

    @Override
    public List<UserCertificateDTO> listUserCertificate(List<String> userCodeList, List<String> certificateTypeCodeList) {
        return RpcResultProcessor.process(userCertificateApi.listUserCertificate(userCodeList, certificateTypeCodeList));
    }

    @Override
    public void saveUserCertificate(String userCode, List<UserCertificateSaveParam> paramList, OperationSceneEnum operationSceneEnum) {
        userCertificateApi.saveUserCertificate(userCode, paramList, operationSceneEnum);
    }

    @Override
    public UserCertificateCheckResultDTO checkUserCertificateDuplicate(UserCertificateDuplicateCheckParam param) {
        return RpcResultProcessor.process(userCertificateApi.checkUserCertificateDuplicate(param));
    }
}
