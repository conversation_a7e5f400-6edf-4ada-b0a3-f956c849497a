package com.imile.attendance.gensis.support;

import com.imile.attendance.gensis.RpcUserCertificateClient;
import com.imile.genesis.api.enums.CertificateTypeEnum;
import com.imile.genesis.api.enums.OperationSceneEnum;
import com.imile.genesis.api.model.component.Certificate;
import com.imile.genesis.api.model.param.user.UserCertificateDuplicateCheckParam;
import com.imile.genesis.api.model.param.user.UserCertificateSaveParam;
import com.imile.genesis.api.model.result.user.UserCertificateCheckResultDTO;
import com.imile.genesis.api.model.result.user.UserCertificateDTO;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR> chen
 * @Date 2025/07/09
 * @Time 20:49
 * @Description
 */
@Service
public class RpcUserCertificateSupport {

    @Resource
    private RpcUserCertificateClient rpcUserCertificateClient;

    /**
     * 根据用户编码和证件类型枚举查询用户证件信息
     */
    public List<UserCertificateDTO> getUserCertificateList(String userCode, CertificateTypeEnum certificateTypeEnum) {
        return getUserCertificateList(userCode, certificateTypeEnum.getCode());
    }

    /**
     * 根据用户编码和证件类型查询用户证件信息
     */
    public List<UserCertificateDTO> getUserCertificateList(String userCode, String certificateTypeCode) {
        return listUserCertificateList(Collections.singletonList(userCode), certificateTypeCode);
    }

    /**
     * 根据用户编码列表和证件类型查询用户证件信息
     */
    public List<UserCertificateDTO> listUserCertificateList(List<String> userCodeList, String certificateTypeCode) {
        return rpcUserCertificateClient.listUserCertificate(userCodeList, Collections.singletonList(certificateTypeCode));
    }

    public void saveUserCertificate(String userCode, List<UserCertificateSaveParam> paramList) {
        rpcUserCertificateClient.saveUserCertificate(userCode, paramList, OperationSceneEnum.HRMS_ATTENDANCE_IN_OUT_MANAGE);
    }

    public UserCertificateCheckResultDTO checkUserCertificateDuplicate(String certificateCode, String certificateTypeCode, String userCode) {
        UserCertificateDuplicateCheckParam param = new UserCertificateDuplicateCheckParam();
        Certificate certificate = new Certificate();
        certificate.setCertificateTypeCode(certificateTypeCode);
        certificate.setCertificateCode(certificateCode);
        param.setCertificateList(Collections.singletonList(certificate));
        param.setUserCode(userCode);
        return rpcUserCertificateClient.checkUserCertificateDuplicate(param);
    }
}
