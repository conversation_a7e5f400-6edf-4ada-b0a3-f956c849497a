package com.imile.attendance.gensis;

import com.imile.genesis.api.enums.OperationSceneEnum;
import com.imile.genesis.api.model.param.user.UserCertificateDuplicateCheckParam;
import com.imile.genesis.api.model.param.user.UserCertificateSaveParam;
import com.imile.genesis.api.model.result.user.UserCertificateCheckResultDTO;
import com.imile.genesis.api.model.result.user.UserCertificateDTO;
import com.imile.rpc.common.RpcResult;

import java.util.List;

/**
 * <AUTHOR> chen
 * @Date 2025/07/09
 * @Time 20:47
 * @Description
 */
public interface RpcUserCertificateClient {

    /**
     * 获取人员证件列表
     *
     * @param userCode 人员编码
     * @return List<UserCertificateDTO>
     */
    List<UserCertificateDTO> getUserCertificateList(String userCode);

    /**
     * 批量获取人员证件
     *
     * @param userCodeList            人员编码列表（一次性不能超过500，自行分批）
     * @param certificateTypeCodeList 证件类型编码列表
     * @return List<UserCertificateDTO>
     */
    List<UserCertificateDTO> listUserCertificate(List<String> userCodeList, List<String> certificateTypeCodeList);

    /**
     * 保存用户证件
     *
     * @param userCode  用户编码
     * @param paramList 证件信息
     */
    void saveUserCertificate(String userCode, List<UserCertificateSaveParam> paramList, OperationSceneEnum operationSceneEnum);

    /**
     * 校验人员证件是否重复
     */
    UserCertificateCheckResultDTO checkUserCertificateDuplicate(UserCertificateDuplicateCheckParam param);
}
