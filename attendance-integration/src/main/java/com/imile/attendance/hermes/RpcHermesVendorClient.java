package com.imile.attendance.hermes;

import com.imile.hermes.vendor.dto.VendorInfoApiDTO;
import com.imile.hermes.vendor.dto.VendorInfoSimpleApiDTO;

import java.util.List;

/**
 * <AUTHOR> chen
 * @Date 2025/07/14
 * @Time 09:58
 * @Description
 */
public interface RpcHermesVendorClient {


    /**
     * 根据code获取供应商
     *
     * @param vendorCodes
     * @return
     */
    List<VendorInfoApiDTO> getByVendorCodes(List<String> vendorCodes);

    /**
     * 查询供应商信息
     *
     * @param vendorIds
     * @return
     */
    List<VendorInfoSimpleApiDTO> selectVendorList(List<Long> vendorIds);
}
