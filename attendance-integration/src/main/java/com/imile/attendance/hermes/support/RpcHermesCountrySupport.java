package com.imile.attendance.hermes.support;

import java.util.List;
import java.util.concurrent.TimeUnit;

import javax.annotation.Resource;

import org.springframework.stereotype.Component;

import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.Cached;
import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.constants.Constants;
import com.imile.attendance.hermes.RpcHermesCountryClient;
import com.imile.attendance.hermes.dto.CountryDTO;
import com.imile.attendance.util.CollectionUtils;
import com.imile.hermes.business.dto.CountryConfigDTO;
import com.imile.hermes.business.query.CountryApiQuery;
import com.imile.ucenter.api.context.RequestInfoHolder;

import jodd.util.StringUtil;

/**
 * <AUTHOR> chen
 * @Date 2025/3/24 
 * @Description hermes国家rpc查询支持类
 */
@Component
public class RpcHermesCountrySupport {

    @Resource
    private RpcHermesCountryClient rpcHermesCountryClient;


    /**
     * 查询所有国家, 并缓存
     * @return 国家配置列表
     */
    @Cached(name = Constants.CacheKey.COUNTRY_CACHE_KEY,
            cacheType = CacheType.REMOTE,
            expire = 15,
            timeUnit = TimeUnit.MINUTES
    )
    public List<CountryDTO> listAllCountry(){
        return CollectionUtils.convert(rpcHermesCountryClient.queryAllCountryConfigList(), CountryDTO.class);
    }



    /**
     * 查询指定国家, 并缓存
     * @param countryName 国家名称
     * @return 国家配置
     */
    @Cached(name = Constants.CacheKey.COUNTRY_CACHE_KEY,
            key = "#countryName",
            cacheType = CacheType.REMOTE,
            expire = 15,
            timeUnit = TimeUnit.MINUTES
    )
    public CountryDTO queryCountry(String countryName) {
        if (StringUtil.isEmpty(countryName)) {
            return null;
        }

        CountryApiQuery countryApiQuery = new CountryApiQuery();
        Long orgId = RequestInfoHolder.getOrgId() == null ?
                BusinessConstant.DEFAULT_ORG_ID : RequestInfoHolder.getOrgId();
        countryApiQuery.setOrgId(orgId);
        countryApiQuery.setCountryName(countryName);
        CountryConfigDTO countryConfigDTO = rpcHermesCountryClient.queryCountryConfig(countryApiQuery);
        return CollectionUtils.convertSingle(countryConfigDTO, CountryDTO.class);
    }

    /**
     * 查询指定国家列表
     * @param countryList 国家列表
     * @return 国家配置列表
     */
    public List<CountryDTO> queryCountryList(List<String> countryList) {
        CountryApiQuery countryQuery = new CountryApiQuery();
        countryQuery.setCountryNames(countryList);
        countryQuery.setOrgId(BusinessConstant.DEFAULT_ORG_ID);
        List<CountryConfigDTO> countryConfigDTOList = rpcHermesCountryClient.queryCountryConfigList(countryQuery);
        return CollectionUtils.convert(countryConfigDTOList, CountryDTO.class);
    }
}
