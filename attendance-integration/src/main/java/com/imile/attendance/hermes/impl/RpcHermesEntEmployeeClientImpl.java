package com.imile.attendance.hermes.impl;

import com.imile.attendance.context.RequestInfoHolder;
import com.imile.attendance.hermes.RpcHermesEntEmployeeClient;
import com.imile.attendance.util.RpcResultProcessor;
import com.imile.hermes.enterprise.api.EntEmployeeApi;
import com.imile.hermes.enterprise.dto.EntEmployeeApiDTO;
import com.imile.hermes.enterprise.query.EntEmployeeApiQuery;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/7/15
 * @Description
 */
@Component
public class RpcHermesEntEmployeeClientImpl implements RpcHermesEntEmployeeClient {

    @Reference(version = "1.0.0", retries = 0, check = false, timeout = 10000)
    private EntEmployee<PERSON>pi entEmployeeApi;

    @Override
    public EntEmployeeApiDTO getEmployeeByCode(String userCode) {
        EntEmployeeApiQuery apiQuery = new EntEmployeeApiQuery();
        apiQuery.setOrgId(RequestInfoHolder.getOrgId());
        apiQuery.setUserCodes(Collections.singletonList(userCode));
        List<EntEmployeeApiDTO> employeeList = RpcResultProcessor.process(entEmployeeApi.getEmployeeList(apiQuery));
        return CollectionUtils.isNotEmpty(employeeList) ? employeeList.get(0) : null;
    }
}
