package com.imile.attendance.hermes.impl;

import com.imile.attendance.hermes.RpcHermesVendorClient;
import com.imile.common.exception.BusinessException;
import com.imile.hermes.vendor.api.VendorInfoApi;
import com.imile.hermes.vendor.dto.VendorInfoApiDTO;
import com.imile.hermes.vendor.dto.VendorInfoSimpleApiDTO;
import com.imile.hermes.vendor.query.VendorInfoApiQuery;
import com.imile.rpc.common.RpcResult;
import com.imile.util.lang.I18nUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR> chen
 * @Date 2025/07/14
 * @Time 09:59
 * @Description
 */
@Slf4j
@Service
public class RpcHermesVendorClientImpl implements RpcHermesVendorClient {

    @Reference(version = "1.0.0", check = false, timeout = 30000)
    private VendorInfoApi vendorInfoApi;

    @Override
    public List<VendorInfoApiDTO> getByVendorCodes(List<String> vendorCodes) {
        if (CollectionUtils.isEmpty(vendorCodes)) {
            return new ArrayList<>();
        }
        RpcResult<List<VendorInfoApiDTO>> byVendorCodes = vendorInfoApi.getByVendorCodes(vendorCodes);
        if (!byVendorCodes.isSuccess()) {
            throw BusinessException.get(byVendorCodes.getErrorCode(), I18nUtils.getMessage(byVendorCodes.getMessage()));
        }
        return byVendorCodes.getResult();
    }

    @Override
    public List<VendorInfoSimpleApiDTO> selectVendorList(List<Long> vendorIds) {
        if (CollectionUtils.isEmpty(vendorIds)) {
            return new ArrayList<>();
        }
        VendorInfoApiQuery query = new VendorInfoApiQuery();
        query.setVendorIds(vendorIds);
        RpcResult<List<VendorInfoSimpleApiDTO>> rpcResult = vendorInfoApi.getSimpleList(query);
        if (!rpcResult.isSuccess()) {
            throw BusinessException.get(rpcResult.getErrorCode(), I18nUtils.getMessage(rpcResult.getMessage()));
        }

        return rpcResult.getResult();
    }
}
