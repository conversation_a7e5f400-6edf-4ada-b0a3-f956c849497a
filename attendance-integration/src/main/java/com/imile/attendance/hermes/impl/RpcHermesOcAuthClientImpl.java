package com.imile.attendance.hermes.impl;

import com.alibaba.fastjson.JSON;
import com.imile.attendance.hermes.RpcHermesOcAuthClient;
import com.imile.attendance.util.RpcResultProcessor;
import com.imile.hermes.enterprise.dto.EntOcSlimpleApiDTO;
import com.imile.hermes.permission.api.UserAuthorizeApi;
import com.imile.rpc.common.RpcResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/7/15
 * @Description
 */
@Service
@Slf4j
public class RpcHermesOcAuthClientImpl implements RpcHermesOcAuthClient {

    @Reference(version = "1.0.0", check = false, timeout = 10000)
    private UserAuthorizeApi userAuthorizeApi;

    @Override
    public List<EntOcSlimpleApiDTO> getOcByUserId(Long orgId, Long userId) {
        RpcResult<List<EntOcSlimpleApiDTO>> rpcResult = userAuthorizeApi.getOcByUserId(orgId, userId);
        List<EntOcSlimpleApiDTO> ocSlimpleApiDTOList = RpcResultProcessor.process(rpcResult);
        return ocSlimpleApiDTOList;
    }

    @Override
    public List<EntOcSlimpleApiDTO> selectOcByUserId(Long orgId, Long userId) {
        RpcResult<List<EntOcSlimpleApiDTO>> rpcResult = userAuthorizeApi.selectOcByUserId(orgId, userId);
        List<EntOcSlimpleApiDTO> ocSlimpleApiDTOList = RpcResultProcessor.process(rpcResult);
        log.info("selectOcByUserId, orgID:{},userId:{},ocSlimpleApiDTOList:{}", orgId, userId, JSON.toJSONString(ocSlimpleApiDTOList));
        return ocSlimpleApiDTOList;
    }
}
