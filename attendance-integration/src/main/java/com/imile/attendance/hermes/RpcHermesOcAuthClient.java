package com.imile.attendance.hermes;

import com.imile.hermes.enterprise.dto.EntOcSlimpleApiDTO;

import java.util.List;

public interface RpcHermesOcAuthClient {
    /**
     * 获取用户权限网点信息(包含自身所在网点)
     *
     * @param orgId  企业id
     * @param userId 用户id
     * @return
     */
    List<EntOcSlimpleApiDTO> getOcByUserId(Long orgId, Long userId);

    /**
     * 获取用户权限网点信息(不包含自身所在网点)
     *
     * @param orgId  企业id
     * @param userId 用户id
     * @return
     */
    List<EntOcSlimpleApiDTO> selectOcByUserId(Long orgId, Long userId);
}
