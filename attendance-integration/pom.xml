<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.imile</groupId>
        <artifactId>attendance</artifactId>
        <version>1.0.0-SNAPSHOT</version>
    </parent>

    <artifactId>attendance-integration</artifactId>

    <name>attendance-integration</name>
    <url>http://maven.apache.org</url>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.imile</groupId>
            <artifactId>attendance-common</artifactId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.imile</groupId>
            <artifactId>com.imile.framework.aliyun</artifactId>
        </dependency>

        <dependency>
            <groupId>com.imile</groupId>
            <artifactId>hermes-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.imile</groupId>
            <artifactId>ipep-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.imile</groupId>
            <artifactId>hrms-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.imile</groupId>
            <artifactId>purchase-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.imile</groupId>
            <artifactId>genesis-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.imile</groupId>
            <artifactId>bpm-api</artifactId>
            <version>${bpm.version}</version>
        </dependency>
        <dependency>
            <groupId>com.imile</groupId>
            <artifactId>permission-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.imile</groupId>
            <artifactId>dtrack-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.imile</groupId>
            <artifactId>saas-tms-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alicp.jetcache</groupId>
            <artifactId>jetcache-anno</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alicp.jetcache</groupId>
            <artifactId>jetcache-redisson</artifactId>
        </dependency>
        <dependency>
            <groupId>com.imile</groupId>
            <artifactId>recognition-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.imile</groupId>
            <artifactId>pcs-api</artifactId>
            <version>1.0.1</version>
        </dependency>
        <dependency>
            <groupId>com.imile</groupId>
            <artifactId>fms-api</artifactId>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>2.2.8.RELEASE</version>
                <configuration>
                    <skip>true</skip>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>
